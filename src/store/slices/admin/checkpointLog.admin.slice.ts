import {
  createSlice,
  createAsyncThunk,
  PayloadAction,
} from "@reduxjs/toolkit";
import { checkpointLogAdminApi } from "../../../services/mainApi/admin/checkpointLog.admin.mainApi";
import {
  AdminCheckpointLogEntry,
  AdminCheckpointLogListParams,
} from "../../../services/mainApi/admin/types/checkpointLog.admin.mainApi.types";
import { BasePaginationMeta } from "../../../services/mainApi/types/base.mainApi.types";
import { RootState } from "../../../store";
import { AdminRole } from "../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminUser } from "../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminLabel } from "../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminBranch } from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminCheckpoint } from "../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";
import { AdminZone } from "../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import dayjs from "dayjs";

interface State {
  checkpointLogs: AdminCheckpointLogEntry[];
  loading: boolean;
  error: string | null;

  filter: {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    branchId: string | number | null;
    zoneId: string | number | null;
    zoneLabels: (string | number)[];
    checkpointId: string | number | null;
    checkpointLabels: (string | number)[];
    roleId: string | number | null;
    userId: string | number | null;
    userLabels: (string | number)[];
    deviceId: string | number | null;
    deviceLabels: (string | number)[];
    page: number;
    limit: number;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;

  // Filter selection objects
  selectedRole: AdminRole | null;
  selectedUser: AdminUser | null;
  selectedDevice: AdminDevice | null;
  selectedCheckpoint: AdminCheckpoint | null;
  selectedZone: AdminZone | null;
  selectedBranch: AdminBranch | null;
  selectedUserLabels: AdminLabel[];
  selectedDeviceLabels: AdminLabel[];
  selectedZoneLabels: AdminLabel[];
  selectedCheckpointLabels: AdminLabel[];
}

const initialState: State = {
  checkpointLogs: [],
  loading: false,
  error: null,

  filter: {
    startDate: dayjs().subtract(1, 'day').startOf('day').format('YYYY-MM-DD'),
    endDate: dayjs().endOf('day').format('YYYY-MM-DD'),
    startTime: "00:00:00",
    endTime: "23:59:59",
    branchId: null,
    zoneId: null,
    zoneLabels: [],
    checkpointId: null,
    checkpointLabels: [],
    roleId: null,
    userId: null,
    userLabels: [],
    deviceId: null,
    deviceLabels: [],
    page: 1,
    limit: 10,
    orderBy: "original_submitted_time",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
  },

  selectedRole: null,
  selectedUser: null,
  selectedDevice: null,
  selectedCheckpoint: null,
  selectedZone: null,
  selectedBranch: null,
  selectedUserLabels: [],
  selectedDeviceLabels: [],
  selectedZoneLabels: [],
  selectedCheckpointLabels: [],
};

export const fetchCheckpointLogs = createAsyncThunk(
  "checkpointLogAdmin/fetchCheckpointLogs",
  async (
    {
      branchCode,
    }: {
      branchCode: string;
    },
    { getState }
  ) => {
    const state = getState() as RootState;
    const checkpointLogAdmin = state.checkpointLogAdmin;

    const params: AdminCheckpointLogListParams = {
      page: checkpointLogAdmin.filter.page,
      limit: checkpointLogAdmin.filter.limit,
      start_date:
        checkpointLogAdmin.filter.startDate || undefined,
      end_date:
        checkpointLogAdmin.filter.endDate || undefined,
      start_time:
        checkpointLogAdmin.filter.startTime || undefined,
      end_time:
        checkpointLogAdmin.filter.endTime || undefined,
      branch_id:
        checkpointLogAdmin.filter.branchId || undefined,
      zone_id:
        checkpointLogAdmin.filter.zoneId || undefined,
      zone_labels:
        checkpointLogAdmin.filter.zoneLabels.length > 0
          ? checkpointLogAdmin.filter.zoneLabels.join(",")
          : undefined,
      checkpoint_id:
        checkpointLogAdmin.filter.checkpointId || undefined,
      checkpoint_labels:
        checkpointLogAdmin.filter.checkpointLabels.length >
        0
          ? checkpointLogAdmin.filter.checkpointLabels.join(
              ","
            )
          : undefined,
      role_id:
        checkpointLogAdmin.filter.roleId || undefined,
      user_id:
        checkpointLogAdmin.filter.userId || undefined,
      user_labels:
        checkpointLogAdmin.filter.userLabels.length > 0
          ? checkpointLogAdmin.filter.userLabels.join(",")
          : undefined,
      device_id:
        checkpointLogAdmin.filter.deviceId || undefined,
      device_labels:
        checkpointLogAdmin.filter.deviceLabels.length > 0
          ? checkpointLogAdmin.filter.deviceLabels.join(",")
          : undefined,
      order_by: checkpointLogAdmin.filter.orderBy,
      order_direction:
        checkpointLogAdmin.filter.orderDirection,
    };

    return await checkpointLogAdminApi.getCheckpointLogs(
      branchCode,
      params
    );
  }
);

const checkpointLogAdminSlice = createSlice({
  name: "checkpointLogAdmin",
  initialState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.filter.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.filter.limit = action.payload;
    },
    setOrderBy: (state, action: PayloadAction<string>) => {
      state.filter.orderBy = action.payload;
    },
    setOrderDirection: (
      state,
      action: PayloadAction<"ASC" | "DESC">
    ) => {
      state.filter.orderDirection = action.payload;
    },
    clearFilters: (state) => {
      state.filter = {
        ...initialState.filter,
        page: state.filter.page,
        limit: state.filter.limit,
      };
      state.selectedRole = null;
      state.selectedUser = null;
      state.selectedDevice = null;
      state.selectedCheckpoint = null;
      state.selectedZone = null;
      state.selectedBranch = null;
      state.selectedUserLabels = [];
      state.selectedDeviceLabels = [];
      state.selectedZoneLabels = [];
      state.selectedCheckpointLabels = [];
    },

    // Setting filter selection objects
    setSelectedRole: (
      state,
      action: PayloadAction<AdminRole | null>
    ) => {
      state.selectedRole = action.payload;
      state.filter.roleId = action.payload?.id || null;
    },
    setSelectedUser: (
      state,
      action: PayloadAction<AdminUser | null>
    ) => {
      state.selectedUser = action.payload;
      state.filter.userId = action.payload?.id || null;
    },
    setSelectedDevice: (
      state,
      action: PayloadAction<AdminDevice | null>
    ) => {
      state.selectedDevice = action.payload;
      state.filter.deviceId = action.payload?.id || null;
    },
    setSelectedCheckpoint: (
      state,
      action: PayloadAction<AdminCheckpoint | null>
    ) => {
      state.selectedCheckpoint = action.payload;
      state.filter.checkpointId =
        action.payload?.id || null;
    },
    setSelectedZone: (
      state,
      action: PayloadAction<AdminZone | null>
    ) => {
      state.selectedZone = action.payload;
      state.filter.zoneId = action.payload?.id || null;
    },
    setSelectedBranch: (
      state,
      action: PayloadAction<AdminBranch | null>
    ) => {
      state.selectedBranch = action.payload;
      state.filter.branchId = action.payload?.id || null;
    },
    setSelectedUserLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedUserLabels = action.payload;
      state.filter.userLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setSelectedDeviceLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedDeviceLabels = action.payload;
      state.filter.deviceLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setSelectedZoneLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedZoneLabels = action.payload;
      state.filter.zoneLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setSelectedCheckpointLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedCheckpointLabels = action.payload;
      state.filter.checkpointLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setStartDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startDate = action.payload;
    },
    setEndDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endDate = action.payload;
    },
    setStartTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startTime = action.payload;
    },
    setEndTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endTime = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Checkpoint Logs
      .addCase(fetchCheckpointLogs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchCheckpointLogs.fulfilled,
        (state, action) => {
          state.loading = false;
          state.checkpointLogs = action.payload.data || [];
          state.pagination.total =
            action.payload.meta?.total || 0;
          state.pagination.page =
            action.payload.meta?.page || 1;
          state.pagination.limit =
            action.payload.meta?.limit || 10;
        }
      )
      .addCase(
        fetchCheckpointLogs.rejected,
        (state, action) => {
          state.loading = false;
          state.error =
            action.error.message ||
            "Failed to fetch checkpoint logs";
        }
      );
  },
});

export default checkpointLogAdminSlice;
