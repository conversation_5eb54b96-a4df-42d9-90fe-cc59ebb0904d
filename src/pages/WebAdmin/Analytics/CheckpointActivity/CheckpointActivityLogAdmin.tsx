import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Md<PERSON>ilterList } from "react-icons/md";
import { useEffect, useRef } from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import ActiveFilters, {
  FilterGroup,
  createStartDateFilter,
  createEndDateFilter,
  createStartTimeFilter,
  createEndTimeFilter,
  createSimpleFilter,
  createLabelFilter,
} from "../../../../components/Admin/Common/ActiveFilters";
import { But<PERSON>, Drawer } from "antd";
import { useState } from "react";
import AnalyticDrawerForm, {
  AnalyticsFilterState,
} from "../../Analytics/Components/AnalyticDrawerForm";
import { EReportType } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { AdminRole } from "../../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminUser } from "../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminActivity } from "../../../../services/mainApi/admin/types/activity.admin.mainApi.types";
import { AdminLabel } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminBranch } from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks";
import CheckpointActivityLogList from "./Components/CheckpointActivityLogList";
import checkpointLogAdminSlice, {
  fetchCheckpointLogs,
} from "../../../../store/slices/admin/checkpointLog.admin.slice";
import { useParams } from "react-router-dom";
import { AdminCheckpoint } from "../../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";
import { AdminZone } from "../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import CheckpointActivityLogExportButton from "./Components/CheckpointActivityLogExportButton";

interface FilterState {
  role: null | AdminRole;
  user: null | AdminUser;
  userLabels: AdminLabel[];
  device: null | AdminDevice;
  deviceLabels: AdminLabel[];
  activity: null | AdminActivity;
  checkpoint: null | AdminCheckpoint;
  checkpointLabels: AdminLabel[];
  zone: null | AdminZone;
  zoneLabels: AdminLabel[];
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  selectedBranch: AdminBranch | null;
}

const CheckpointActivityLogAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const isFetchingRef = useRef(false);

  // Get branch code from URL params
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  // Redux state
  const checkpointLogState = useAppSelector(
    (state) => state.checkpointLogAdmin
  );
  const loading = checkpointLogState.loading;
  const pagination = checkpointLogState.pagination;
  const checkpointLogs = checkpointLogState.checkpointLogs;
  const selectedRole = checkpointLogState.selectedRole;
  const selectedUser = checkpointLogState.selectedUser;
  const selectedDevice = checkpointLogState.selectedDevice;
  const selectedCheckpoint =
    checkpointLogState.selectedCheckpoint;
  const selectedZone = checkpointLogState.selectedZone;
  const selectedBranch = checkpointLogState.selectedBranch;
  const selectedUserLabels =
    checkpointLogState.selectedUserLabels;
  const selectedDeviceLabels =
    checkpointLogState.selectedDeviceLabels;
  const selectedZoneLabels =
    checkpointLogState.selectedZoneLabels;
  const selectedCheckpointLabels =
    checkpointLogState.selectedCheckpointLabels;
  const filterState = checkpointLogState.filter;

  // Load data on mount and when filters change
  useEffect(() => {
    if (branchCode && !isFetchingRef.current) {
      isFetchingRef.current = true;
      dispatch(fetchCheckpointLogs({ branchCode }))
        .finally(() => {
          isFetchingRef.current = false;
        });
    }
  }, [
    dispatch,
    branchCode,
    filterState.page,
    filterState.limit,
    filterState.startDate,
    filterState.endDate,
    filterState.startTime,
    filterState.endTime,
    filterState.branchId,
    filterState.zoneId,
    filterState.zoneLabels,
    filterState.checkpointId,
    filterState.checkpointLabels,
    filterState.roleId,
    filterState.userId,
    filterState.userLabels,
    filterState.deviceId,
    filterState.deviceLabels,
    filterState.orderBy,
    filterState.orderDirection,
  ]);

  const { actions } = checkpointLogAdminSlice;

  // Convert applied filter state to format expected by drawer
  const getAppliedFilters = (): FilterState => {
    return {
      role: selectedRole,
      user: selectedUser,
      userLabels: selectedUserLabels || [],
      device: selectedDevice,
      deviceLabels: selectedDeviceLabels || [],
      activity: null, // We don't have activity filter in the checkpoint log slice
      checkpoint: selectedCheckpoint,
      zone: selectedZone,
      zoneLabels: selectedZoneLabels || [],
      checkpointLabels: selectedCheckpointLabels || [],
      startDate: filterState.startDate,
      endDate: filterState.endDate,
      startTime: filterState.startTime,
      endTime: filterState.endTime,
      selectedBranch: selectedBranch,
    };
  };

  // Apply filters from the drawer
  const handleApplyFilters = (
    newFilters: AnalyticsFilterState
  ) => {
    // Update redux state
    dispatch(
      actions.setSelectedRole(newFilters.role || null)
    );
    dispatch(
      actions.setSelectedUser(newFilters.user || null)
    );
    dispatch(
      actions.setSelectedUserLabels(
        newFilters.userLabels || []
      )
    );
    dispatch(
      actions.setSelectedDevice(newFilters.device || null)
    );
    dispatch(
      actions.setSelectedDeviceLabels(
        newFilters.deviceLabels || []
      )
    );
    dispatch(
      actions.setSelectedCheckpoint(
        newFilters.checkpoint || null
      )
    );
    dispatch(
      actions.setSelectedCheckpointLabels(
        newFilters.checkpointLabels || []
      )
    );
    dispatch(
      actions.setSelectedZone(newFilters.site || null)
    );
    dispatch(
      actions.setSelectedZoneLabels(
        newFilters.siteLabels || []
      )
    );
    dispatch(
      actions.setSelectedBranch(
        newFilters.selectedBranch || null
      )
    );
    dispatch(
      actions.setStartDate(newFilters.startDate || null)
    );
    dispatch(
      actions.setEndDate(newFilters.endDate || null)
    );
    dispatch(
      actions.setStartTime(newFilters.startTime || null)
    );
    dispatch(
      actions.setEndTime(newFilters.endTime || null)
    );

    // Reset page to 1
    dispatch(actions.setPage(1));

    setDrawerVisible(false);
  };

  // Handle page change
  const handlePageChange = (
    page: number,
    pageSize?: number
  ) => {
    if (pageSize && pageSize !== filterState.limit) {
      dispatch(actions.setLimit(pageSize));
    }
    dispatch(actions.setPage(page));
  };

  // Check if there are any active filters
  const hasActiveFilters = (): boolean => {
    // Check if any filter is active
    return !!(
      filterState.startDate ||
      filterState.endDate ||
      filterState.startTime ||
      filterState.endTime ||
      filterState.branchId ||
      filterState.roleId ||
      filterState.userId ||
      filterState.userLabels.length > 0 ||
      filterState.deviceId ||
      filterState.deviceLabels.length > 0 ||
      filterState.checkpointId ||
      filterState.checkpointLabels.length > 0 ||
      filterState.zoneId ||
      filterState.zoneLabels.length > 0
    );
  };

  // Get filter groups configuration for ActiveFilters component
  const getFilterGroups = (): FilterGroup[] => {
    const appliedFilters = getAppliedFilters();

    return [
      {
        key: "datetime",
        filters: [
          createStartDateFilter(appliedFilters.startDate),
          createEndDateFilter(appliedFilters.endDate),
          createStartTimeFilter(appliedFilters.startTime),
          createEndTimeFilter(appliedFilters.endTime),
        ],
      },
      {
        key: "user",
        filters: [
          createSimpleFilter(
            "role",
            "role",
            appliedFilters.role,
            "role_name"
          ),
          createSimpleFilter(
            "user",
            "user",
            appliedFilters.user,
            "name"
          ),
          createLabelFilter(
            "userLabels",
            "userLabels",
            appliedFilters.userLabels
          ),
        ],
      },
      {
        key: "device",
        filters: [
          createSimpleFilter(
            "device",
            "device",
            appliedFilters.device,
            "device_name"
          ),
          createLabelFilter(
            "deviceLabels",
            "deviceLabels",
            appliedFilters.deviceLabels
          ),
        ],
      },
      {
        key: "checkpoint",
        filters: [
          createSimpleFilter(
            "checkpoint",
            "checkpoint",
            appliedFilters.checkpoint,
            "checkpoint_name"
          ),
          createLabelFilter(
            "checkpointLabels",
            "checkpointLabels",
            appliedFilters.checkpointLabels
          ),
          createSimpleFilter(
            "zone",
            "zone",
            appliedFilters.zone,
            "zone_name"
          ),
          createLabelFilter(
            "zoneLabels",
            "zoneLabels",
            appliedFilters.zoneLabels
          ),
        ],
      },
      {
        key: "other",
        filters: [
          createSimpleFilter(
            "selectedBranch",
            "selectedBranch",
            appliedFilters.selectedBranch,
            "branch_name"
          ),
        ],
      },
    ];
  };

  const handleClearAll = () => {
    dispatch(actions.clearFilters());
  };

  const handleRemoveFilter = (
    filterKey: keyof FilterState
  ) => {
    switch (filterKey) {
      case "role":
        dispatch(actions.setSelectedRole(null));
        break;
      case "user":
        dispatch(actions.setSelectedUser(null));
        break;
      case "userLabels":
        dispatch(actions.setSelectedUserLabels([]));
        break;
      case "device":
        dispatch(actions.setSelectedDevice(null));
        break;
      case "deviceLabels":
        dispatch(actions.setSelectedDeviceLabels([]));
        break;
      case "activity":
        // We don't have activity filter in the checkpoint log slice
        break;
      case "checkpoint":
        dispatch(actions.setSelectedCheckpoint(null));
        break;
      case "checkpointLabels":
        dispatch(actions.setSelectedCheckpointLabels([]));
        break;
      case "zone":
        dispatch(actions.setSelectedZone(null));
        break;
      case "zoneLabels":
        dispatch(actions.setSelectedZoneLabels([]));
        break;
      case "selectedBranch":
        dispatch(actions.setSelectedBranch(null));
        break;
      case "startDate":
        dispatch(actions.setStartDate(null));
        break;
      case "endDate":
        dispatch(actions.setEndDate(null));
        break;
      case "startTime":
        dispatch(actions.setStartTime(null));
        break;
      case "endTime":
        dispatch(actions.setEndTime(null));
        break;
    }
  };

  // Handle clicking on an active filter to edit it
  const handleEditFilter = () => {
    // We only need to open the drawer
    setDrawerVisible(true);
  };

  // Convert FilterState to AnalyticsFilterState for the drawer form
  const getInitialDrawerValues =
    (): Partial<AnalyticsFilterState> => {
      const appliedFilters = getAppliedFilters();

      return {
        startDate: appliedFilters.startDate || "",
        endDate: appliedFilters.endDate || "",
        startTime: appliedFilters.startTime || "",
        endTime: appliedFilters.endTime || "",
        selectedBranch:
          appliedFilters.selectedBranch || null,
        role: appliedFilters.role,
        user: appliedFilters.user,
        userLabels: appliedFilters.userLabels,
        device: appliedFilters.device,
        deviceLabels: appliedFilters.deviceLabels,
        activity: appliedFilters.activity,
        checkpoint: appliedFilters.checkpoint,
        checkpointLabels: appliedFilters.checkpointLabels,
        site: appliedFilters.zone,
        siteLabels: appliedFilters.zoneLabels,
      };
    };

  // Create a reusable function to get filter info
  const getFilterInfo = () => ({
    role: checkpointLogState.selectedRole || null,
    user: checkpointLogState.selectedUser || null,
    userLabels: checkpointLogState.selectedUserLabels || [],
    device: checkpointLogState.selectedDevice || null,
    deviceLabels:
      checkpointLogState.selectedDeviceLabels || [],
    checkpoint:
      checkpointLogState.selectedCheckpoint || null,
    checkpointLabels:
      checkpointLogState.selectedCheckpointLabels || [],
    zone: checkpointLogState.selectedZone || null,
    zoneLabels: checkpointLogState.selectedZoneLabels || [],
    startDate: checkpointLogState.filter.startDate || null,
    endDate: checkpointLogState.filter.endDate || null,
    startTime: checkpointLogState.filter.startTime || null,
    endTime: checkpointLogState.filter.endTime || null,
    selectedBranch:
      checkpointLogState.selectedBranch || null,
  });

  return (
    <WebAdminLayout activePage="checkpoint-activity-log">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdHistory />}
            title={t(
              "checkpointActivityLog.title",
              "Checkpoint Activity Log"
            )}
            description={t(
              "checkpointActivityLog.description",
              "View and manage checkpoint activity logs"
            )}
            actions={[
              // Desktop view - show all buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <CheckpointActivityLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                />
                <Button
                  key="filter"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button and dropdown for downloads
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <CheckpointActivityLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                  buttonText={t(
                    "common.download",
                    "Download"
                  )}
                />
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList />}
                  size="large"
                  type="primary"
                  className="bg-indigo-600 hover:bg-indigo-700 border-0 shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters using the reusable component */}
          <ActiveFilters<FilterState>
            filterGroups={getFilterGroups()}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAll}
            onShowFilters={() => {
              setDrawerVisible(true);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Results List */}
          <CheckpointActivityLogList
            activityLogs={checkpointLogs}
            loading={loading}
            total={pagination.total || 0}
            page={pagination.page || 1}
            limit={pagination.limit || 10}
            onPageChange={handlePageChange}
          />
        </div>

        {/* Filter Drawer - Using the refactored component with initialValues */}
        <Drawer
          title={
            <div className="text-lg font-semibold text-gray-800 dark:text-white">
              {t(
                "checkpointActivityLog.filters.title",
                "Filter Checkpoint Activity Log"
              )}
            </div>
          }
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          className="dark:bg-gray-800"
        >
          <AnalyticDrawerForm
            reportType={EReportType.CHECKPOINT_ACTIVITY}
            onApply={handleApplyFilters}
            onClose={() => setDrawerVisible(false)}
            initialValues={getInitialDrawerValues()}
          />
        </Drawer>
      </div>
    </WebAdminLayout>
  );
};

export default CheckpointActivityLogAdmin;
