import baseMainApi from "../base.mainApi";
import {
  BaseGetRequest,
  BaseResponse,
} from "../types/base.mainApi.types";
import {
  AdminDevice,
  CreateDeviceRequest,
  GetGpsTrackingIntervalResponse,
  UpdateDeviceRequest,
} from "./types/device.admin.mainApi.types";

const deviceAdminMainApi = {
  getDevices: async (
    branchCode: string,
    params?: BaseGetRequest
  ) => {
    const response = await baseMainApi.get<
      BaseResponse<AdminDevice[]>
    >(`/web-api/admin/${branchCode}/device`, { params });
    return response.data;
  },

  getDeviceById: async (branchCode: string, id: string) => {
    const response = await baseMainApi.get<
      BaseResponse<AdminDevice>
    >(`/web-api/admin/${branchCode}/device/${id}`);
    return response.data;
  },

  createDevice: async (
    branchCode: string,
    data: CreateDeviceRequest
  ) => {
    const response = await baseMainApi.post<
      BaseResponse<AdminDevice>
    >(`/web-api/admin/${branchCode}/device`, data);
    return response.data;
  },

  updateDevice: async (
    branchCode: string,
    id: string,
    data: UpdateDeviceRequest
  ) => {
    const response = await baseMainApi.put<
      BaseResponse<AdminDevice>
    >(`/web-api/admin/${branchCode}/device/${id}`, data);
    return response.data;
  },

  deleteDevice: async (branchCode: string, id: string) => {
    const response = await baseMainApi.delete<
      BaseResponse<null>
    >(`/web-api/admin/${branchCode}/device/${id}`);
    return response.data;
  },

  updateGpsTrackingInterval: async (
    branchCode: string,
    data: {
      interval: number;
      active: boolean;
    }
  ) => {
    const response = await baseMainApi.put<
      BaseResponse<null>
    >(`/web-api/admin/${branchCode}/device/interval`, data);
    return response.data;
  },

  getInterval: async (branchCode: string) => {
    const response = await baseMainApi.get<
      GetGpsTrackingIntervalResponse
    >(`/web-api/admin/${branchCode}/device/interval`);
    return response.data;
  },
};

export default deviceAdminMainApi;
