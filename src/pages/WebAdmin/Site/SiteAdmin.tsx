import { useCallback, useEffect, useRef } from "react";
import { Button, Input, Select } from "antd";
import {
  SearchOutlined,
  SortAscendingOutlined,
} from "@ant-design/icons";
import { MdLocationOn } from "react-icons/md";
import { FiPlus } from "react-icons/fi";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import { useTranslation } from "react-i18next";
import SiteList from "./Components/SiteList";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks";
import addSiteAdminSlice from "../../../store/slices/admin/addSite.admin.slice";
import AddSiteModal from "./Components/AddSiteModal";
import siteAdminSlice, {
  fetchSitesAdmin,
} from "../../../store/slices/admin/site.admin.slice";
import _ from "lodash";
import { useParams } from "react-router";

const { Option } = Select;

export default function SiteAdmin() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { filter } = useAppSelector(
    (state) => state.siteAdmin
  );
  const { branchCode } = useParams();
  const isFetching = useRef(false);

  const debouncedSearchRef = useRef(
    _.debounce(() => {
      fetchSites();
    }, 500)
  );

  const fetchSites = useCallback(() => {
    if (isFetching.current) return;
    isFetching.current = true;
    dispatch(fetchSitesAdmin(branchCode || ""));
    isFetching.current = false;
  }, [dispatch, branchCode]);

  const handleSearch = useCallback(
    (value: string) => {
      dispatch(
        siteAdminSlice.actions.setFilter({
          search: value || null,
          page: 1,
        })
      );
      debouncedSearchRef.current();
    },
    [dispatch]
  );

  const handleSortChange = useCallback(
    (value: string) => {
      dispatch(
        siteAdminSlice.actions.setFilter({ orderBy: value })
      );
    },
    [dispatch]
  );

  const handleSortDirectionToggle = useCallback(() => {
    dispatch(
      siteAdminSlice.actions.setFilter({
        orderDirection:
          filter.orderDirection === "ASC" ? "DESC" : "ASC",
      })
    );
  }, [dispatch, filter.orderDirection]);

  const handleAddSite = useCallback(() => {
    dispatch(addSiteAdminSlice.actions.openCreate());
  }, [dispatch]);

  useEffect(() => {
    fetchSites();
  }, [fetchSites, filter.orderBy, filter.orderDirection]);

  return (
    <WebAdminLayout activePage="monitoring-site">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header Section */}
          <PageHeader
            icon={<MdLocationOn />}
            title={t("site.title")}
            description={t("site.description")}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<FiPlus />}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={handleAddSite}
              >
                {t("site.addButton")}
              </Button>,
            ]}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder={t("site.search.placeholder")}
                prefix={<SearchOutlined />}
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                size="large"
                onChange={(e) =>
                  handleSearch(e.target.value)
                }
                allowClear
              />
              <div className="flex gap-2">
                <Select
                  defaultValue="created_at"
                  className="min-w-[160px] dark:bg-gray-700"
                  size="large"
                  onChange={handleSortChange}
                  value={filter.orderBy}
                >
                  <Option value="created_at">
                    {t("site.search.sortOptions.createdAt")}
                  </Option>
                  <Option value="zone_name">
                    {t("site.search.sortOptions.name")}
                  </Option>
                </Select>
                <Button
                  icon={
                    <SortAscendingOutlined
                      rotate={
                        filter.orderDirection === "DESC"
                          ? 180
                          : 0
                      }
                    />
                  }
                  size="large"
                  onClick={handleSortDirectionToggle}
                  className="dark:border-gray-600 dark:text-gray-300"
                />
              </div>
            </div>
          </div>

          {/* Content Section */}
          <SiteList />
        </div>
      </div>
      <AddSiteModal />
    </WebAdminLayout>
  );
}
