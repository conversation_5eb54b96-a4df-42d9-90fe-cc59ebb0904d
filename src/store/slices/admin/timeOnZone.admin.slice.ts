import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../../../store";
import { timeOnZoneAdminMainApi } from "../../../services/mainApi/admin/timeOnZone.admin.mainApi";
import {
  AdminTimeOnZoneListParams,
  TimeOnZoneEntry,
} from "../../../services/mainApi/admin/types/timeOnZone.admin.mainApi.types";
import { BasePaginationMeta } from "../../../services/mainApi/types/base.mainApi.types";
import { AdminBranch } from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminZone } from "../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { AdminUser } from "../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminLabel } from "../../../services/mainApi/admin/types/label.admin.mainApi.types";
import dayjs from "dayjs";

interface TimeOnZoneState {
  timeOnZoneData: TimeOnZoneEntry[];
  loading: boolean;
  error: string | null;
  pagination: BasePaginationMeta;

  filter: {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    branchId: string | number | null;
    zoneId: string | number | null;
    zoneLabels: (string | number)[];
    userId: string | number | null;
    userLabels: (string | number)[];
    deviceId: string | number | null;
    deviceLabels: (string | number)[];
    page: number;
    limit: number;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };

  // Selected filter objects
  selectedBranch: AdminBranch | null;
  selectedZone: AdminZone | null;
  selectedZoneLabels: AdminLabel[];
  selectedUser: AdminUser | null;
  selectedUserLabels: AdminLabel[];
  selectedDevice: AdminDevice | null;
  selectedDeviceLabels: AdminLabel[];
}

// Initial state
const initialState: TimeOnZoneState = {
  timeOnZoneData: [],
  loading: false,
  error: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    total_pages: 0,
  },
  filter: {
    startDate: dayjs().subtract(1, "day").format("YYYY-MM-DD"),
    endDate: dayjs().subtract(1, "day").format("YYYY-MM-DD"),
    startTime: "00:00:00",
    endTime: "23:59:59",
    branchId: null,
    zoneId: null,
    zoneLabels: [],
    userId: null,
    userLabels: [],
    deviceId: null,
    deviceLabels: [],
    page: 1,
    limit: 10,
    orderBy: "seqnum",
    orderDirection: "DESC",
  },
  selectedBranch: null,
  selectedZone: null,
  selectedZoneLabels: [],
  selectedUser: null,
  selectedUserLabels: [],
  selectedDevice: null,
  selectedDeviceLabels: [],
};

// Async thunk for fetching time on zone data
export const fetchTimeOnZoneData = createAsyncThunk(
  "timeOnZoneAdmin/fetchTimeOnZoneData",
  async (
    { branchCode }: { branchCode: string },
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const timeOnZoneAdmin = state.timeOnZoneAdmin;

      const params: AdminTimeOnZoneListParams = {
        page: timeOnZoneAdmin.filter.page,
        limit: timeOnZoneAdmin.filter.limit,
        start_date: timeOnZoneAdmin.filter.startDate || undefined,
        end_date: timeOnZoneAdmin.filter.endDate || undefined,
        start_time: timeOnZoneAdmin.filter.startTime || undefined,
        end_time: timeOnZoneAdmin.filter.endTime || undefined,
        branch_id: timeOnZoneAdmin.filter.branchId || undefined,
        zone_id: timeOnZoneAdmin.filter.zoneId || undefined,
        zone_labels: timeOnZoneAdmin.filter.zoneLabels.length > 0
          ? timeOnZoneAdmin.filter.zoneLabels.join(",")
          : undefined,
        user_id: timeOnZoneAdmin.filter.userId || undefined,
        user_labels: timeOnZoneAdmin.filter.userLabels.length > 0
          ? timeOnZoneAdmin.filter.userLabels.join(",")
          : undefined,
        device_id: timeOnZoneAdmin.filter.deviceId || undefined,
        device_labels: timeOnZoneAdmin.filter.deviceLabels.length > 0
          ? timeOnZoneAdmin.filter.deviceLabels.join(",")
          : undefined,
      };

      const response = await timeOnZoneAdminMainApi.getTimeOnZone(
        branchCode,
        params
      );

      return response;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Failed to fetch time on zone data"
      );
    }
  }
);

// Export time on zone data
export const exportTimeOnZoneData = createAsyncThunk(
  "timeOnZoneAdmin/exportTimeOnZoneData",
  async (
    {
      branchCode,
      format,
      saveType,
    }: {
      branchCode: string;
      format: "pdf" | "spreadsheet";
      saveType: "link" | "buffer";
    },
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const timeOnZoneAdmin = state.timeOnZoneAdmin;

      const params: AdminTimeOnZoneListParams = {
        page: timeOnZoneAdmin.filter.page,
        limit: timeOnZoneAdmin.filter.limit,
        start_date: timeOnZoneAdmin.filter.startDate || undefined,
        end_date: timeOnZoneAdmin.filter.endDate || undefined,
        start_time: timeOnZoneAdmin.filter.startTime || undefined,
        end_time: timeOnZoneAdmin.filter.endTime || undefined,
        branch_id: timeOnZoneAdmin.filter.branchId || undefined,
        zone_id: timeOnZoneAdmin.filter.zoneId || undefined,
        zone_labels: timeOnZoneAdmin.filter.zoneLabels.length > 0
          ? timeOnZoneAdmin.filter.zoneLabels.join(",")
          : undefined,
        user_id: timeOnZoneAdmin.filter.userId || undefined,
        user_labels: timeOnZoneAdmin.filter.userLabels.length > 0
          ? timeOnZoneAdmin.filter.userLabels.join(",")
          : undefined,
        device_id: timeOnZoneAdmin.filter.deviceId || undefined,
        device_labels: timeOnZoneAdmin.filter.deviceLabels.length > 0
          ? timeOnZoneAdmin.filter.deviceLabels.join(",")
          : undefined,
      };

      return await timeOnZoneAdminMainApi.exportTimeOnZone(
        branchCode,
        params,
        format,
        saveType
      );
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Failed to export time on zone data"
      );
    }
  }
);

// Slice
const timeOnZoneAdminSlice = createSlice({
  name: "timeOnZoneAdmin",
  initialState,
  reducers: {
    // Filter actions
    setFilter: (
      state,
      action: PayloadAction<Partial<TimeOnZoneState["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.filter.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.filter.limit = action.payload;
      state.filter.page = 1; // Reset to first page when changing limit
    },
    setOrderBy: (state, action: PayloadAction<string>) => {
      state.filter.orderBy = action.payload;
    },
    setOrderDirection: (
      state,
      action: PayloadAction<"ASC" | "DESC">
    ) => {
      state.filter.orderDirection = action.payload;
    },
    setStartDate: (state, action: PayloadAction<string | null>) => {
      state.filter.startDate = action.payload;
    },
    setEndDate: (state, action: PayloadAction<string | null>) => {
      state.filter.endDate = action.payload;
    },
    setStartTime: (state, action: PayloadAction<string | null>) => {
      state.filter.startTime = action.payload;
    },
    setEndTime: (state, action: PayloadAction<string | null>) => {
      state.filter.endTime = action.payload;
    },
    setSelectedBranch: (state, action: PayloadAction<AdminBranch | null>) => {
      state.selectedBranch = action.payload;
      state.filter.branchId = action.payload?.id || null;
    },
    setSelectedZone: (state, action: PayloadAction<AdminZone | null>) => {
      state.selectedZone = action.payload;
      state.filter.zoneId = action.payload?.id || null;
    },
    setSelectedZoneLabels: (state, action: PayloadAction<AdminLabel[]>) => {
      state.selectedZoneLabels = action.payload;
      state.filter.zoneLabels = action.payload.map(label => label.id);
    },
    setSelectedUser: (state, action: PayloadAction<AdminUser | null>) => {
      state.selectedUser = action.payload;
      state.filter.userId = action.payload?.id || null;
    },
    setSelectedUserLabels: (state, action: PayloadAction<AdminLabel[]>) => {
      state.selectedUserLabels = action.payload;
      state.filter.userLabels = action.payload.map(label => label.id);
    },
    setSelectedDevice: (state, action: PayloadAction<AdminDevice | null>) => {
      state.selectedDevice = action.payload;
      state.filter.deviceId = action.payload?.id || null;
    },
    setSelectedDeviceLabels: (state, action: PayloadAction<AdminLabel[]>) => {
      state.selectedDeviceLabels = action.payload;
      state.filter.deviceLabels = action.payload.map(label => label.id);
    },
    resetFilters: (state) => {
      state.filter = {
        ...initialState.filter,
        page: state.filter.page,
        limit: state.filter.limit,
      };
      state.selectedBranch = null;
      state.selectedZone = null;
      state.selectedZoneLabels = [];
      state.selectedUser = null;
      state.selectedUserLabels = [];
      state.selectedDevice = null;
      state.selectedDeviceLabels = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch time on zone data
      .addCase(fetchTimeOnZoneData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTimeOnZoneData.fulfilled, (state, action) => {
        state.loading = false;
        state.timeOnZoneData = action.payload.data || [];
        state.pagination.total = action.payload.meta?.total || 0;
        state.pagination.page = action.payload.meta?.page || 1;
        state.pagination.limit = action.payload.meta?.limit || 10;
        state.pagination.total_pages = action.payload.meta?.total_pages || 1;
      })
      .addCase(fetchTimeOnZoneData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || "Failed to fetch time on zone data";
      })
      // Export time on zone data
      .addCase(exportTimeOnZoneData.rejected, (state, action) => {
        state.error = action.payload as string || "Failed to export time on zone data";
      });
  },
});

export const { actions } = timeOnZoneAdminSlice;
export default timeOnZoneAdminSlice;
