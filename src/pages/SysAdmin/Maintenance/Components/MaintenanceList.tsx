import {Empty, Spin} from "antd";
import { Setting } from "../../../../services/mainApi/types/setting.mainApi.types";
import {useTranslation} from "react-i18next";
import MaintenanceItem from "./MaintenanceItem";

interface MaintenanceListProps {
  settings: Setting[];
  loading: boolean;
  onEdit: (maintenance: Setting) => void;
}

export default function MaintenanceList({
                                      settings,
                                      loading,
                                      onEdit,
                                    }: MaintenanceListProps) {
  const {t} = useTranslation();

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Spin size="large" tip="Loading setting..."/>
      </div>
    );
  }

  if (settings.length === 0) {
    return (
      <div className="p-8 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <span className="text-gray-500 dark:text-gray-400 text-base">
              {t("setting.noSettingFound", "No Setting found")}
            </span>
          }
          className="py-12"
        />
      </div>
    );
  }

  return (
    <div
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6"
    >
      {settings.map((setting) => (
        <MaintenanceItem maintenance={setting} onEdit={onEdit}/>
      ))}
    </div>
  );
}
