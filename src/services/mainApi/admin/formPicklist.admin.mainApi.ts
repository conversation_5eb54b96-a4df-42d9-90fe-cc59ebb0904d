import baseMainApi from "../base.mainApi";
import {
  AdminFormPicklistListResponse,
  GetFormPicklistRequest,
  CreateFormPicklistRequest,
  UpdateFormPicklistRequest,
} from "./types/formPicklist.admin.mainApi.types";

const formPicklistAdminMainApi = {
  getFormPicklists: async (
    branchCode: string,
    params: GetFormPicklistRequest = {}
  ) => {
    const response =
      await baseMainApi.get<AdminFormPicklistListResponse>(
        `/web-api/admin/${branchCode}/form/picklist`,
        { params }
      );
    return response.data;
  },
  createFormPicklist: async (
    branchCode: string,
    data: CreateFormPicklistRequest
  ) => {
    const response =
      await baseMainApi.post<AdminFormPicklistListResponse>(
        `/web-api/admin/${branchCode}/form/picklist`,
        data
      );
    return response.data;
  },
  updateFormPicklist: async (
    branchCode: string,
    id: string,
    data: UpdateFormPicklistRequest
  ) => {
    const response =
      await baseMainApi.put<AdminFormPicklistListResponse>(
        `/web-api/admin/${branchCode}/form/picklist/${id}`,
        data
      );
    return response.data;
  },
  getFormPicklistById: async (
    branchCode: string,
    id: string
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/form/picklist/${id}`
    );
    return response.data;
  },
  deleteFormPicklist: async (
    branchCode: string,
    id: string
  ) => {
    const response = await baseMainApi.delete(
      `/web-api/admin/${branchCode}/form/picklist/${id}`
    );
    return response.data;
  },
};

export default formPicklistAdminMainApi;
