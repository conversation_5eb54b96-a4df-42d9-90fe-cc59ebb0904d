import React, { useEffect, useState } from "react";
import type { MenuProps } from "antd";
import {
  <PERSON>ton,
  ConfigProvider,
  Menu,
  Switch,
  theme,
} from "antd";
import { useLocation, useNavigate } from "react-router-dom";
import "./MainLayout.css";
import Header from "./Header/Header";
import { useTheme } from "../../context/ThemeContext";
import { useAppSelector } from "../../store/hooks";
import clsx from "clsx";
import UserInfo from "./Header/UserInfo.tsx";
import { MenuItemGroupType } from "antd/es/menu/interface";
import { MdBusiness } from "react-icons/md";
import LanguageSelector from "./Header/LanguageSelector";
import { useTranslation } from "react-i18next";

interface MainLayoutProps {
  children: React.ReactNode;
  activePage?: string;
  menuItems: MenuItem[];
  menuPaths: Map<string, string>;
  layoutTitle: string;
  hideMenu?: boolean;
}

type MenuItem = Required<MenuProps>["items"][number];

export default function MainLayout({
  children,
  activePage,
  menuItems,
  menuPaths,
  hideMenu,
}: MainLayoutProps) {
  const [collapsed, setCollapsed] = useState(
    window.innerWidth <= 768
  );
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const { isDark, toggleDarkMode } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const currentPath = location.pathname;
  const user = useAppSelector((state) => state.auth);
  const { t } = useTranslation();

  useEffect(() => {
    const handleResize = () =>
      setCollapsed(window.innerWidth <= 768);
    window.addEventListener("resize", handleResize);
    return () =>
      window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    // Find parent key for current active page
    const findParentKey = (
      items: MenuItem[],
      childKey: string
    ): string | null => {
      for (const item of items) {
        if (item && "children" in item && item.children) {
          for (const child of item.children) {
            if (
              child &&
              "key" in child &&
              child.key === childKey
            ) {
              return item.key as string;
            }
          }
        }
      }
      return null;
    };

    if (activePage) {
      const parentKey = findParentKey(
        menuItems,
        activePage
      );
      if (parentKey) {
        setOpenKeys([parentKey]);
      }
    }
  }, [activePage, menuItems]);

  const handleMenuClick: MenuProps["onClick"] = ({
    key,
  }) => {
    const path = menuPaths.get(key.toString());
    if (path) {
      navigate(path);
      if (window.innerWidth <= 768) setCollapsed(true);
    }
  };

  const onClickSelectBranch = () => {
    if (currentPath === "/admin/branch") return;
    const split = currentPath.split("/");
    const next = split[4];
    if (next) {
      navigate(`/admin/branch?next=${next}`);
    } else {
      navigate("/admin/branch");
    }
  };

  const renderWithMenu = () => {
    return (
      <>
        <div
          className={`sidebar bg-white dark:bg-gray-800 dark:text-gray-200 ${
            collapsed
              ? "sidebar-collapsed"
              : "sidebar-expanded"
          }`}
        >
          <div className="logo-section flex items-center justify-between border-b border-gray-100 dark:border-gray-700 px-4 py-3">
            <div className="flex items-center">
              <span className="text-xl font-bold text-white px-2.5 py-1 rounded-lg">
                <img
                  src="/logo.png"
                  alt="Logo"
                  className="w-8 h-8 object-contain"
                />
              </span>
              {!collapsed && (
                <div className="ml-3 flex flex-col">
                  <span className="text-base font-semibold text-gray-900 dark:text-gray-200">
                    {t("mainLayout.appName")}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {user?.user?.current_branch
                      ?.branch_name ||
                      user?.user?.name ||
                      t("mainLayout.defaultBranch")}
                  </span>
                </div>
              )}
            </div>
          </div>

          <div className="sidebar-menu-container">
            <Menu
              mode="inline"
              selectedKeys={activePage ? [activePage] : []}
              openKeys={openKeys}
              onOpenChange={(keys) => setOpenKeys(keys)}
              onClick={handleMenuClick}
              inlineCollapsed={collapsed}
              className="border-none bg-transparent dark:bg-gray-800"
              theme={isDark ? "dark" : "light"}
              style={{
                padding: "8px",
              }}
              items={
                menuItems.map((item) => ({
                  ...item,
                  className: clsx(
                    "rounded-lg",
                    // Tambahkan class khusus untuk submenu yang terbuka
                    {
                      "submenu-open": openKeys.includes(
                        item?.key as string
                      ),
                    }
                  ),
                  style: {
                    marginBottom: 4,
                  },
                })) as MenuItemGroupType[]
              }
            />
          </div>

          <div className="absolute bottom-0 w-full border-t border-gray-100 dark:border-gray-700">
            <div
              className={`dark-mode-switch ${collapsed ? "px-4" : "px-6"} py-4`}
            >
              <div className="flex items-center justify-between">
                {!collapsed && (
                  <span className="text-gray-700 dark:text-gray-200">
                    {t("mainLayout.darkMode")}
                  </span>
                )}
                <Switch
                  checked={isDark}
                  onChange={toggleDarkMode}
                  checkedChildren="🌙"
                  unCheckedChildren="☀️"
                />
              </div>
            </div>
          </div>
        </div>

        {!collapsed && (
          <div
            className="sidebar-overlay"
            onClick={() => setCollapsed(true)}
          />
        )}

        <Header
          collapsed={collapsed}
          onToggleCollapse={() => setCollapsed(!collapsed)}
        >
          <div className="flex items-center gap-4">
            <Button
              type="default"
              icon={<MdBusiness className="text-lg" />}
              onClick={onClickSelectBranch}
              className="flex items-center gap-2 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            >
              {t("mainLayout.changeClient")}
            </Button>
            <LanguageSelector />
            <UserInfo />
          </div>
        </Header>

        <div
          className={`main-content ${
            collapsed
              ? "main-content-collapsed"
              : "main-content-expanded"
          } dark:bg-gray-900`}
        >
          <div className="main-content-inner">
            {children}
          </div>
        </div>
      </>
    );
  };

  const renderWithoutMenu = () => {
    return (
      <>
        <div
          className={clsx(
            "border-b bg-white dark:bg-gray-800 dark:border-gray-700"
          )}
        >
          <div className="logo-section flex items-center justify-between border-b border-gray-100 dark:border-gray-700 px-4 py-3">
            <div className="flex items-center">
              <span className="text-xl font-bold bg-blue-600 text-white px-2.5 py-1 rounded-lg">
                <img
                  src="/logo.png"
                  alt="Logo"
                  className="w-8 h-8 object-contain"
                />
              </span>
              <div className="ml-3 flex flex-col">
                <span className="text-base font-semibold text-gray-900 dark:text-gray-200">
                  {t("mainLayout.appName")}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {user?.user?.current_branch
                    ?.branch_name ||
                    user?.user?.name ||
                    t("mainLayout.defaultBranch")}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Button
                type="default"
                icon={<MdBusiness className="text-lg" />}
                onClick={onClickSelectBranch}
                className="flex items-center gap-2 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              >
                {t("mainLayout.selectClient")}
              </Button>
              <LanguageSelector />
              <UserInfo />
            </div>
          </div>
        </div>
        <div className={`dark:bg-gray-900`}>
          <div className="main-content-inner">
            {children}
          </div>
        </div>
      </>
    );
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: isDark
          ? theme.darkAlgorithm
          : theme.defaultAlgorithm,
      }}
    >
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {!hideMenu ? renderWithMenu() : renderWithoutMenu()}
      </div>
    </ConfigProvider>
  );
}
