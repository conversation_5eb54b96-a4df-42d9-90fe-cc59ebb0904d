import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import branchSysAdminMainApi from "../../../services/mainApi/sysadmin/branch.sysadmin.mainApi";
import { Branch } from "../../../services/mainApi/sysadmin/types/branch.mainApi.types";

export type SortField = "created_at" | "branch_name";
export type SortOrder = "asc" | "desc";

// Types
interface BranchState {
  items: Branch[];
  loading: boolean;
  error: string | null;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  filters: {
    searchQuery: string;
    sortField: SortField;
    sortOrder: SortOrder;
  };
}

// Initial state
const initialState: BranchState = {
  items: [],
  loading: false,
  error: null,
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0,
  },
  filters: {
    searchQuery: "",
    sortField: "created_at",
    sortOrder: "desc",
  },
};

// Async thunks
export const fetchBranches = createAsyncThunk(
  "branch/fetchBranches",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const { pagination, filters } = state.branchSysadmin;

      const response = await branchSysAdminMainApi.getList({
        search: filters.searchQuery,
        page: pagination.current,
        limit: pagination.pageSize,
        orderBy: filters.sortField,
        orderDirection: filters.sortOrder,
      });

      return {
        data: response.data,
        total: response.meta.total,
      };
    } catch {
      return rejectWithValue("Failed to fetch branches");
    }
  }
);

// Slice
const branchListSysadminSlice = createSlice({
  name: "branch",
  initialState,
  reducers: {
    setSearchQuery: (
      state,
      action: PayloadAction<string>
    ) => {
      state.filters.searchQuery = action.payload;
      state.pagination.current = 1; // Reset to first page
    },
    setSort: (
      state,
      action: PayloadAction<{
        field: SortField;
        order: SortOrder;
      }>
    ) => {
      state.filters.sortField = action.payload.field;
      state.filters.sortOrder = action.payload.order;
      state.pagination.current = 1; // Reset to first page
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.pagination.current = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchBranches.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBranches.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.data;
        state.pagination.total = action.payload.total;
      })
      .addCase(fetchBranches.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Actions
export const { setSearchQuery, setSort, setPage } =
  branchListSysadminSlice.actions;

// Selectors
export const selectBranches = (state: RootState) =>
  state.branchSysadmin.items;
export const selectBranchLoading = (state: RootState) =>
  state.branchSysadmin.loading;
export const selectBranchError = (state: RootState) =>
  state.branchSysadmin.error;
export const selectBranchPagination = (state: RootState) =>
  state.branchSysadmin.pagination;
export const selectBranchFilters = (state: RootState) =>
  state.branchSysadmin.filters;

export default branchListSysadminSlice;
