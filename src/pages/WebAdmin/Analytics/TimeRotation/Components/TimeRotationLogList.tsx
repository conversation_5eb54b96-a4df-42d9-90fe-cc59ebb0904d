import React from "react";
import { Empty, Pagination, Spin } from "antd";
import { useTranslation } from "react-i18next";
import TimeRotationLogItem from "./TimeRotationLogItem";

import { TimeRotationCheckpoint } from "../../../../../services/mainApi/admin/types/timeRotation.admin.mainApi.types";

// Interface for the time rotation log list props
interface TimeRotationLogListProps {
  timeRotationLogs: TimeRotationCheckpoint[];
  loading: boolean;
  total: number;
  page: number;
  limit: number;
  onPageChange: (page: number, pageSize?: number) => void;
}

const TimeRotationLogList: React.FC<TimeRotationLogListProps> = ({
  timeRotationLogs,
  loading,
  total,
  page,
  limit,
  onPageChange,
}) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="flex justify-center items-center py-16">
        <Spin size="large" />
      </div>
    );
  }

  if (timeRotationLogs.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-12">
        <Empty
          description={t(
            "timeRotationLog.noLogs",
            "No time rotation logs found"
          )}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {timeRotationLogs.map((log) => (
        <TimeRotationLogItem
          key={log.checkpoint_id}
          timeRotationLog={log}
        />
      ))}

      {/* Pagination */}
      {total > 0 && (
        <div className="flex justify-end my-6">
          <Pagination
            current={page}
            pageSize={limit}
            total={total}
            onChange={onPageChange}
            showSizeChanger
            pageSizeOptions={["10", "20", "50", "100"]}
            className="dark:text-gray-300"
          />
        </div>
      )}
    </div>
  );
};

export default TimeRotationLogList;
