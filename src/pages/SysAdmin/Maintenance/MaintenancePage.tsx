import SysAdminLayout from "../../../components/Layout/SysAdminLayout.tsx";
import { MdOutlineSettings } from "react-icons/md";
import { Pagination } from "antd";
import { useTranslation } from "react-i18next";
import EditMaintenanceModal from "./Components/EditMaintenanceModal.tsx";
import MaintenanceList from "./Components/MaintenanceList.tsx";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchMaintenance,
  selectSetting,
  selectSettingLoading,
  selectSettingPagination,
  setPage,
} from "../../../store/slices/sysadmin/settingList.sysadmin.slice.ts";
import type { AppDispatch } from "../../../store";
import type { Maintenance } from "../../../services/mainApi/sysadmin/types/maintenance.mainApi.types";
import { useEffect } from "react";
import EditSettingSysadminSlice from "../../../store/slices/sysadmin/EditSetting.sysadmin.slice.ts";
// import maintenanceSysAdminMainApi from "../../../services/mainApi/sysadmin/maintenance.sysadmin.mainApi";

export default function MaintenancePage() {
    const { t } = useTranslation();
    const dispatch = useDispatch<AppDispatch>();

    // Get maintenance data from Redux store
    const maintenances = useSelector(selectSetting);
    const loading = useSelector(selectSettingLoading);
    const pagination = useSelector(selectSettingPagination);

    const handleEditMaintenance = (maintenance: Maintenance) => {
      dispatch(EditSettingSysadminSlice.actions.openEdit(maintenance));
    }

    const handlePageChange = (page: number) => {
      dispatch(setPage(page));
    }

    useEffect(() => {
        // Fetch maintenance data when component mounts
        dispatch(fetchMaintenance());
    }, [dispatch]);

    return (
        <SysAdminLayout activePage={"settings"}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Sticky Header */}
        <div className="sticky top-0 z-10 bg-gray-50 dark:bg-gray-900 pb-6">
          {/* Header Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6 backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90 transition-all duration-300">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <span className="bg-blue-500 text-white p-2 rounded-lg mr-3 shadow-md">
                    <MdOutlineSettings className="text-xl" />
                  </span>
                  {t("setting.title", "Setting")}
                </h1>
                <p className="text-gray-500 dark:text-gray-400 mt-2 ml-12">
                  {t("setting.description", "Setting page")}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* License List with Pagination */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm mt-6 overflow-hidden transition-all duration-300 hover:shadow-md">
                  <MaintenanceList
                    settings={maintenances}
                    loading={loading}
                    onEdit={handleEditMaintenance}
                  />

          {/* Pagination */}
          <div className="p-6 border-t border-gray-100 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50">
            <Pagination
              current={pagination.current}
              total={pagination.total}
              pageSize={pagination.pageSize}
              onChange={handlePageChange}
              showTotal={(total) => `${t("common.total")} ${total} ${t("common.items")}`}
              showSizeChanger={false}
              className="flex justify-end"
              itemRender={(page, type, originalElement) => {
                if (type === 'page' && page === pagination.current) {
                  return <div className="bg-blue-500 text-white flex items-center justify-center h-8 w-8 rounded-md">{page}</div>;
                }
                return originalElement;
              }}
            />
          </div>
        </div>

        {/* Modals */}
        <EditMaintenanceModal/>
      </div>
    </SysAdminLayout>
    );
}