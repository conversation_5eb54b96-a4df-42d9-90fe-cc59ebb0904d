import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Tag, Tooltip} from "antd";
import {MdEdit} from "react-icons/md";
import {
  AppstoreOutlined,
  ClockCircleOutlined,
  DownOutlined,
  ExpandAltOutlined,
  InfoCircleOutlined,
  UpOutlined,
  UserOutlined
} from "@ant-design/icons";
import {License} from "../../../../services/mainApi/types/license.mainApi.types";
import {useTranslation} from "react-i18next";
import {useState} from "react";
import dayjs from "dayjs";

interface LicenseItemProps {
  license: License;
  onEdit: (license: License) => void;
}

export default function LicenseItem({license, onEdit}: LicenseItemProps) {
  const {t} = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  // Toggle card expansion
  const toggleExpand = () => {
    setIsExpanded(prev => !prev);
  };

  return (
    <Card
      className={`
        shadow-sm hover:shadow-lg transition-all duration-300 h-full
        bg-white dark:bg-gray-800
        dark:border-gray-700 rounded-xl
        ${!license.active ? "opacity-90" : ""}
        overflow-hidden
      `}
      bodyStyle={{padding: '24px', height: '100%', display: 'flex', flexDirection: 'column'}}
      actions={[
        <div
          key="expand"
          className={`
            flex items-center justify-center cursor-pointer px-4 py-2
            ${isExpanded ? 'text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300' : 'text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300'}
            font-medium text-sm transition-all duration-300
            hover:bg-gray-50 dark:hover:bg-gray-750
          `}
          onClick={toggleExpand}
        >
          <span
            className="mr-2">{isExpanded ? t("common.showLess", "Show Less") : t("common.showMore", "Show More Details")}</span>
          <span className="transition-transform duration-300 ease-in-out transform">
            {isExpanded ? <UpOutlined className="animate-pulse"/> : <DownOutlined className="animate-bounce-subtle"/>}
          </span>
        </div>
      ]}
    >
      <div className="space-y-4 flex-1">
        {/* Header with Status Badge and Edit Button */}
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center">
                {license.license_name}
              </h3>
              <Badge
                status={license.active ? "success" : "error"}
                text={license.active ? t("common.active", "Active") : t("common.inactive", "Inactive")}
                className={`
                  ${license.active ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}
                  text-xs font-medium
                `}
              />
            </div>
            <div className="flex items-center mt-1 text-xs text-gray-500">
              <ClockCircleOutlined className="mr-1"/>
              <span>
                {t("common.created")}: {dayjs(license.created_at).format("DD MMM YYYY")}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Tooltip title={t("common.expand")}>
              <Button
                type="text"
                shape="circle"
                icon={<ExpandAltOutlined className="text-lg"/>}
                onClick={(e) => {
                  e.stopPropagation();
                  toggleExpand();
                }}
                className="text-indigo-500 hover:text-indigo-600 dark:text-indigo-400 dark:hover:text-indigo-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 transition-all duration-300"
              />
            </Tooltip>
            <Tooltip title={t("common.edit")}>
              <Button
                type="text"
                shape="circle"
                icon={<MdEdit className="text-xl"/>}
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(license);
                }}
                className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300"
              />
            </Tooltip>
          </div>
        </div>

        {/* License Type Indicator */}
        <div className="mb-3">
          <Tag
            icon={<AppstoreOutlined/>}
            className={`
              bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400
              border-blue-100 dark:border-blue-800
              px-3 py-1 rounded-full text-xs font-medium
            `}
          >
            {t("license.type", "License Package")}
          </Tag>
        </div>

        {/* Key Metrics with Visual Indicators */}
        <div className="grid grid-cols-2 gap-x-4 gap-y-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
          <div className="flex flex-col">
            <span className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
              <UserOutlined className="mr-1"/>
              {t("license.table.maxUser")}
            </span>
            <span className="text-base font-medium text-gray-800 dark:text-gray-200">
              {license.max_user}
            </span>
          </div>
          <div className="flex flex-col">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {t("license.table.maxSubbranch")}
            </span>
            <span className="text-base font-medium text-gray-800 dark:text-gray-200">
              {license.max_subbranch}
            </span>
          </div>
          <div className="flex flex-col">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {t("license.table.maxLabel")}
            </span>
            <span className="text-base font-medium text-gray-800 dark:text-gray-200">
              {license.max_label}
            </span>
          </div>
          <div className="flex flex-col">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {t("license.maxBeacon", "Max Beacon")}
            </span>
            <span className="text-base font-medium text-gray-800 dark:text-gray-200">
              {license.max_beacon}
            </span>
          </div>
        </div>

        {/* Collapsible Details */}
        {isExpanded && (
          <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700 animate-fadeIn">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
              <InfoCircleOutlined className="mr-2 text-blue-500"/>
              {t("license.additionalDetails", "Additional Details")}
            </h4>
            <div className="grid grid-cols-2 gap-x-4 gap-y-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {t("license.maxScheduler", "Max Scheduler")}
                </span>
                <span className="text-base font-medium text-gray-800 dark:text-gray-200">
                  {license.max_scheduler}
                </span>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {t("license.maxAlert", "Max Alert")}
                </span>
                <span className="text-base font-medium text-gray-800 dark:text-gray-200">
                  {license.max_alert}
                </span>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {t("license.maxDevice", "Max Device")}
                </span>
                <span className="text-base font-medium text-gray-800 dark:text-gray-200">
                  {license.max_device}
                </span>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {t("license.maxSite", "Max Site")}
                </span>
                <span className="text-base font-medium text-gray-800 dark:text-gray-200">
                  {license.max_site}
                </span>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {t("license.maxCheckpoint", "Max Checkpoint")}
                </span>
                <span className="text-base font-medium text-gray-800 dark:text-gray-200">
                  {license.max_checkpoint}
                </span>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {t("license.maxGeofence", "Max Geofence")}
                </span>
                <span className="text-base font-medium text-gray-800 dark:text-gray-200">
                  {license.max_geofence}
                </span>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {t("license.maxForm", "Max Form")}
                </span>
                <span className="text-base font-medium text-gray-800 dark:text-gray-200">
                  {license.max_form}
                </span>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {t("license.maxTask", "Max Task")}
                </span>
                <span className="text-base font-medium text-gray-800 dark:text-gray-200">
                  {license.max_task}
                </span>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {t("license.maxActivity", "Max Activity")}
                </span>
                <span className="text-base font-medium text-gray-800 dark:text-gray-200">
                  {license.max_activity}
                </span>
              </div>
            </div>

            {/* Last updated info */}
            {license.updated_at && license.updated_at !== license.created_at && (
              <div
                className="mt-4 text-xs text-gray-500 flex items-center p-2 bg-blue-50/50 dark:bg-blue-900/10 rounded-lg">
                <InfoCircleOutlined className="mr-2 text-blue-500"/>
                <span>
                  {t("common.lastUpdated", "Last updated")}: {dayjs(license.updated_at).format("DD MMM YYYY HH:mm")}
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    </Card>
  );
}
