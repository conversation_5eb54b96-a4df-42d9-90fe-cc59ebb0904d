import baseMainApi from "../base.mainApi";
import {
  AdminCreateUpdateGeofenceRequest,
  AdminGeofence,
  AdminGeofenceGetRequest,
  AdminGeofenceListResponse,
} from "./types/geofence.admin.mainApi.types";
import { BaseResponse } from "../types/base.mainApi.types";

const geofenceAdminMainApi = {
  getGeofences: async (
    branchCode: string,
    params?: AdminGeofenceGetRequest
  ) => {
    const response =
      await baseMainApi.get<AdminGeofenceListResponse>(
        `/web-api/admin/${branchCode}/geofence`,
        { params }
      );
    return response.data;
  },

  createGeofence: async (
    branchCode: string,
    data: AdminCreateUpdateGeofenceRequest
  ) => {
    const response =
      await baseMainApi.post<AdminGeofenceListResponse>(
        `/web-api/admin/${branchCode}/geofence`,
        data
      );
    return response.data;
  },

  updateGeofence: async (
    branchCode: string,
    geofenceId: string | number,
    data: AdminCreateUpdateGeofenceRequest
  ) => {
    const response =
      await baseMainApi.put<AdminGeofenceListResponse>(
        `/web-api/admin/${branchCode}/geofence/${geofenceId}`,
        data
      );
    return response.data;
  },

  getGeofenceById: async (
    branchCode: string,
    geofenceId: string | number
  ) => {
    const response = await baseMainApi.get<
      BaseResponse<AdminGeofence>
    >(
      `/web-api/admin/${branchCode}/geofence/${geofenceId}`
    );
    return response.data;
  },
};

export default geofenceAdminMainApi;
