import { useTranslation } from "react-i18next";
import { MdHelpOutline, MdFilterList } from "react-icons/md";
import { useState, useEffect } from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import { Button } from "antd";
import UnknownDataList from "./Components/UnknownDataList";

const UnknownDataAdmin = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
  });
  const [unknownDataLogs, setUnknownDataLogs] = useState<Array<any>>([]);

  // Load data from API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // API call would go here
        // const response = await unknownDataApi.getUnknownDataLogs(pagination.page, pagination.limit);

        // Using dummy data instead
        const dummyData = [
          {
            id: 1,
            data_type: "GPS",
            timestamp: new Date().toISOString(),
            status: "unresolved",
            severity: "medium",
            raw_data: "{\"lat\": 123.456, \"lng\": 789.012, \"unknown_field\": \"value\"}",
            branch_name: "Main Branch",
            branch_id: 1,
            user_name: "John Doe",
            user_id: 101
          },
          {
            id: 2,
            data_type: "Sensor",
            timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            status: "investigating",
            severity: "high",
            raw_data: "{\"temperature\": 35, \"humidity\": \"invalid\"}",
            device_name: "Sensor XYZ",
            device_id: 202
          },
          {
            id: 3,
            data_type: "API Response",
            timestamp: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
            status: "resolved",
            severity: "low",
            raw_data: "{\"error\": \"unknown format\", \"code\": 500}",
            analysis_result: "Malformed JSON response",
            resolution: "Updated API parser",
            notes: "Similar issues might occur with legacy systems"
          },
          {
            id: 4,
            data_type: "Form Submission",
            timestamp: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
            status: "unresolved",
            user_name: "Jane Smith",
            user_id: 102,
            role_name: "Manager",
            role_id: 3,
            branch_name: "Downtown Office",
            branch_id: 2
          },
          {
            id: 5,
            data_type: "Authentication",
            timestamp: new Date(Date.now() - 345600000).toISOString(), // 4 days ago
            status: "investigating",
            severity: "critical",
            raw_data: "{\"token\": \"invalid\", \"method\": \"unknown\"}",
            device_name: "Mobile App",
            device_id: 303
          }
        ];

        setUnknownDataLogs(dummyData);
        setPagination({
          ...pagination,
          total: dummyData.length,
        });
      } catch (error) {
        console.error("Error fetching unknown data logs:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [pagination.page, pagination.limit]);

  // Handle page change
  const handlePageChange = (page: number, pageSize?: number) => {
    setPagination({
      ...pagination,
      page: page,
      limit: pageSize || pagination.limit,
    });
  };

  return (
    <WebAdminLayout activePage="unknown-data">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdHelpOutline />}
            title={t(
              "unknownData.title",
              "Unknown Data"
            )}
            description={t(
              "unknownData.description",
              "View and analyze unrecognized or corrupted data for troubleshooting"
            )}
            actions={[
              <Button
                key="filter"
                icon={<MdFilterList />}
                size="large"
                onClick={() => {
                  // Will implement filter functionality later
                }}
              >
                {t("common.filters", "Filters")}
              </Button>,
            ]}
          />

          {/* Unknown Data List */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">
              {t("unknownData.dataList", "Unknown Data List")}
            </h2>

            <UnknownDataList
              unknownDataLogs={unknownDataLogs}
              loading={loading}
              total={pagination.total}
              page={pagination.page}
              limit={pagination.limit}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      </div>
    </WebAdminLayout>
  );
};

export default UnknownDataAdmin;
