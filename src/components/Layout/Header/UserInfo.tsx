import { Button, Dropdown } from "antd";
import {
  UserOutlined,
  LogoutOutlined,
} from "@ant-design/icons";
import { useNavigate } from "react-router";
import {
  removeUserData,
  removeTokens,
} from "../../../utils/storage";
import authServiceMainApi from "../../../services/mainApi/authService.mainApi.ts";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks";
import authSlice from "../../../store/slices/auth.slice.ts";
import type { MenuProps } from "antd";

type MenuItem = Required<MenuProps>["items"][number];

export default function UserInfo() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const user = useAppSelector((state) => state.auth.user);

  const handleLogout = async () => {
    try {
      await authServiceMainApi.logout();
      removeUserData();
      removeTokens();
      dispatch(authSlice.actions.clearUser());
      navigate("/login");
      window.location.reload();
    } catch (err) {
      console.log("Logout failed", err);
    }
  };

  const userMenuItems: MenuItem[] = [
    {
      key: "profile",
      label: "My Profile",
      icon: <UserOutlined />,
      onClick: () => navigate("/profile"),
    },
    { type: "divider" },
    {
      key: "logout",
      label: "Logout",
      icon: <LogoutOutlined />,
      onClick: handleLogout,
    },
  ];

  return (
    <Dropdown
      menu={{ items: userMenuItems }}
      trigger={["click"]}
    >
      <Button
        type="text"
        className="flex items-center text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white"
      >
        <UserOutlined className="text-lg" />
        <span className="ml-2 hidden sm:inline font-medium">
          {user?.name || "Admin"}
        </span>
      </Button>
    </Dropdown>
  );
}
