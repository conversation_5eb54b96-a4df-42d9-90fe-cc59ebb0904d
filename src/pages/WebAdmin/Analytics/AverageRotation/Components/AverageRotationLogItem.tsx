import React from "react";
import { useTranslation } from "react-i18next";
import {
  FiCalendar,
  FiGrid,
  FiMapPin,
  FiMonitor,
} from "react-icons/fi";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { AdminAverageRotationLogEntry } from "../../../../../services/mainApi/admin/types/averageRotationLog.admin.mainApi.types.ts";

dayjs.extend(relativeTime);

interface AverageRotationLogItemProps {
  averageRotationLog: AdminAverageRotationLogEntry;
}

const AverageRotationLogItem: React.FC<
  AverageRotationLogItemProps
> = ({ averageRotationLog }) => {
  const { t } = useTranslation();

  // Helper function to format period minutes to human readable
  const formatPeriodMinutes = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} ${t("common.minutes", "minutes")}`;
    }

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (remainingMinutes === 0) {
      return `${hours} ${hours === 1 ? t("common.hour", "hour") : t("common.hours", "hours")}`;
    }

    return `${hours} ${hours === 1 ? t("common.hour", "hour") : t("common.hours", "hours")} ${remainingMinutes} ${t("common.minutes", "minutes")}`;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-0 p-4 mb-4">
      {/* Header - with gradient background */}
      <div className="mb-3 -mx-4 -mt-4 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 rounded-t-xl border-b border-gray-100 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center gap-2">
              {averageRotationLog.checkpoint_name}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {averageRotationLog.zone_name}
            </p>
          </div>
        </div>
      </div>

      {/* Content in grid format - 3 columns */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 px-1">
        {/* Beacon/NFC Serial */}
        <div className="group flex items-center gap-2 text-sm hover:bg-indigo-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
          <div className="flex items-center justify-center w-7 h-7 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex-shrink-0">
            <FiMonitor className="w-4 h-4" />
          </div>
          <div className="flex flex-col min-w-0">
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {t(
                  "averageRotationLog.serialNumber",
                  "Beacon/NFC Serial"
                )}
                :
              </span>
              <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                {averageRotationLog.serial_number_hex}
              </span>
            </div>
          </div>
        </div>

        {/* Zone/Site Name */}
        <div className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
          <div className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
            <FiMapPin className="w-4 h-4" />
          </div>
          <div className="flex flex-col min-w-0">
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {t(
                  "averageRotationLog.zoneName",
                  "Zone Name"
                )}
                :
              </span>
              <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                {averageRotationLog.zone_name}
              </span>
            </div>
          </div>
        </div>

        {/* Period */}
        <div className="group flex items-center gap-2 text-sm hover:bg-purple-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
          <div className="flex items-center justify-center w-7 h-7 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 flex-shrink-0">
            <FiCalendar className="w-4 h-4" />
          </div>
          <div className="flex flex-col min-w-0">
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {t("averageRotationLog.period", "Period")}:
              </span>
              <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                {formatPeriodMinutes(
                  averageRotationLog.period_minutes
                )}
              </span>
            </div>
          </div>
        </div>

        {/* Actual Hits */}
        <div className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
          <div className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
            <FiGrid className="w-4 h-4" />
          </div>
          <div className="flex flex-col min-w-0">
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {t(
                  "averageRotationLog.actualHits",
                  "Actual Hits"
                )}
                :
              </span>
              <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                {averageRotationLog.scan_count}
              </span>
            </div>
          </div>
        </div>

        {/* Average Rotation Minutes */}
        <div className="group flex items-center gap-2 text-sm hover:bg-red-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
          <div className="flex items-center justify-center w-7 h-7 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300 flex-shrink-0">
            <FiMapPin className="w-4 h-4" />
          </div>
          <div className="flex flex-col min-w-0">
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {t(
                  "averageRotationLog.avgRotationMinutes",
                  "Avg. Rotation Time"
                )}
                :
              </span>
              <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                {formatPeriodMinutes(
                  averageRotationLog.avg_rotation_minutes
                )}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Footer Section with modern styling */}
      <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center bg-gray-50 dark:bg-gray-800 -mx-4 -mb-4 px-4 py-3 rounded-b-xl">
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {/* Footer content can be added here if needed */}
        </div>
      </div>
    </div>
  );
};

export default AverageRotationLogItem;
