import {Navigate} from "react-router-dom";
import {ReactElement} from "react";
import {useAppSelector} from "../../store/hooks";
import {checkModule} from "../../utils/permission";
import {ModuleType} from "../../pages/WebAdmin/Roles/Components/ModalAddAdminRoles/types";
import {routeConfig} from "../../config/routes";

interface PermissionGuardProps {
  routeKey: keyof typeof routeConfig.admin;
  children: ReactElement;
}

// This component will conditionally render child routes based on permissions
export const PermissionGuard = ({routeKey, children}: PermissionGuardProps) => {
  const authorizedModule = useAppSelector((state) => state.auth.role?.permissions);
  const route = routeConfig.admin[routeKey];

  if (routeKey === "selectBranch" || routeKey === "dashboard") {
    return children;
  }

  // If the route has module and type properties, check authorization
  if ('module' in route && 'type' in route) {
    const isAuthorized = checkModule(
      authorizedModule,
      route.module as string,
      route.type as ModuleType,
      "allow_view"
    );

    // Only render the route if authorized or if it's the dashboard (always show)
    if (isAuthorized) {
      return children;
    }

    // Redirect to branch selection if not authorized
    return <Navigate to="/admin/branch" replace/>;
  }

  // If no module/type specified (like dashboard), always render
  return children;
};
