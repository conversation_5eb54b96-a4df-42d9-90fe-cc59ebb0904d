import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import timezoneMainApi from "../../services/mainApi/timezone.mainApi";
import { Timezone } from "../../services/mainApi/types/timezone.mainApi.types";

interface SelectTimezoneProps {
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  placeholder?: string;
}

export default function SelectTimezone({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select timezone",
}: SelectTimezoneProps) {
  const [timezones, setTimezones] = useState<Timezone[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);

  useEffect(() => {
    const fetchTimezones = async () => {
      if (isFetching.current) return;

      try {
        isFetching.current = true;
        setLoading(true);
        const response = await timezoneMainApi.getList();
        setTimezones(response.data || []);
      } catch (error) {
        console.error("Failed to fetch timezones:", error);
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchTimezones();
  }, []);

  const formatTimezoneLabel = (timezone: Timezone) => {
    return `${timezone.timezone_name} (${timezone.gmt_offset})`;
  };

  return (
    <Select
      className={className}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      disabled={disabled}
      loading={loading}
      showSearch
      optionFilterProp="label"
      options={timezones.map((timezone) => ({
        value: timezone.id,
        label: formatTimezoneLabel(timezone),
      }))}
    />
  );
}
