import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Tooltip } from "antd";
import {
  <PERSON><PERSON>lertTriangle,
  FiCheckCircle,
  FiClock,
  FiEdit,
  FiMapPin,
  FiChevronDown,
  FiChevronUp,
  FiTag,
  FiBattery
} from "react-icons/fi";
import { MdLocationOn } from "react-icons/md";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useTranslation } from "react-i18next";
import addCheckpointAdminSlice from "../../../../store/slices/admin/addCheckpoint.admin.slice.ts";
import {
  AdminCheckpoint,
  ECheckPointType,
} from "../../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types.ts";
import { useAppDispatch } from "../../../../store/hooks.ts";

dayjs.extend(relativeTime);

type DayKey =
  | "monday"
  | "tuesday"
  | "wednesday"
  | "thursday"
  | "friday"
  | "saturday"
  | "sunday";

interface CheckpointItemProps {
  checkpoint: AdminCheckpoint;
}

const CheckpointItem: React.FC<CheckpointItemProps> = ({
  checkpoint,
}) => {
  const { t } = useTranslation();
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);

  const dispatch = useAppDispatch();
  const { actions } = addCheckpointAdminSlice;

  // Function to truncate text and add ellipsis
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return "-";
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  const onUpdate = () => {
    dispatch(actions.openUpdate(checkpoint));
  };

  const schedules = [
    { day: "monday", label: "M" },
    { day: "tuesday", label: "T" },
    { day: "wednesday", label: "W" },
    { day: "thursday", label: "T" },
    { day: "friday", label: "F" },
    { day: "saturday", label: "S" },
    { day: "sunday", label: "S" },
  ];

  const renderScheduleInfo = (
    checkpoint: AdminCheckpoint
  ) => {
    return (
      <div className="flex gap-1">
        {schedules.map(({ day, label }) => (
          <Tooltip
            key={day}
            title={`${checkpoint[`${day}_count` as DayKey] || 0} ${t("checkpoint.modal.form.schedule.visits")}`}
          >
            <span
              className={`px-2 py-1 text-xs rounded ${
                checkpoint[day as DayKey]
                  ? "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                  : "bg-gray-50 text-gray-400 dark:bg-gray-800 dark:text-gray-500"
              }`}
            >
              {label}
            </span>
          </Tooltip>
        ))}
      </div>
    );
  };

  // Render technical info based on checkpoint type
  const renderTechnicalInfo = () => {
    const checkpointTypeId = checkpoint.checkpoint_type_id;

    return (
      <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs">
        <div className="flex items-center gap-1">
          <span className="text-gray-600 dark:text-gray-300">
            {t("checkpoint.technical.type")}:
          </span>
          <span className="font-medium">
            {
              checkpoint.checkpoint_type
                .checkpoint_type_name
            }
          </span>
        </div>

        {/* Render specific technical details based on checkpoint type */}
        {(checkpointTypeId === ECheckPointType.NFC || checkpointTypeId === ECheckPointType.RFID) && (
          <>
            <div className="flex items-center gap-1">
              <span className="text-gray-600 dark:text-gray-300">
                {t("checkpoint.technical.uuid")}:
              </span>
              <span className="font-medium">
                {checkpoint.serial_number_hex || "-"}
              </span>
            </div>
          </>
        )}

        {checkpointTypeId === ECheckPointType.BEACON && (
          <>
            <div className="flex items-center gap-1">
              <span className="text-gray-600 dark:text-gray-300">
                {t("checkpoint.technical.uuid")}:
              </span>
              <span className="font-medium">
                {checkpoint.serial_number_hex || "-"}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-gray-600 dark:text-gray-300">
                {t("checkpoint.technical.major")}:
              </span>
              <span className="font-medium">
                {checkpoint.major_value || "-"}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-gray-600 dark:text-gray-300">
                {t("checkpoint.technical.minor")}:
              </span>
              <span className="font-medium">
                {checkpoint.minor_value || "-"}
              </span>
            </div>
          </>
        )}

        {checkpointTypeId === ECheckPointType.GEOFENCE && checkpoint.geofence && (
          <>
            <div className="flex items-center gap-1">
              <span className="text-gray-600 dark:text-gray-300">
                {t("checkpoint.technical.name")}:
              </span>
              <span className="font-medium">
                {checkpoint.geofence.geofence_name || "-"}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-gray-600 dark:text-gray-300">
                {t("checkpoint.technical.type")}:
              </span>
              <span className="font-medium">
                {checkpoint.geofence.geofence_data?.type || "-"}
              </span>
            </div>
          </>
        )}

      </div>
    );
  };

  // Add renderLabels function
  const renderLabels = () => {
    if (!checkpoint.labels || checkpoint.labels.length === 0) {
      return (
        <span className="text-gray-500 dark:text-gray-400 text-xs">
          {t("common.noLabels", "No labels")}
        </span>
      );
    }

    return (
      <div className="flex flex-wrap gap-2">
        {checkpoint.labels.map((label) => (
          <Tag key={label.id} className="m-0">
            {label.label_name}
          </Tag>
        ))}
      </div>
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Header section */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <MdLocationOn className="text-blue-500" />
            {checkpoint.checkpoint_name}
            {checkpoint.active === false && (
              <Tag
                color="red"
                className="ml-2"
              >
                {t("checkpoint.list.status.inactive")}
              </Tag>
            )}
          </h3>
          {checkpoint.zone?.zone_name && (
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              {checkpoint.zone.zone_name}
            </p>
          )}
          <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            <p>
              {isDescriptionExpanded
                ? checkpoint.checkpoint_description || "-"
                : truncateText(checkpoint.checkpoint_description || "-", 100)}
            </p>
            {checkpoint.checkpoint_description && checkpoint.checkpoint_description.length > 100 && (
              <button
                onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                className="text-blue-500 hover:text-blue-600 flex items-center mt-1 text-xs font-medium"
              >
                {isDescriptionExpanded
                  ? <><FiChevronUp className="mr-1" /> {t("common.showLess")}</>
                  : <><FiChevronDown className="mr-1" /> {t("common.showMore")}</>
                }
              </button>
            )}
          </div>
        </div>
        <div>
          <Tooltip title={t("common.edit")}>
            <Button
              type="text"
              icon={<FiEdit className="text-lg" />}
              onClick={onUpdate}
              className="text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400"
            />
          </Tooltip>
        </div>
      </div>

      {/* Content section */}
      <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Left column */}
        <div className="space-y-4">
          {/* Details section */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
              <FiCheckCircle className="text-green-500" />
              {t("checkpoint.list.details")}
            </h4>
            {renderTechnicalInfo()}
          </div>

          {/* Labels section */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
              <FiTag className="text-purple-500" />
              {t("checkpoint.list.labels", "Labels")}
            </h4>
            {renderLabels()}
          </div>

          {/* Location section - only show if coordinates are available */}
          {(checkpoint.latitude !== null &&
            checkpoint.longitude !== null) ||
          checkpoint.zone?.zone_address ? (
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                <FiMapPin className="text-red-500" />
                {t(
                  "checkpoint.modal.form.coordinates.label"
                )}
              </h4>
              <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs">
                {checkpoint.latitude !== null &&
                  checkpoint.longitude !== null && (
                    <>
                      <div className="flex items-center gap-1">
                        <span className="text-gray-600 dark:text-gray-300">
                          {t(
                            "checkpoint.modal.form.coordinates.latitude"
                          )}
                          :
                        </span>
                        <span className="font-medium">
                          {checkpoint.latitude.toFixed(6)}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="text-gray-600 dark:text-gray-300">
                          {t(
                            "checkpoint.modal.form.coordinates.longitude"
                          )}
                          :
                        </span>
                        <span className="font-medium">
                          {checkpoint.longitude.toFixed(6)}
                        </span>
                      </div>
                    </>
                  )}
                {checkpoint.zone?.zone_address && (
                  <div className="col-span-2 flex items-start gap-1 mt-1">
                    <span className="text-gray-600 dark:text-gray-300">
                      {t(
                        "checkpoint.modal.form.address.label"
                      )}
                      :
                    </span>
                    <span className="font-medium">
                      {checkpoint.zone.zone_address}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ) : null}
        </div>

        {/* Right column */}
        <div className="space-y-4">
          {/* Schedule section */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
              <FiClock className="text-blue-500" />
              {t("checkpoint.list.schedule")}
            </h4>
            {renderScheduleInfo(checkpoint)}
          </div>

          {/* Alert section */}
          {checkpoint.visit_interval && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                <FiAlertTriangle className="text-yellow-500" />
                {t("checkpoint.modal.form.alert.title")}
              </h4>
              <div className="grid grid-cols-1 gap-1 text-xs">
                <div className="flex items-center">
                  <span className="font-medium text-green-600 dark:text-green-400">
                    {t(
                      "checkpoint.modal.form.alert.enabled"
                    )}
                  </span>
                </div>
                {checkpoint.rotation_interval && (
                  <div className="flex items-center gap-1">
                    <span className="text-gray-600 dark:text-gray-300">
                      {t("checkpoint.modal.form.rotationInterval.label", "Rotation Interval")}:
                    </span>
                    <span className="font-medium">
                      {checkpoint.rotation_interval} {t("checkpoint.form.minutes", "minutes")}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}
          {checkpoint.checkpoint_type.checkpoint_type_name === 'BEACONS' && (
          <div
            className="group flex items-center gap-2 text-sm hover:bg-indigo-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex-shrink-0">
              <FiBattery className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("checkpointBatteryLog.batteryLevel", "Battery Level")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {checkpoint.latest_voltage ? checkpoint.latest_voltage+'%' : 'N/A'}
                </span>
              </div>
            </div>
          </div>
          )}
        </div>
      </div>

      {/* Footer section */}
      <div className="px-4 py-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400">
        {t("common.created")}{" "}
        {dayjs(checkpoint.created_at).format(
          "DD MMM YYYY HH:mm"
        )}
      </div>
    </div>
  );
};

export default CheckpointItem;
