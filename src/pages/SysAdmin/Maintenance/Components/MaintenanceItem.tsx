import {But<PERSON>, <PERSON>, Tag, Tooltip} from "antd";
import {
  EditOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { Maintenance } from "../../../../services/mainApi/sysadmin/types/maintenance.mainApi.types";
import dayjs from "dayjs";

interface MaintenanceItemProps {
  maintenance: Maintenance;
  onEdit: (maintenance: Maintenance) => void;
}

export default function MaintenanceItem({maintenance, onEdit}: MaintenanceItemProps) {

  return (
    <Card
    className={`
      shadow-sm hover:shadow-md transition-all duration-200 flex flex-col h-full
      bg-white dark:bg-gray-800 
      dark:border-gray-700
      ${!maintenance.is_maintenance ? "opacity-75" : ""}
    `}
    style={{ height: "100%" }}
    bodyStyle={{ flex: 1 }}
    actions={[
      <Tooltip
        title="Edit Setting"
        key="edit"
      >
        <Button
          type="text"
          icon={<EditOutlined />}
          onClick={() => onEdit(maintenance)}
          className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-gray-700/50"
        />
      </Tooltip>,
    ]}
  >
    <div className="space-y-4">
      <div className="flex items-start justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
            Maintenance mode
          </h3>
          <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
            Created:{" "}
            {dayjs(maintenance.updated_at).format(
              "DD MMM YYYY HH:mm"
            )}
          </p>
        </div>
        <Tag
          className={`
            ${
              maintenance.is_maintenance
                ? "bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-400 border-green-200 dark:border-green-800"
                : "bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-400 border-red-200 dark:border-red-800"
            }
          `}
        >
          {maintenance.is_maintenance ? "Active" : "Inactive"}
        </Tag>
      </div>

      <div className="space-y-2">
        {maintenance.description && (
          <div className="flex items-start text-gray-600 dark:text-gray-300">
            <InfoCircleOutlined className="mr-2 mt-1 text-gray-500 dark:text-gray-400" />
            <span className="text-sm">
              {maintenance.description}
            </span>
          </div>
        )}
      </div>
    </div>
  </Card>
  );
}