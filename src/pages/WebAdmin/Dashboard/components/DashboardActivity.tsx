import { Typo<PERSON>, Spin } from "antd";
import { useTranslation } from "react-i18next";
import {useEffect, useState, useCallback, useRef} from "react";
import { logAlertAdminApi } from "../../../../services/mainApi/admin/logAlert.admin.mainApi";
import { AdminLogAlert } from "../../../../services/mainApi/admin/types/logAlert.admin.mainApi.types";
import DashboardActivityList from "./DashboardActivityList";
import { useAuth } from "../../../../hooks/useAuth";

const { Title } = Typography;

interface DashboardActivityProps {
  isActive: boolean;
}

const DashboardActivity = ({ isActive }: DashboardActivityProps) => {
  const { t } = useTranslation();
  const { userData } = useAuth();
  const [activities, setActivities] = useState<AdminLogAlert[]>([]);
  const [initialLoading, setInitialLoading] = useState(true);
  const isFetching = useRef(false);
  const isInitialFetch = useRef(true);

  const fetchActivities = useCallback(async () => {
    if (isFetching.current) return;

    try {
      if (userData?.current_branch?.branch_code) {
        isFetching.current = true;
        const response = await logAlertAdminApi.getLogAlerts(userData.current_branch.branch_code, {
        });
        setActivities(response.data || []);
      }
    } catch (error) {
      console.error("Error fetching activities:", error);
    } finally {
      if (isInitialFetch.current) {
        setInitialLoading(false);
        isInitialFetch.current = false;
      }
      isFetching.current = false;
    }
  }, [userData?.current_branch?.branch_code]);

  useEffect(() => {
    // Selalu fetch data saat pertama kali mount
    fetchActivities();
    
    // Hanya jalankan interval jika tab activity aktif
    let intervalId: NodeJS.Timeout | null = null;
    if (isActive) {
      intervalId = setInterval(() => {
        fetchActivities();
      }, 20000);
    }
    
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [fetchActivities, isActive]);

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
      <Title className="dark:text-gray-200" level={2}>
        {t("dashboard.title")}
      </Title>

      {initialLoading ? (
        <div className="flex justify-center items-center py-8">
          <Spin size="large" />
        </div>
      ) : (
        <DashboardActivityList activities={activities} onRefresh={fetchActivities} />
      )}
    </div>
  );
};

export default DashboardActivity;
