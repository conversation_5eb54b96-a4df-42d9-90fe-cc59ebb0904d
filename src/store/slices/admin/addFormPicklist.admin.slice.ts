import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { v4 as uuidv4 } from "uuid";
import { RootState } from "../..";
import { isAxiosError } from "axios";
import { handleApiError } from "../../../utils/error";
import formPicklistAdminMainApi from "../../../services/mainApi/admin/formPicklist.admin.mainApi";
import { AdminFormPicklist } from "../../../services/mainApi/admin/types/formPicklist.admin.mainApi.types";

// Interface untuk option picklist
interface PicklistOption {
  rowId: string;
  form_picklist_option_name: string;
  form_picklist_option_description: string | null;
  active: boolean;
}

// Interface untuk state
interface State {
  open: boolean;
  mode: "create" | "update";
  loading: boolean;
  submitting: boolean;
  errorMessage: string[] | null;
  selectedPicklist: AdminFormPicklist | null;

  // Form fields
  form_picklist_name: string;
  form_picklist_description: string | null;
  active: boolean;
  options: PicklistOption[];
}

const initialState: State = {
  open: false,
  mode: "create",
  loading: false,
  submitting: false,
  errorMessage: null,
  selectedPicklist: null,

  form_picklist_name: "",
  form_picklist_description: null,
  active: true,
  options: [],
};

// Create Picklist Thunk
export const createFormPicklist = createAsyncThunk(
  "addFormPicklistAdmin/createFormPicklist",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    const state = getState() as RootState;
    const addPicklistState = state.addFormPicklistAdmin;

    try {
      const response =
        await formPicklistAdminMainApi.createFormPicklist(
          branchCode,
          {
            form_picklist_name:
              addPicklistState.form_picklist_name,
            form_picklist_description:
              addPicklistState.form_picklist_description,
            active: addPicklistState.active,
            options: addPicklistState.options.map(
              (option) => ({
                form_picklist_option_name:
                  option.form_picklist_option_name,
                form_picklist_option_description:
                  option.form_picklist_option_description,
                active: option.active,
              })
            ),
          }
        );
      return response.data;
    } catch (e) {
      if (isAxiosError(e)) {
        const { errors } = handleApiError(e);
        return rejectWithValue(errors);
      }
      return rejectWithValue(["Failed to create picklist"]);
    }
  }
);

// Update Picklist Thunk
export const updateFormPicklist = createAsyncThunk(
  "addFormPicklistAdmin/updateFormPicklist",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    const state = getState() as RootState;
    const addPicklistState = state.addFormPicklistAdmin;

    if (!addPicklistState.selectedPicklist) {
      return rejectWithValue([
        "No picklist selected for update",
      ]);
    }

    try {
      const response =
        await formPicklistAdminMainApi.updateFormPicklist(
          branchCode,
          addPicklistState.selectedPicklist.id,
          {
            form_picklist_name:
              addPicklistState.form_picklist_name,
            form_picklist_description:
              addPicklistState.form_picklist_description,
            active: addPicklistState.active,
            options: addPicklistState.options.map(
              (option) => ({
                form_picklist_option_name:
                  option.form_picklist_option_name,
                form_picklist_option_description:
                  option.form_picklist_option_description,
                active: option.active,
              })
            ),
          }
        );
      return response.data;
    } catch (e) {
      if (isAxiosError(e)) {
        const { errors } = handleApiError(e);
        return rejectWithValue(errors);
      }
      return rejectWithValue(["Failed to update picklist"]);
    }
  }
);

const addFormPicklistAdminSlice = createSlice({
  name: "addFormPicklistAdmin",
  initialState,
  reducers: {
    onClose: () => initialState,

    setError: (
      state,
      action: PayloadAction<string[] | null>
    ) => {
      state.errorMessage = action.payload;
    },

    open: (state) => {
      state.open = true;
      state.mode = "create";
    },

    openUpdate: (
      state,
      action: PayloadAction<AdminFormPicklist>
    ) => {
      state.open = true;
      state.mode = "update";
      state.selectedPicklist = action.payload;
      state.errorMessage = null;

      // Populate form fields
      state.form_picklist_name =
        action.payload.form_picklist_name;
      state.form_picklist_description =
        action.payload.form_picklist_description;
      state.active = action.payload.active;
      state.options = action.payload.options.map(
        (option) => ({
          rowId: uuidv4(),
          form_picklist_option_name:
            option.form_picklist_option_name,
          form_picklist_option_description:
            option.form_picklist_option_description,
          active: option.active,
        })
      );
    },

    close: (state) => {
      state.open = false;
      state.errorMessage = null;
    },

    setName: (state, action: PayloadAction<string>) => {
      state.form_picklist_name = action.payload;
    },

    setDescription: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.form_picklist_description = action.payload;
    },

    setActive: (state, action: PayloadAction<boolean>) => {
      state.active = action.payload;
    },

    // Option management
    addOption: (state) => {
      state.options.push({
        rowId: uuidv4(),
        form_picklist_option_name: "",
        form_picklist_option_description: null,
        active: true,
      });
    },

    removeOption: (
      state,
      action: PayloadAction<string>
    ) => {
      state.options = state.options.filter(
        (option) => option.rowId !== action.payload
      );
    },

    setOptionName: (
      state,
      action: PayloadAction<{
        rowId: string;
        form_picklist_option_name: string;
      }>
    ) => {
      const option = state.options.find(
        (opt) => opt.rowId === action.payload.rowId
      );
      if (option) {
        option.form_picklist_option_name =
          action.payload.form_picklist_option_name;
      }
    },

    setOptionDescription: (
      state,
      action: PayloadAction<{
        rowId: string;
        form_picklist_option_description: string | null;
      }>
    ) => {
      const option = state.options.find(
        (opt) => opt.rowId === action.payload.rowId
      );
      if (option) {
        option.form_picklist_option_description =
          action.payload.form_picklist_option_description;
      }
    },

    setOptionActive: (
      state,
      action: PayloadAction<{
        rowId: string;
        active: boolean;
      }>
    ) => {
      const option = state.options.find(
        (opt) => opt.rowId === action.payload.rowId
      );
      if (option) {
        option.active = action.payload.active;
      }
    },
  },
  extraReducers: (builder) => {
    // Create picklist cases
    builder
      .addCase(createFormPicklist.pending, (state) => {
        state.submitting = true;
        state.errorMessage = null;
      })
      .addCase(createFormPicklist.fulfilled, (state) => {
        state.submitting = false;
      })
      .addCase(
        createFormPicklist.rejected,
        (state, action) => {
          state.submitting = false;
          state.errorMessage = action.payload as string[];
        }
      )

      // Update picklist cases
      .addCase(updateFormPicklist.pending, (state) => {
        state.submitting = true;
        state.errorMessage = null;
      })
      .addCase(updateFormPicklist.fulfilled, (state) => {
        state.submitting = false;
      })
      .addCase(
        updateFormPicklist.rejected,
        (state, action) => {
          state.submitting = false;
          state.errorMessage = action.payload as string[];
        }
      );
  },
});

export const { actions } = addFormPicklistAdminSlice;
export default addFormPicklistAdminSlice;
