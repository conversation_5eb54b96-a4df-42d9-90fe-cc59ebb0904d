import { BaseResponse } from "../../types/base.mainApi.types";

/**
 * Time On Zone API Types for Admin
 */

/**
 * Query parameters for time on zone API
 */
export interface AdminTimeOnZoneListParams {
  start_date?: string; // Format: YYYY-MM-DD (e.g., 2025-03-01)
  start_time?: string; // Format: HH:MM (e.g., 08:00)
  end_date?: string; // Format: YYYY-MM-DD (e.g., 2025-03-31)
  end_time?: string; // Format: HH:MM (e.g., 17:30)
  branch_id?: string | number;
  zone_id?: string | number;
  zone_labels?: string | number | (string | number)[]; // Comma-separated values (e.g., "1,2,3")
  user_id?: string | number;
  user_labels?: string | number | (string | number)[]; // Comma-separated values (e.g., "1,2,3")
  device_id?: string | number;
  device_labels?: string | number | (string | number)[]; // Comma-separated values (e.g., "1,2,3")
  page?: number;
  limit?: number;
}

/**
 * Time On Zone entry data
 */
export interface TimeOnZoneEntry {
  zone_id: string;
  zone_name: string;
  user_id: string;
  user_name: string;
  device_id: string;
  device_name: string;
  branch_id: string;
  branch_name: string;
  branch_code: string;
  latitude: number;
  longitude: number;
  original_submitted_time: string; // ISO date string
  entry: string; // ISO date string
  exit: string; // ISO date string
  duration: number; // Duration in seconds
}

/**
 * Time on zone response type using BaseResponse
 */
export type AdminTimeOnZoneListResponse = BaseResponse<
  TimeOnZoneEntry[]
>;
