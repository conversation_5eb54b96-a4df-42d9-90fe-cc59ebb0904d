import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MdLocationOn } from "react-icons/md";
import { useState } from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import ActiveFilters, {
  createEndDateFilter,
  createStartDateFilter,
  FilterGroup,
} from "../../../../components/Admin/Common/ActiveFilters";
import { Button, DatePicker, Drawer, Space } from "antd";
import GpsHeatmap from "./Components/GpsHeatmap";
import indonesiaGpsData, { getDataForTimeRange } from "./mockData";
import type { Dayjs } from 'dayjs';

// Define the filter state type
interface FilterState {
  startDate: Date | null;
  endDate: Date | null;
}

const GpsHeatmapAdmin = () => {
  const { t } = useTranslation();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [filterState, setFilterState] = useState<FilterState>({
    startDate: null,
    endDate: null,
  });
  const [filteredData, setFilteredData] = useState(indonesiaGpsData);
  const [loading, setLoading] = useState(false);

  // Check if there are any active filters
  const hasActiveFilters = (): boolean => {
    return !!(filterState.startDate || filterState.endDate);
  };

  // Handle applying filters
  const handleApplyFilters = (newFilters: FilterState) => {
    setFilterState(newFilters);
    setDrawerVisible(false);
    
    // Apply filter to data
    if (newFilters.startDate && newFilters.endDate) {
      setLoading(true);
      // Simulate API call with timeout
      setTimeout(() => {
        setFilteredData(getDataForTimeRange(newFilters.startDate!, newFilters.endDate!));
        setLoading(false);
      }, 500);
    } else {
      setFilteredData(indonesiaGpsData);
    }
  };

  // Get filter groups configuration for ActiveFilters component
  const getFilterGroups = (): FilterGroup[] => {
    return [
      {
        key: "datetime",
        filters: [
          createStartDateFilter(filterState.startDate ? filterState.startDate.toISOString().split('T')[0] : null),
          createEndDateFilter(filterState.endDate ? filterState.endDate.toISOString().split('T')[0] : null),
        ],
      },
    ];
  };

  // Handle removing a filter
  const handleRemoveFilter = (filterKey: keyof FilterState) => {
    const newFilterState = { ...filterState };
    newFilterState[filterKey] = null;
    handleApplyFilters(newFilterState);
  };

  // Handle editing filters
  const handleEditFilter = () => {
    setDrawerVisible(true);
  };

  // Handle clearing all filters
  const handleClearAll = () => {
    handleApplyFilters({
      startDate: null,
      endDate: null,
    });
  };

  // Filter drawer form content
  const FilterDrawerForm = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-base font-medium mb-2 text-gray-800 dark:text-white">
          {t("gpsHeatmap.dateRange", "Date Range")}
        </h3>
        <Space direction="vertical" size={12} className="w-full">
          <DatePicker
            className="w-full"
            placeholder={t("common.startDate", "Start Date")}
            value={filterState.startDate ? null : null} // Using null instead of Date to avoid type issues
            onChange={(date: Dayjs | null) => {
              setFilterState({
                ...filterState,
                startDate: date ? date.toDate() : null,
              });
            }}
          />
          <DatePicker
            className="w-full"
            placeholder={t("common.endDate", "End Date")}
            value={filterState.endDate ? null : null} // Using null instead of Date to avoid type issues
            onChange={(date: Dayjs | null) => {
              setFilterState({
                ...filterState,
                endDate: date ? date.toDate() : null,
              });
            }}
          />
        </Space>
      </div>

      <div className="flex justify-end pt-4 space-x-3 border-t border-gray-200 dark:border-gray-700">
        <Button onClick={() => setDrawerVisible(false)}>
          {t("common.cancel", "Cancel")}
        </Button>
        <Button
          type="primary"
          onClick={() => handleApplyFilters(filterState)}
          className="bg-indigo-600 hover:bg-indigo-700"
        >
          {t("common.apply", "Apply")}
        </Button>
      </div>
    </div>
  );

  return (
    <WebAdminLayout activePage="gps-heatmap">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdLocationOn />}
            title={t("gpsHeatmap.title", "GPS Heatmap")}
            description={t(
              "gpsHeatmap.description",
              "View location density of users across Indonesia"
            )}
            actions={[
              // Desktop view - show all buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <Button
                  key="filter"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList />}
                  size="large"
                  type="primary"
                  className="bg-indigo-600 hover:bg-indigo-700 border-0 shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters */}
          <ActiveFilters<FilterState>
            filterGroups={getFilterGroups()}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAll}
            onShowFilters={() => {
              setDrawerVisible(true);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Main content - GpsHeatmap */}
          <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                {t("gpsHeatmap.heatmapTitle", "Location Density Map")}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t("gpsHeatmap.heatmapDescription", "Visualizing user location data across Indonesia")}
              </p>
            </div>
            <div className="h-[600px]">
              <GpsHeatmap
                points={filteredData}
                loading={loading}
                initialCenter={[-2.5, 118]} // Center of Indonesia approximately
                initialZoom={5}
              />
            </div>
          </div>
        </div>

        {/* Filter Drawer */}
        <Drawer
          title={
            <div className="text-lg font-semibold text-gray-800 dark:text-white">
              {t(
                "gpsHeatmap.filters.title",
                "Filter GPS Heatmap"
              )}
            </div>
          }
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          className="dark:bg-gray-800"
        >
          <FilterDrawerForm />
        </Drawer>
      </div>
    </WebAdminLayout>
  );
};

export default GpsHeatmapAdmin;
