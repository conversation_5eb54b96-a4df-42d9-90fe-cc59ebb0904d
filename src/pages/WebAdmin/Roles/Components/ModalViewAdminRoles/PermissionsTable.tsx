import { Md<PERSON><PERSON><PERSON>, MdCheckCircle } from "react-icons/md";
import { PermissionsTableProps } from "./types.ts";
import { Tag } from "antd";
import { useTranslation } from "react-i18next";

const ModuleTypeTag = ({ type }: { type: string }) => {
  const config = {
    MODULE: {
      color: "blue",
      icon: "⚡",
    },
    REPORT: {
      color: "purple",
      icon: "📊",
    },
  }[type] || { color: "default", icon: "🔷" };

  return (
    <Tag
      color={config.color}
      className="ml-2"
    >
      {config.icon} {type}
    </Tag>
  );
};

const PermissionsTable = ({
  permissions,
  type,
}: PermissionsTableProps) => {
  const { t } = useTranslation();

  return (
    <div className="space-y-2">
      <div className="flex items-center">
        <h4 className="text-md font-medium dark:text-white">
          {type === "MODULE"
            ? t("roles.permissions.modulePermissions")
            : t("roles.permissions.reportPermissions")}
        </h4>
        <ModuleTypeTag type={type} />
      </div>
      <div className="overflow-x-auto rounded-lg border dark:border-gray-700">
        <table className="w-full text-sm bg-white dark:bg-gray-800">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-700">
              <th className="text-left px-4 py-3 border-b dark:border-gray-600 font-medium">
                {t("roles.permissions.name")}
              </th>
              <th className="text-center px-4 py-3 border-b dark:border-gray-600 font-medium">
                {t("roles.permissions.view")}
              </th>
              <th className="text-center px-4 py-3 border-b dark:border-gray-600 font-medium">
                {t("roles.permissions.create")}
              </th>
              <th className="text-center px-4 py-3 border-b dark:border-gray-600 font-medium">
                {t("roles.permissions.update")}
              </th>
              <th className="text-center px-4 py-3 border-b dark:border-gray-600 font-medium">
                {t("roles.permissions.delete")}
              </th>
            </tr>
          </thead>
          <tbody>
            {permissions.map((permission, index) => (
              <tr
                key={permission.id}
                className={`
                  border-b dark:border-gray-600 
                  ${index % 2 === 0 ? "bg-gray-50 dark:bg-gray-700/50" : ""}
                  hover:bg-gray-100 dark:hover:bg-gray-700
                  transition-colors
                `}
              >
                <td className="px-4 py-3 dark:text-white">
                  <div className="font-medium">
                    {permission.module.module_name}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {permission.module.module_description}
                  </div>
                </td>
                <td className="text-center px-4 py-3">
                  {permission.allow_view ? (
                    <MdCheckCircle className="inline text-green-500 text-xl" />
                  ) : (
                    <MdCancel className="inline text-red-500 text-xl" />
                  )}
                </td>
                <td className="text-center px-4 py-3">
                  {permission.allow_create ? (
                    <MdCheckCircle className="inline text-green-500 text-xl" />
                  ) : (
                    <MdCancel className="inline text-red-500 text-xl" />
                  )}
                </td>
                <td className="text-center px-4 py-3">
                  {permission.allow_update ? (
                    <MdCheckCircle className="inline text-green-500 text-xl" />
                  ) : (
                    <MdCancel className="inline text-red-500 text-xl" />
                  )}
                </td>
                <td className="text-center px-4 py-3">
                  {permission.allow_delete ? (
                    <MdCheckCircle className="inline text-green-500 text-xl" />
                  ) : (
                    <MdCancel className="inline text-red-500 text-xl" />
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

PermissionsTable.displayName = "PermissionsTable";

export default PermissionsTable;
