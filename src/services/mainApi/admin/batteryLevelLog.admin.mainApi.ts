import baseMainApi from "../base.mainApi";
import {
  AdminBatteryLevelLogListResponse,
  AdminBatteryLevelLogListParams,
} from "./types/batteryLevelLog.admin.mainApi.types";

export const batteryLevelLogAdminApi = {
  getBatteryLevelLogs: async (
    branchCode: string,
    params?: AdminBatteryLevelLogListParams
  ) => {
    const response =
      await baseMainApi.get<AdminBatteryLevelLogListResponse>(
        `/web-api/admin/${branchCode}/battery-level`,
        { params }
      );
    return response.data;
  },

  exportBatteryLevelLogs: async (
    branchCode: string,
    params: AdminBatteryLevelLogListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/battery-level/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
}; 