import {FC} from "react";
import {EReportType} from "../../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import {Form, InputNumber, Select} from "antd";
import {useDispatch, useSelector} from "react-redux";
import {RootState} from "../../../../../store";
import addSchedulerAdminSlice from "../../../../../store/slices/admin/addScheduler.admin.slice";
import SelectRole from "../../../../../components/Roles/SelectRole.tsx";
import SelectLabel from "../../../../../components/Label/SelectLabel.tsx";
import SelectUser from "../../../../../components/SelectUser/SelectUser.tsx";
import SelectDevice from "../../../../../components/SelectDevice/SelectDevice.tsx";
import SelectActivity from "../../../../../components/Activity/SelectActivity.tsx";
import {useTranslation} from "react-i18next";
import SelectCheckpoint from "../../../../../components/Checkpoint/SelectCheckpoint.tsx";
import SelectZone from "../../../../../components/Zone/SelectZone.tsx";
import SelectForm from "../../../../../components/Form/SelectForm.tsx";

interface SchedulerFormFieldsProps {
  reportTypeId: string;
}

/**
 * Komponen yang me-render form filter yang sesuai berdasarkan tipe laporan pada Scheduler
 */
const SchedulerFormFields: FC<SchedulerFormFieldsProps> = ({
                                                             reportTypeId,
                                                           }) => {
  switch (reportTypeId) {
    case EReportType.ACTIVITY_LOG:
      return <ActivityLogFilterForm/>;
    case EReportType.ALARMS:
      return <AlarmFilterForm/>;
    case EReportType.AVERAGE_ROTATION:
      return <AverageRotationFilterForm/>;
    case EReportType.BRANCH_DETAILS:
      return <BranchDetailFilterForm/>;
    case EReportType.CHECKPOINT_ACTIVITY:
      return <CheckpointActivityFilterForm/>;
    case EReportType.CHECKPOINT_BATTERY:
      return <CheckpointBatteryFilterForm/>;
    case EReportType.EXCEPTION:
      return <ExceptionFilterForm/>;
    case EReportType.EXCEPTION_DETAILED:
      return <ExceptionDetailedFilterForm/>;
    case EReportType.FORMS:
      return <FormsFilterForm/>;
    case EReportType.GEOFENCE:
      return <GeofenceFilterForm/>;
    case EReportType.GPS_HEATMAP:
      return <GpsHeatmapFilterForm/>;
    case EReportType.MISSED_ZONE:
      return <MissedZoneFilterForm/>;
    case EReportType.SIGN_ON_OFF:
      return <SignOnOffFilterForm/>;
    case EReportType.TASKS:
      return <TaskFilterForm/>;
    case EReportType.TIME_AND_ROTATION:
      return <TimeAndRotationFilterForm/>;
    case EReportType.TIME_ON_ZONE:
      return <TimeOnZoneFilterForm/>;
    case EReportType.UNKNOWN:
    default:
      return <UnknownFilterForm/>;
  }
};

// Reusable filter components
const UserFilterGroup = () => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {user_id, user_label_ids} = useSelector(
    (state: RootState) => state.addSchedulerAdmin
  );

  const {setUserId, setUserLabelIds} =
    addSchedulerAdminSlice.actions;

  return (
    <div className="mb-4">
      <h3 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
        {t(
          "scheduler.modal.form.grouping.user",
          "User Group"
        )}
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* User Selection */}
        <Form.Item
          label={t(
            "scheduler.modal.form.user.label",
            "User"
          )}
          tooltip={t(
            "scheduler.modal.form.user.tooltip",
            "Select the user for filtering"
          )}
        >
          <SelectUser
            className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
            value={user_id || undefined}
            onChange={(value) =>
              dispatch(setUserId(value || ""))
            }
          />
        </Form.Item>

        {/* User Label */}
        <Form.Item
          label={t(
            "scheduler.modal.form.userLabel.label",
            "User Label"
          )}
          tooltip={t(
            "scheduler.modal.form.userLabel.tooltip",
            "Select user labels for filtering"
          )}
        >
          <SelectLabel
            className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
            mode="multiple"
            value={user_label_ids}
            onChange={(values) =>
              dispatch(setUserLabelIds(values as string[]))
            }
            disabled={!!user_id}
          />
        </Form.Item>
      </div>
    </div>
  );
};

const DeviceFilterGroup = () => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {device_id, device_label_ids} = useSelector(
    (state: RootState) => state.addSchedulerAdmin
  );

  const {setDeviceId, setDeviceLabelIds} =
    addSchedulerAdminSlice.actions;

  return (
    <div className="mb-4">
      <h3 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
        {t(
          "scheduler.modal.form.grouping.device",
          "Device Group"
        )}
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Device Selection */}
        <Form.Item
          label={t(
            "scheduler.modal.form.device.label",
            "Device"
          )}
          tooltip={t(
            "scheduler.modal.form.device.tooltip",
            "Select the device for filtering"
          )}
        >
          <SelectDevice
            className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
            value={device_id || undefined}
            onChange={(value) =>
              dispatch(setDeviceId(value))
            }
          />
        </Form.Item>

        {/* Device Label */}
        <Form.Item
          label={t(
            "scheduler.modal.form.deviceLabel.label",
            "Device Label"
          )}
          tooltip={t(
            "scheduler.modal.form.deviceLabel.tooltip",
            "Select device labels for filtering"
          )}
        >
          <SelectLabel
            className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
            mode="multiple"
            value={device_label_ids}
            onChange={(values) =>
              dispatch(
                setDeviceLabelIds(values as string[])
              )
            }
            disabled={!!device_id}
          />
        </Form.Item>
      </div>
    </div>
  );
};

const RoleFilterGroup = () => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {role_id} = useSelector(
    (state: RootState) => state.addSchedulerAdmin
  );

  const {setRoleId} = addSchedulerAdminSlice.actions;

  return (
    <div className="mb-4">
      <h3 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
        {t("scheduler.modal.form.grouping.role", "Role")}
      </h3>
      <div className="grid grid-cols-1">
        <Form.Item
          label={t(
            "scheduler.modal.form.role.label",
            "Role"
          )}
          tooltip={t(
            "scheduler.modal.form.role.tooltip",
            "Select the role for filtering"
          )}
        >
          <SelectRole
            className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
            value={role_id || undefined}
            onChange={(value) => dispatch(setRoleId(value))}
          />
        </Form.Item>
      </div>
    </div>
  );
};

const SiteFilterGroup = () => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {zone_id, zone_label_ids} = useSelector(
    (state: RootState) => state.addSchedulerAdmin
  );

  const {setZoneId, setZoneLabelIds} =
    addSchedulerAdminSlice.actions;

  return (
    <div className="mb-4">
      <h3 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
        {t("scheduler.modal.form.grouping.site", "Site")}
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Site Selection */}
        <Form.Item
          label={t(
            "scheduler.modal.form.site.label",
            "Site"
          )}
          tooltip={t(
            "scheduler.modal.form.site.tooltip",
            "Select the site for filtering"
          )}
        >
          <SelectZone
            className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
            value={zone_id || undefined}
            onChange={(value) => dispatch(setZoneId(value))}
          />
        </Form.Item>

        {/* Site Label */}
        <Form.Item
          label={t(
            "scheduler.modal.form.siteLabel.label",
            "Site Label"
          )}
          tooltip={t(
            "scheduler.modal.form.siteLabel.tooltip",
            "Select site labels for filtering"
          )}
        >
          <SelectLabel
            className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
            mode="multiple"
            value={zone_label_ids}
            onChange={(values) =>
              dispatch(setZoneLabelIds(values as string[]))
            }
            disabled={!!zone_id}
          />
        </Form.Item>
      </div>
    </div>
  );
};

const CheckpointFilterGroup = () => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {checkpoint_id, checkpoint_label_ids} =
    useSelector(
      (state: RootState) => state.addSchedulerAdmin
    );

  const {setCheckpointId, setCheckpointLabelIds} =
    addSchedulerAdminSlice.actions;

  return (
    <div className="mb-4">
      <h3 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
        {t(
          "scheduler.modal.form.grouping.checkpoint",
          "Checkpoint"
        )}
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Checkpoint Selection */}
        <Form.Item
          label={t(
            "scheduler.modal.form.checkpoint.label",
            "Checkpoint"
          )}
          tooltip={t(
            "scheduler.modal.form.checkpoint.tooltip",
            "Select the checkpoint for filtering"
          )}
        >
          <SelectCheckpoint
            className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
            value={checkpoint_id || undefined}
            onChange={(value) =>
              dispatch(setCheckpointId(value))
            }
          />
        </Form.Item>

        {/* Checkpoint Label */}
        <Form.Item
          label={t(
            "scheduler.modal.form.checkpointLabel.label",
            "Checkpoint Label"
          )}
          tooltip={t(
            "scheduler.modal.form.checkpointLabel.tooltip",
            "Select checkpoint labels for filtering"
          )}
        >
          <SelectLabel
            className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
            mode="multiple"
            value={checkpoint_label_ids}
            onChange={(values) =>
              dispatch(
                setCheckpointLabelIds(values as string[])
              )
            }
            disabled={!!checkpoint_id}
          />
        </Form.Item>
      </div>
    </div>
  );
};

const FormFilterGroup = () => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {form_id} = useSelector(
    (state: RootState) => state.addSchedulerAdmin
  );

  const {setFormId} = addSchedulerAdminSlice.actions;

  return (
    <div className="mb-4">
      <h3 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
        {t(
          "scheduler.modal.form.grouping.forms",
          "Forms"
        )}
      </h3>
      <div className="grid grid-cols-1">
        <Form.Item
          label={t(
            "scheduler.modal.form.forms.label",
            "Forms"
          )}
          tooltip={t(
            "scheduler.modal.form.forms.tooltip",
            "Select the forms for filtering"
          )}
        >
          <SelectForm
            className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
            value={form_id || undefined}
            onChange={(value) =>
              dispatch(setFormId(value))
            }
            placeholder={t("scheduler.modal.form.forms.placeholder", "Select a form")}
            clearable={true}
          />
        </Form.Item>
      </div>
    </div>
  );
};

// Individual form components for each report type
const ActivityLogFilterForm = () => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {activity_id} = useSelector(
    (state: RootState) => state.addSchedulerAdmin
  );

  const {setActivityId} = addSchedulerAdminSlice.actions;

  return (
    <div>
      <RoleFilterGroup/>
      <UserFilterGroup/>
      <DeviceFilterGroup/>

      {/* Activity Section */}
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
          {t(
            "scheduler.modal.form.grouping.activity",
            "Activity"
          )}
        </h3>
        <div className="grid grid-cols-1">
          <Form.Item
            label={t(
              "scheduler.modal.form.activity.label",
              "Activity"
            )}
            tooltip={t(
              "scheduler.modal.form.activity.tooltip",
              "Select the activity for filtering"
            )}
          >
            <SelectActivity
              className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              value={activity_id || undefined}
              onChange={(value) =>
                dispatch(setActivityId(value))
              }
            />
          </Form.Item>
        </div>
      </div>
    </div>
  );
};

const AlarmFilterForm = () => {
  return (
    <div>
      <RoleFilterGroup/>
      <UserFilterGroup/>
      <DeviceFilterGroup/>
    </div>
  );
};

const AverageRotationFilterForm = () => {
  return (
    <div>
      <SiteFilterGroup/>
    </div>
  );
};

const BranchDetailFilterForm = () => {
  const {t} = useTranslation();

  return (
    <div>
      <div className="text-gray-500 italic">
        {t(
          "scheduler.modal.form.noAdditionalFields",
          "No additional fields required for this report type."
        )}
      </div>
    </div>
  );
};

const CheckpointActivityFilterForm = () => {
  return (
    <div>
      <SiteFilterGroup/>
      <CheckpointFilterGroup/>
      <RoleFilterGroup/>
      <UserFilterGroup/>
      <DeviceFilterGroup/>
    </div>
  );
};

const CheckpointBatteryFilterForm = () => {
  return (
    <div>
      <SiteFilterGroup/>
      <CheckpointFilterGroup/>
    </div>
  );
};

const ExceptionFilterForm = () => {
  return (
    <div>
      <SiteFilterGroup/>
      <CheckpointFilterGroup/>
      <RoleFilterGroup/>
      <UserFilterGroup/>
      <DeviceFilterGroup/>
    </div>
  );
};

const ExceptionDetailedFilterForm = () => {
  return (
    <div>
      <SiteFilterGroup/>
      <CheckpointFilterGroup/>
      <RoleFilterGroup/>
      <UserFilterGroup/>
      <DeviceFilterGroup/>
    </div>
  );
};

const FormsFilterForm = () => {
  return (
    <div>
      <RoleFilterGroup/>
      <UserFilterGroup/>
      <DeviceFilterGroup/>
      <FormFilterGroup/>
    </div>
  );
};

const GeofenceFilterForm = () => {
  return (
    <div>
      <SiteFilterGroup/>
      <RoleFilterGroup/>
      <UserFilterGroup/>
      <DeviceFilterGroup/>
    </div>
  );
};

const GpsHeatmapFilterForm = () => {
  return (
    <div>
      <UserFilterGroup/>
    </div>
  );
};

const MissedZoneFilterForm = () => {
  return (
    <div>
      <SiteFilterGroup/>
    </div>
  );
};

const SignOnOffFilterForm = () => {
  return (
    <div>
      <UserFilterGroup/>
      <DeviceFilterGroup/>
    </div>
  );
};

const TaskFilterForm = () => {
  const {t} = useTranslation();
  // Karena tidak ada task_id di state, kita mungkin perlu mengandalkan opsi lain yang ada

  return (
    <div>
      <RoleFilterGroup/>
      <UserFilterGroup/>
      <DeviceFilterGroup/>

      {/* Task Section */}
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
          {t("scheduler.modal.form.grouping.task", "Task")}
        </h3>
        <div className="text-gray-500 italic">
          {t(
            "scheduler.modal.form.taskSelection.unavailable",
            "Task selection is currently unavailable or requires additional configuration in the slice."
          )}
        </div>
      </div>
    </div>
  );
};

const TimeAndRotationFilterForm = () => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {rotation_interval, rotation_method} =
    useSelector(
      (state: RootState) => state.addSchedulerAdmin
    );

  const {setRotationInterval, setRotationMethod} =
    addSchedulerAdminSlice.actions;

  return (
    <div>
      <SiteFilterGroup/>
      <CheckpointFilterGroup/>
      <RoleFilterGroup/>
      <UserFilterGroup/>
      <DeviceFilterGroup/>

      {/* Rotation Settings */}
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
          {t(
            "scheduler.modal.form.grouping.rotation",
            "Rotation Settings"
          )}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Rotation Interval */}
          <Form.Item
            label={t(
              "scheduler.modal.form.rotationInterval.label",
              "Rotation Interval (minutes)"
            )}
            tooltip={t(
              "scheduler.modal.form.rotationInterval.tooltip",
              "Enter rotation interval in minutes"
            )}
            required
          >
            <InputNumber
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              min={1}
              value={rotation_interval}
              onChange={(value) =>
                dispatch(setRotationInterval(value || 0))
              }
            />
          </Form.Item>

          {/* Rotation Method */}
          <Form.Item
            label={t(
              "scheduler.modal.form.rotationMethod.label",
              "Rotation Method"
            )}
            tooltip={t(
              "scheduler.modal.form.rotationMethod.tooltip",
              "Select rotation calculation method"
            )}
            required
          >
            <Select
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              value={rotation_method}
              onChange={(value) =>
                dispatch(setRotationMethod(value))
              }
              options={[
                {
                  value: "percentage",
                  label: t(
                    "scheduler.modal.form.rotationMethod.percentage",
                    "Percentage"
                  ),
                },
                {
                  value: "count",
                  label: t(
                    "scheduler.modal.form.rotationMethod.count",
                    "Count"
                  ),
                },
              ]}
            />
          </Form.Item>
        </div>
      </div>
    </div>
  );
};

const TimeOnZoneFilterForm = () => {
  return (
    <div>
      <SiteFilterGroup/>
      <UserFilterGroup/>
      <DeviceFilterGroup/>
    </div>
  );
};

const UnknownFilterForm = () => {
  const {t} = useTranslation();

  return (
    <div>
      <div className="text-gray-500 italic">
        {t(
          "scheduler.modal.form.unknownReportType",
          "Unknown report type selected."
        )}
      </div>
    </div>
  );
};

export default SchedulerFormFields;
