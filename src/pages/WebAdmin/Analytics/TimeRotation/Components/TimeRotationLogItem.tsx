import React, { useState } from "react";
import {Tag, Progress, Tooltip} from "antd";
import {
  FiRepeat,
  FiCalendar,
  FiBarChart2,
  FiChevronDown,
  FiChevronRight, FiTarget, FiClock, FiCheckCircle
} from "react-icons/fi";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useTranslation } from "react-i18next";
import { TimeRotationCheckpoint } from "../../../../../services/mainApi/admin/types/timeRotation.admin.mainApi.types";

dayjs.extend(relativeTime);

interface TimeRotationLogItemProps {
  timeRotationLog: TimeRotationCheckpoint;
}

const TimeRotationLogItem: React.FC<TimeRotationLogItemProps> = ({ timeRotationLog }) => {
  const { t } = useTranslation();
  const [expandedDayIndex, setExpandedDayIndex] = useState<number | null>(null);

  // Format minutes to a readable format
  const formatMinutes = (minutes: number): string => {
    if (minutes === undefined) return t("timeRotationLog.notAvailable", "N/A");

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (hours > 0) {
      return `${hours}h ${remainingMinutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  // Determine color based on percentage
  const getComplianceColor = (percentage: number): "success" | "exception" | "normal" | "active" => {
    if (percentage >= 90) return "success";
    if (percentage >= 70) return "normal";
    return "exception";
  };

  // Format time from the dateTime string
  const formatTime = (dateTimeStr: string): string => {
    return dayjs(dateTimeStr).format("HH:mm:ss");
  };

  // Toggle day expansion
  const toggleDayExpansion = (index: number) => {
    setExpandedDayIndex(expandedDayIndex === index ? null : index);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-0 p-4 mb-4">
      {/* Header - with gradient background */}
      <div className="mb-3 -mx-4 -mt-4 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 rounded-t-xl border-b border-gray-100 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
              {timeRotationLog.checkpoint_name}
            </h3>
          </div>
          <Tooltip title={t("timeRotationLog.interval", "Rotation Interval")}>
            <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
              <FiClock className="inline" />
              {formatMinutes(timeRotationLog.interval)}
            </span>
          </Tooltip>
        </div>
      </div>

      {/* Content - Compact Two Column Layout */}
      <div className="grid grid-cols-2 gap-3 px-1">
        {/* Left Column */}
        <div className="space-y-2">
          {/* Rotation Interval - compact */}
          <div className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
              <FiRepeat className="w-4 h-4" />
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("timeRotationLog.rotationInterval", "Rotation Interval")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {formatMinutes(timeRotationLog.interval)}
                </span>
              </div>
            </div>
          </div>

          {/* Method - compact */}
          <div className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
              <FiTarget className="w-4 h-4" />
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("timeRotationLog.method", "Method")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {timeRotationLog.method}
                </span>
                <Tag color="purple" className="ml-1">
                  {timeRotationLog.method}
                </Tag>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-2">
          {/* Interval Tag - compact */}
          <div className="group flex items-center gap-2 text-sm hover:bg-indigo-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex-shrink-0">
              <FiClock className="w-4 h-4" />
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("timeRotationLog.interval", "Interval")}:
                </span>
                <Tag color="blue">
                  <span className="flex items-center gap-1">
                    <FiRepeat className="text-blue-500" />
                    {formatMinutes(timeRotationLog.interval)}
                  </span>
                </Tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Rotations Data */}
      {timeRotationLog.rotations.length > 0 ? (
        <div className="mt-4 space-y-4">
          {timeRotationLog.rotations.map((day, index) => {
            const formattedPercentage = day.percentage.toFixed(2);
            const isExpanded = expandedDayIndex === index;

            return (
              <div key={`${day.date}-${index}`} className="border border-gray-100 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200">
                {/* Day Header - Always Visible */}
                <div
                  className="flex justify-between items-center p-3 cursor-pointer hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors duration-200"
                  onClick={() => toggleDayExpansion(index)}
                >
                  <div className="flex items-center gap-2">
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
                      {isExpanded ?
                        <FiChevronDown className="w-3 h-3" /> :
                        <FiChevronRight className="w-3 h-3" />
                      }
                    </div>
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex-shrink-0">
                      <FiCalendar className="w-3 h-3" />
                    </div>
                    <span className="font-medium text-gray-700 dark:text-gray-200">{dayjs(day.date).format("DD MMM YYYY")}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-sm">
                      <span className="text-gray-600 dark:text-gray-300">{day.completed}/{day.compliance}</span>
                    </span>
                    <div className="w-[120px]">
                      <Progress
                        percent={parseFloat(formattedPercentage)}
                        size="small"
                        status={getComplianceColor(day.percentage)}
                        format={(percent) => `${percent?.toFixed(2)}%`}
                      />
                    </div>
                  </div>
                </div>

                {/* Expanded Content */}
                {isExpanded && (
                  <div className="p-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-100 dark:border-gray-600">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div className="group flex items-center gap-2 text-sm hover:bg-purple-50 dark:hover:bg-gray-600 py-1 px-2 rounded-md transition-colors duration-200">
                        <div className="flex items-center justify-center w-7 h-7 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 flex-shrink-0">
                          <FiClock className="w-4 h-4" />
                        </div>
                        <div className="flex flex-col min-w-0">
                          <div className="flex items-center gap-1">
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {t("timeRotationLog.timeRange", "Time Range")}:
                            </span>
                            <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                              {formatTime(day.dateTimeRange.start)} - {formatTime(day.dateTimeRange.end)}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-600 py-1 px-2 rounded-md transition-colors duration-200">
                        <div className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
                          <FiBarChart2 className="w-4 h-4" />
                        </div>
                        <div className="flex flex-col min-w-0">
                          <div className="flex items-center gap-1">
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {t("timeRotationLog.completionRate", "Completion Rate")}:
                            </span>
                            <span className={`text-sm font-medium ${day.percentage >= 90 ? 'text-green-500 dark:text-green-400' : day.percentage >= 70 ? 'text-yellow-500 dark:text-yellow-400' : 'text-red-500 dark:text-red-400'}`}>
                              {formattedPercentage}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Interval Table */}
                    {day.intervals.length > 0 && (
                      <div className="mt-4">
                        <h4 className="font-medium mb-2 text-sm flex items-center gap-2 text-gray-700 dark:text-gray-200">
                          <div className="flex items-center justify-center w-5 h-5 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
                            <FiBarChart2 className="w-3 h-3" />
                          </div>
                          {t("timeRotationLog.intervals", "Hourly Intervals")}
                        </h4>
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                          {day.intervals.map((interval, i) => (
                            <div key={i} className={`relative bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm border ${interval.rotation > 0 ? 'border-green-500 dark:border-green-500 bg-green-50 dark:bg-green-900/20' : 'border-gray-100 dark:border-gray-700'} hover:shadow-md transition-all duration-200`}>
                              {interval.rotation > 0 && (
                                <div className="absolute top-0 left-0 right-0 h-1 bg-green-500 dark:bg-green-500 rounded-t-md"></div>
                              )}
                              <div className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-1 flex items-center gap-1">
                                <div className="flex items-center justify-center w-4 h-4 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
                                  <FiClock className="w-2 h-2" />
                                </div>
                                {interval.time}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400 mb-2 pl-5">
                                {formatTime(interval.dateTimeRange.start)} - {formatTime(interval.dateTimeRange.end)}
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-xs text-gray-600 dark:text-gray-300 flex items-center gap-1">
                                  <div className="flex items-center justify-center w-4 h-4 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
                                    <FiRepeat className="w-2 h-2" />
                                  </div>
                                  {t("timeRotationLog.rotations", "Rotations")}:
                                </span>
                                <div className="flex items-center gap-1">
                                  {interval.rotation > 0 && <FiCheckCircle className="text-green-500 dark:text-green-400" />}
                                  <span className={`text-sm font-medium ${interval.rotation > 0 ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}`}>{interval.rotation}</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <FiBarChart2 className="mx-auto text-gray-400 dark:text-gray-500 text-3xl mb-2" />
          <p>{t("timeRotationLog.noRotationData", "No rotation data available")}</p>
        </div>
      )}

      {/* Footer Section */}
      <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <div className="text-xs text-gray-500 dark:text-gray-400 flex flex-wrap gap-2">
            <span className="flex items-center gap-1">
              <div className="flex items-center justify-center w-4 h-4 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 flex-shrink-0">
                <FiClock className="w-2 h-2" />
              </div>
              {t("timeRotationLog.interval", "Interval")}: {formatMinutes(timeRotationLog.interval)}
            </span>
            <span>•</span>
            <span className="flex items-center gap-1">
              <div className="flex items-center justify-center w-4 h-4 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 flex-shrink-0">
                <FiTarget className="w-2 h-2" />
              </div>
              {t("timeRotationLog.method", "Method")}: {timeRotationLog.method}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimeRotationLogItem;
