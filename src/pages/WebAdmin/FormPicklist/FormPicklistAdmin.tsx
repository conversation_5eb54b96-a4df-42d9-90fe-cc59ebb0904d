import {Button, Input, Select} from "antd";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {PlusOutlined, SearchOutlined, SortAscendingOutlined, SortDescendingOutlined,} from "@ant-design/icons";
import {MdFormatListBulleted} from "react-icons/md";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import {useTranslation} from "react-i18next";
import {useSelector} from "react-redux";
import {RootState} from "../../../store";
import {useParams} from "react-router-dom";
import {useCallback, useEffect, useRef} from "react";
import PicklistList from "./Components/PicklistList";
import {
  actions,
  debouncedFetchFormPicklists,
  fetchFormPicklists
} from "../../../store/slices/admin/formPicklist.admin.slice";
import ModalAddFormPicklist from "./Components/ModalAddFormPicklist/ModalAddFormPicklist";
import {useAppDispatch} from "../../../store/hooks";
import addFormPicklistAdminSlice from "../../../store/slices/admin/addFormPicklist.admin.slice";

export default function FormPicklistAdmin() {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const {branchCode} = useParams<{
    branchCode: string;
  }>();

  const {filter, picklists, loading, pagination} = useSelector(
    (state: RootState) => state.formPicklistAdmin
  );

  const isFetching = useRef(false);

  // Create a fetch function that respects isFetching flag
  const fetch = useCallback(async () => {
    if (isFetching.current || !branchCode) return;
    isFetching.current = true;
    await dispatch(fetchFormPicklists(branchCode));
    isFetching.current = false;
  }, [branchCode, dispatch]);

  // Handle search input change with debounce
  const handleSearchChange = (value: string) => {
    dispatch(actions.setFilter({search: value}));
    if (branchCode) {
      debouncedFetchFormPicklists(dispatch, branchCode);
    }
  };

  // Handle order by change
  const handleOrderByChange = (value: string) => {
    dispatch(actions.setFilter({orderBy: value}));
  };

  // Handle order direction toggle
  const handleOrderDirectionToggle = useCallback(() => {
    const newDirection =
      filter.orderDirection === "ASC" ? "DESC" : "ASC";
    dispatch(
      actions.setFilter({orderDirection: newDirection})
    );
  }, [dispatch, filter.orderDirection]);

  // Handle pagination change
  const handlePageChange = (
    page: number,
    pageSize?: number
  ) => {
    dispatch(
      actions.setFilter({
        page,
        limit: pageSize || filter.limit,
      })
    );
    fetch();
  };

  // Fetch picklists on component mount and when filter changes
  useEffect(() => {
    fetch();
  }, [
    fetch,
    filter.orderBy,
    filter.orderDirection,
    filter.page,
    filter.limit,
  ]);


  return (
    <WebAdminLayout activePage="forms-picklist">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdFormatListBulleted/>}
            title={t("formPicklist.title")}
            description={t("formPicklist.description")}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined/>}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={() => {
                  dispatch(
                    addFormPicklistAdminSlice.actions.open()
                  );
                }}
              >
                {t("formPicklist.addButton")}
              </Button>,
            ]}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex gap-4 flex-wrap">
              <Input
                placeholder={t(
                  "formPicklist.search.placeholder"
                )}
                prefix={<SearchOutlined/>}
                size="large"
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                onChange={(e) =>
                  handleSearchChange(e.target.value)
                }
                value={filter.search || ""}
              />
              <div className="flex gap-2">
                <Select
                  value={filter.orderBy}
                  onChange={handleOrderByChange}
                  className="min-w-[160px] dark:bg-gray-700"
                  size="large"
                >
                  <Select.Option value="created_at">
                    {t(
                      "formPicklist.search.sortOptions.createdAt"
                    )}
                  </Select.Option>
                  <Select.Option value="form_picklist_name">
                    {t(
                      "formPicklist.search.sortOptions.name"
                    )}
                  </Select.Option>
                </Select>
                <Button
                  icon={
                    filter.orderDirection === "ASC" ? (
                      <SortAscendingOutlined/>
                    ) : (
                      <SortDescendingOutlined/>
                    )
                  }
                  size="large"
                  className="dark:border-gray-600 dark:text-gray-300"
                  onClick={handleOrderDirectionToggle}
                />
              </div>
            </div>
          </div>

          {/* Picklist List */}
          <PicklistList
            picklists={picklists}
            loading={loading}
            total={pagination.total}
            page={filter.page || 1}
            limit={filter.limit || 10}
            onPageChange={handlePageChange}
          />
        </div>
      </div>
      <ModalAddFormPicklist/>
    </WebAdminLayout>
  );
}
