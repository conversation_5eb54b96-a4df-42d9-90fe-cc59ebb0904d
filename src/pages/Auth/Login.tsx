import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Checkbox,
  Form,
  Input,
  Typography,
} from "antd";
import { useNavigate } from "react-router";
import { setTokens } from "../../utils/storage";
import authServiceMainApi from "../../services/mainApi/authService.mainApi.ts";
import { useTheme } from "../../context/ThemeContext";

const { Title } = Typography;

interface LoginForm {
  email: string;
  password: string;
  remember: boolean;
}

export default function Login() {
  const { isDark } = useTheme();
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(
    null
  );
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { message } = App.useApp();

  const onFinish = async (values: LoginForm) => {
    setLoading(true);
    setError(null);
    try {
      const response = await authServiceMainApi.login(
        values.email,
        values.password
      );
      const { data } = response;
      if (!data) {
        throw "Invalid credentials";
      }
      setTokens(data.access_token, data.access_token);

      const { user } = data;

      message.success("Login successful!");

      setTimeout(() => {
        if (user.system_access) {
          navigate("/sysadmin/dashboard");
        } else if (user.web_access) {
          navigate("/admin/branch");
        } else {
          navigate("/login");
        }
      }, 300);
    } catch (e: any) {
      const error = e;
      if (
        error.response?.data?.message ===
        "Invalid credentials"
      ) {
        setError("Invalid username or password");
        form.setFields([
          {
            name: "password",
            errors: [
              "The password you entered is incorrect",
            ],
          },
        ]);
      } else if (error.response?.data?.message) {
        setError(error.response.data.message);
      } else {
        setError(
          "An error occurred. Please try again later."
        );
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className={`min-h-screen ${isDark ? "bg-gray-900" : "bg-gray-50"} flex`}
    >
      {/* Form Section - Left Side */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="flex flex-col items-center">
            <img
              src="/logo.png"
              alt="Logo"
              className="h-12 w-12 mb-4"
            />
            <Title
              level={2}
              className={`text-center ${isDark ? "text-gray-100" : "text-gray-900"} mb-2`}
            >
              Uniguard Z
            </Title>
            <p
              className={`text-center ${isDark ? "text-gray-400" : "text-gray-600"}`}
            >
              Please log in to your account
            </p>
          </div>

          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              className="mb-6 rounded-lg"
              closable
              onClose={() => setError(null)}
            />
          )}

          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            onValuesChange={() => {
              if (error) {
                setError(null);
                form.setFields([
                  { name: "password", errors: [] },
                ]);
              }
            }}
            className="mt-8 space-y-6"
          >
            <Form.Item
              label={
                <span
                  className={`font-medium ${isDark ? "text-gray-300" : "text-gray-700"}`}
                >
                  Email
                </span>
              }
              name="email"
              rules={[
                {
                  required: true,
                  message: "Please enter your email!",
                },
                {
                  type: "email",
                  message:
                    "Please enter a valid email format!",
                },
              ]}
            >
              <Input
                size="large"
                placeholder="Enter your email"
                className={`rounded-lg ${isDark ? "bg-gray-800 border-gray-700 text-gray-100" : ""}`}
              />
            </Form.Item>

            <Form.Item
              label={
                <span
                  className={`font-medium ${isDark ? "text-gray-300" : "text-gray-700"}`}
                >
                  Password
                </span>
              }
              name="password"
              rules={[
                {
                  required: true,
                  message: "Please enter your password!",
                },
              ]}
            >
              <Input.Password
                size="large"
                placeholder="Enter your password"
                className={`rounded-lg ${isDark ? "bg-gray-800 border-gray-700 text-gray-100" : ""}`}
              />
            </Form.Item>

            <div className="flex items-center justify-between">
              <Form.Item
                name="remember"
                valuePropName="checked"
                noStyle
              >
                <Checkbox>
                  <span
                    className={`${isDark ? "text-gray-400" : "text-gray-600"}`}
                  >
                    Remember Me
                  </span>
                </Checkbox>
              </Form.Item>
              <a
                href="#"
                className={`text-sm text-blue-600 hover:text-blue-400 transition-colors ${isDark ? "text-blue-400 hover:text-blue-300" : ""}`}
              >
                Forgot Password?
              </a>
            </div>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                loading={loading}
                className="w-full h-12 rounded-lg font-medium text-lg border-0"
              >
                {loading ? "Processing..." : "Login"}
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>

      {/* Illustration Section - Right Side */}
      <div className="hidden lg:flex flex-1 relative">
        <img
          src="https://www.uniguard.com.au/wp-content/uploads/2024/05/Technological_Solutions_Security.webp"
          alt="Login illustration"
          className="absolute inset-0 w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-50"></div>
      </div>
    </div>
  );
}
