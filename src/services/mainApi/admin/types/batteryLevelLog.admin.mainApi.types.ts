import { BaseResponse } from "../../types/base.mainApi.types";
import { AdminCheckpoint } from "./checkpoint.admin.mainApi.types";
import { AdminBranch } from "./branch.admin.mainApi.types";
import { Timezone } from "../../types/timezone.mainApi.types";
import { AdminDevice } from "./device.admin.mainApi.types";

export interface AdminBatteryLevelLogEntry {
  id: string;
  uuid: string;
  parent_branch_id: string;
  branch_id: string;
  branch_name: string;
  timezone_id: string;
  timezone_name: string;
  checkpoint_id: string;
  checkpoint_name: string;
  device_id: string;
  device_name: string;
  voltage: number;
  original_submitted_time: string;
  event_time: string;
  
  // Relations (if needed)
  checkpoint?: AdminCheckpoint;
  device?: AdminDevice;
  branch?: AdminBranch;
  parent_branch?: AdminBranch;
  timezone?: Timezone;
}

export interface AdminBatteryLevelLogListParams {
  page?: number;
  limit?: number;
  start_date?: string;
  end_date?: string;
  branch_id?: string | number;
  site_id?: string | number;
  site_labels?: string | number | (string | number)[];
  checkpoint_id?: string | number;
  checkpoint_labels?: string | number | (string | number)[];
  order_by?: string;
  order_direction?: "ASC" | "DESC";
}

export type AdminBatteryLevelLogListResponse = BaseResponse<
  AdminBatteryLevelLogEntry[]
>; 