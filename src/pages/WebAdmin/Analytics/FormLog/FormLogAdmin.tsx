import { useTranslation } from "react-i18next";
import {
  <PERSON>d<PERSON><PERSON><PERSON><PERSON><PERSON>,
  MdDescription,
} from "react-icons/md";
import {useEffect, useRef, useState} from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import ActiveFilters, {
  createEndDateFilter,
  createEndTimeFilter,
  createLabelFilter,
  createSimpleFilter,
  createStartDateFilter,
  createStartTimeFilter,
  FilterGroup,
} from "../../../../components/Admin/Common/ActiveFilters";
import { <PERSON><PERSON>, Drawer } from "antd";
import AnalyticDrawerForm, {
  AnalyticsFilterState,
} from "../../Analytics/Components/AnalyticDrawerForm";
import { EReportType } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { AdminRole } from "../../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminUser } from "../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminLabel } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminBranch } from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks";
import FormLogList from "./Components/FormLogList";
import formLogAdminSlice, {
  fetchFormLogs,
} from "../../../../store/slices/admin/formLog.admin.slice";
import { useParams } from "react-router-dom";
import { AdminForm } from "../../../../services/mainApi/admin/types/form.admin.mainApi.types";
import FormLogExportButton from "./Components/FormLogExportButton";

interface FilterState {
  role: null | AdminRole;
  user: null | AdminUser;
  userLabels: AdminLabel[];
  device: null | AdminDevice;
  deviceLabels: AdminLabel[];
  form: null | AdminForm;
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  selectedBranch: AdminBranch | null;
}

const FormLogAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const isFetching = useRef(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  // Get branch code from URL params
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  // Redux state
  const formLogState = useAppSelector(
    (state) => state.formLogAdmin
  );
  const loading = formLogState.loading;
  const pagination = formLogState.pagination;
  const formLogs = formLogState.formLogs;
  const selectedRole = formLogState.selectedRole;
  const selectedUser = formLogState.selectedUser;
  const selectedDevice = formLogState.selectedDevice;
  const selectedForm = formLogState.selectedForm;
  const selectedBranch = formLogState.selectedBranch;
  const selectedUserLabels =
    formLogState.selectedUserLabels;
  const selectedDeviceLabels =
    formLogState.selectedDeviceLabels;
  const filterState = formLogState.filter;

  // Load data on mount and when filters change
  useEffect(() => {
    if (branchCode && !isFetching.current) {
      isFetching.current = true;
      dispatch(fetchFormLogs({ branchCode }))
        .then(() => {
          isFetching.current = false;
        })
        .catch(() => {
          isFetching.current = false;
        });
    }
  }, [
    dispatch,
    branchCode,
    filterState.page,
    filterState.limit,
    filterState.startDate,
    filterState.endDate,
    filterState.startTime,
    filterState.endTime,
    filterState.branchId,
    filterState.roleId,
    filterState.userId,
    filterState.userLabels,
    filterState.deviceId,
    filterState.deviceLabels,
    filterState.formId,
    filterState.orderBy,
    filterState.orderDirection,
  ]);

  const { actions } = formLogAdminSlice;

  // Convert applied filter state to format expected by drawer
  const getAppliedFilters = (): FilterState => {
    return {
      role: selectedRole,
      user: selectedUser,
      userLabels: selectedUserLabels || [],
      device: selectedDevice,
      deviceLabels: selectedDeviceLabels || [],
      form: selectedForm,
      startDate: filterState.startDate,
      endDate: filterState.endDate,
      startTime: filterState.startTime,
      endTime: filterState.endTime,
      selectedBranch: selectedBranch,
    };
  };

  // Apply filters from the drawer
  const handleApplyFilters = (
    newFilters: AnalyticsFilterState
  ) => {
    // Update redux state
    dispatch(
      actions.setSelectedRole(newFilters.role || null)
    );
    dispatch(
      actions.setSelectedUser(newFilters.user || null)
    );
    dispatch(
      actions.setSelectedUserLabels(
        newFilters.userLabels || []
      )
    );
    dispatch(
      actions.setSelectedDevice(newFilters.device || null)
    );
    dispatch(
      actions.setSelectedDeviceLabels(
        newFilters.deviceLabels || []
      )
    );
    dispatch(
      actions.setSelectedForm(newFilters.forms || null)
    );
    dispatch(
      actions.setSelectedBranch(
        newFilters.selectedBranch || null
      )
    );
    dispatch(
      actions.setStartDate(newFilters.startDate || null)
    );
    dispatch(
      actions.setEndDate(newFilters.endDate || null)
    );
    dispatch(
      actions.setStartTime(newFilters.startTime || null)
    );
    dispatch(
      actions.setEndTime(newFilters.endTime || null)
    );

    // Reset page to 1
    dispatch(actions.setPage(1));

    setDrawerVisible(false);
  };

  // Handle page change
  const handlePageChange = (
    page: number,
    pageSize?: number
  ) => {
    if (pageSize && pageSize !== filterState.limit) {
      dispatch(actions.setLimit(pageSize));
    }
    dispatch(actions.setPage(page));
  };

  // Check if there are any active filters
  const hasActiveFilters = (): boolean => {
    // Check if any filter is active
    return !!(
      filterState.startDate ||
      filterState.endDate ||
      filterState.startTime ||
      filterState.endTime ||
      filterState.branchId ||
      filterState.roleId ||
      filterState.userId ||
      filterState.userLabels.length > 0 ||
      filterState.deviceId ||
      filterState.deviceLabels.length > 0 ||
      filterState.formId
    );
  };

  // Get filter groups configuration for ActiveFilters component
  const getFilterGroups = (): FilterGroup[] => {
    const appliedFilters = getAppliedFilters();

    return [
      {
        key: "datetime",
        filters: [
          createStartDateFilter(appliedFilters.startDate),
          createEndDateFilter(appliedFilters.endDate),
          createStartTimeFilter(appliedFilters.startTime),
          createEndTimeFilter(appliedFilters.endTime),
        ],
      },
      {
        key: "user",
        filters: [
          createSimpleFilter(
            "role",
            "role",
            appliedFilters.role,
            "role_name"
          ),
          createSimpleFilter(
            "user",
            "user",
            appliedFilters.user,
            "name"
          ),
          createLabelFilter(
            "userLabels",
            "userLabels",
            appliedFilters.userLabels
          ),
        ],
      },
      {
        key: "device",
        filters: [
          createSimpleFilter(
            "device",
            "device",
            appliedFilters.device,
            "device_name"
          ),
          createLabelFilter(
            "deviceLabels",
            "deviceLabels",
            appliedFilters.deviceLabels
          ),
        ],
      },
      {
        key: "other",
        filters: [
          createSimpleFilter(
            "form",
            "form",
            appliedFilters.form,
            "form_name"
          ),
          createSimpleFilter(
            "selectedBranch",
            "selectedBranch",
            appliedFilters.selectedBranch,
            "branch_name"
          ),
        ],
      },
    ];
  };

  const handleClearAll = () => {
    dispatch(actions.clearFilters());
  };

  const handleRemoveFilter = (
    filterKey: keyof FilterState
  ) => {
    switch (filterKey) {
      case "role":
        dispatch(actions.setSelectedRole(null));
        break;
      case "user":
        dispatch(actions.setSelectedUser(null));
        break;
      case "userLabels":
        dispatch(actions.setSelectedUserLabels([]));
        break;
      case "device":
        dispatch(actions.setSelectedDevice(null));
        break;
      case "deviceLabels":
        dispatch(actions.setSelectedDeviceLabels([]));
        break;
      case "form":
        dispatch(actions.setSelectedForm(null));
        break;
      case "selectedBranch":
        dispatch(actions.setSelectedBranch(null));
        break;
      case "startDate":
        dispatch(actions.setStartDate(null));
        break;
      case "endDate":
        dispatch(actions.setEndDate(null));
        break;
      case "startTime":
        dispatch(actions.setStartTime(null));
        break;
      case "endTime":
        dispatch(actions.setEndTime(null));
        break;
    }
  };

  // Handle clicking on an active filter to edit it
  const handleEditFilter = () => {
    // We only need to open the drawer
    setDrawerVisible(true);
  };

  // Convert FilterState to AnalyticsFilterState for the drawer form
  const getInitialDrawerValues =
    (): Partial<AnalyticsFilterState> => {
      const appliedFilters = getAppliedFilters();

      return {
        startDate: appliedFilters.startDate || "",
        endDate: appliedFilters.endDate || "",
        startTime: appliedFilters.startTime || "",
        endTime: appliedFilters.endTime || "",
        selectedBranch:
          appliedFilters.selectedBranch || null,
        role: appliedFilters.role,
        user: appliedFilters.user,
        userLabels: appliedFilters.userLabels,
        device: appliedFilters.device,
        deviceLabels: appliedFilters.deviceLabels,
        forms: appliedFilters.form,
      };
    };

  // Create a reusable function to get filter info
  const getFilterInfo = () => ({
    role: formLogState.selectedRole || null,
    user: formLogState.selectedUser || null,
    userLabels: formLogState.selectedUserLabels || [],
    device: formLogState.selectedDevice || null,
    deviceLabels: formLogState.selectedDeviceLabels || [],
    form: formLogState.selectedForm || null,
    startDate: formLogState.filter.startDate || null,
    endDate: formLogState.filter.endDate || null,
    startTime: formLogState.filter.startTime || null,
    endTime: formLogState.filter.endTime || null,
    selectedBranch: formLogState.selectedBranch || null,
    branchName: formLogState.selectedBranch?.branch_name,
  });

  return (
    <WebAdminLayout activePage="analytics-forms-log">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdDescription />}
            title={t("formLog.title", "Forms Log")}
            description={t(
              "formLog.description",
              "View and manage form logs"
            )}
            actions={[
              // Desktop view - show all buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <FormLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                />
                <Button
                  key="filter"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button and dropdown for downloads
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <FormLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                  buttonText={t(
                    "common.download",
                    "Download"
                  )}
                />
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList />}
                  size="large"
                  type="primary"
                  className="bg-indigo-600 hover:bg-indigo-700 border-0 shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters using the reusable component */}
          <ActiveFilters<FilterState>
            filterGroups={getFilterGroups()}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAll}
            onShowFilters={() => {
              setDrawerVisible(true);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Results List */}
          <FormLogList
            formLogs={formLogs}
            loading={loading}
            total={pagination.total || 0}
            page={pagination.page || 1}
            limit={pagination.limit || 10}
            onPageChange={handlePageChange}
          />
        </div>

        {/* Filter Drawer - Using the refactored component with initialValues */}
        <Drawer
          title={
            <div className="text-lg font-semibold text-gray-800 dark:text-white">
              {t(
                "formLog.filters.title",
                "Filter Form Log"
              )}
            </div>
          }
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          className="dark:bg-gray-800"
        >
          <AnalyticDrawerForm
            reportType={EReportType.FORMS}
            onApply={handleApplyFilters}
            onClose={() => setDrawerVisible(false)}
            initialValues={getInitialDrawerValues()}
          />
        </Drawer>
      </div>
    </WebAdminLayout>
  );
};

export default FormLogAdmin;
