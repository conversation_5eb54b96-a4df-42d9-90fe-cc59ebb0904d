import { Empty, Pagination, Spin, Tag } from "antd";
import { RolesGridProps } from "./types";
import { useTranslation } from "react-i18next";
import { MdEdit, MdRemoveRedEye } from "react-icons/md";
import dayjs from "dayjs";
import { AdminRole } from "../../../../../services/mainApi/admin/types/roles.admin.mainApi.types.ts";

const RolesGrid = ({
  roles,
  loading,
  pagination,
  onViewRole,
  onPageChange,
  onEditRole,
}: RolesGridProps) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Spin size="large" />
      </div>
    );
  }

  if (!roles.length) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Empty
          description={
            <span className="text-gray-500 dark:text-gray-400">
              {t("roles.grid.empty", "No roles found")}
            </span>
          }
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Roles Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {roles.map((role: AdminRole) => (
          <div
            key={role.id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow group"
          >
            {/* Role Content */}
            <div className="flex flex-col h-full">
              <div className="flex-grow">
                <div className="flex items-center justify-between mb-1">
                  <h3
                    className="text-base font-medium text-gray-900 dark:text-gray-100 truncate"
                    title={role.role_name}
                  >
                    {role.role_name}
                  </h3>
                  <Tag color={role.active ? "success" : "error"} className="ml-1">
                    {role.active
                      ? t("roles.modal.status.active")
                      : t("roles.modal.status.inactive")}
                  </Tag>
                </div>
                {role.super_admin && (
                  <div className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full inline-block dark:bg-blue-900 dark:text-blue-200 mb-2">
                    {t("roles.modal.superAdmin", "Super Admin")}
                  </div>
                )}
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  {dayjs(role.created_at).format("DD MMM YYYY")}
                </p>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-1 pt-2 mt-2 border-t border-gray-100 dark:border-gray-700">
                {!role.super_admin && (
                  <button
                    onClick={() => onEditRole(role)}
                    className="p-1.5 text-blue-600 hover:bg-blue-50 rounded-md dark:text-blue-400 dark:hover:bg-blue-900/20"
                    title={t("roles.table.editTooltip")}
                  >
                    <MdEdit />
                  </button>
                )}
                <button
                  onClick={() => onViewRole(role)}
                  className="p-1.5 text-green-600 hover:bg-green-50 rounded-md dark:text-green-400 dark:hover:bg-green-900/20"
                  title={t("roles.table.viewTooltip")}
                >
                  <MdRemoveRedEye />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3">
        <Pagination
          current={pagination.current}
          total={pagination.total}
          pageSize={pagination.pageSize}
          onChange={onPageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              {t("common.pagination.total", { total })}
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

RolesGrid.displayName = "RolesGrid";

export default RolesGrid;
