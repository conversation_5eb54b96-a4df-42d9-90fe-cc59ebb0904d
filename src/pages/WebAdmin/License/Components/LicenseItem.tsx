import React, {useState} from "react";
import {Badge, Tag} from "antd";
import {FiChevronDown, FiChevronUp, FiClock, FiInfo} from "react-icons/fi";
import {AppstoreOutlined} from "@ant-design/icons";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import {useTranslation} from "react-i18next";
import {AdminLicense} from "../../../../services/mainApi/admin/types/license.admin.mainApi.types";

dayjs.extend(relativeTime);

interface LicenseItemProps {
  license: AdminLicense;
}

const LicenseItem: React.FC<LicenseItemProps> = ({
                                                   license,
                                                 }) => {
  const {t} = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(prev => !prev);
  };

  return (
    <div
      key={license.id}
      className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border ${
        license.active
          ? "border-gray-100 dark:border-gray-700"
          : "border-red-100 dark:border-red-900 bg-red-50/10"
      } overflow-hidden`}
    >
      <div className="p-4">
        {/* License Name and Status */}
        <div className="flex items-start justify-between mb-3 pb-3 border-b dark:border-gray-700">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">
                {license.license_name}
              </h3>
              <Badge
                status={license.active ? "success" : "error"}
                text={license.active ? t("common.active", "Active") : t("common.inactive", "Inactive")}
                className={`
                  ${license.active ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}
                  text-xs font-medium
                `}
              />
            </div>
          </div>
        </div>

        {/* License Type Indicator */}
        <div className="mb-3">
          <Tag
            icon={<AppstoreOutlined className="mr-1"/>}
            className={`
              bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400
              border-blue-100 dark:border-blue-800
              px-3 py-1 rounded-full text-xs font-medium
            `}
          >
            {t("license.type", "License Package")}
          </Tag>
        </div>

        {/* Key Metrics */}
        <div className="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg mb-4">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            <div className="text-sm">
              <span className="text-gray-600 dark:text-gray-300">
                {t("license.maxUser", "Max Users")}:
              </span>{" "}
              <span className="font-medium">
                {license.max_user}
              </span>
            </div>
            <div className="text-sm">
              <span className="text-gray-600 dark:text-gray-300">
                {t("license.maxSubbranch", "Max Subbranch")}:
              </span>{" "}
              <span className="font-medium">
                {license.max_subbranch}
              </span>
            </div>
            <div className="text-sm">
              <span className="text-gray-600 dark:text-gray-300">
                {t("license.maxLabel", "Max Labels")}:
              </span>{" "}
              <span className="font-medium">
                {license.max_label}
              </span>
            </div>
            <div className="text-sm">
              <span className="text-gray-600 dark:text-gray-300">
                {t("license.maxBeacon", "Max Beacons")}:
              </span>{" "}
              <span className="font-medium">
                {license.max_beacon}
              </span>
            </div>
          </div>
        </div>

        {/* Show More Button */}
        <div
          className={`
            flex items-center justify-center cursor-pointer py-2 mb-2
            ${isExpanded ? 'text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300' : 'text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300'}
            font-medium text-sm transition-all duration-300
            hover:bg-gray-50 dark:hover:bg-gray-750 rounded-md
          `}
          onClick={toggleExpand}
        >
          <span
            className="mr-2">{isExpanded ? t("common.showLess", "Show Less") : t("common.showMore", "Show More Details")}</span>
          <span className="transition-transform duration-300 ease-in-out transform">
            {isExpanded ? <FiChevronUp className="animate-pulse"/> : <FiChevronDown className="animate-bounce-subtle"/>}
          </span>
        </div>

        {/* Collapsible Details */}
        {isExpanded && (
          <div className="animate-fadeIn">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Limitations */}
              <div className="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                <h4 className="text-xs uppercase text-gray-500 dark:text-gray-400 mb-2 font-medium flex items-center">
                  <FiInfo className="mr-1 text-blue-500"/>
                  {t("license.limitations", "Limitations")}
                </h4>
                <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                  <div className="text-sm">
                    <span className="text-gray-600 dark:text-gray-300">
                      {t("license.maxScheduler", "Max Scheduler")}:
                    </span>{" "}
                    <span className="font-medium">
                      {license.max_scheduler}
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-600 dark:text-gray-300">
                      {t("license.maxAlert", "Max Alerts")}:
                    </span>{" "}
                    <span className="font-medium">
                      {license.max_alert}
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-600 dark:text-gray-300">
                      {t("license.maxDevice", "Max Devices")}:
                    </span>{" "}
                    <span className="font-medium">
                      {license.max_device}
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-600 dark:text-gray-300">
                      {t("license.maxSite", "Max Sites")}:
                    </span>{" "}
                    <span className="font-medium">
                      {license.max_site}
                    </span>
                  </div>
                </div>
              </div>

              {/* Additional Limits */}
              <div className="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                <h4 className="text-xs uppercase text-gray-500 dark:text-gray-400 mb-2 font-medium flex items-center">
                  <FiInfo className="mr-1 text-blue-500"/>
                  {t("license.additionalLimits", "Additional Limits")}
                </h4>
                <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                  <div className="text-sm">
                    <span className="text-gray-600 dark:text-gray-300">
                      {t("license.maxCheckpoint", "Max Checkpoint")}:
                    </span>{" "}
                    <span className="font-medium">
                      {license.max_checkpoint || 0}
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-600 dark:text-gray-300">
                      {t("license.maxGeofence", "Max Geofence")}:
                    </span>{" "}
                    <span className="font-medium">
                      {license.max_geofence || 0}
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-600 dark:text-gray-300">
                      {t("license.maxForm", "Max Form")}:
                    </span>{" "}
                    <span className="font-medium">
                      {license.max_form || 0}
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-600 dark:text-gray-300">
                      {t("license.maxTask", "Max Task")}:
                    </span>{" "}
                    <span className="font-medium">
                      {license.max_task || 0}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer with timestamps */}
        <div
          className="flex flex-wrap items-center gap-3 text-xs text-gray-500 dark:text-gray-400 pt-3 mt-3 border-t dark:border-gray-700">
          <div className="flex items-center gap-1">
            <FiClock className="text-xs"/>
            <span>
              {t("common.createdAt", "Created")}:{" "}
              {dayjs(license.created_at).fromNow()}
            </span>
          </div>
          {license.updated_at && (
            <div className="flex items-center gap-1">
              <FiClock className="text-xs"/>
              <span>
                {t("common.updatedAt", "Updated")}:{" "}
                {dayjs(license.updated_at).fromNow()}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LicenseItem;
