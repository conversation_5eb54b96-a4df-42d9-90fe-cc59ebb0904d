import {Avatar, List, Spin} from "antd";
import {UserOutlined} from "@ant-design/icons";
import {useTranslation} from "react-i18next";
import {convertToLocalTimezone} from "../../../../../utils/dateUtils.ts";

// Define the interface for checkpoint activity data
interface CheckpointActivity {
  id: string;
  userName: string;
  eventId: string;
  eventName: string;
  eventSubmittedTime: string;
  latitude: number | null;
  longitude: number | null;
}

interface ActivityListProps {
  activities: CheckpointActivity[];
  loading: boolean;
  onActivityClick: (id: string) => void;
}

const ActivityList: React.FC<ActivityListProps> = ({
                                                     activities,
                                                     loading,
                                                     onActivityClick,
                                                   }) => {
  const {t} = useTranslation();

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div className="p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
            {t("dashboard.activity.title")}
          </h3>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {t("dashboard.activity.totalUsers", {
              count: activities.length,
            })}
          </span>
        </div>
      </div>

      <div className="p-2">
        {loading && (
          <div className="flex justify-center items-center py-4">
            <Spin size="small"/>
            <span className="ml-2 text-sm text-gray-500">Loading activities...</span>
          </div>
        )}

        {!loading && activities.length === 0 && (
          <div className="text-center py-3 text-sm text-gray-500 dark:text-gray-400">
            No activities found
          </div>
        )}

        {!loading && activities.length > 0 && (
          <List
            itemLayout="horizontal"
            dataSource={activities}
            className="user-activity-list"
            size="small"
            renderItem={(item) => (
              <List.Item
                className="rounded-md px-2 py-1.5 mb-1.5 cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                onClick={() => onActivityClick(item.id)}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      size="small"
                      icon={<UserOutlined/>}
                      className="bg-blue-500"
                    />
                  }
                  title={
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100 line-clamp-1">
                      {item.userName}
                    </span>
                  }
                  description={
                    <div className="text-xs text-gray-500 dark:text-gray-400 space-y-0.5">
                      <div className="line-clamp-1">{item.eventName}</div>
                      <div
                        className="text-xs text-gray-400 dark:text-gray-500">{convertToLocalTimezone(item.eventSubmittedTime)?.format("DD MMM YYYY HH:mm")}</div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </div>
    </div>
  );
};

export default ActivityList;
