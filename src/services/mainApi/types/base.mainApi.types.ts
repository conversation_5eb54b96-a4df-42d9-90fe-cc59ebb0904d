export interface ValidationErrorDetail {
  field_name: string;
  messages: string[];
}

export interface BaseError {
  code: string | number;
  details?: ValidationErrorDetail[] | string;
}

export interface BasePaginationMeta {
  total?: number;
  page?: number;
  limit?: number;
  total_pages?: number;
}

export interface BaseResponse<
  T,
  M extends BasePaginationMeta = BasePaginationMeta,
> {
  success: boolean;
  message: string;
  data: T | null;
  meta: {
    timestamp: string;
    path: string;
  } & M;
  error: BaseError | null;
}

export interface BaseGetRequest {
  page?: number;
  limit?: number;
  search?: string;
  order_by?: string;
  order_direction?: "ASC" | "DESC";
  ignore_active_status?: boolean;
}
