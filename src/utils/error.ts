import {
  BaseError,
  ValidationErrorDetail,
} from "../services/mainApi/types/base.mainApi.types";
import { AxiosError } from "axios";

function isValidationError(
  error: BaseError
): error is BaseError & {
  details: ValidationErrorDetail[];
} {
  return (
    Array.isArray(error.details) &&
    error.details.length > 0 &&
    "field_name" in error.details[0]
  );
}

export function handleApiError(error: AxiosError): {
  errors: string | string[];
} {
  const data: any = error?.response?.data;
  if (data.error) {
    const apiError = data.error;

    // Handle validation errors (422)
    if (isValidationError(apiError)) {
      return {
        errors: apiError.details
          .map((detail) => detail.messages)
          .flat(),
      };
    }

    // Handle regular errors (404, 500, etc)
    if (typeof apiError.details === "string") {
      return { errors: apiError.details };
    }

    return {
      errors: apiError.message || "An error occurred",
    };
  }

  return { errors: "An unexpected error occurred" };
}
