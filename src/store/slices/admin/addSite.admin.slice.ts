import {
  createSlice,
  PayloadAction,
  createAsyncThunk,
} from "@reduxjs/toolkit";
import { AdminZone } from "../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { v4 as uuidv4 } from "uuid";
import zoneAdminMainApi from "../../../services/mainApi/admin/zone.admin.mainApi";
import { fetchSitesAdmin } from "./site.admin.slice";
import { RootState } from "../..";
import axios from "axios";
import { handleApiError } from "../../../utils/error";

interface State {
  isOpen: boolean;
  mode: "create" | "edit";
  selectedSite: AdminZone | null;
  loading: boolean;
  error: string | string[] | null;

  // Form fields
  siteName: string;
  siteDescription: string;
  siteAddress: string;
  siteLatitude: number;
  siteLongitude: number;
  siteTimezone: string;
  siteIntervalActive: boolean;
  siteIntervalStartTime: string;
  siteIntervalEndTime: string;
  siteSubject: string | null;
  siteRecipients: {
    rowId: string;
    recipient_type: string;
    recipient_contact: string;
  }[];
  siteLabels: string[];
}

const initialState: State = {
  isOpen: false,
  mode: "create",
  selectedSite: null,
  loading: false,
  error: null,

  siteName: "",
  siteDescription: "",
  siteAddress: "",
  siteLatitude: 0,
  siteLongitude: 0,
  siteTimezone: "",
  siteIntervalActive: false,
  siteIntervalStartTime: "",
  siteIntervalEndTime: "",
  siteSubject: "",
  siteRecipients: [],
  siteLabels: [],
};

// Helper function to convert state to API payload
const stateToPayload = (state: State) => {
  return {
    zone_name: state.siteName,
    zone_description: state.siteDescription,
    zone_address: state.siteAddress,
    latitude: state.siteLatitude,
    longitude: state.siteLongitude,
    timezone_id: state.siteTimezone,
    interval_active: state.siteIntervalActive,
    interval_start_time: state.siteIntervalActive
      ? state.siteIntervalStartTime
      : undefined,
    interval_end_time: state.siteIntervalActive
      ? state.siteIntervalEndTime
      : undefined,
    subject: state.siteSubject || undefined,
    recipients: state.siteRecipients.map((recipient) => ({
      recipient_type: recipient.recipient_type,
      recipient_contact: recipient.recipient_contact,
    })),
    labels: state.siteLabels,
  };
};

// Create site async thunk
export const createSite = createAsyncThunk(
  "addSiteAdmin/createSite",
  async (
    { branchCode }: { branchCode: string },
    { getState, dispatch, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const payload = stateToPayload(state.addSiteAdmin);

      const response = await zoneAdminMainApi.createZone(
        branchCode,
        {
          zone_name: payload.zone_name,
          zone_description: payload.zone_description,
          zone_address: payload.zone_address,
          latitude: payload.latitude,
          longitude: payload.longitude,
          timezone_id: payload.timezone_id,
          interval_active: payload.interval_active,
          interval_start_time:
            payload.interval_start_time || "",
          interval_end_time:
            payload.interval_end_time || "",
          subject: payload.subject || "",
          recipients: payload.recipients,
          label_ids: payload.labels,
        }
      );

      // Refresh site list after successful creation
      dispatch(fetchSitesAdmin(branchCode));

      return response;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          handleApiError(error).errors
        );
      }
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue(
        "Terjadi kesalahan saat membuat site"
      );
    }
  }
);

// Update site async thunk
export const updateSite = createAsyncThunk(
  "addSiteAdmin/updateSite",
  async (
    {
      branchCode,
      zoneId,
    }: {
      branchCode: string;
      zoneId: string | number;
    },
    { getState, dispatch, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const payload = stateToPayload(state.addSiteAdmin);
      const response = await zoneAdminMainApi.updateZone(
        branchCode,
        zoneId,
        {
          zone_name: payload.zone_name,
          zone_description: payload.zone_description,
          zone_address: payload.zone_address,
          latitude: payload.latitude,
          longitude: payload.longitude,
          timezone_id: payload.timezone_id,
          interval_active: payload.interval_active,
          interval_start_time:
            payload.interval_start_time || "",
          interval_end_time:
            payload.interval_end_time || "",
          subject: payload.subject || "",
          recipients: payload.recipients,
          label_ids: payload.labels,
        }
      );

      // Refresh site list after successful update
      dispatch(fetchSitesAdmin(branchCode));

      return response;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          handleApiError(error).errors
        );
      }
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue(
        "Terjadi kesalahan saat mengupdate site"
      );
    }
  }
);

const addSiteAdminSlice = createSlice({
  name: "addSiteAdmin",
  initialState,
  reducers: {
    openCreate: (state) => {
      state.isOpen = true;
      state.mode = "create";
      state.selectedSite = null;
      state.siteName = "";
      state.siteDescription = "";
      state.siteAddress = "";
      state.siteLatitude = 0;
      state.siteLongitude = 0;
      state.siteTimezone = "";
      state.siteIntervalActive = false;
      state.siteIntervalStartTime = "";
      state.siteIntervalEndTime = "";
      state.siteSubject = "";
      state.siteRecipients = [];
      state.siteLabels = [];
      state.error = null;
    },
    openEdit: (state, action: PayloadAction<AdminZone>) => {
      state.isOpen = true;
      state.mode = "edit";
      state.selectedSite = action.payload;
      state.siteName = action.payload.zone_name;
      state.siteDescription =
        action.payload.zone_description || "";
      state.siteAddress = action.payload.zone_address;
      state.siteLatitude = action.payload.latitude;
      state.siteLongitude = action.payload.longitude;
      state.siteTimezone = action.payload.timezone_id;
      state.siteIntervalActive =
        action.payload.interval_active || false;
      state.siteIntervalStartTime =
        action.payload.interval_start_time || "";
      state.siteIntervalEndTime =
        action.payload.interval_end_time || "";
      state.siteSubject = action.payload.subject || "";
      state.siteRecipients = action.payload.recipients.map(
        (recipient) => ({
          rowId: uuidv4(),
          recipient_type: recipient.recipient_type,
          recipient_contact: recipient.recipient_contact,
        })
      );
      state.siteLabels = action.payload.labels.map(
        (label) => label.id
      );
      state.error = null;
    },
    setSiteName: (state, action: PayloadAction<string>) => {
      state.siteName = action.payload;
    },
    setSiteDescription: (
      state,
      action: PayloadAction<string>
    ) => {
      state.siteDescription = action.payload;
    },
    setSiteAddress: (
      state,
      action: PayloadAction<string>
    ) => {
      state.siteAddress = action.payload;
    },
    setSiteLatitude: (
      state,
      action: PayloadAction<number>
    ) => {
      state.siteLatitude = action.payload;
    },
    setSiteLongitude: (
      state,
      action: PayloadAction<number>
    ) => {
      state.siteLongitude = action.payload;
    },
    setSiteTimezone: (
      state,
      action: PayloadAction<string>
    ) => {
      state.siteTimezone = action.payload;
    },
    setSiteIntervalActive: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.siteIntervalActive = action.payload;
    },
    setSiteIntervalStartTime: (
      state,
      action: PayloadAction<string>
    ) => {
      state.siteIntervalStartTime = action.payload;
    },
    setSiteIntervalEndTime: (
      state,
      action: PayloadAction<string>
    ) => {
      state.siteIntervalEndTime = action.payload;
    },
    setSiteSubject: (
      state,
      action: PayloadAction<string>
    ) => {
      state.siteSubject = action.payload;
    },
    addRecipient: (state) => {
      const id = uuidv4();
      state.siteRecipients.push({
        rowId: id,
        recipient_type: "EMAIL",
        recipient_contact: "",
      });
    },
    removeRecipient: (
      state,
      action: PayloadAction<string>
    ) => {
      state.siteRecipients = state.siteRecipients.filter(
        (recipient) => recipient.rowId !== action.payload
      );
    },
    setRecipientType: (
      state,
      action: PayloadAction<{
        rowId: string;
        recipient_type: string;
      }>
    ) => {
      const { rowId, recipient_type } = action.payload;
      const recipient = state.siteRecipients.find(
        (recipient) => recipient.rowId === rowId
      );
      if (recipient) {
        recipient.recipient_type = recipient_type;
        recipient.recipient_contact = "";
      }
    },
    setRecipientContact: (
      state,
      action: PayloadAction<{
        rowId: string;
        recipient_contact: string;
      }>
    ) => {
      const { rowId, recipient_contact } = action.payload;
      const recipient = state.siteRecipients.find(
        (recipient) => recipient.rowId === rowId
      );
      if (recipient) {
        recipient.recipient_contact = recipient_contact;
      }
    },
    setSiteLabels: (
      state,
      action: PayloadAction<string[]>
    ) => {
      state.siteLabels = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    close: (state) => {
      state.isOpen = false;
      state.selectedSite = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle createSite
      .addCase(createSite.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSite.fulfilled, (state) => {
        state.loading = false;
        state.isOpen = false;
      })
      .addCase(createSite.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string | string[];
      })

      // Handle updateSite
      .addCase(updateSite.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSite.fulfilled, (state) => {
        state.loading = false;
        state.isOpen = false;
      })
      .addCase(updateSite.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string | string[];
      });
  },
});

export default addSiteAdminSlice;
