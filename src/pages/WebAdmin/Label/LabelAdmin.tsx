import { Button, Input, Select } from "antd";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {
  PlusOutlined,
  SearchOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
} from "@ant-design/icons";
import { MdLabel } from "react-icons/md";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks";
import labelAdminSlice, {
  fetchLabelsAdmin,
} from "../../../store/slices/admin/label.admin.slice";
import { useCallback, useRef } from "react";
import { useParams } from "react-router-dom";
import _ from "lodash";
import LabelGrid from "./Components/LabelGrid";
import ModalAddLabel from "./Components/ModalAddLabel/ModalAddLabel";
import { openCreate } from "../../../store/slices/admin/addLabel.admin.slice";
import { useTranslation } from "react-i18next";

const { Option } = Select;

const LabelAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { filter } = useAppSelector(
    (state) => state.labelAdmin
  );
  const { branchCode } = useParams();
  const { actions } = labelAdminSlice;

  const debouncedFetchRef = useRef(
    _.debounce(async () => {
      fetch();
    }, 500)
  );

  const fetch = useCallback(async () => {
    await dispatch(fetchLabelsAdmin(branchCode || ""));
  }, [branchCode, dispatch]);

  const handleSearch = (value: string) => {
    dispatch(actions.setFilter({ search: value || null }));
    debouncedFetchRef.current();
  };

  const handleSort = (value: string) => {
    dispatch(actions.setFilter({ orderBy: value }));
    fetch();
  };

  const toggleSortDirection = () => {
    dispatch(
      actions.setFilter({
        orderDirection:
          filter.orderDirection === "ASC" ? "DESC" : "ASC",
      })
    );
    fetch();
  };

  const onAdd = useCallback(() => {
    dispatch(openCreate());
  }, [dispatch]);

  return (
    <WebAdminLayout activePage="label">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdLabel />}
            title={t("label.title")}
            description={t("label.description")}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={onAdd}
              >
                {t("label.addButton")}
              </Button>,
            ]}
          />
          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder={t("label.search.placeholder")}
                prefix={<SearchOutlined />}
                size="large"
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                onChange={(e) =>
                  handleSearch(e.target.value)
                }
                value={filter.search || ""}
              />
              <div className="flex gap-2">
                <Select
                  value={filter.orderBy}
                  size="large"
                  style={{ minWidth: 120 }}
                  onChange={handleSort}
                  className="dark:bg-gray-700"
                >
                  <Option value="created_at">
                    {t(
                      "label.search.sortOptions.createdAt"
                    )}
                  </Option>
                  <Option value="label_name">
                    {t(
                      "label.search.sortOptions.labelName"
                    )}
                  </Option>
                  <Option value="label_description">
                    {t(
                      "label.search.sortOptions.labelDescription"
                    )}
                  </Option>
                </Select>
                <Button
                  icon={
                    filter.orderDirection === "ASC" ? (
                      <SortAscendingOutlined />
                    ) : (
                      <SortDescendingOutlined />
                    )
                  }
                  size="large"
                  onClick={toggleSortDirection}
                  className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100 dark:hover:bg-gray-600"
                >
                  {t(
                    `label.search.sortDirection.${
                      filter.orderDirection === "ASC"
                        ? "asc"
                        : "desc"
                    }`
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Labels Grid */}
          <LabelGrid />
        </div>
      </div>
      {/* Modals */}
      <ModalAddLabel />
    </WebAdminLayout>
  );
};

export default LabelAdmin;
