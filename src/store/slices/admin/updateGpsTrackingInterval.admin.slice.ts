import {createAsyncThunk, createSlice, PayloadAction} from '@reduxjs/toolkit';
import {handleApiError} from "../../../utils/error.ts";
import {isAxiosError} from "axios";
import deviceAdminMainApi from "../../../services/mainApi/admin/device.admin.mainApi.ts";
import {RootState} from "../../index.ts";

interface UpdateGpsTrackingIntervalState {
  open: boolean;
  loading: boolean;
  error: string[] | null;

  // Form fields
  interval: number;
  active: boolean;
}

const initialState: UpdateGpsTrackingIntervalState = {
  open: false,
  loading: false,
  error: null,

  // Default values
  interval: 0,
  active: false,
};

export const fetchGpsTrackingIntervalAdmin = createAsyncThunk(
  'updateGpsTrackingIntervalAdmin/fetchGpsTrackingInterval',
  async (branchCode: string, { rejectWithValue }) => {
    try {
      const response = await deviceAdminMainApi.getInterval(branchCode);
      return {
        interval: response.data?.interval || 0,
        active: response.data?.active || false,
      }
    } catch (error) {
      if (isAxiosError(error)) {
        return rejectWithValue(handleApiError(error).errors);
      }
      return rejectWithValue(['Failed to fetch GPS tracking interval']);
    }
  }
);

export const updateGpsTrackingIntervalAdmin = createAsyncThunk(
  'updateGpsTrackingIntervalAdmin/updateGpsTrackingInterval',
  async (branchCode: string, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const { interval, active } = state.updateGpsTrackingIntervalAdmin;

      const response = await deviceAdminMainApi.updateGpsTrackingInterval(
        branchCode,
        {
          interval,
          active,
        }
      );

      return response.data;
    } catch (error) {
      if (isAxiosError(error)) {
        return rejectWithValue(handleApiError(error).errors);
      }
      return rejectWithValue(['Failed to update GPS tracking interval']);
    }
  }
);

const updateGpsTrackingIntervalAdminSlice = createSlice({
  name: 'updateGpsTrackingIntervalAdmin',
  initialState: {
    ...initialState,
  },
  reducers: {
    setOpen: (state, action: PayloadAction<boolean>) => {
      state.open = action.payload;
    },
    close: () => {
      return {
        ...initialState,
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string[] | null>) => {
      state.error = action.payload;
    },
    setInterval: (state, action: PayloadAction<number>) => {
      state.interval = action.payload;
    },
    setActive: (state, action: PayloadAction<boolean>) => {
      state.active = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchGpsTrackingIntervalAdmin.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchGpsTrackingIntervalAdmin.fulfilled,
        (state, action) => {
          state.loading = false;
          state.interval = action.payload.interval;
          state.active = action.payload.active;
        }
      )
      .addCase(
        fetchGpsTrackingIntervalAdmin.rejected,
        (state, action) => {
          state.loading = false;
          state.error = action.payload as string[];
        }
      );
  }
});

export default updateGpsTrackingIntervalAdminSlice;
