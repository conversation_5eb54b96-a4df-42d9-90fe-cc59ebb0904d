import React, {useState} from "react";
import type {MenuProps} from "antd";
import {But<PERSON>, Dropdown, Tooltip} from "antd";
import {FiDownload} from "react-icons/fi";
import {useTranslation} from "react-i18next";
import {useParams} from "react-router-dom";
import {missedZoneAdminApi} from "../../../../../services/mainApi/admin/missedZone.admin.mainApi";
import {AdminLabel} from "../../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import {AdminZone} from "../../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import {AdminBranch} from "../../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import {AdminMissedZoneLogListParams} from "../../../../../services/mainApi/admin/types/missedZone.admin.mainApi.types";

interface MissedZoneLogExportButtonProps {
  filters: {
    site: AdminZone | null;
    siteLabels: AdminLabel[];
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    selectedBranch: AdminBranch | null;
  };
  className?: string;
  buttonText?: string;
  showPdf?: boolean;
  showSpreadsheet?: boolean;
}

type ExportFormat = "pdf" | "spreadsheet";

const MissedZoneLogExportButton: React.FC<
  MissedZoneLogExportButtonProps
> = ({
       filters,
       className,
       buttonText,
       showPdf = true,
       showSpreadsheet = true,
     }) => {
  const {t} = useTranslation();
  const [isDownloading, setIsDownloading] = useState(false);
  const {branchCode} = useParams<{
    branchCode: string;
  }>();

  const handleDownload = async (format: ExportFormat) => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const filterParams: AdminMissedZoneLogListParams = {
        start_date: filters.startDate || undefined,
        end_date: filters.endDate || undefined,
        start_time: filters.startTime || undefined,
        end_time: filters.endTime || undefined,
        branch_id: filters.selectedBranch?.id ? parseInt(filters.selectedBranch.id) : undefined,
        site_id: filters.site?.id ? parseInt(filters.site.id) : undefined,
        site_labels:
          filters.siteLabels
            ?.map((label) => label.id)
            .join(",") || undefined,
      };

      const blob =
        await missedZoneAdminApi.exportMissedZoneLogs(
          branchCode,
          filterParams,
          format,
          "buffer"
        );

      if (format === "pdf") {
        // Create URL and open in new tab
        const url = URL.createObjectURL(blob);
        window.open(url, "_blank");

        // Delay URL revocation to ensure the new tab has time to load
        setTimeout(() => {
          URL.revokeObjectURL(url);
        }, 5000);
      } else {
        // Create URL and trigger download
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `missed_zone_logs_${new Date().toISOString().slice(0, 10)}.xlsx`;
        link.click();

        // Clean up
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error(`Error downloading ${format}:`, error);
    } finally {
      setIsDownloading(false);
    }
  };

  // If only one format is shown, render a simple button
  if (
    (showPdf && !showSpreadsheet) ||
    (!showPdf && showSpreadsheet)
  ) {
    const format = showPdf ? "pdf" : "spreadsheet";
    const tooltipText = showPdf
      ? t("missedZoneLog.downloadPdf", "Download as PDF")
      : t(
        "missedZoneLog.downloadSpreadsheet",
        "Download as Spreadsheet"
      );

    return (
      <Tooltip title={tooltipText}>
        <Button
          icon={<FiDownload/>}
          loading={isDownloading}
          onClick={() => handleDownload(format)}
          size="large"
          className={className}
        >
          {buttonText ||
            (showPdf
              ? t("missedZoneLog.pdf", "PDF")
              : t(
                "missedZoneLog.spreadsheet",
                "Spreadsheet"
              ))}
        </Button>
      </Tooltip>
    );
  }

  // Otherwise, render a dropdown with both options
  const items: MenuProps["items"] = [
    {
      key: "pdf",
      label: t("missedZoneLog.exportPdf", "Export as PDF"),
      onClick: () => handleDownload("pdf"),
      disabled: !showPdf,
    },
    {
      key: "spreadsheet",
      label: t(
        "missedZoneLog.exportSpreadsheet",
        "Export as Spreadsheet"
      ),
      onClick: () => handleDownload("spreadsheet"),
      disabled: !showSpreadsheet,
    },
  ];

  return (
    <Dropdown
      menu={{items}}
      disabled={isDownloading}
    >
      <Button
        icon={<FiDownload/>}
        loading={isDownloading}
        size="large"
        className={className}
      >
        {buttonText || t("missedZoneLog.export", "Export")}
      </Button>
    </Dropdown>
  );
};

export default MissedZoneLogExportButton;
