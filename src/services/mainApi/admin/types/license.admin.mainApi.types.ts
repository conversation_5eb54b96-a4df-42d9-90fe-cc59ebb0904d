import { BaseResponse } from "../../types/base.mainApi.types";

export interface AdminLicense {
  id: string;
  license_name: string;
  max_subbranch: number;
  max_user: number;
  max_label: number;
  max_scheduler: number;
  max_alert: number;
  max_device: number;
  max_site: number;
  max_checkpoint: number;
  max_geofence: number;
  max_form: number;
  max_task: number;
  max_activity: number;
  max_beacon: number;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

export type AdminLicenseListResponse = BaseResponse<
  AdminLicense[]
>;
