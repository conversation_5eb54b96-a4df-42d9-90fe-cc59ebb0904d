import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import rolesAdminMainApi from "../../services/mainApi/admin/roles.admin.mainApi";
import { AdminRole } from "../../services/mainApi/admin/types/roles.admin.mainApi.types";
import clsx from "clsx";

interface SelectRolesProps {
  value?: string;
  onChange?: (
    value: string,
    role: AdminRole | null
  ) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  clearable?: boolean;
}

export default function SelectRole({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select role",
  clearable = true,
}: SelectRolesProps) {
  const [roles, setRoles] = useState<AdminRole[]>([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);

  useEffect(() => {
    const fetchRoles = async () => {
      if (isFetching.current) return;

      try {
        isFetching.current = true;
        setLoading(true);
        const response = await rolesAdminMainApi.getRoles(
          {}
        );
        setRoles(response.data || []);
      } catch (error) {
        console.error("Failed to fetch roles:", error);
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchRoles();
  }, []);

  const handleChange = (
    selectedValue: string | undefined
  ) => {
    if (!onChange) return;

    if (!selectedValue) {
      onChange(selectedValue || "", null);
      return;
    }

    const selectedRole =
      roles.find((role) => role.id === selectedValue) ||
      null;
    onChange(selectedValue, selectedRole);
  };

  return (
    <div className={clsx("ant-select-wrapper", className)}>
      <Select
        className="w-full"
        placeholder={placeholder}
        value={value}
        onChange={handleChange}
        disabled={disabled}
        loading={loading}
        showSearch
        optionFilterProp="label"
        options={roles.map((role) => ({
          value: role.id,
          label: role.role_name,
        }))}
        allowClear={clearable}
      />
    </div>
  );
}
