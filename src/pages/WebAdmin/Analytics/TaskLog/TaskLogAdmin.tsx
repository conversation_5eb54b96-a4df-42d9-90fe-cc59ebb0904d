import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MdTask } from "react-icons/md";
import {
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import ActiveFilters, {
  createEndDateFilter,
  createEndTimeFilter,
  createLabelFilter,
  createSimpleFilter,
  createStartDateFilter,
  createStartTimeFilter,
  FilterGroup,
} from "../../../../components/Admin/Common/ActiveFilters";
import { <PERSON><PERSON>, Drawer } from "antd";
import AnalyticDrawerForm, {
  AnalyticsFilterState,
} from "../../Analytics/Components/AnalyticDrawerForm";
import { EReportType } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { AdminRole } from "../../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminUser } from "../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminLabel } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminBranch } from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks";
import TaskLogList from "./Components/TaskLogList";
import taskLogAdminSlice, {
  fetchTaskLogs,
} from "../../../../store/slices/admin/taskLog.admin.slice";
import { useParams } from "react-router-dom";
import { AdminTask } from "../../../../services/mainApi/admin/types/task.admin.mainApi.types.ts";
import TaskLogExportButton from "./Components/TaskLogExportButton.tsx";

interface FilterState {
  role: null | AdminRole;
  user: null | AdminUser;
  userLabels: AdminLabel[];
  device: null | AdminDevice;
  deviceLabels: AdminLabel[];
  task: null | AdminTask;
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  selectedBranch: AdminBranch | null;
}

const TaskLogAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [drawerVisible, setDrawerVisible] = useState(false);

  // Get branch code from URL params
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  // Redux state
  const taskLogState = useAppSelector(
    (state) => state.taskLogAdmin
  );
  const loading = taskLogState.loading;
  const pagination = taskLogState.pagination;
  const taskLogs = taskLogState.taskLogs;
  const selectedRole = taskLogState.selectedRole;
  const selectedUser = taskLogState.selectedUser;
  const selectedDevice = taskLogState.selectedDevice;
  const selectedTask = taskLogState.selectedTask;
  const selectedBranch = taskLogState.selectedBranch;
  const selectedUserLabels =
    taskLogState.selectedUserLabels;
  const selectedDeviceLabels =
    taskLogState.selectedDeviceLabels;
  const filterState = taskLogState.filter;

  const isFetching = useRef(false);

  const fetchData = useCallback(async () => {
    if (isFetching.current) return;
    isFetching.current = true;
    await dispatch(
      fetchTaskLogs({ branchCode: branchCode || "" })
    );
    isFetching.current = false;
  }, [branchCode, dispatch]);

  // Load data on mount and when filters change
  useEffect(() => {
    if (branchCode) {
      fetchData();
    }
  }, [
    dispatch,
    branchCode,
    filterState.page,
    filterState.limit,
    filterState.startDate,
    filterState.endDate,
    filterState.startTime,
    filterState.endTime,
    filterState.branchId,
    filterState.roleId,
    filterState.userId,
    filterState.userLabels,
    filterState.deviceId,
    filterState.deviceLabels,
    filterState.taskId,
    filterState.orderBy,
    filterState.orderDirection,
    fetchData,
  ]);

  const { actions } = taskLogAdminSlice;

  // Convert applied filter state to format expected by drawer
  const getAppliedFilters = (): FilterState => {
    return {
      role: selectedRole,
      user: selectedUser,
      userLabels: selectedUserLabels || [],
      device: selectedDevice,
      deviceLabels: selectedDeviceLabels || [],
      task: selectedTask,
      startDate: filterState.startDate,
      endDate: filterState.endDate,
      startTime: filterState.startTime,
      endTime: filterState.endTime,
      selectedBranch: selectedBranch,
    };
  };

  // Apply filters from the drawer
  const handleApplyFilters = (
    newFilters: AnalyticsFilterState
  ) => {
    // Update redux state
    dispatch(
      actions.setSelectedRole(newFilters.role || null)
    );
    dispatch(
      actions.setSelectedUser(newFilters.user || null)
    );
    dispatch(
      actions.setSelectedUserLabels(
        newFilters.userLabels || []
      )
    );
    dispatch(
      actions.setSelectedDevice(newFilters.device || null)
    );
    dispatch(
      actions.setSelectedDeviceLabels(
        newFilters.deviceLabels || []
      )
    );
    dispatch(
      actions.setSelectedTask(newFilters.tasks || null)
    );
    dispatch(
      actions.setSelectedBranch(
        newFilters.selectedBranch || null
      )
    );
    dispatch(
      actions.setStartDate(newFilters.startDate || null)
    );
    dispatch(
      actions.setEndDate(newFilters.endDate || null)
    );
    dispatch(
      actions.setStartTime(newFilters.startTime || null)
    );
    dispatch(
      actions.setEndTime(newFilters.endTime || null)
    );

    // Reset page to 1
    dispatch(actions.setPage(1));

    setDrawerVisible(false);
  };

  // Handle page change
  const handlePageChange = (
    page: number,
    pageSize?: number
  ) => {
    if (pageSize && pageSize !== filterState.limit) {
      dispatch(actions.setLimit(pageSize));
    }
    dispatch(actions.setPage(page));
  };

  // Check if there are any active filters
  const hasActiveFilters = (): boolean => {
    // Check if any filter is active
    return !!(
      filterState.startDate ||
      filterState.endDate ||
      filterState.startTime ||
      filterState.endTime ||
      filterState.branchId ||
      filterState.roleId ||
      filterState.userId ||
      filterState.userLabels.length > 0 ||
      filterState.deviceId ||
      filterState.deviceLabels.length > 0 ||
      filterState.taskId
    );
  };

  // Get filter groups configuration for ActiveFilters component
  const getFilterGroups = (): FilterGroup[] => {
    const appliedFilters = getAppliedFilters();

    return [
      {
        key: "datetime",
        filters: [
          createStartDateFilter(appliedFilters.startDate),
          createEndDateFilter(appliedFilters.endDate),
          createStartTimeFilter(appliedFilters.startTime),
          createEndTimeFilter(appliedFilters.endTime),
        ],
      },
      {
        key: "user",
        filters: [
          createSimpleFilter(
            "role",
            "role",
            appliedFilters.role,
            "role_name"
          ),
          createSimpleFilter(
            "user",
            "user",
            appliedFilters.user,
            "name"
          ),
          createLabelFilter(
            "userLabels",
            "userLabels",
            appliedFilters.userLabels
          ),
        ],
      },
      {
        key: "device",
        filters: [
          createSimpleFilter(
            "device",
            "device",
            appliedFilters.device,
            "device_name"
          ),
          createLabelFilter(
            "deviceLabels",
            "deviceLabels",
            appliedFilters.deviceLabels
          ),
        ],
      },
      {
        key: "other",
        filters: [
          createSimpleFilter(
            "task",
            "task",
            appliedFilters.task,
            "task_name"
          ),
          createSimpleFilter(
            "selectedBranch",
            "selectedBranch",
            appliedFilters.selectedBranch,
            "branch_name"
          ),
        ],
      },
    ];
  };

  const handleClearAll = () => {
    dispatch(actions.clearFilters());
  };

  const handleRemoveFilter = (
    filterKey: keyof FilterState
  ) => {
    switch (filterKey) {
      case "role":
        dispatch(actions.setSelectedRole(null));
        break;
      case "user":
        dispatch(actions.setSelectedUser(null));
        break;
      case "userLabels":
        dispatch(actions.setSelectedUserLabels([]));
        break;
      case "device":
        dispatch(actions.setSelectedDevice(null));
        break;
      case "deviceLabels":
        dispatch(actions.setSelectedDeviceLabels([]));
        break;
      case "task":
        dispatch(actions.setSelectedTask(null));
        break;
      case "selectedBranch":
        dispatch(actions.setSelectedBranch(null));
        break;
      case "startDate":
        dispatch(actions.setStartDate(null));
        break;
      case "endDate":
        dispatch(actions.setEndDate(null));
        break;
      case "startTime":
        dispatch(actions.setStartTime(null));
        break;
      case "endTime":
        dispatch(actions.setEndTime(null));
        break;
    }
  };

  // Handle clicking on an active filter to edit it
  const handleEditFilter = () => {
    // We only need to open the drawer
    setDrawerVisible(true);
  };

  // Convert FilterState to AnalyticsFilterState for the drawer form
  const getInitialDrawerValues =
    (): Partial<AnalyticsFilterState> => {
      const appliedFilters = getAppliedFilters();

      return {
        startDate: appliedFilters.startDate || "",
        endDate: appliedFilters.endDate || "",
        startTime: appliedFilters.startTime || "",
        endTime: appliedFilters.endTime || "",
        selectedBranch:
          appliedFilters.selectedBranch || null,
        role: appliedFilters.role,
        user: appliedFilters.user,
        userLabels: appliedFilters.userLabels,
        device: appliedFilters.device,
        deviceLabels: appliedFilters.deviceLabels,
        tasks: appliedFilters.task,
      };
    };

  // Get filter info for download buttons
  const getFilterInfo = () => ({
    startDate: filterState.startDate,
    endDate: filterState.endDate,
    startTime: filterState.startTime,
    endTime: filterState.endTime,
    selectedBranch: selectedBranch,
    role: selectedRole,
    user: selectedUser,
    userLabels: selectedUserLabels,
    device: selectedDevice,
    deviceLabels: selectedDeviceLabels,
    task: selectedTask,
  });

  return (
    <WebAdminLayout activePage="analytics-tasks-log">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdTask />}
            title={t("taskLog.title", "Tasks Log")}
            description={t(
              "taskLog.description",
              "View and manage task logs"
            )}
            actions={[
              // Desktop view - show all buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <TaskLogExportButton
                  key="download"
                  filters={getFilterInfo()}
                  className="mr-2"
                />
                <Button
                  key="filter"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button and dropdown for downloads
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <TaskLogExportButton
                  key="download-mobile"
                  filters={getFilterInfo()}
                  className="mr-2"
                  buttonText={t(
                    "common.download",
                    "Download"
                  )}
                />
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList />}
                  size="large"
                  type="primary"
                  className="bg-indigo-600 hover:bg-indigo-700 border-0 shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters using the reusable component */}
          <ActiveFilters<FilterState>
            filterGroups={getFilterGroups()}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAll}
            onShowFilters={() => {
              setDrawerVisible(true);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Results List */}
          <TaskLogList
            taskLogs={taskLogs}
            loading={loading}
            total={pagination.total || 0}
            page={pagination.page || 1}
            limit={pagination.limit || 10}
            onPageChange={handlePageChange}
          />
        </div>

        {/* Filter Drawer with modern styling */}
        <Drawer
          title={
            <div className="text-lg font-semibold text-gray-800 dark:text-white">
              {t(
                "taskLog.filters.title",
                "Filter Task Log"
              )}
            </div>
          }
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          className="dark:bg-gray-800"
        >
          <AnalyticDrawerForm
            reportType={EReportType.TASKS}
            onApply={handleApplyFilters}
            onClose={() => setDrawerVisible(false)}
            initialValues={getInitialDrawerValues()}
          />
        </Drawer>
      </div>
    </WebAdminLayout>
  );
};

export default TaskLogAdmin;
