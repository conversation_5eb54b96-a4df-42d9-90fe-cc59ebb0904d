import {
  createSlice,
  createAsyncThunk,
  PayloadAction,
} from "@reduxjs/toolkit";
import { exceptionReportLogAdminApi } from "../../../services/mainApi/admin/exceptionReportLog.admin.mainApi";
import {
  AdminExceptionReportLogEntry
} from "../../../services/mainApi/admin/types/exceptionReportLog.admin.mainApi.types";
import { BasePaginationMeta } from "../../../services/mainApi/types/base.mainApi.types";
import { RootState } from "../../../store";
import { AdminRole } from "../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminUser } from "../../../services/mainApi/admin/types/user.admin.mainApi.types.ts";
import { AdminDevice } from "../../../services/mainApi/admin/types/device.admin.mainApi.types.ts";
import { AdminLabel } from "../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminBranch } from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminZone } from "../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { AdminCheckpoint } from "../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types.ts";
import { isAxiosError } from "axios";
import { handleApiError } from "../../../utils/error.ts";
import dayjs from "dayjs";

interface State {
  exceptionLogs: AdminExceptionReportLogEntry[];
  loading: boolean;
  error: string | null;

  filter: {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    branchId: string | number | null;
    siteId: string | number | null;
    siteLabels: (string | number)[];
    checkpointId: string | number | null;
    checkpointLabels: (string | number)[];
    roleId: string | number | null;
    userId: string | number | null;
    userLabels: (string | number)[];
    deviceId: string | number | null;
    deviceLabels: (string | number)[];
    page: number;
    limit: number;
  };
  pagination: BasePaginationMeta;

  // Filter selection objects
  selectedBranch: AdminBranch | null;
  selectedSite: AdminZone | null;
  selectedSiteLabels: AdminLabel[];
  selectedCheckpoint: AdminCheckpoint | null;
  selectedCheckpointLabels: AdminLabel[];
  selectedRole: AdminRole | null;
  selectedUser: AdminUser | null;
  selectedUserLabels: AdminLabel[];
  selectedDevice: AdminDevice | null;
  selectedDeviceLabels: AdminLabel[];
}

const initialState: State = {
  exceptionLogs: [],
  loading: false,
  error: null,

  filter: {
    startDate: dayjs().startOf("day").format("YYYY-MM-DD"),
    endDate: dayjs().endOf("day").format("YYYY-MM-DD"),
    startTime: "00:00:00",
    endTime: "23:59:59",
    branchId: null,
    siteId: null,
    siteLabels: [],
    checkpointId: null,
    checkpointLabels: [],
    roleId: null,
    userId: null,
    userLabels: [],
    deviceId: null,
    deviceLabels: [],
    page: 1,
    limit: 10,
  },
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
  },

  selectedBranch: null,
  selectedSite: null,
  selectedSiteLabels: [],
  selectedCheckpoint: null,
  selectedCheckpointLabels: [],
  selectedRole: null,
  selectedUser: null,
  selectedUserLabels: [],
  selectedDevice: null,
  selectedDeviceLabels: [],
};

export const fetchExceptionLogs = createAsyncThunk(
  "exceptionLogAdmin/fetchExceptionLogs",
  async (
    {
      branchCode,
    }: {
      branchCode: string;
    },
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const exceptionLogAdmin = (state as any).exceptionLogAdmin;

      const params = {
        page: exceptionLogAdmin.filter.page,
        limit: exceptionLogAdmin.filter.limit,
        start_date: exceptionLogAdmin.filter.startDate || undefined,
        end_date: exceptionLogAdmin.filter.endDate || undefined,
        start_time: exceptionLogAdmin.filter.startTime || undefined,
        end_time: exceptionLogAdmin.filter.endTime || undefined,
        branch_id: exceptionLogAdmin.filter.branchId || undefined,
        site_id: exceptionLogAdmin.filter.siteId || undefined,
        site_labels: exceptionLogAdmin.filter.siteLabels.length > 0
          ? exceptionLogAdmin.filter.siteLabels.join(",")
          : undefined,
        checkpoint_id: exceptionLogAdmin.filter.checkpointId || undefined,
        checkpoint_labels: exceptionLogAdmin.filter.checkpointLabels.length > 0
          ? exceptionLogAdmin.filter.checkpointLabels.join(",")
          : undefined,
        role_id: exceptionLogAdmin.filter.roleId || undefined,
        user_id: exceptionLogAdmin.filter.userId || undefined,
        user_labels: exceptionLogAdmin.filter.userLabels.length > 0
          ? exceptionLogAdmin.filter.userLabels.join(",")
          : undefined,
        device_id: exceptionLogAdmin.filter.deviceId || undefined,
        device_labels: exceptionLogAdmin.filter.deviceLabels.length > 0
          ? exceptionLogAdmin.filter.deviceLabels.join(",")
          : undefined,
      };

      return await exceptionReportLogAdminApi.getExceptionReportLogs(
        branchCode,
        params
      );
    } catch (error) {
      if (isAxiosError(error)) {
        const e = handleApiError(error).errors;
        return rejectWithValue(e);
      }
      return rejectWithValue(
        error instanceof Error
          ? error.message
          : "An error occurred"
      );
    }
  }
);

const exceptionLogAdminSlice = createSlice({
  name: "exceptionLogAdmin",
  initialState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.filter.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.filter.limit = action.payload;
    },
    clearFilters: (state) => {
      state.filter = {
        ...initialState.filter,
        page: state.filter.page,
        limit: state.filter.limit,
      };
      state.selectedBranch = null;
      state.selectedSite = null;
      state.selectedSiteLabels = [];
      state.selectedCheckpoint = null;
      state.selectedCheckpointLabels = [];
      state.selectedRole = null;
      state.selectedUser = null;
      state.selectedUserLabels = [];
      state.selectedDevice = null;
      state.selectedDeviceLabels = [];
    },

    // Setting filter selection objects
    setSelectedBranch: (
      state,
      action: PayloadAction<AdminBranch | null>
    ) => {
      state.selectedBranch = action.payload;
      state.filter.branchId = action.payload?.id || null;
    },
    setSelectedSite: (
      state,
      action: PayloadAction<AdminZone | null>
    ) => {
      state.selectedSite = action.payload;
      state.filter.siteId = action.payload?.id || null;
    },
    setSelectedSiteLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedSiteLabels = action.payload;
      state.filter.siteLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setSelectedCheckpoint: (
      state,
      action: PayloadAction<AdminCheckpoint | null>
    ) => {
      state.selectedCheckpoint = action.payload;
      state.filter.checkpointId = action.payload?.id || null;
    },
    setSelectedCheckpointLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedCheckpointLabels = action.payload;
      state.filter.checkpointLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setSelectedRole: (
      state,
      action: PayloadAction<AdminRole | null>
    ) => {
      state.selectedRole = action.payload;
      state.filter.roleId = action.payload?.id || null;
    },
    setSelectedUser: (
      state,
      action: PayloadAction<AdminUser | null>
    ) => {
      state.selectedUser = action.payload;
      state.filter.userId = action.payload?.id || null;
    },
    setSelectedUserLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedUserLabels = action.payload;
      state.filter.userLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setSelectedDevice: (
      state,
      action: PayloadAction<AdminDevice | null>
    ) => {
      state.selectedDevice = action.payload;
      state.filter.deviceId = action.payload?.id || null;
    },
    setSelectedDeviceLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedDeviceLabels = action.payload;
      state.filter.deviceLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setStartDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startDate = action.payload;
    },
    setEndDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endDate = action.payload;
    },
    setStartTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startTime = action.payload;
    },
    setEndTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endTime = action.payload;
    },
    setSiteLabels: (
      state,
      action: PayloadAction<(string | number)[] | null>
    ) => {
      state.filter.siteLabels = action.payload || [];
    },
    setCheckpointLabels: (
      state,
      action: PayloadAction<(string | number)[] | null>
    ) => {
      state.filter.checkpointLabels = action.payload || [];
    },
    setUserLabels: (
      state,
      action: PayloadAction<(string | number)[] | null>
    ) => {
      state.filter.userLabels = action.payload || [];
    },
    setDeviceLabels: (
      state,
      action: PayloadAction<(string | number)[] | null>
    ) => {
      state.filter.deviceLabels = action.payload || [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Exception Logs
      .addCase(fetchExceptionLogs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchExceptionLogs.fulfilled,
        (state, action) => {
          state.loading = false;
          state.exceptionLogs = action.payload.data || [];
          state.pagination.total = action.payload.meta?.total || 0;
          state.pagination.page = action.payload.meta?.page || 1;
          state.pagination.limit = action.payload.meta?.limit || 10;
        }
      )
      .addCase(
        fetchExceptionLogs.rejected,
        (state, action) => {
          state.loading = false;
          state.error = action.error.message || "Failed to fetch exception logs";
        }
      );
  },
});

export default exceptionLogAdminSlice;