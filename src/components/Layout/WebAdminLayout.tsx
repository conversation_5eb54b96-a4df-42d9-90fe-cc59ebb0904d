import React from "react";
import MainLayout from "./MainLayout";
import { useMenuConfig } from "../../config/routes/menuConfig";
import { useTranslation } from "react-i18next";

interface WebAdminLayoutProps {
  children: React.ReactNode;
  activePage?: string;
  hideMenu?: boolean;
}

export default function WebAdminLayout({
  children,
  activePage,
  hideMenu,
}: WebAdminLayoutProps) {
  const { webAdminConfig } = useMenuConfig();
  const { t } = useTranslation();

  return (
    <MainLayout
      menuItems={webAdminConfig.menuItems}
      menuPaths={webAdminConfig.menuPaths}
      activePage={activePage}
      layoutTitle={t("layout.webAdmin")}
      hideMenu={hideMenu}
    >
      {children}
    </MainLayout>
  );
}
