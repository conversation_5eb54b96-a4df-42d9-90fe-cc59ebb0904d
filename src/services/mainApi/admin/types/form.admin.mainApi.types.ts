import { FieldType } from "../../../../types/FieldType.enum";
import { Branch } from "../../sysadmin/types/branch.mainApi.types";
import {
  BaseResponse,
  BaseGetRequest,
} from "../../types/base.mainApi.types";
import { AdminCheckpoint } from "./checkpoint.admin.mainApi.types";
import { AdminRole } from "./roles.admin.mainApi.types";

export interface FormFieldType {
  id: FieldType;
  field_type_name: string;
  field_type_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

export interface FormPicklist {
  id: string;
  branch_id: string;
  form_picklist_name: string;
  form_picklist_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
}

export interface FormField {
  id: string;
  form_id: string;
  field_type_id: FieldType;
  form_picklist_id: string | null;
  form_field_name: string;
  form_field_description: string | null;
  form_field_require: boolean;
  active: boolean;
  created_by: string;
  updated_by: string | null;
  created_at: string;
  updated_at: string;
  field_type: FormFieldType;
  picklist: FormPicklist | null;
}

export interface AdminForm {
  id: string;
  parent_branch_id: string;
  role_id: string | null;
  checkpoint_id: string | null;
  form_name: string;
  form_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
  fields: FormField[];
  branches: Branch[];
  checkpoint: AdminCheckpoint | null;
  role: AdminRole;
}

export type AdminFormListResponse = BaseResponse<
  AdminForm[]
>;

export interface GetFormRequest extends BaseGetRequest {
  ignore_active_status?: boolean;
}

interface FormFieldRequest {
  field_type_id: FieldType;
  form_field_name: string;
  form_field_description: string | null;
  form_field_require: boolean;
  form_picklist_id: string | null;
  active: boolean;
}

export interface CreateFormRequest {
  form_name: string;
  form_description: string | null;
  role_id: string | null;
  fields: FormFieldRequest[];
  branch_ids: string[];
  active: boolean;
  checkpoint_id?: string | null;
}

// Update menggunakan interface yang sama dengan Create
export type UpdateFormRequest = CreateFormRequest;
