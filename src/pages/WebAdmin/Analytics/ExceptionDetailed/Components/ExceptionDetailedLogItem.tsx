import React from "react";
import {<PERSON><PERSON>, <PERSON>lapse} from "antd";
import {FiCheckCircle, FiClock, FiInfo, FiMapPin, FiSmartphone, FiXCircle, FiHash} from "react-icons/fi";
import {useTranslation} from "react-i18next";
import {
  AdminExceptionReportDetailedLogEntry
} from "../../../../../services/mainApi/admin/types/exceptionReportDetailedLog.admin.mainApi.types";
import {ECheckPointType} from "../../../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";
import {formatDateTime} from "../../../../../utils/dateUtils.ts";

// Interface for the exception detailed log item props
interface ExceptionDetailedLogItemProps {
  exceptionDetailedLog: AdminExceptionReportDetailedLogEntry;
}

// Helper function to get checkpoint type name
const getCheckpointTypeName = (checkpointTypeId: number): string => {
  switch (checkpointTypeId.toString()) {
    case ECheckPointType.BEACON:
      return "Beacon";
    case ECheckPointType.NFC:
      return "NFC";
    case ECheckPointType.RFID:
      return "RFID";
    case ECheckPointType.GEOFENCE:
      return "Geofence";
    default:
      return "Unknown";
  }
};

const ExceptionDetailedLogItem: React.FC<ExceptionDetailedLogItemProps> = ({exceptionDetailedLog}) => {
  const {t} = useTranslation();
  const {Panel} = Collapse;

  // Calculate completion percentage
  const completionPercentage = exceptionDetailedLog.expected_visit_time > 0
    ? Math.round((exceptionDetailedLog.actual_visit_time / exceptionDetailedLog.expected_visit_time) * 100)
    : 0;

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-0 p-4 mb-4">
      {/* Header - Checkpoint Name with gradient background */}
      <div
        className="mb-3 -mx-4 -mt-4 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 rounded-t-xl border-b border-gray-100 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center gap-2">
          <FiMapPin className="text-red-500"/>
          {exceptionDetailedLog.checkpoint_name}
        </h3>
      </div>

      {/* Content - Compact Two Column Layout */}
      <div className="grid grid-cols-2 gap-3 px-1">
        {/* Left Column */}
        <div className="space-y-2">
          {/* Expected Visits - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
              <FiClock className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("exceptionDetailedLog.expectedVisits", "Expected")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {exceptionDetailedLog.expected_visit_time}
                </span>
              </div>
            </div>
          </div>

          {/* Actual Visits - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
              <FiCheckCircle className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("exceptionDetailedLog.actualVisits", "Actual")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {exceptionDetailedLog.actual_visit_time}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-2">
          {/* Missed Visits - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-red-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300 flex-shrink-0">
              <FiXCircle className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("exceptionDetailedLog.missedVisits", "Missed")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {exceptionDetailedLog.missed_visit_time}
                </span>
              </div>
            </div>
          </div>

          {/* Completion Rate - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-indigo-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex-shrink-0">
              <FiClock className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("exceptionDetailedLog.completionRate", "Completion")}:
                </span>
                <Badge
                  color={exceptionDetailedLog.missed_visit_time > 0 ? "red" : "green"}
                  count={`${completionPercentage}%`}
                  className="font-medium text-xs"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Device Information Section */}
      <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
          <FiSmartphone className="text-blue-500"/>
          {t("exceptionDetailedLog.deviceInformation", "Device Information")}
        </h4>
        <div className="grid grid-cols-2 gap-3">
          {/* Left Column - Device Info */}
          <div className="space-y-2">
            {/* Serial Number Hex */}
            <div className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
              <div className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
                <FiHash className="w-4 h-4"/>
              </div>
              <div className="flex flex-col min-w-0">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t("exceptionDetailedLog.serialNumberHex", "Serial Hex")}:
                  </span>
                  <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                    {exceptionDetailedLog.serial_number_hex}
                  </span>
                </div>
              </div>
            </div>

            {/* Serial Number Dec */}
            <div className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
              <div className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
                <FiHash className="w-4 h-4"/>
              </div>
              <div className="flex flex-col min-w-0">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t("exceptionDetailedLog.serialNumberDec", "Serial Dec")}:
                  </span>
                  <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                    {exceptionDetailedLog.serial_number_dec}
                  </span>
                </div>
              </div>
            </div>

            {/* Checkpoint Type */}
            <div className="group flex items-center gap-2 text-sm hover:bg-purple-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
              <div className="flex items-center justify-center w-7 h-7 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 flex-shrink-0">
                <FiInfo className="w-4 h-4"/>
              </div>
              <div className="flex flex-col min-w-0">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t("exceptionDetailedLog.checkpointType", "Type")}:
                  </span>
                  <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                    {getCheckpointTypeName(exceptionDetailedLog.checkpoint_type_id)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Beacon Info */}
          <div className="space-y-2">
            {/* Major Value */}
            <div className="group flex items-center gap-2 text-sm hover:bg-orange-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
              <div className="flex items-center justify-center w-7 h-7 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 flex-shrink-0">
                <FiHash className="w-4 h-4"/>
              </div>
              <div className="flex flex-col min-w-0">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t("exceptionDetailedLog.majorValue", "Major")}:
                  </span>
                  <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                    {exceptionDetailedLog.major_value}
                  </span>
                </div>
              </div>
            </div>

            {/* Minor Value */}
            <div className="group flex items-center gap-2 text-sm hover:bg-teal-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
              <div className="flex items-center justify-center w-7 h-7 rounded-full bg-teal-100 dark:bg-teal-900 text-teal-600 dark:text-teal-300 flex-shrink-0">
                <FiHash className="w-4 h-4"/>
              </div>
              <div className="flex flex-col min-w-0">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t("exceptionDetailedLog.minorValue", "Minor")}:
                  </span>
                  <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                    {exceptionDetailedLog.minor_value}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Visit History - Collapsible Section */}
      {exceptionDetailedLog.visit_info && exceptionDetailedLog.visit_info.length > 0 && (
        <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
          <Collapse
            ghost
            className="bg-transparent"
          >
            <Panel
              header={
                <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  <FiInfo className="text-blue-500"/>
                  <span>
                    {t("exceptionDetailedLog.visitHistory", "Visit History")}
                    <span className="ml-2 text-xs text-gray-500">
                      ({exceptionDetailedLog.visit_info.length} {t("exceptionDetailedLog.visits", "visits")})
                    </span>
                  </span>
                </div>
              }
              key="1"
            >
              <div className="space-y-2 mt-2">
                {exceptionDetailedLog.visit_info.map((visit, index) => (
                  <div
                    key={index}
                    className="text-sm py-2 px-3 bg-gray-50 dark:bg-gray-700 rounded-md flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <FiClock className="text-blue-500"/>
                      <span className="font-medium">Visit {index + 1}</span>
                    </div>
                    <span className="text-gray-600 dark:text-gray-300">
                      {
                        formatDateTime(visit.date, "DD MMM YYYY HH:mm", visit.timezone_name)
                      }
                    </span>
                  </div>
                ))}
              </div>
            </Panel>
          </Collapse>
        </div>
      )}
    </div>
  );
};

export default ExceptionDetailedLogItem;
