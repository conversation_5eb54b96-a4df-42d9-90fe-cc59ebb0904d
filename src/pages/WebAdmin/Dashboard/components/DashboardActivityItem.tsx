import { <PERSON><PERSON>, <PERSON>, <PERSON>, Tooltip, Typography, message } from "antd";
import { AdminLogAlert } from "../../../../services/mainApi/admin/types/logAlert.admin.mainApi.types";
import dayjs from "dayjs";
import { <PERSON><PERSON><PERSON>, <PERSON>Info, FiUser, FiX } from "react-icons/fi";
import { logAlertAdminApi } from "../../../../services/mainApi/admin/logAlert.admin.mainApi.ts";
import { useParams } from "react-router-dom";
import React, { useCallback, useMemo } from "react";

interface DashboardActivityItemProps {
  activity: AdminLogAlert;
  onRefresh: () => Promise<void>;
}

// Extracted user information component
const UserInfo = React.memo(({ user }: { user: AdminLogAlert['user'] }) => {
  if (!user.name) return null;

  return (
    <Typography.Text type="secondary" className="text-xs flex items-center mt-1">
      <FiUser className="mr-1"/>
      {user.name}
      {user.role && (
        <Tag color="blue" className="ml-1">
          {user.role.role_name}
        </Tag>
      )}
    </Typography.Text>
  );
});

// Extracted metadata component
const MetadataInfo = React.memo(({ uuid, logUuid }: { uuid?: string, logUuid?: string }) => {
  return (
    <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex flex-wrap gap-1">
      <div className="bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded-md flex items-center">
        <FiInfo className="mr-1" size={10}/> UUID: {uuid || "-"}
      </div>
      <div className="bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded-md flex items-center">
        <FiInfo className="mr-1" size={10}/> Log: {logUuid || "-"}
      </div>
    </div>
  );
});

const DashboardActivityItem: React.FC<DashboardActivityItemProps> = ({
  activity,
  onRefresh,
}) => {
  const { branchCode } = useParams<{ branchCode: string }>();

  const isAlarm = useCallback(() => {
    return Boolean(activity.payload_data?.alarm);
  }, [activity.payload_data?.alarm]);

  const isAlarmNotFinished = useCallback(() => {
    return isAlarm() && !activity.payload_data?.alarm.end_date_time;
  }, [isAlarm, activity.payload_data?.alarm]);

  const cardClassName = useMemo(() => {
    const baseClass = "mb-3 hover:shadow-md transition-shadow";
    return isAlarmNotFinished()
      ? `${baseClass} border-orange-400 bg-orange-50 dark:bg-orange-900/20 dark:border-orange-700`
      : baseClass;
  }, [isAlarmNotFinished]);

  const handleDelete = async () => {
    if (!branchCode) {
      message.error("Branch code is missing");
      return;
    }

    try {
      await logAlertAdminApi.deleteFromDashboard(branchCode, activity.id);
      message.success("Activity deleted successfully");
      await onRefresh();
    } catch (error) {
      console.error("Error deleting activity:", error);
      message.error("Failed to delete activity");
    }
  };

  return (
    <Card
      className={cardClassName}
      size="small"
      title={
        <div className="flex items-center py-1">
          <span className="mr-2 text-sm font-medium">{activity.alert_event_name}</span>
          {isAlarm() && (
            <Tag color="orange" className="m-0 text-xs">Alarm</Tag>
          )}
        </div>
      }
      extra={
        <Tooltip title="Delete">
          <Button
            type="text"
            size="small"
            icon={<FiX/>}
            onClick={handleDelete}
            className="text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400"
          />
        </Tooltip>
      }
      bodyStyle={{padding: "12px"}}
      headStyle={{padding: "0 12px"}}
    >
      <div className="flex flex-col">
        <Typography.Text strong className="text-sm mb-1">
          {activity.reference_name}
        </Typography.Text>
        <Typography.Text type="secondary" className="text-xs flex items-center">
          <FiClock className="mr-1"/> {dayjs(activity.original_submitted_time).format("DD MMM YYYY HH:mm")}
        </Typography.Text>

        {/* User information */}
        <UserInfo user={activity.user} />

        {/* Metadata information */}
        <MetadataInfo uuid={activity.uuid} logUuid={activity.log_uuid} />
      </div>
    </Card>
  );
};

export default React.memo(DashboardActivityItem);
