import { StyleProvider } from "@ant-design/cssinjs";
import { ConfigProvider, theme } from "antd";
import { ReactNode } from "react";
import { useTheme } from "../context/ThemeContext";

interface Props {
  children: ReactNode;
}

export function AntdProvider({ children }: Props) {
  const { isDark } = useTheme();

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: "#428cf8",
        },
        algorithm: isDark
          ? theme.darkAlgorithm
          : theme.defaultAlgorithm,
      }}
      prefixCls="ant"
    >
      <StyleProvider hashPriority="low">
        {children}
      </StyleProvider>
    </ConfigProvider>
  );
}
