import baseMainApi from "../base.mainApi";
import {
  AdminZoneGetRequest,
  AdminZoneListResponse,
  AdminCreateUpdateZoneRequest,
} from "./types/zone.admin.mainApi.types";

// Extended request type to include status

const zoneAdminMainApi = {
  getZones: async (
    branchCode: string,
    params?: AdminZoneGetRequest
  ) => {
    const response =
      await baseMainApi.get<AdminZoneListResponse>(
        `/web-api/admin/${branchCode}/zone`,
        { params }
      );
    return response.data;
  },

  createZone: async (
    branchCode: string,
    data: AdminCreateUpdateZoneRequest
  ) => {
    const response =
      await baseMainApi.post<AdminZoneListResponse>(
        `/web-api/admin/${branchCode}/zone`,
        data
      );
    return response.data;
  },

  updateZone: async (
    branchCode: string,
    zoneId: string | number,
    data: AdminCreateUpdateZoneRequest
  ) => {
    const response =
      await baseMainApi.put<AdminZoneListResponse>(
        `/web-api/admin/${branchCode}/zone/${zoneId}`,
        data
      );
    return response.data;
  },
};

export default zoneAdminMainApi;
