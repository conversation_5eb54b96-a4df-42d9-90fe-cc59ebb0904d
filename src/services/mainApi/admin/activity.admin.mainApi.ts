import baseMainApi from "../base.mainApi";
import { BaseGetRequest } from "../types/base.mainApi.types";
import {
  CreateActivityRequest,
  AdminActivityListResponse,
  AdminActivityDetailResponse,
  UpdateActivityRequest,
} from "./types/activity.admin.mainApi.types";

export const activityAdminApi = {
  getActivities: async (
    branchCode: string,
    params?: BaseGetRequest
  ) => {
    const response =
      await baseMainApi.get<AdminActivityListResponse>(
        `/web-api/admin/${branchCode}/activity`,
        { params }
      );
    return response.data;
  },

  getActivity: async (branchCode: string, id: string) => {
    const response =
      await baseMainApi.get<AdminActivityDetailResponse>(
        `/web-api/admin/${branchCode}/activity/${id}`
      );
    return response.data;
  },

  createActivity: async (
    branchCode: string,
    data: CreateActivityRequest
  ) => {
    const response =
      await baseMainApi.post<AdminActivityDetailResponse>(
        `/web-api/admin/${branchCode}/activity`,
        data
      );
    return response.data;
  },

  updateActivity: async (
    branchCode: string,
    id: string,
    data: UpdateActivityRequest
  ) => {
    const response =
      await baseMainApi.put<AdminActivityDetailResponse>(
        `/web-api/admin/${branchCode}/activity/${id}`,
        data
      );
    return response.data;
  },
};
