import baseMainApi from "../base.mainApi";
import {
  BeaconListParams,
  BeaconListResponse,
  CreateBeaconDto,
  CreateBeaconResponse,
  UpdateBeaconDto,
  UpdateBeaconResponse,
} from "./types/beacon.mainApi.types";

const beaconSysAdminMainApi = {
  getList: async (params: BeaconListParams = {}) => {
    const response = await baseMainApi.get<BeaconListResponse>(
      "/web-api/sysadmin/beacons",
      {
        params: {
          search: params.search,
          active: params.active,
          page: params.page || 1,
          limit: params.limit || 10,
          start_date: params.start_date,
          end_date: params.end_date,
          order_by: params.order_by || "created_at",
          order_direction: params.order_direction || "DESC",
        },
      }
    );
    return response.data;
  },

  create: async (data: CreateBeaconDto) => {
    const response = await baseMainApi.post<CreateBeaconResponse>(
      "/web-api/sysadmin/beacons",
      data
    );
    return response.data;
  },

  update: async (id: string, data: UpdateBeaconDto) => {
    const response = await baseMainApi.put<UpdateBeaconResponse>(
      `/web-api/sysadmin/beacons/${id}`,
      data
    );
    return response.data;
  },
};

export default beaconSysAdminMainApi; 