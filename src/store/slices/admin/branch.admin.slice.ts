import {createAsyncThunk, createSlice, PayloadAction,} from "@reduxjs/toolkit";
import {RootState} from "../..";
import branchAdminMainApi from "../../../services/mainApi/admin/branch.admin.mainApi";
import {
  AdminBranch,
  AdminBranchGetRequestQuery
} from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import {BasePaginationMeta,} from "../../../services/mainApi/types/base.mainApi.types";

interface State {
  loading: boolean;
  errorMessage: string | null;
  branches: AdminBranch[];
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;
}

const initState: State = {
  loading: false,
  errorMessage: null,
  branches: [],
  filter: {
    search: null,
    page: null,
    limit: null,
    orderBy: "created_at",
    orderDirection: "ASC",
  },
  pagination: {
    total: 0,
  },
};

export const fetchBranches = createAsyncThunk(
  "branchAdmin/fetchBranches",
  async (_, {getState, rejectWithValue}) => {
    try {
      const state = getState() as RootState;
      const {filter} = state.branchAdmin;
      const params: AdminBranchGetRequestQuery = {
        search: filter.search || "",
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
        ignore_active_status: true,
      };
      const response = await branchAdminMainApi.getBranches(
        {
          ...params,
        }
      );
      return {
        data: response.data,
        meta: response.meta,
      };
    } catch {
      return rejectWithValue("Failed to fetch branches");
    }
  }
);

const branchAdminSlice = createSlice({
  name: "branchAdmin",
  initialState: {...initState},
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = {...state.filter, ...action.payload};
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchBranches.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(fetchBranches.fulfilled, (state, action) => {
        state.loading = false;
        state.branches = action.payload.data || [];
        state.pagination = action.payload.meta;
      })
      .addCase(fetchBranches.rejected, (state, action) => {
        state.loading = false;
        state.errorMessage = action.payload as string;
      });
  },
});

export default branchAdminSlice;
