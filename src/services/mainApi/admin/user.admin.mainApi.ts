import baseMain<PERSON>pi from "../base.mainApi";
import {
  AdminUserDetailResponse,
  AdminUserListResponse,
  CreateUpdateUserRequest,
  UpdateUserRequest,
  GetUserRequest,
} from "./types/user.admin.mainApi.types";

const userAdminMainApi = {
  getUsers: async (
    params: GetUserRequest,
    branchCode: string
  ) => {
    const response =
      await baseMainApi.get<AdminUserListResponse>(
        `/web-api/admin/${branchCode}/user`,
        { params }
      );
    return response.data;
  },

  getUserById: async (branchCode: string, id: string) => {
    const response =
      await baseMainApi.get<AdminUserDetailResponse>(
        `/web-api/admin/${branchCode}/user/${id}`
      );
    return response.data;
  },

  createUser: async (
    data: CreateUpdateUserRequest,
    branchCode: string
  ) => {
    const response =
      await baseMainApi.post<AdminUserDetailResponse>(
        `/web-api/admin/${branchCode}/user`,
        data
      );
    return response.data;
  },

  updateUser: async (
    data: UpdateUserRequest,
    branchCode: string,
    id: string
  ) => {
    const response =
      await baseMainApi.put<AdminUserDetailResponse>(
        `/web-api/admin/${branchCode}/user/${id}`,
        data
      );
    return response.data;
  },

  deleteUser: async (branchCode: string, id: string) => {
    const response =
      await baseMainApi.delete<AdminUserDetailResponse>(
        `/web-api/admin/branch/${branchCode}/users/${id}`
      );
    return response.data;
  },
};

export default userAdminMainApi;
