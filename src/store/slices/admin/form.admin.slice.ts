import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { AdminForm } from "../../../services/mainApi/admin/types/form.admin.mainApi.types";
import formAdminMainApi from "../../../services/mainApi/admin/form.admin.mainApi";
import { BasePaginationMeta } from "../../../services/mainApi/types/base.mainApi.types";
import { RootState } from "../../index";
import { debounce } from "lodash";

interface State {
  forms: AdminForm[];
  loading: boolean;
  errorMessage: string | null;
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
    ignoreActiveStatus: boolean;
  };
  pagination: BasePaginationMeta;
}

const initState: State = {
  forms: [],
  loading: false,
  errorMessage: null,
  filter: {
    search: null,
    page: 1,
    limit: 10,
    orderBy: "created_at",
    orderDirection: "DESC",
    ignoreActiveStatus: false,
  },
  pagination: {
    total: 0,
  },
};

export const fetchFormsAdmin = createAsyncThunk(
  "formAdmin/fetchForms",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const { filter } = state.formAdmin;
      const params = {
        search: filter.search || "",
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
        ignore_active_status: filter.ignoreActiveStatus,
      };
      const response = await formAdminMainApi.getForms(
        branchCode,
        {
          ...params,
        }
      );
      return {
        data: response.data,
        meta: response.meta,
      };
    } catch {
      return rejectWithValue("Failed to fetch forms");
    }
  }
);

// Create a debounced version of fetchForms for search
export const debouncedFetchForms = debounce(
  (dispatch, branchCode) => {
    dispatch(fetchFormsAdmin(branchCode));
  },
  500
);

const formAdminSlice = createSlice({
  name: "formAdmin",
  initialState: {
    ...initState,
  },
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchFormsAdmin.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchFormsAdmin.fulfilled,
        (state, action) => {
          state.loading = false;
          state.forms = action.payload.data || [];
          state.pagination = action.payload.meta;
        }
      )
      .addCase(
        fetchFormsAdmin.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      );
  },
});

export default formAdminSlice;
