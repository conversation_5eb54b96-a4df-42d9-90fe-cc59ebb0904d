.gps-heatmap-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.gps-heatmap {
  width: 100%;
  height: 100%;
  min-height: 400px;
  z-index: 1;
}

/* Fix untuk Safari */
.leaflet-container, .leaflet-container * {
  box-sizing: border-box;
}

/* Pastikan atribusi terbaca tapi tidak terlalu menonjol */
.leaflet-control-attribution {
  font-size: 9px;
  background-color: rgba(255, 255, 255, 0.7);
}

/* Styling untuk popup */
.leaflet-popup-content-wrapper {
  border-radius: 4px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

/* Dark mode support */
.dark .leaflet-tile {
  filter: brightness(0.6) invert(1) contrast(3) hue-rotate(200deg) saturate(0.3) brightness(0.7);
}

.dark .leaflet-container {
  background: #1f2937;
}

.dark .leaflet-control-attribution {
  background: rgba(31, 41, 55, 0.8) !important;
  color: #d1d5db !important;
}

.dark .leaflet-control-zoom a {
  background: #374151 !important;
  color: #d1d5db !important;
  border-color: #4b5563 !important;
}

.dark .leaflet-control-zoom a:hover {
  background: #4b5563 !important;
}

/* Responsive styling */
@media (max-width: 768px) {
  .gps-heatmap-container {
    height: 400px;
  }
}

@media (max-width: 480px) {
  .gps-heatmap-container {
    height: 300px;
  }
}

/* Make sure leaflet controls are visible with dark theme */
.leaflet-control-zoom {
  border: 1px solid rgba(0, 0, 0, 0.2);
  background-color: white;
}

.dark .leaflet-control-zoom {
  border-color: rgba(255, 255, 255, 0.2);
  background-color: rgba(30, 30, 30, 0.9);
}

.dark .leaflet-control-zoom a {
  color: white;
}

/* Fix for attribution text in dark theme */
.dark .leaflet-container .leaflet-control-attribution {
  background-color: rgba(0, 0, 0, 0.7) !important;
  color: #ddd !important;
}

.dark .leaflet-container .leaflet-control-attribution a {
  color: #9ca3af !important;
}

/* Loading overlay */
.gps-heatmap-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.dark .gps-heatmap-loading {
  background-color: rgba(17, 24, 39, 0.7);
}
