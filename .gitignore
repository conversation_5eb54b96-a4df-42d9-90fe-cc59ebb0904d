# Dependencies
node_modules
.pnp
.pnp.js
.yarn/install-state.gz

# Testing
coverage
/cypress/videos/
/cypress/screenshots/
/playwright-report/
/blob-report/
/test-results/

# Production
dist
dist-ssr
build
*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/launch.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.swp
*.swo
.project
.classpath
.settings/
*.sublime-workspace
*.sublime-project

# Environment files

# TypeScript
*.tsbuildinfo
.tscache/
.typescript/

# Cache and temporary files
.cache
.temp
.tmp
.eslintcache
.stylelintcache
.prettiercache

# Bundle analyzer
stats.html
bundle-analysis.html

# PWA files
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map

# Misc
.DS_Store
Thumbs.db
desktop.ini
.directory
*.pem
.vercel
.netlify
