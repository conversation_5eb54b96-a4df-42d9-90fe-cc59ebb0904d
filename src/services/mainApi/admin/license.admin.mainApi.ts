import type { AdminLicenseListResponse } from "./types/license.admin.mainApi.types";
import baseMainApi from "../base.mainApi";

const licenseAdminApi = {
  getList: async (
    branchCode: string
  ): Promise<AdminLicenseListResponse> => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/license`
    );
    return response.data;
  },
};

export default licenseAdminApi;
