import { Navigate } from "react-router-dom";
import { useAuth } from "../../hooks/useAuth";
import { Spin } from "antd";

const RootRedirect = () => {
  const auth = useAuth();

  if (auth.loading) {
    return (
      <div className="h-screen w-screen flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  if (!auth.isAuthenticated) {
    return (
      <Navigate
        to="/login"
        replace
      />
    );
  }

  if (auth.hasSystemAccess) {
    return (
      <Navigate
        to="/sysadmin/dashboard"
        replace
      />
    );
  }

  if (auth.hasWebAccess) {
    return (
      <Navigate
        to="/admin/branch"
        replace
      />
    );
  }

  return (
    <Navigate
      to="/login"
      replace
    />
  );
};

export default RootRedirect;
