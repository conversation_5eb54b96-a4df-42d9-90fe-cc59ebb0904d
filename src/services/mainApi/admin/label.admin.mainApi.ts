import baseMainApi from "../base.mainApi";
import {
  BaseGetRequest,
  BaseResponse,
} from "../types/base.mainApi.types";
import type {
  AdminLabel,
  AdminLabelDetail,
} from "./types/label.admin.mainApi.types";

interface CreateLabelRequest {
  label_name: string;
  label_description: string;
}

const labelAdminMainApi = {
  getLabels: async (
    branchCode: string,
    params?: BaseGetRequest
  ) => {
    const response = await baseMainApi.get<
      BaseResponse<AdminLabel[]>
    >(`/web-api/admin/${branchCode}/label`, { params });
    return response.data;
  },

  getLabelById: async (branchCode: string, id: string) => {
    const response = await baseMainApi.get<
      BaseResponse<AdminLabelDetail>
    >(`/web-api/admin/${branchCode}/label/${id}`);
    return response.data;
  },

  createLabel: async (
    branchCode: string,
    data: CreateLabelRequest
  ) => {
    const response = await baseMainApi.post<
      BaseResponse<AdminLabel>
    >(`/web-api/admin/${branchCode}/label`, data);
    return response.data;
  },

  updateLabel: async (
    branchCode: string,
    id: string,
    data: CreateLabelRequest
  ) => {
    const response = await baseMainApi.put<
      BaseResponse<AdminLabel>
    >(`/web-api/admin/${branchCode}/label/${id}`, data);
    return response.data;
  },

  deleteLabel: async (branchCode: string, id: string) => {
    const response = await baseMainApi.delete<
      BaseResponse<null>
    >(`/web-api/admin/${branchCode}/label/${id}`);
    return response.data;
  },
};

export default labelAdminMainApi;
