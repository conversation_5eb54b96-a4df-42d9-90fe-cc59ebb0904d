import React from "react";
import { useAppSelector } from "../../../../store/hooks";
import GeofenceListItem from "./GeofenceListItem";
import { Empty, Pagination, Spin } from "antd";
import { useTranslation } from "react-i18next";
import { useAppDispatch } from "../../../../store/hooks";
import { setFilter } from "../../../../store/slices/admin/geofence.admin.slice";

const GeofenceList: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { geofences, loading, filter, pagination } =
    useAppSelector((state) => state.geofenceAdmin);

  const handlePageChange = (
    page: number,
    pageSize: number
  ) => {
    dispatch(
      setFilter({
        page,
        limit: pageSize,
      })
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Spin size="large" />
      </div>
    );
  }

  if (!geofences || geofences.length === 0) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Empty
          description={
            <span className="text-gray-500 dark:text-gray-400">
              {t(
                "geofence.list.empty",
                "No geofences found"
              )}
            </span>
          }
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Geofences Grid */}
      <div className="grid grid-cols-1 gap-4">
        {geofences.map((geofence) => (
          <GeofenceListItem
            key={geofence.id}
            geofence={geofence}
          />
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <Pagination
          current={filter.page || 1}
          pageSize={filter.limit || 10}
          total={pagination.total}
          onChange={handlePageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              {t("common.pagination.total", { total })}
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

export default GeofenceList;
