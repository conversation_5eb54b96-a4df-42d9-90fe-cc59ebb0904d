import { useCallback, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks";
import { fetchLicenseAdmin } from "../../../../store/slices/admin/license.admin.slice";
import { Empty, Pagination, Spin } from "antd";
import { useParams } from "react-router-dom";
import LicenseItem from "./LicenseItem";

const LicenseList = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { licenses, loading, pagination } = useAppSelector(
    (state) => state.licenseAdmin
  );
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const isFetching = useRef(false);

  const fetch = useCallback(async () => {
    if (!branchCode) return;
    if (isFetching.current) return;

    isFetching.current = true;
    await dispatch(fetchLicenseAdmin(branchCode));
    isFetching.current = false;
  }, [branchCode, dispatch]);

  useEffect(() => {
    fetch();
  }, [fetch]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!licenses.length) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8">
        <Empty
          description={t(
            "license.list.empty",
            "No licenses found"
          )}
          className="dark:text-gray-400"
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4">
        {licenses.map((license) => (
          <LicenseItem
            key={license.id}
            license={license}
          />
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <Pagination
          current={pagination.page}
          pageSize={pagination.limit}
          total={pagination.total}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              Total {total} items
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

export default LicenseList;
