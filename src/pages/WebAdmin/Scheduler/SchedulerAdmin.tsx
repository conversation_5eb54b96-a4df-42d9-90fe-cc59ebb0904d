import { Button, Input, Select } from "antd";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {
  PlusOutlined,
  SearchOutlined,
  SortAscendingOutlined,
} from "@ant-design/icons";
import { MdSchedule } from "react-icons/md";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import { useTranslation } from "react-i18next";
import SchedulerList from "./Components/SchedulerList";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks";
import {
  fetchSchedulers,
  setFilter,
} from "../../../store/slices/admin/scheduler.admin.slice";
import { useCallback, useRef } from "react";
import _ from "lodash";
import { useParams } from "react-router-dom";
import ModalAddScheduler from "./Components/ModalAddScheduler/ModalAddScheduler";
import addSchedulerAdminSlice from "../../../store/slices/admin/addScheduler.admin.slice";

const { Option } = Select;

export default function SchedulerAdmin() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { filter } = useAppSelector(
    (state) => state.schedulerAdmin
  );
  const { branchCode } = useParams();
  const isFetching = useRef(false);
  const { actions } = addSchedulerAdminSlice;

  const fetch = useCallback(() => {
    if (isFetching.current) return;
    isFetching.current = true;
    dispatch(fetchSchedulers(branchCode || ""));
    isFetching.current = false;
  }, [dispatch, branchCode]);

  const debouncedSearchRef = useRef(
    _.debounce(() => {
      fetch();
    }, 500)
  );

  const handleSearch = useCallback(
    (value: string) => {
      dispatch(
        setFilter({ search: value || null, page: 1 })
      );
      debouncedSearchRef.current();
    },
    [dispatch]
  );

  const handleSortChange = useCallback(
    (value: string) => {
      dispatch(setFilter({ orderBy: value }));
    },
    [dispatch]
  );

  const handleSortDirectionToggle = useCallback(() => {
    dispatch(
      setFilter({
        orderDirection:
          filter.orderDirection === "ASC" ? "DESC" : "ASC",
      })
    );
  }, [dispatch, filter.orderDirection]);

  const handleOpenAddModal = useCallback(() => {
    dispatch(actions.open());
  }, [dispatch]);

  return (
    <WebAdminLayout activePage="scheduler">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdSchedule />}
            title={t("scheduler.title")}
            description={t("scheduler.description")}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={handleOpenAddModal}
              >
                {t("scheduler.addButton")}
              </Button>,
            ]}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder={t(
                  "scheduler.search.placeholder"
                )}
                prefix={<SearchOutlined />}
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                size="large"
                onChange={(e) =>
                  handleSearch(e.target.value)
                }
                allowClear
              />
              <div className="flex gap-2">
                <Select
                  defaultValue="created_at"
                  className="min-w-[160px] dark:bg-gray-700"
                  size="large"
                  onChange={handleSortChange}
                  value={filter.orderBy}
                >
                  <Option value="created_at">
                    {t(
                      "scheduler.sort.createdAt",
                      "Created Date"
                    )}
                  </Option>
                  <Option value="scheduler_name">
                    {t("scheduler.sort.name", "Name")}
                  </Option>
                </Select>
                <Button
                  icon={
                    <SortAscendingOutlined
                      rotate={
                        filter.orderDirection === "DESC"
                          ? 180
                          : 0
                      }
                    />
                  }
                  size="large"
                  onClick={handleSortDirectionToggle}
                  className="dark:border-gray-600 dark:text-gray-300"
                />
              </div>
            </div>
          </div>

          {/* Content Section */}
          <SchedulerList />
        </div>
      </div>

      {/* Modal */}
      <ModalAddScheduler />
    </WebAdminLayout>
  );
}
