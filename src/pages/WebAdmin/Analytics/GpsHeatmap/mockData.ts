// Mock data for GPS heatmap in Indonesia
// Data points are [latitude, longitude, intensity] format

// Major cities in Indonesia
const majorCities = [
  { name: "Jakarta", lat: -6.2088, lng: 106.8456 },
  { name: "Surabaya", lat: -7.2575, lng: 112.7521 },
  { name: "<PERSON><PERSON>", lat: -6.9175, lng: 107.6191 },
  { name: "Medan", lat: 3.5952, lng: 98.6722 },
  { name: "Semarang", lat: -6.9932, lng: 110.4203 },
  { name: "Makassar", lat: -5.1477, lng: 119.4327 },
  { name: "Palembang", lat: -2.9761, lng: 104.7754 },
  { name: "Yogyakarta", lat: -7.7971, lng: 110.3688 },
  { name: "Denpasar", lat: -8.6705, lng: 115.2126 },
  { name: "Balikpapan", lat: -1.2379, lng: 116.8529 },
  { name: "Manado", lat: 1.4748, lng: 124.8421 },
  { name: "Padang", lat: -0.9471, lng: 100.4172 },
  { name: "Pontianak", lat: 0.0263, lng: 109.3425 },
  { name: "Pekanbaru", lat: 0.5070, lng: 101.4478 },
  { name: "Banjarmasin", lat: -3.3186, lng: 114.5944 },
];

// Generate random points around major cities
const generatePoints = () => {
  const points: number[][] = [];
  
  // For each major city, generate a cluster of points
  majorCities.forEach(city => {
    // Number of points to generate varies by city size (Jakarta gets more)
    const numPoints = city.name === "Jakarta" ? 200 : 
                     ["Surabaya", "Bandung", "Medan"].includes(city.name) ? 120 : 
                     ["Semarang", "Makassar", "Denpasar"].includes(city.name) ? 80 : 40;
    
    for (let i = 0; i < numPoints; i++) {
      // Random offset from city center (smaller for larger cities to simulate density)
      const latOffset = (Math.random() - 0.5) * (city.name === "Jakarta" ? 0.1 : 0.2);
      const lngOffset = (Math.random() - 0.5) * (city.name === "Jakarta" ? 0.1 : 0.2);
      
      // Add intensity value (1 for center, diminishing outward)
      const distance = Math.sqrt(latOffset * latOffset + lngOffset * lngOffset);
      const intensity = Math.max(0.1, 1 - distance * 10);
      
      points.push([
        city.lat + latOffset,
        city.lng + lngOffset,
        intensity
      ]);
    }
  });
  
  // Add random scattered points across Indonesia
  for (let i = 0; i < 300; i++) {
    // Approximate bounds of Indonesia
    const lat = -10 + Math.random() * 15; // -10 to 5
    const lng = 95 + Math.random() * 35;  // 95 to 130
    const intensity = 0.1 + Math.random() * 0.3; // Lower intensity for random points
    
    points.push([lat, lng, intensity]);
  }
  
  return points;
};

// Generate static dataset
export const indonesiaGpsData = generatePoints();

// Generate time-based datasets (for filtering by time period)
export const getDataForTimeRange = (startDate: Date, endDate: Date) => {
  // In a real app, this would filter actual data by date
  // For mock purposes, we'll just return a subset of the points based on the date range
  
  // Convert dates to day numbers for simple calculation
  const startDay = startDate.getTime();
  const endDay = endDate.getTime();
  const totalDays = (endDay - startDay) / (1000 * 60 * 60 * 24);
  
  // Proportion of data to include (longer time ranges = more data)
  const proportion = Math.min(1, totalDays / 30);
  
  // Take a portion of the full dataset
  const pointsToInclude = Math.floor(indonesiaGpsData.length * proportion);
  
  return indonesiaGpsData.slice(0, pointsToInclude);
};

// Default export for the full dataset
export default indonesiaGpsData; 