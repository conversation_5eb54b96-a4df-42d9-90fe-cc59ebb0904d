import React from "react";
import { Dropdown } from "antd";
import type { MenuProps } from "antd";
import { useTranslation } from "react-i18next";

const languageFlags: Record<string, string> = {
  "en-US": "🇺🇸",
  "id-ID": "🇮🇩",
  "zh-CN": "🇨🇳",
  "mt-MT": "🇲🇹",
};

const LanguageSelector: React.FC = () => {
  const { i18n } = useTranslation();

  const items: MenuProps["items"] = [
    {
      key: "en-US",
      label: (
        <div className="flex items-center gap-2">
          <span>{languageFlags["en-US"]}</span>
          <span>English</span>
        </div>
      ),
    },
    {
      key: "id-ID",
      label: (
        <div className="flex items-center gap-2">
          <span>{languageFlags["id-ID"]}</span>
          <span>Indonesia</span>
        </div>
      ),
    },
    {
      key: "zh-CN",
      label: (
        <div className="flex items-center gap-2">
          <span>{languageFlags["zh-CN"]}</span>
          <span>中文</span>
        </div>
      ),
    },
    {
      key: "mt-MT",
      label: (
        <div className="flex items-center gap-2">
          <span>{languageFlags["mt-MT"]}</span>
          <span>Malta</span>
        </div>
      ),
    },
  ];

  const handleMenuClick: MenuProps["onClick"] = ({
    key,
  }) => {
    i18n.changeLanguage(key);
  };

  return (
    <Dropdown
      menu={{
        items,
        onClick: handleMenuClick,
        selectable: true,
        selectedKeys: [i18n.language],
      }}
      trigger={["click"]}
    >
      <button className="flex items-center justify-center w-10 h-10 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
        <span className="text-xl">
          {languageFlags[i18n.language]}
        </span>
      </button>
    </Dropdown>
  );
};

export default LanguageSelector;
