import React, { useState } from "react";
import { Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { FiCalendar, FiMapPin, FiChevronDown, FiChevronRight, FiCpu, FiInfo } from "react-icons/fi";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { AdminBranchDetailsLogBranchDetail } from "../../../../../services/mainApi/admin/types/branchDetailsLog.admin.mainApi.types";

dayjs.extend(relativeTime);

interface BranchDetailsLogItemProps {
  branchDetailsLog: AdminBranchDetailsLogBranchDetail;
}

const BranchDetailsLogItem: React.FC<BranchDetailsLogItemProps> = ({
  branchDetailsLog,
}) => {
  const { t } = useTranslation();
  const [expandedZones, setExpandedZones] = useState<Record<string, boolean>>({});

  const toggleZoneExpand = (zoneId: string) => {
    setExpandedZones(prev => ({
      ...prev,
      [zoneId]: !prev[zoneId]
    }));
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-0 p-4 mb-0 h-full flex flex-col">
      {/* Header with Branch Name as Title */}
      <div className="mb-3 -mx-4 -mt-4 px-4 py-3 bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-gray-700 dark:to-gray-800 rounded-t-xl border-b border-gray-100 dark:border-gray-700">
        <Tooltip title={branchDetailsLog.branch_name}>
          <h3 className="text-xl font-semibold text-gray-800 dark:text-white truncate">
            {branchDetailsLog.branch_name}
          </h3>
        </Tooltip>
      </div>

      {/* Branch Info Section */}
      <div className="flex flex-col gap-3 mb-4">
        {/* Branch ID */}
        <div className="flex items-center gap-2 text-sm">
          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
            <FiInfo className="w-4 h-4" />
          </div>
          <div className="flex flex-col">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {t("branchDetailsLog.branchId", "Branch ID")}
            </span>
            <span className="text-sm font-medium text-gray-800 dark:text-white">
              {branchDetailsLog.branch_id}
            </span>
          </div>
        </div>

        {/* Checkpoints & Zones Summary */}
        <div className="grid grid-cols-2 gap-3">
          {/* Total Checkpoints */}
          <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
                <FiCpu className="w-4 h-4" />
              </div>
              <div className="flex flex-col">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("branchDetailsLog.totalCheckpoints", "Checkpoints")}
                </span>
                <span className="text-lg font-semibold text-gray-800 dark:text-white">
                  {branchDetailsLog.total_checkpoints}
                </span>
              </div>
            </div>
          </div>

          {/* Total Zones */}
          <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 flex-shrink-0">
                <FiMapPin className="w-4 h-4" />
              </div>
              <div className="flex flex-col">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("branchDetailsLog.totalZones", "Zones")}
                </span>
                <span className="text-lg font-semibold text-gray-800 dark:text-white">
                  {branchDetailsLog.total_zones}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Zones Section - Accordion Style */}
      <div className="space-y-3 flex-grow overflow-auto">
        <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider flex items-center gap-2 pb-1 border-b border-gray-200 dark:border-gray-700">
          <FiMapPin className="w-4 h-4" />
          {t("branchDetailsLog.zones", "Zones")}
        </h4>
        
        {branchDetailsLog.zones.map((zone) => (
          <div 
            key={zone.zone_id} 
            className="border border-gray-100 dark:border-gray-700 rounded-lg overflow-hidden"
          >
            {/* Zone Header */}
            <div 
              className="flex items-center gap-2 text-sm bg-gray-50 dark:bg-gray-700 py-2 px-3 rounded-t-md cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
              onClick={() => toggleZoneExpand(zone.zone_id)}
            >
              <div className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
                {expandedZones[zone.zone_id] ? (
                  <FiChevronDown className="w-3 h-3" />
                ) : (
                  <FiChevronRight className="w-3 h-3" />
                )}
              </div>
              <div className="flex-grow flex items-center justify-between">
                <div className="flex flex-col">
                  <Tooltip title={zone.zone_name}>
                    <span className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate max-w-[150px]">
                      {zone.zone_name}
                    </span>
                  </Tooltip>
                </div>
                <div className="text-xs bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300 px-2 py-1 rounded-full">
                  {zone.checkpoint_count} {zone.checkpoint_count === 1 ? 'checkpoint' : 'checkpoints'}
                </div>
              </div>
            </div>

            {/* Checkpoints List */}
            {expandedZones[zone.zone_id] && (
              <div className="p-2 bg-white dark:bg-gray-800 max-h-60 overflow-y-auto">
                <div className="grid gap-2">
                  {zone.checkpoints.map((checkpoint) => (
                    <div key={checkpoint.id} className="flex flex-col p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-200 truncate">
                          {checkpoint.checkpoint_name}
                        </span>
                        {checkpoint.latest_check_time && (
                          <Tooltip title={dayjs(checkpoint.latest_check_time).format("DD MMM YYYY HH:mm:ss")}>
                            <span className="text-xs bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded">
                              {dayjs(checkpoint.latest_check_time).fromNow()}
                            </span>
                          </Tooltip>
                        )}
                      </div>
                      {checkpoint.latest_voltage && (
                        <div className="mt-2 flex items-center gap-1">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {t("branchDetailsLog.voltage", "Voltage")}:
                          </span>
                          <span className="text-xs font-medium text-amber-600 dark:text-amber-400">
                            {checkpoint.latest_voltage}V
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Footer with last update info */}
      <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 flex items-center text-xs text-gray-500 dark:text-gray-400">
        <FiCalendar className="mr-1 w-3 h-3" />
        {t("branchDetailsLog.lastUpdated", "Last updated")}: {dayjs().format("DD MMM YYYY")}
      </div>
    </div>
  );
};

export default BranchDetailsLogItem;
