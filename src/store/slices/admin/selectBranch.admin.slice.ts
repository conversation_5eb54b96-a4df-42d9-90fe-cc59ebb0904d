import {createAsyncThunk, createSlice, PayloadAction,} from "@reduxjs/toolkit";
import branchAdminMainApi from "../../../services/mainApi/admin/branch.admin.mainApi";
import {
  AdminBranch} from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";

interface State {
  loading: boolean;
  errorMessage: string | null;
  branches: AdminBranch[];
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
}

const initState: State = {
  loading: false,
  errorMessage: null,
  branches: [],
  filter: {
    search: null,
    page: null,
    limit: null,
    orderBy: "created_at",
    orderDirection: "ASC",
  },
};

export const fetchBranches = createAsyncThunk(
  "selectBranch/fetchBranches",
  async (_, {rejectWithValue}) => {
    try {
      const response =
        await branchAdminMainApi.selectBranch();
      return response.data;
    } catch {
      return rejectWithValue("Failed to fetch branches");
    }
  }
);

const selectBranchAdminSlice = createSlice({
  name: "selectBranch",
  initialState: {
    ...initState,
  },
  reducers: {
    setBranches: (state, action) => {
      state.branches = action.payload;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = {...state.filter, ...action.payload};
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchBranches.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(fetchBranches.fulfilled, (state, action) => {
        state.loading = false;
        state.branches = action.payload || [];
      })
      .addCase(fetchBranches.rejected, (state, action) => {
        state.loading = false;
        state.errorMessage = action.payload as string;
      });
  },
});

export default selectBranchAdminSlice;
