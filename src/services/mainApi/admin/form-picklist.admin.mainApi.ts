import baseMainApi from "../base.mainApi";
import {
  AdminFormPicklistGetRequest,
  AdminFormPicklistListResponse,
} from "./types/form-picklist.admin.mainApi.types";

const formPicklistAdminMainApi = {
  getFormPicklists: async (
    branchCode: string,
    params?: AdminFormPicklistGetRequest
  ) => {
    const response =
      await baseMainApi.get<AdminFormPicklistListResponse>(
        `/web-api/admin/${branchCode}/form/picklist`,
        { params }
      );
    return response.data;
  },
};

export default formPicklistAdminMainApi;
