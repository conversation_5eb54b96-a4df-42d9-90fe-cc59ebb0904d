import { <PERSON>, <PERSON>, But<PERSON>, Tooltip } from "antd";
import {
  EditOutlined,
  InfoCircleOutlined,
  BarcodeOutlined,
} from "@ant-design/icons";
import { Beacon } from "../../../../services/mainApi/sysadmin/types/beacon.mainApi.types.ts";
import { BeaconSkeleton } from "./BeaconSkeleton.tsx";
import dayjs from "dayjs";

interface BeaconListProps {
  beacons: Beacon[];
  loading: boolean;
  onEdit: (beacon: Beacon) => void;
  onDelete: (beacon: Beacon) => void; // still required by props, but will not be used
}

export default function BeaconList({
  beacons,
  loading,
  onEdit,
}: BeaconListProps) {
  if (loading) {
    return <BeaconSkeleton />;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
      {beacons.map((beacon) => (
        <div key={beacon.id} className="h-full">
          <Card
            className="shadow-sm hover:shadow-md transition-all duration-200 flex flex-col h-full bg-white dark:bg-gray-800 dark:border-gray-700"
            style={{ height: "100%", position: "relative" }}
            bodyStyle={{ flex: 1, padding: 0 }}
          >
            {/* Top section: Edit button (right), Status tag (left) */}
            <div className="flex items-start justify-between px-5 pt-5 pb-2">
              <Tag
                className={`text-xs px-3 py-1 rounded-full font-medium border-0 ${
                  beacon.active
                    ? "bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-400"
                    : "bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-400"
                }`}
              >
                {beacon.active ? "Active" : "Inactive"}
              </Tag>
              <Tooltip title="Edit Beacon">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => onEdit(beacon)}
                  className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-gray-700/50"
                  style={{ fontSize: 18 }}
                />
              </Tooltip>
            </div>

            {/* Main content */}
            <div className="flex flex-col gap-2 px-5 pb-5">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-1 truncate">
                {beacon.beacon_name}
              </h3>
              <div className="flex items-center text-gray-500 dark:text-gray-400 text-sm mb-1">
                <span className="font-medium">Branch:</span>
                <span className="ml-1 truncate">{beacon.parent_branch.branch_name}</span>
              </div>
              <div className="flex items-center text-gray-400 dark:text-gray-500 text-xs mb-2">
                <span>Created:</span>
                <span className="ml-1">{dayjs(beacon.created_at).format("DD MMM YYYY HH:mm")}</span>
              </div>
              <div className="flex items-center text-gray-600 dark:text-gray-300 text-sm mb-1">
                <BarcodeOutlined className="mr-2 text-gray-500 dark:text-gray-400" />
                <span className="truncate">UUID: {beacon.beacon_uuid.slice(0, 12)}...</span>
              </div>
              {beacon.beacon_description && (
                <div className="flex items-start text-gray-600 dark:text-gray-300 text-sm">
                  <InfoCircleOutlined className="mr-2 mt-1 text-gray-500 dark:text-gray-400" />
                  <span className="whitespace-pre-line">{beacon.beacon_description}</span>
                </div>
              )}
            </div>
          </Card>
        </div>
      ))}

      {beacons.length === 0 && (
        <div className="col-span-full flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
          <p className="text-lg">No beacons found</p>
          <p className="text-sm">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  );
} 