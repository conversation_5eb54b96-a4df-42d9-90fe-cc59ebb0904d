import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import schedulerAdminMainApi from "../../../services/mainApi/admin/scheduler.admin.mainApi";
import {
  AdminScheduler,
  CreateSchedulerRequest,
  EReportFormatType,
  EReportType,
  ERportFrequency,
  ERotationMethod,
} from "../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { isAxiosError } from "axios";
import { handleApiError } from "../../../utils/error.ts";
import dayjs from "dayjs";

interface State {
  open: boolean;
  mode: "create" | "update";
  errorMessage: string | null;
  loading: boolean;
  selectedScheduler: AdminScheduler | null;

  // Form fields
  scheduler_name: string;
  scheduler_description: string;
  report_type_id: EReportType;
  report_format_type: EReportFormatType | string;
  frequency_id: ERportFrequency;
  generate_date: string;
  generate_time: string;
  stop_if_blank: boolean;
  detailed_report: boolean;
  active: boolean;

  start_time: string;
  end_time: string;
  selected_branch_id: string | null;
  role_id: string | null;
  user_id: string | null;
  user_label_ids: string[];
  device_id: string | null;
  device_label_ids: string[];
  zone_id: string | null;
  zone_label_ids: string[];
  checkpoint_id: string | null;
  checkpoint_label_ids: string[];
  form_id: string | null;
  activity_id: string | null;
  rotation_method: ERotationMethod | string;
  rotation_interval: number;

  subject: string;
  message: string;
  email: string[];
}

const initialState: State = {
  open: false,
  mode: "create",
  errorMessage: null,
  loading: false,
  selectedScheduler: null,

  // Form fields with initial values
  scheduler_name: "",
  scheduler_description: "",
  report_type_id: EReportType.ACTIVITY_LOG,
  report_format_type: EReportFormatType.PDF,
  frequency_id: ERportFrequency.DAILY,
  generate_date: dayjs().format("YYYY-MM-DD"),
  generate_time: "00:01",
  stop_if_blank: false,
  detailed_report: false,
  active: true,

  start_time: "00:00:00",
  end_time: "23:59:59",
  selected_branch_id: "",
  role_id: null,
  user_id: "",
  user_label_ids: [],
  device_id: null,
  device_label_ids: [],
  zone_id: null,
  zone_label_ids: [],
  checkpoint_id: null,
  checkpoint_label_ids: [],
  form_id: null,
  activity_id: null,
  rotation_method: ERotationMethod.COUNT,
  rotation_interval: 30,

  subject: "",
  message: "",
  email: [],
};

// Helper function to convert string IDs to numbers
const convertIdsToNumbers = (
  values: string[]
): number[] => {
  return values.map((id) => Number(id));
};

// Helper function to convert string ID to number or null
const convertIdToNumberOrNull = (
  id: string | null
): number | null => {
  return id ? Number(id) : null;
};

// Extended request type to include optional fields
interface ExtendedSchedulerRequest
  extends CreateSchedulerRequest {
  start_period?: string;
  end_period?: string;
}

export const createScheduler = createAsyncThunk(
  "addSchedulerAdmin/createScheduler",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const {
        scheduler_name,
        scheduler_description,
        report_type_id,
        report_format_type,
        frequency_id,
        generate_date,
        generate_time,
        stop_if_blank,
        detailed_report,
        active,
        start_time,
        end_time,
        selected_branch_id,
        role_id,
        user_id,
        user_label_ids,
        device_id,
        device_label_ids,
        zone_id,
        zone_label_ids,
        checkpoint_id,
        checkpoint_label_ids,
        form_id,
        activity_id,
        rotation_method,
        rotation_interval,
        subject,
        message,
        email,
      } = state.addSchedulerAdmin;

      const payload: ExtendedSchedulerRequest = {
        scheduler_name,
        scheduler_description: scheduler_description || "",
        report_type_id: Number(report_type_id),
        report_format_type,
        frequency_id: Number(frequency_id),
        generate_date,
        generate_time,
        stop_if_blank,
        detailed_report,
        start_time,
        end_time,
        selected_branch_id: convertIdToNumberOrNull(
          selected_branch_id
        ),
        role_id: convertIdToNumberOrNull(role_id),
        user_id: convertIdToNumberOrNull(user_id),
        user_label_ids: convertIdsToNumbers(user_label_ids),
        device_id: convertIdToNumberOrNull(device_id),
        device_label_ids: convertIdsToNumbers(
          device_label_ids
        ),
        zone_id: convertIdToNumberOrNull(zone_id),
        zone_label_ids: convertIdsToNumbers(zone_label_ids),
        checkpoint_id:
          convertIdToNumberOrNull(checkpoint_id),
        checkpoint_label_ids: convertIdsToNumbers(
          checkpoint_label_ids
        ),
        form_id: convertIdToNumberOrNull(form_id),
        activity_id: convertIdToNumberOrNull(activity_id),
        rotation_interval,
        rotation_method,
        subject,
        message,
        email,
        active,
      };

      const response =
        await schedulerAdminMainApi.createScheduler(
          branchCode,
          payload
        );

      return response.data;
    } catch (error) {
      if (isAxiosError(error)) {
        return rejectWithValue(
          handleApiError(error).errors
        );
      }
      return rejectWithValue("Failed to create scheduler");
    }
  }
);

export const updateScheduler = createAsyncThunk(
  "addSchedulerAdmin/updateScheduler",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const {
        scheduler_name,
        scheduler_description,
        report_type_id,
        report_format_type,
        frequency_id,
        generate_date,
        generate_time,
        stop_if_blank,
        detailed_report,
        active,
        start_time,
        end_time,
        selected_branch_id,
        role_id,
        user_id,
        user_label_ids,
        device_id,
        device_label_ids,
        zone_id,
        zone_label_ids,
        checkpoint_id,
        checkpoint_label_ids,
        form_id,
        activity_id,
        rotation_method,
        rotation_interval,
        subject,
        message,
        email,
        selectedScheduler,
      } = state.addSchedulerAdmin;

      if (!selectedScheduler) {
        return rejectWithValue(
          "Schedule ID is required for update"
        );
      }

      const payload: ExtendedSchedulerRequest = {
        scheduler_name,
        scheduler_description: scheduler_description || "",
        report_type_id: Number(report_type_id),
        report_format_type,
        frequency_id: Number(frequency_id),
        generate_date,
        generate_time,
        stop_if_blank,
        detailed_report,
        start_time,
        end_time,
        selected_branch_id: convertIdToNumberOrNull(
          selected_branch_id
        ),
        role_id: convertIdToNumberOrNull(role_id),
        user_id: convertIdToNumberOrNull(user_id),
        user_label_ids: convertIdsToNumbers(user_label_ids),
        device_id: convertIdToNumberOrNull(device_id),
        device_label_ids: convertIdsToNumbers(
          device_label_ids
        ),
        zone_id: convertIdToNumberOrNull(zone_id),
        zone_label_ids: convertIdsToNumbers(zone_label_ids),
        checkpoint_id:
          convertIdToNumberOrNull(checkpoint_id),
        checkpoint_label_ids: convertIdsToNumbers(
          checkpoint_label_ids
        ),
        form_id: convertIdToNumberOrNull(form_id),
        activity_id: convertIdToNumberOrNull(activity_id),
        rotation_interval,
        rotation_method,
        subject,
        message,
        email,
        active,
      };

      const response =
        await schedulerAdminMainApi.updateScheduler(
          branchCode,
          selectedScheduler.id,
          payload
        );

      return response.data;
    } catch (error) {
      if (isAxiosError(error)) {
        return rejectWithValue(
          handleApiError(error).errors
        );
      }
      return rejectWithValue("Failed to update scheduler");
    }
  }
);

const addSchedulerAdminSlice = createSlice({
  name: "addSchedulerAdmin",
  initialState,
  reducers: {
    open: (state) => {
      state.open = true;
      state.mode = "create";
    },
    openEdit: (
      state,
      action: PayloadAction<AdminScheduler>
    ) => {
      state.open = true;
      state.mode = "update";
      state.selectedScheduler = action.payload;
      state.scheduler_name = action.payload.scheduler_name;
      state.scheduler_description =
        action.payload.scheduler_description || "";
      state.report_type_id = action.payload
        .report_type_id as unknown as EReportType;
      state.report_format_type =
        action.payload.report_format_type;
      state.frequency_id = action.payload
        .frequency_id as unknown as ERportFrequency;
      state.generate_date = action.payload.generate_date;
      state.generate_time = action.payload.generate_time;
      state.stop_if_blank = action.payload.stop_if_blank;
      state.detailed_report =
        action.payload.detailed_report;
      state.active = action.payload.active;
      state.start_time = action.payload.start_time;
      state.end_time = action.payload.end_time;
      state.selected_branch_id =
        action.payload.selected_branch_id;
      state.role_id = action.payload.role_id;
      state.user_id = action.payload.user_id;
      state.user_label_ids =
        action.payload.user_labels.map(l => l.id) || [];
      state.device_id = action.payload.device_id;
      state.device_label_ids =
        action.payload.device_labels.map(l => l.id) || [];
      state.zone_id = action.payload.zone_id;
      state.zone_label_ids =
        action.payload.zone_labels.map(l => l.id) || [];
      state.checkpoint_id = action.payload.checkpoint_id;
      state.checkpoint_label_ids =
        action.payload.checkpoint_labels.map(l => l.id) || [];
      state.form_id = action.payload.form_id;
      state.activity_id = action.payload.activity_id;
      state.rotation_method =
        action.payload.rotation_method;
      state.rotation_interval =
        action.payload.rotation_interval;
      state.subject = action.payload.subject;
      state.message = action.payload.message;
      state.email = action.payload.recipients.map(
        (recipient) => recipient.recipient_contact
      );
    },
    close: () => {
      return {
        ...initialState,
      };
    },
    setError: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.errorMessage = action.payload;
    },
    // Form field setters
    setName: (state, action: PayloadAction<string>) => {
      state.scheduler_name = action.payload;
    },
    setDescription: (
      state,
      action: PayloadAction<string>
    ) => {
      state.scheduler_description = action.payload;
    },
    setSelectedBranchId: (
      state,
      action: PayloadAction<string>
    ) => {
      state.selected_branch_id = action.payload;
    },
    setRoleId: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.role_id = action.payload;
    },
    setUserId: (state, action: PayloadAction<string>) => {
      state.user_id = action.payload;
      if (state.user_id) {
        state.user_label_ids = [];
      }
    },
    setUserLabelIds: (
      state,
      action: PayloadAction<string[]>
    ) => {
      state.user_label_ids = action.payload;
    },
    setReportTypeId: (
      state,
      action: PayloadAction<EReportType>
    ) => {
      state.report_type_id = action.payload;
    },
    setDeviceId: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.device_id = action.payload;
      if (state.device_id) {
        state.device_label_ids = [];
      }
    },
    setDeviceLabelIds: (
      state,
      action: PayloadAction<string[]>
    ) => {
      state.device_label_ids = action.payload;
    },
    setZoneId: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.zone_id = action.payload;
      if (state.zone_id) {
        state.zone_label_ids = [];
      }
    },
    setZoneLabelIds: (
      state,
      action: PayloadAction<string[]>
    ) => {
      state.zone_label_ids = action.payload;
    },
    setCheckpointId: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.checkpoint_id = action.payload;
      if (state.checkpoint_id) {
        state.checkpoint_label_ids = [];
      }
    },
    setCheckpointLabelIds: (
      state,
      action: PayloadAction<string[]>
    ) => {
      state.checkpoint_label_ids = action.payload;
    },
    setFormId: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.form_id = action.payload;
    },
    setActivityId: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.activity_id = action.payload;
    },
    setFrequencyId: (
      state,
      action: PayloadAction<ERportFrequency>
    ) => {
      state.frequency_id = action.payload;
    },
    setReportFormatType: (
      state,
      action: PayloadAction<string>
    ) => {
      state.report_format_type = action.payload;
    },
    setGenerateDate: (
      state,
      action: PayloadAction<string>
    ) => {
      state.generate_date = action.payload;
    },
    setGenerateTime: (
      state,
      action: PayloadAction<string>
    ) => {
      state.generate_time = action.payload;
    },
    setStartTime: (
      state,
      action: PayloadAction<string>
    ) => {
      state.start_time = action.payload;
    },
    setEndTime: (state, action: PayloadAction<string>) => {
      state.end_time = action.payload;
    },
    setStopIfBlank: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.stop_if_blank = action.payload;
    },
    setDetailedReport: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.detailed_report = action.payload;
    },
    setSubject: (state, action: PayloadAction<string>) => {
      state.subject = action.payload;
    },
    setMessage: (state, action: PayloadAction<string>) => {
      state.message = action.payload;
    },
    setRotationMethod: (
      state,
      action: PayloadAction<string>
    ) => {
      state.rotation_method = action.payload;
    },
    setRotationInterval: (
      state,
      action: PayloadAction<number>
    ) => {
      state.rotation_interval = action.payload;
    },
    setEmail: (state, action: PayloadAction<string[]>) => {
      state.email = action.payload;
    },
    setActive: (state, action: PayloadAction<boolean>) => {
      state.active = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create scheduler
      .addCase(createScheduler.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(createScheduler.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(
        createScheduler.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      )
      // Update scheduler
      .addCase(updateScheduler.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(updateScheduler.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(
        updateScheduler.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      );
  },
});

export default addSchedulerAdminSlice;
