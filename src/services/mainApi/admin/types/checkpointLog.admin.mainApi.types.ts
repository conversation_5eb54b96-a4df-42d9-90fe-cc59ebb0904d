import {BaseResponse} from "../../types/base.mainApi.types";
import {AdminCheckpoint} from "./checkpoint.admin.mainApi.types";
import {AdminRole} from "./roles.admin.mainApi.types";
import {AdminBranch} from "./branch.admin.mainApi.types";
import {AdminZone} from "./zone.admin.mainApi.types";
import {AdminUser} from "./user.admin.mainApi.types.ts";
import {Timezone} from "../../types/timezone.mainApi.types.ts";
import {AdminDevice} from "./device.admin.mainApi.types.ts";

// Checkpoint type interface
interface CheckpointType {
  id: string;
  checkpoint_type_name: string;
  checkpoint_type_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

export interface AdminCheckpointLogEntry {
  id: string;
  uuid: string;
  parent_branch_id: string;
  branch_id: string;
  branch_name: string;
  role_id: string | null;
  role_name: string | null;
  user_id: string;
  user_name: string;
  device_id: string | null;
  device_name: string | null;
  timezone_id: string;
  timezone_name: string;
  latitude: number | null;
  longitude: number | null;
  checkpoint_id: string;
  checkpoint_name: string;
  zone_id: string;
  zone_name: string;
  is_required_day: boolean | null;
  target_scan_count: number | null;
  is_beacon: boolean;
  major_value: string;
  minor_value: string;
  checkpoint_type_id: string;
  serial_number: string;
  original_submitted_time: string;
  event_time: string;

  // Relations
  checkpoint: AdminCheckpoint;
  zone: AdminZone;
  role: AdminRole | null;
  user: AdminUser;
  device: AdminDevice | null;
  branch: AdminBranch;
  parent_branch: AdminBranch;
  timezone: Timezone;
  checkpoint_type: CheckpointType;
}

export interface AdminCheckpointLogListParams {
  page?: number;
  limit?: number;
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  branch_id?: string | number;
  zone_id?: string | number;
  zone_labels?: string | number | (string | number)[];
  checkpoint_id?: string | number;
  checkpoint_labels?: string | number | (string | number)[];
  role_id?: string | number;
  user_id?: string | number;
  user_labels?: string | number | (string | number)[];
  device_id?: string | number;
  device_labels?: string | number | (string | number)[];
  order_by?: string;
  order_direction?: "ASC" | "DESC";
}

export type AdminCheckpointLogListResponse = BaseResponse<
  AdminCheckpointLogEntry[]
>;
export type AdminCheckpointLogDetailResponse =
  BaseResponse<AdminCheckpointLogEntry>;
