/**
 * Map Component with marker and geofence navigation capabilities
 *
 * @example
 * ```tsx
 * // Create a ref to access navigation methods
 * const mapRef = useRef<MapRef>(null);
 *
 * // Navigate to specific marker
 * mapRef.current?.goToMarkerById('marker-1');
 *
 * // Navigate through markers
 * mapRef.current?.goToNextMarker();
 * mapRef.current?.goToPrevMarker();
 *
 * // Navigate through geofences
 * mapRef.current?.goToGeofenceById('geofence-1');
 * mapRef.current?.goToNextGeofence();
 * mapRef.current?.goToPrevGeofence();
 * ```
 */

import {MapContainer, Marker, Polygon, Popup, TileLayer} from "react-leaflet";
import {icon, LatLng, LatLngBounds, Map as LeafletMap} from "leaflet";
import "leaflet/dist/leaflet.css";
import {forwardRef, useImperativeHandle, useRef, useState} from "react";
import {convertToLocalTimezone} from "../../utils/dateUtils";

// Fix untuk ikon marker default
const defaultIcon = icon({
  iconUrl: "/marker-icon.png",
  shadowUrl: "/marker-shadow.png",
  iconSize: [25, 41],
  iconAnchor: [12, 41],
});

// Icon for highlighted markers
const highlightedIcon = icon({
  iconUrl: "/marker-icon-red.png", // Assuming there's a red marker icon available
  shadowUrl: "/marker-shadow.png",
  iconSize: [25, 41],
  iconAnchor: [12, 41],
});

interface MarkerData {
  id: string;
  position: [number, number]; // Explicitly type as tuple
  title: string;
  description: string;
  date: string; // ISO date string
}

interface GeofenceData {
  id: string;
  geofence: [number, number][]; // Array of coordinate tuples
}

interface MapProps {
  geofences: GeofenceData[];
  checkpointActivityMarkers: MarkerData[];
  geolocationActivityMarkers: MarkerData[];
}

export interface MapRef {
  goToMarkerById: (id: string) => void;
  goToPrevMarker: () => void;
  goToNextMarker: () => void;
  goToGeofenceById: (id: string) => void;
  goToPrevGeofence: () => void;
  goToNextGeofence: () => void;
  goToGeolocationMarkerById: (id: string) => void;
  goToPrevGeolocationMarker: () => void;
  goToNextGeolocationMarker: () => void;
}

const Map = forwardRef<MapRef, MapProps>(({geofences, checkpointActivityMarkers, geolocationActivityMarkers = []}, ref) => {
  const mapRef = useRef<LeafletMap | null>(null);
  const [currentMarkerIndex, setCurrentMarkerIndex] = useState<number>(0);
  const [currentGeofenceIndex, setCurrentGeofenceIndex] = useState<number>(0);
  const [currentGeolocationMarkerIndex, setCurrentGeolocationMarkerIndex] = useState<number>(0);

  // Calculate bounds for auto-centering
  const calculateBounds = () => {
    const bounds = new LatLngBounds([]);

    // Add checkpoint marker positions to bounds
    checkpointActivityMarkers.forEach(marker => {
      bounds.extend(new LatLng(marker.position[0], marker.position[1]));
    });

    // Add geolocation marker positions to bounds
    geolocationActivityMarkers.forEach(marker => {
      bounds.extend(new LatLng(marker.position[0], marker.position[1]));
    });

    // Add geofence coordinates to bounds
    geofences.forEach(({geofence}) => {
      geofence.forEach(coord => {
        bounds.extend(new LatLng(coord[0], coord[1]));
      });
    });

    return bounds;
  };

  const bounds = calculateBounds();

  // Helper function to calculate center of geofence
  const calculateGeofenceCenter = (geofence: [number, number][]): [number, number] => {
    const lat = geofence.reduce((sum, coord) => sum + coord[0], 0) / geofence.length;
    const lng = geofence.reduce((sum, coord) => sum + coord[1], 0) / geofence.length;
    return [lat, lng];
  };

  // Navigation functions
  const goToPosition = (position: [number, number], zoom: number = 18) => {
    mapRef.current?.flyTo(position, zoom, {
      duration: 1.5, // Animation duration in seconds
    });
  };

  const goToMarkerById = (id: string) => {
    const marker = checkpointActivityMarkers.find(m => m.id === id);
    if (marker) {
      const index = checkpointActivityMarkers.indexOf(marker);
      setCurrentMarkerIndex(index);
      goToPosition(marker.position);
    }
  };

  const goToPrevMarker = () => {
    const newIndex = (currentMarkerIndex - 1 + checkpointActivityMarkers.length) % checkpointActivityMarkers.length;
    setCurrentMarkerIndex(newIndex);
    goToPosition(checkpointActivityMarkers[newIndex].position);
  };

  const goToNextMarker = () => {
    const newIndex = (currentMarkerIndex + 1) % checkpointActivityMarkers.length;
    setCurrentMarkerIndex(newIndex);
    goToPosition(checkpointActivityMarkers[newIndex].position);
  };

  const goToGeofenceById = (id: string) => {
    const geofence = geofences.find(g => g.id === id);
    if (geofence) {
      const index = geofences.indexOf(geofence);
      setCurrentGeofenceIndex(index);
      goToPosition(calculateGeofenceCenter(geofence.geofence));
    }
  };

  const goToPrevGeofence = () => {
    const newIndex = (currentGeofenceIndex - 1 + geofences.length) % geofences.length;
    setCurrentGeofenceIndex(newIndex);
    goToPosition(calculateGeofenceCenter(geofences[newIndex].geofence));
  };

  const goToNextGeofence = () => {
    const newIndex = (currentGeofenceIndex + 1) % geofences.length;
    setCurrentGeofenceIndex(newIndex);
    goToPosition(calculateGeofenceCenter(geofences[newIndex].geofence));
  };

  // Geolocation marker navigation functions
  const goToGeolocationMarkerById = (id: string) => {
    const marker = geolocationActivityMarkers.find(m => m.id === id);
    if (marker) {
      const index = geolocationActivityMarkers.indexOf(marker);
      setCurrentGeolocationMarkerIndex(index);
      goToPosition(marker.position);
    }
  };

  const goToPrevGeolocationMarker = () => {
    if (geolocationActivityMarkers.length === 0) return;
    const newIndex = (currentGeolocationMarkerIndex - 1 + geolocationActivityMarkers.length) % geolocationActivityMarkers.length;
    setCurrentGeolocationMarkerIndex(newIndex);
    goToPosition(geolocationActivityMarkers[newIndex].position);
  };

  const goToNextGeolocationMarker = () => {
    if (geolocationActivityMarkers.length === 0) return;
    const newIndex = (currentGeolocationMarkerIndex + 1) % geolocationActivityMarkers.length;
    setCurrentGeolocationMarkerIndex(newIndex);
    goToPosition(geolocationActivityMarkers[newIndex].position);
  };

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    goToMarkerById,
    goToPrevMarker,
    goToNextMarker,
    goToGeofenceById,
    goToPrevGeofence,
    goToNextGeofence,
    goToGeolocationMarkerById,
    goToPrevGeolocationMarker,
    goToNextGeolocationMarker,
  }));

  return (
    <MapContainer
      ref={mapRef}
      bounds={
        bounds.isValid() ? bounds : undefined
      }
      className="h-full w-full"
      zoomControl={true}
    >
      <TileLayer
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      />

      {/* Render Geofences */}
      {geofences.map(({id, geofence}) => (
        <Polygon
          key={`geofence-${id}`}
          positions={geofence}
          pathOptions={{
            color: currentGeofenceIndex === geofences.findIndex(g => g.id === id) ? 'red' : 'blue',
            fillColor: currentGeofenceIndex === geofences.findIndex(g => g.id === id) ? 'red' : 'blue',
            fillOpacity: 0.2,
            weight: currentGeofenceIndex === geofences.findIndex(g => g.id === id) ? 3 : 2,
          }}
        />
      ))}

      {/* Render Checkpoint Activity Markers */}
      {checkpointActivityMarkers.map((marker, index) => (
        <Marker
          key={`checkpoint-marker-${marker.id}`}
          position={marker.position}
          icon={index === currentMarkerIndex ? highlightedIcon : defaultIcon}
        >
          <Popup>
            <div>
              <h3 className="font-bold">{marker.title}</h3>
              <p>{marker.description}</p>
              <p className="text-sm text-gray-500">
                {convertToLocalTimezone(marker.date)?.format("DD MMM YYYY HH:mm")}
              </p>
            </div>
          </Popup>
        </Marker>
      ))}

      {/* Render Geolocation Activity Markers */}
      {geolocationActivityMarkers.map((marker, index) => (
        <Marker
          key={`geolocation-marker-${marker.id}`}
          position={marker.position}
          icon={index === currentGeolocationMarkerIndex ? highlightedIcon : defaultIcon}
        >
          <Popup>
            <div>
              <h3 className="font-bold">{marker.title}</h3>
              <p>{marker.description}</p>
              <p className="text-sm text-gray-500">
                {convertToLocalTimezone(marker.date)?.format("DD MMM YYYY HH:mm")}
              </p>
            </div>
          </Popup>
        </Marker>
      ))}
    </MapContainer>
  );
});

Map.displayName = "Map";

export default Map;
