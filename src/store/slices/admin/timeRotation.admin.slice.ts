import {createAsyncThunk, createSlice, PayloadAction,} from "@reduxjs/toolkit";
import {AdminRole} from "../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import {AdminBranch} from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import {AdminZone} from "../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import {timeRotationAdminMainApi} from "../../../services/mainApi/admin/timeRotation.admin.mainApi";
import {
  TimeRotationCheckpoint
} from "../../../services/mainApi/admin/types/timeRotation.admin.mainApi.types";
import {RootState} from "../../../store";
import {isAxiosError} from "axios";
import {handleApiError} from "../../../utils/error.ts";
import { AdminLabel } from "../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminUser } from "../../../services/mainApi/admin/types/user.admin.mainApi.types.ts";
import { AdminCheckpoint } from "../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types.ts";
import { AdminDevice } from "../../../services/mainApi/admin/types/device.admin.mainApi.types.ts";
import dayjs from "dayjs";

export interface TimeRotationFilter {
  page: number;
  limit: number;
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branchId: string | null;
  zoneId: string | null;
  zoneLabels: (string | number)[];
  checkpointId: string | null;
  checkpointLabels: (string | number)[];
  roleId: string | null;
  userId: string | null;
  userLabels: (string | number)[];
  deviceId: string | null;
  deviceLabels: (string | number)[];
  rotationInterval: number | null;
  rotationMethod: string | null;
}

interface TimeRotationState {
  timeRotationData: TimeRotationCheckpoint[];
  timeRotationLogs: TimeRotationCheckpoint[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages?: number;
    timestamp?: string;
  };
  filter: TimeRotationFilter;

  // Filter selection objects
  selectedBranch: AdminBranch | null;
  selectedZone: AdminZone | null;
  selectedZoneLabels: AdminLabel[];
  selectedCheckpoint: AdminCheckpoint | null;
  selectedCheckpointLabels: AdminLabel[];
  selectedRole: AdminRole | null;
  selectedUser: AdminUser | null;
  selectedUserLabels: AdminLabel[];
  selectedDevice: AdminDevice | null;
  selectedDeviceLabels: AdminLabel[];
}

// Initial filter state
const initialFilter: TimeRotationFilter = {
  page: 1,
  limit: 10,
  startDate: dayjs().subtract(1, "day").format("YYYY-MM-DD"),
  endDate: dayjs().format("YYYY-MM-DD"),
  startTime: "00:00:00",
  endTime: "23:59:59",
  branchId: null,
  zoneId: null,
  zoneLabels: [],
  checkpointId: null,
  checkpointLabels: [],
  roleId: null,
  userId: null,
  userLabels: [],
  deviceId: null,
  deviceLabels: [],
  rotationInterval: 10,
  rotationMethod: "COUNT",
};

// Initial state
const initialState: TimeRotationState = {
  timeRotationData: [],
  timeRotationLogs: [],
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
  },
  filter: initialFilter,
  selectedBranch: null,
  selectedZone: null,
  selectedZoneLabels: [],
  selectedCheckpoint: null,
  selectedCheckpointLabels: [],
  selectedRole: null,
  selectedUser: null,
  selectedUserLabels: [],
  selectedDevice: null,
  selectedDeviceLabels: [],
};

// Async thunk for fetching time rotation logs
export const fetchTimeRotationLogs = createAsyncThunk(
  "timeRotation/fetchTimeRotationLogs",
  async (
    {branchCode}: { branchCode: string },
    {getState, rejectWithValue}
  ) => {
    try {
      const state = getState() as RootState;
      const timeRotationAdmin = state.timeRotationAdmin;

      const params = {
        page: timeRotationAdmin.filter.page,
        limit: timeRotationAdmin.filter.limit,
        start_date: timeRotationAdmin.filter.startDate || undefined,
        end_date: timeRotationAdmin.filter.endDate || undefined,
        start_time: timeRotationAdmin.filter.startTime || undefined,
        end_time: timeRotationAdmin.filter.endTime || undefined,
        branch_id: timeRotationAdmin.filter.branchId || undefined,
        zone_id: timeRotationAdmin.filter.zoneId || undefined,
        zone_labels: timeRotationAdmin.filter.zoneLabels.length > 0 ? timeRotationAdmin.filter.zoneLabels.join(',') : undefined,
        checkpoint_id: timeRotationAdmin.filter.checkpointId || undefined,
        checkpoint_labels: timeRotationAdmin.filter.checkpointLabels.length > 0 ? timeRotationAdmin.filter.checkpointLabels.join(',') : undefined,
        role_id: timeRotationAdmin.filter.roleId || undefined,
        user_id: timeRotationAdmin.filter.userId || undefined,
        user_labels: timeRotationAdmin.filter.userLabels.length > 0 ? timeRotationAdmin.filter.userLabels.join(',') : undefined,
        device_id: timeRotationAdmin.filter.deviceId || undefined,
        device_labels: timeRotationAdmin.filter.deviceLabels.length > 0 ? timeRotationAdmin.filter.deviceLabels.join(',') : undefined,
        rotation_interval: timeRotationAdmin.filter.rotationInterval || undefined,
        rotation_method: timeRotationAdmin.filter.rotationMethod || undefined,
      };

      // Call the API
      return await timeRotationAdminMainApi.getTimeRotationLogs(branchCode, params);

    } catch (error) {
      if (isAxiosError(error)) {
        const e = handleApiError(error).errors;
        return rejectWithValue(e);
      }
      return rejectWithValue(
        error instanceof Error
          ? error.message
          : "An error occurred"
      );
    }
  }
);

// Slice
const timeRotationAdminSlice = createSlice({
  name: "timeRotationAdmin",
  initialState,
  reducers: {
    // Pagination actions
    setPage: (state, action: PayloadAction<number>) => {
      state.filter.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.filter.limit = action.payload;
      state.filter.page = 1; // Reset to first page when changing limit
    },
    // Filter actions
    setStartDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startDate = action.payload;
    },
    setEndDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endDate = action.payload;
    },
    setStartTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startTime = action.payload;
    },
    setEndTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endTime = action.payload;
    },
    setSelectedRole: (
      state,
      action: PayloadAction<AdminRole | null>
    ) => {
      state.selectedRole = action.payload;
      state.filter.roleId = action.payload?.id || null;
    },
    setSelectedZone: (
      state,
      action: PayloadAction<AdminZone | null>
    ) => {
      state.selectedZone = action.payload;
      state.filter.zoneId = action.payload?.id || null;
    },
    setSelectedBranch: (
      state,
      action: PayloadAction<AdminBranch | null>
    ) => {
      state.selectedBranch = action.payload;
      state.filter.branchId = action.payload?.id || null;
    },
    setZoneLabels: (
      state,
      action: PayloadAction<(string | number)[] | null>
    ) => {
      state.filter.zoneLabels = action.payload || [];
    },
    setSelectedZoneLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedZoneLabels = action.payload;
      state.filter.zoneLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setCheckpointId: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.checkpointId = action.payload;
    },
    setSelectedCheckpoint: (
      state,
      action: PayloadAction<AdminCheckpoint | null>
    ) => {
      state.selectedCheckpoint = action.payload;
      state.filter.checkpointId = action.payload?.id || null;
    },
    setCheckpointLabels: (
      state,
      action: PayloadAction<(string | number)[] | null>
    ) => {
      state.filter.checkpointLabels = action.payload || [];
    },
    setSelectedCheckpointLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedCheckpointLabels = action.payload;
      state.filter.checkpointLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setSelectedUser: (
      state,
      action: PayloadAction<AdminUser | null>
    ) => {
      state.selectedUser = action.payload;
      state.filter.userId = action.payload?.id || null;
    },
    setUserLabels: (
      state,
      action: PayloadAction<(string | number)[] | null>
    ) => {
      state.filter.userLabels = action.payload || [];
    },
    setSelectedUserLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedUserLabels = action.payload;
      state.filter.userLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setSelectedDevice: (
      state,
      action: PayloadAction<AdminDevice | null>
    ) => {
      state.selectedDevice = action.payload;
      state.filter.deviceId = action.payload?.id || null;
    },
    setDeviceLabels: (
      state,
      action: PayloadAction<(string | number)[] | null>
    ) => {
      state.filter.deviceLabels = action.payload || [];
    },
    setSelectedDeviceLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedDeviceLabels = action.payload;
      state.filter.deviceLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setRotationInterval: (
      state,
      action: PayloadAction<number | null>
    ) => {
      state.filter.rotationInterval = action.payload;
    },
    setRotationMethod: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.rotationMethod = action.payload;
    },
    clearFilters: (state) => {
      state.filter = {
        ...initialFilter,
        page: state.filter.page,
        limit: state.filter.limit,
      };
      state.selectedRole = null;
      state.selectedZone = null;
      state.selectedZoneLabels = [];
      state.selectedCheckpoint = null;
      state.selectedCheckpointLabels = [];
      state.selectedBranch = null;
      state.selectedUser = null;
      state.selectedUserLabels = [];
      state.selectedDevice = null;
      state.selectedDeviceLabels = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Time Rotation Logs
      .addCase(
        fetchTimeRotationLogs.pending,
        (state) => {
          state.loading = true;
          state.error = null;
        }
      )
      .addCase(
        fetchTimeRotationLogs.fulfilled,
        (state, action) => {
          state.loading = false;
          state.timeRotationLogs = action.payload.data || [];
          state.timeRotationData = action.payload.data || []; // For backward compatibility
          state.pagination.total = action.payload.meta?.total || 0;
          state.pagination.page = action.payload.meta?.page || 1;
          state.pagination.limit = action.payload.meta?.limit || 10;
          state.pagination.total_pages = action.payload.meta?.total_pages || 1;
          state.pagination.timestamp = action.payload.meta?.timestamp;
        }
      )
      .addCase(
        fetchTimeRotationLogs.rejected,
        (state, action) => {
          state.loading = false;
          state.error = action.error.message || "Failed to fetch time rotation logs";
        }
      );
  },
});

// Export the slice directly so actions can be accessed via timeRotationAdminSlice.actions.method()
export default timeRotationAdminSlice;
