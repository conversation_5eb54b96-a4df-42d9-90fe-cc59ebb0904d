import baseMainApi from "../base.mainApi";
import {
  AdminFormLogListResponse,
  AdminFormLogListParams,
} from "./types/formLog.admin.mainApi.types";

export const formLogAdminApi = {
  getFormLogs: async (
    branchCode: string,
    params?: AdminFormLogListParams
  ) => {
    const response =
      await baseMainApi.get<AdminFormLogListResponse>(
        `/web-api/admin/${branchCode}/form-log`,
        { params }
      );
    return response.data;
  },

  exportFormLogs: async (
    branchCode: string,
    params: AdminFormLogListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/form-log/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },

  exportFormLogById: async (
    branchCode: string,
    id: string,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/form-log/export/${id}/${format}/${saveType}`,
      {
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
};
