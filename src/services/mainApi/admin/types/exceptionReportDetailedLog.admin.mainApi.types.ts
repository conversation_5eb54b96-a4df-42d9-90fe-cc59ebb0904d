import {BaseResponse} from "../../types/base.mainApi.types";

export interface VisitInfo {
  date: string;
  timezone_id: string;
  timezone_name: string;
}

export interface AdminExceptionReportDetailedLogEntry {
  checkpoint_id: string;
  checkpoint_name: string;
  serial_number_hex: string;
  serial_number_dec: string;
  major_value: number;
  minor_value: number;
  checkpoint_type_id: number;
  expected_visit_time: number;
  actual_visit_time: number;
  missed_visit_time: number;
  visit_info: VisitInfo[];
}

export interface AdminExceptionReportDetailedLogListParams {
  page?: number;
  limit?: number;
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  order_by?: string;
  order_direction?: "ASC" | "DESC";
  branch_id?: string | number;
  role_id?: string | number;
  user_id?: string | number;
  user_labels?: string | number | (string | number)[];
  site_id?: string | number;
  site_labels?: string | number | (string | number)[];
  checkpoint_id?: string | number;
  checkpoint_labels?: string | number | (string | number)[];
  device_id?: string | number;
  device_labels?: string | number | (string | number)[];
}

export type AdminExceptionReportDetailedLogListResponse = BaseResponse<
  AdminExceptionReportDetailedLogEntry[]
>;
