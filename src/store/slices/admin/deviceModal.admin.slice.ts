import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import deviceAdminMainApi from "../../../services/mainApi/admin/device.admin.mainApi";
import {
  AdminDevice,
  CreateDeviceRequest,
  EDeviceType,
} from "../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { RootState } from "../..";
import { fetchDevicesAdmin } from "./device.admin.slice";
import { isAxiosError } from "axios";
import { handleApiError } from "../../../utils/error";

interface DeviceModalState {
  visible: boolean;
  isLoading: boolean;
  isSubmitting: boolean;
  mode: "create" | "update";
  currentDevice: AdminDevice | null;
  formData: {
    device_name: string;
    uniguard_device_type_id: EDeviceType | null;
    serial_number: string;
    imei: string;
    device_description: string;
    label_ids: string[];
  };
  errorMessages: string[] | null;
}

const initialState: DeviceModalState = {
  visible: false,
  isLoading: false,
  isSubmitting: false,
  mode: "create",
  currentDevice: null,
  formData: {
    device_name: "",
    uniguard_device_type_id: null,
    serial_number: "",
    imei: "",
    device_description: "",
    label_ids: [],
  },
  errorMessages: null,
};

export const fetchDeviceById = createAsyncThunk(
  "deviceModal/fetchDeviceById",
  async (
    {
      branchCode,
      deviceId,
    }: { branchCode: string; deviceId: string },
    { rejectWithValue }
  ) => {
    try {
      const response =
        await deviceAdminMainApi.getDeviceById(
          branchCode,
          deviceId
        );
      return response.data;
    } catch (error) {
      if (isAxiosError(error)) {
        return rejectWithValue(
          handleApiError(error).errors
        );
      }
      return rejectWithValue(
        "Failed to fetch device details"
      );
    }
  }
);

export const createDevice = createAsyncThunk(
  "deviceModal/createDevice",
  async (
    {
      branchCode,
      data,
    }: { branchCode: string; data: CreateDeviceRequest },
    { dispatch, rejectWithValue }
  ) => {
    try {
      const response =
        await deviceAdminMainApi.createDevice(
          branchCode,
          data
        );
      // Refresh the device list
      await dispatch(fetchDevicesAdmin(branchCode));
      return response.data;
    } catch (error) {
      if (isAxiosError(error)) {
        return rejectWithValue(
          handleApiError(error).errors
        );
      }
      return rejectWithValue("Failed to create device");
    }
  }
);

export const updateDevice = createAsyncThunk(
  "deviceModal/updateDevice",
  async (
    {
      branchCode,
      deviceId,
      data,
    }: {
      branchCode: string;
      deviceId: string;
      data: CreateDeviceRequest;
    },
    { dispatch, rejectWithValue }
  ) => {
    try {
      const response =
        await deviceAdminMainApi.updateDevice(
          branchCode,
          deviceId,
          {
            ...data,
          }
        );
      // Refresh the device list
      await dispatch(fetchDevicesAdmin(branchCode));
      return response.data;
    } catch (error) {
      if (isAxiosError(error)) {
        return rejectWithValue(
          handleApiError(error).errors
        );
      }
      return rejectWithValue("Failed to update device");
    }
  }
);

const deviceModalSlice = createSlice({
  name: "deviceModal",
  initialState: {
    ...initialState,
  },
  reducers: {
    openCreateModal: (state) => {
      state.visible = true;
      state.mode = "create";
      state.currentDevice = null;
      state.formData = initialState.formData;
      state.errorMessages = null;
    },
    openUpdateModal: (
      state,
      action: PayloadAction<AdminDevice>
    ) => {
      state.visible = true;
      state.mode = "update";
      state.currentDevice = action.payload;
      state.formData = {
        device_name: action.payload.device_name,
        uniguard_device_type_id:
          action.payload.uniguard_device_type_id,
        serial_number: action.payload.serial_number || "",
        imei: action.payload.imei || "",
        device_description:
          action.payload.device_description || "",
        label_ids:
          action.payload.labels?.map((label) => label.id) ||
          [],
      };
      state.errorMessages = null;
    },
    closeModal: () => {
      return { ...initialState };
    },
    setDeviceName: (
      state,
      action: PayloadAction<string>
    ) => {
      state.formData.device_name = action.payload;
    },
    setDeviceTypeId: (
      state,
      action: PayloadAction<EDeviceType>
    ) => {
      state.formData.uniguard_device_type_id =
        action.payload;
    },
    setImei: (state, action: PayloadAction<string>) => {
      state.formData.imei = action.payload;
    },
    setSerialNumber: (
      state,
      action: PayloadAction<string>
    ) => {
      state.formData.serial_number = action.payload;
    },
    setDeviceDescription: (
      state,
      action: PayloadAction<string>
    ) => {
      state.formData.device_description = action.payload;
    },
    setLabelIds: (
      state,
      action: PayloadAction<string[]>
    ) => {
      state.formData.label_ids = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDeviceById.pending, (state) => {
        state.isLoading = true;
        state.errorMessages = null;
      })
      .addCase(
        fetchDeviceById.fulfilled,
        (state, action) => {
          state.isLoading = false;
          state.currentDevice = action.payload || null;
          state.formData = {
            device_name: action.payload?.device_name || "",
            uniguard_device_type_id:
              action.payload?.uniguard_device_type_id ||
              null,
            serial_number:
              action.payload?.serial_number || "",
            imei: action.payload?.imei || "",
            device_description:
              action.payload?.device_description || "",
            label_ids:
              action.payload?.labels?.map(
                (label) => label.id
              ) || [],
          };
        }
      )
      .addCase(
        fetchDeviceById.rejected,
        (state, action) => {
          state.isLoading = false;
          state.errorMessages = action.payload as string[];
        }
      )
      .addCase(createDevice.pending, (state) => {
        state.isSubmitting = true;
        state.errorMessages = null;
      })
      .addCase(createDevice.fulfilled, (state) => {
        state.isSubmitting = false;
        state.visible = false;
        state.formData = initialState.formData;
      })
      .addCase(createDevice.rejected, (state, action) => {
        state.isSubmitting = false;
        state.errorMessages = action.payload as string[];
      })
      .addCase(updateDevice.pending, (state) => {
        state.isSubmitting = true;
        state.errorMessages = null;
      })
      .addCase(updateDevice.fulfilled, (state) => {
        state.isSubmitting = false;
        state.visible = false;
      })
      .addCase(updateDevice.rejected, (state, action) => {
        state.isSubmitting = false;
        state.errorMessages = action.payload as string[];
      });
  },
});

export const selectDeviceModalState = (state: RootState) =>
  state.deviceModal;

export default {
  reducer: deviceModalSlice.reducer,
  actions: deviceModalSlice.actions,
};
