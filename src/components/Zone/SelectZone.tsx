import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import zoneAdminMainApi from "../../services/mainApi/admin/zone.admin.mainApi";
import { AdminZone } from "../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { useParams } from "react-router-dom";

interface SelectZoneProps {
  value?: string;
  onChange?: (
    value: string,
    zone: AdminZone | null
  ) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  clearable?: boolean;
  query?: {
    assigned_to_geofence_id?: boolean;
  };
}

export default function SelectZone({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select zone",
  clearable = true,
  query,
}: SelectZoneProps) {
  const [zones, setZones] = useState<AdminZone[]>([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);
  const { branchCode } = useParams();

  useEffect(() => {
    const fetchZones = async () => {
      if (isFetching.current) return;

      try {
        isFetching.current = true;
        setLoading(true);
        const response = await zoneAdminMainApi.getZones(
          branchCode || "",
          {
            assigned_to_geofence:
              query?.assigned_to_geofence_id,
          }
        );
        setZones(response.data || []);
      } catch (error) {
        console.error("Failed to fetch zones:", error);
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchZones();
  }, [branchCode]);

  const handleChange = (
    selectedValue: string | undefined
  ) => {
    if (!onChange) return;

    if (!selectedValue) {
      onChange(selectedValue || "", null);
      return;
    }

    const selectedZone =
      zones.find((zone) => zone.id === selectedValue) ||
      null;
    onChange(selectedValue, selectedZone);
  };

  return (
    <Select
      className={className}
      placeholder={placeholder}
      value={value || undefined}
      onChange={handleChange}
      disabled={disabled}
      loading={loading}
      showSearch
      optionFilterProp="label"
      options={zones.map((zone) => ({
        value: zone.id,
        label: zone.zone_name,
      }))}
      allowClear={clearable}
    />
  );
}
