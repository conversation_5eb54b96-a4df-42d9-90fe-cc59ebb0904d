import React from "react";
import MainLayout from "./MainLayout";
import { useMenuConfig } from "../../config/routes/menuConfig";

interface SysAdminLayoutProps {
  children: React.ReactNode;
  activePage: string;
}

export default function SysAdminLayout({
  children,
  activePage,
}: SysAdminLayoutProps) {
  const { sysAdminConfig } = useMenuConfig();

  return (
    <MainLayout
      menuItems={sysAdminConfig.menuItems}
      menuPaths={sysAdminConfig.menuPaths}
      activePage={activePage}
      layoutTitle="System Admin"
    >
      {children}
    </MainLayout>
  );
}
