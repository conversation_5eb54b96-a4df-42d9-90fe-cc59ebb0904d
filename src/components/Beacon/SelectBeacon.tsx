import { Select, Spin } from "antd";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import beaconAdminMainApi from "../../services/mainApi/admin/beacon.admin.mainApi";
import { AdminBeacon } from "../../services/mainApi/admin/types/beacon.admin.mainApi.types";
import { SelectProps } from "antd/es/select";

interface SelectBeaconProps
  extends Omit<SelectProps, "options"> {
  value?: string;
  onChange?: (value: string) => void;
}

const SelectBeacon = ({
  value,
  onChange,
  ...props
}: SelectBeaconProps) => {
  const [loading, setLoading] = useState(false);
  const [beacons, setBeacons] = useState<AdminBeacon[]>([]);
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  useEffect(() => {
    const fetchBeacons = async () => {
      if (!branchCode) return;

      setLoading(true);
      try {
        const response =
          await beaconAdminMainApi.getBeacons(branchCode);
        setBeacons(response.data || []);
      } catch (error) {
        console.error("Error fetching beacons:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchBeacons();
  }, [branchCode]);

  return (
    <Select
      showSearch
      placeholder="Select beacon"
      optionFilterProp="children"
      value={value}
      onChange={onChange}
      loading={loading}
      notFoundContent={
        loading ? <Spin size="small" /> : null
      }
      filterOption={(input, option) =>
        (
          option?.label?.toString().toLowerCase() || ""
        ).includes(input.toLowerCase())
      }
      options={beacons.map((beacon) => ({
        value: beacon.id,
        label: beacon.beacon_name,
      }))}
      {...props}
    />
  );
};

export default SelectBeacon;
