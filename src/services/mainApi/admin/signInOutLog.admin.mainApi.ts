import baseMainApi from "../base.mainApi";
import {
  AdminSignInOutLogListParams,
  AdminSignInOutLogListResponse,
} from "./types/signInOutLog.admin.mainApi.types";

export const signInOutLogAdminApi = {
  getSignInOutLogs: async (
    branchCode: string,
    params?: AdminSignInOutLogListParams
  ) => {
    const response =
      await baseMainApi.get<AdminSignInOutLogListResponse>(
        `/web-api/admin/${branchCode}/sign-in-out-log`,
        { params }
      );
    return response.data;
  },

  exportSignInOutLogs: async (
    branchCode: string,
    params: AdminSignInOutLogListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/sign-in-out-log/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },

  exportSignInOutLogById: async (
    branchCode: string,
    id: string,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/sign-in-out-log/export/${id}/${format}/${saveType}`,
      {
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
};
