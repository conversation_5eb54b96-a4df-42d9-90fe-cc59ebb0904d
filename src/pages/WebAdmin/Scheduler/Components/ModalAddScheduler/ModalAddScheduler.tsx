import {
  App,
  But<PERSON>,
  DatePicker,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Switch,
  Tag,
  TimePicker,
  Tooltip,
} from "antd";
import { useTranslation } from "react-i18next";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks";
import {
  InfoCircleOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { MdSchedule as ScheduleIcon } from "react-icons/md";
import { useParams } from "react-router-dom";
import ErrorMessage from "../../../../../components/Common/ErrorMessage/ErrorMessage";
import { fetchSchedulers } from "../../../../../store/slices/admin/scheduler.admin.slice";
import GridSelectBranch from "../../../../../components/Branch/GridSelectBranch";
import { useState } from "react";
import dayjs from "dayjs";
import {
  EReportType,
  ERportFrequency,
} from "../../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import addSchedulerAdminSlice, {
  createScheduler,
  updateScheduler,
} from "../../../../../store/slices/admin/addScheduler.admin.slice";
import SchedulerFormFields from "./SchedulerFormFields.tsx";

// Placeholder components - These would need to be implemented or imported
// In a real implementation, create these components similar to SelectRole
const SelectReportType = ({
  value,
  onChange,
}: {
  value?: string;
  onChange: (value: string) => void;
}) => {
  const { t } = useTranslation();
  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder="Select a report type"
      className="w-full [&_.ant-select-selector]:dark:bg-gray-700 [&_.ant-select-selector]:dark:border-gray-600 [&_.ant-select-selection-item]:dark:text-gray-200"
    >
      {Object.values(EReportType).map((reportType) => (
        <Select.Option
          key={reportType}
          value={reportType}
        >
          {t(`common.reportType.options.${reportType}`)}
        </Select.Option>
      ))}
    </Select>
  );
};

const SelectFrequency = ({
  value,
  onChange,
}: {
  value?: string;
  onChange: (value: string) => void;
}) => {
  const { t } = useTranslation();
  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder="Select frequency"
      className="w-full [&_.ant-select-selector]:dark:bg-gray-700 [&_.ant-select-selector]:dark:border-gray-600 [&_.ant-select-selection-item]:dark:text-gray-200"
    >
      {Object.values(ERportFrequency).map((frequency) => (
        <Select.Option
          key={frequency}
          value={frequency}
        >
          {t(`common.reportFrequency.options.${frequency}`)}
        </Select.Option>
      ))}
    </Select>
  );
};
const { TextArea } = Input;
const { Option } = Select;

const ModalAddScheduler = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { actions } = addSchedulerAdminSlice;
  const { message } = App.useApp();
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const modal = useAppSelector(
    (state) => state.addSchedulerAdmin
  );

  const [emailInput, setEmailInput] = useState<string>("");

  const handleClose = async () => {
    dispatch(actions.close());
  };

  const onSubmit = async () => {
    dispatch(actions.setError(null));
    try {
      if (modal.mode === "update") {
        const update = await dispatch(
          updateScheduler(branchCode || "")
        );
        if (update.meta.requestStatus === "rejected") {
          throw new Error("Failed to update scheduler");
        }
      } else {
        const create = await dispatch(
          createScheduler(branchCode || "")
        );
        if (create.meta.requestStatus === "rejected") {
          throw new Error("Failed to create scheduler");
        }
      }
      message.success("Scheduler saved successfully");
      handleClose();
      await dispatch(fetchSchedulers(branchCode || ""));
    } catch {
      message.error(
        "An unexpected error occurred while saving the scheduler"
      );
    }
  };

  // Handle email tag input
  const handleEmailInputChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setEmailInput(e.target.value);
  };

  const handleEmailInputConfirm = () => {
    if (
      emailInput &&
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailInput) &&
      !modal.email.includes(emailInput)
    ) {
      dispatch(
        actions.setEmail([...modal.email, emailInput])
      );
    }
    setEmailInput("");
  };

  const handleEmailRemove = (removedEmail: string) => {
    const newEmails = modal.email.filter(
      (email) => email !== removedEmail
    );
    dispatch(actions.setEmail(newEmails));
  };

  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <ScheduleIcon className="text-blue-500" />
          <span>
            {modal.mode === "update"
              ? t("scheduler.modal.editTitle")
              : t("scheduler.modal.addTitle")}
          </span>
        </div>
      }
      open={modal.open}
      onOk={onSubmit}
      onCancel={handleClose}
      width={800}
      className="rounded-xl"
      okButtonProps={{
        className: "bg-blue-500 hover:bg-blue-600 border-0",
      }}
    >
      <Form
        layout="vertical"
        className="mt-4"
      >
        {/* Basic Information Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
            <h3 className="text-lg font-medium">
              {t("scheduler.modal.sections.basic.title")}
            </h3>
            <Tooltip
              title={t(
                "scheduler.modal.sections.basic.tooltip"
              )}
            >
              <InfoCircleOutlined className="text-gray-400" />
            </Tooltip>
          </div>

          <div className="p-6 rounded-lg space-y-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600">
            {/* Scheduler Name & Description */}
            <div className="grid grid-cols-1 gap-4">
              <Form.Item
                label={t("scheduler.modal.form.name.label")}
                required
                tooltip={t(
                  "scheduler.modal.form.name.tooltip"
                )}
              >
                <Input
                  placeholder={t(
                    "scheduler.modal.form.name.placeholder"
                  )}
                  className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                  value={modal.scheduler_name}
                  onChange={(e) =>
                    dispatch(
                      actions.setName(e.target.value)
                    )
                  }
                />
              </Form.Item>

              <Form.Item
                label={t(
                  "scheduler.modal.form.description.label"
                )}
                tooltip={t(
                  "scheduler.modal.form.description.tooltip"
                )}
              >
                <TextArea
                  rows={3}
                  placeholder={t(
                    "scheduler.modal.form.description.placeholder"
                  )}
                  className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                  value={modal.scheduler_description || ""}
                  onChange={(e) =>
                    dispatch(
                      actions.setDescription(e.target.value)
                    )
                  }
                />
              </Form.Item>
            </div>

            {/* Report Format */}
            <Form.Item
              label={t(
                "scheduler.modal.form.reportFormat.label",
                "Report Format"
              )}
              required
              tooltip={t(
                "scheduler.modal.form.reportFormat.tooltip",
                "Select the format for generated reports"
              )}
            >
              <Select
                value={modal.report_format_type}
                onChange={(value) =>
                  dispatch(
                    actions.setReportFormatType(value)
                  )
                }
                className="[&_.ant-select-selector]:dark:bg-gray-700 [&_.ant-select-selector]:dark:border-gray-600 [&_.ant-select-selection-item]:dark:text-gray-200"
              >
                <Option value="PDF">
                  {t(
                    "common.reportFormat.options.pdf",
                    "PDF"
                  )}
                </Option>
                <Option value="SPREADSHEET">
                  {t(
                    "common.reportFormat.options.spreadsheet",
                    "Spreadsheet"
                  )}
                </Option>
              </Select>
            </Form.Item>
          </div>
        </div>

        {/* Schedule Configuration Section */}
        <div className="mt-8 space-y-4">
          <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
            <h3 className="text-lg font-medium">
              {t("scheduler.modal.sections.schedule.title")}
            </h3>
            <Tooltip
              title={t(
                "scheduler.modal.sections.schedule.tooltip"
              )}
            >
              <InfoCircleOutlined className="text-gray-400" />
            </Tooltip>
          </div>

          <div className="p-6 rounded-lg space-y-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600">
            {/* Frequency */}
            <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
              <Form.Item
                label={t(
                  "scheduler.modal.form.frequency.label",
                  "Frequency"
                )}
                required
                tooltip={t(
                  "scheduler.modal.form.frequency.tooltip",
                  "Select how often this report should be generated"
                )}
              >
                <SelectFrequency
                  value={modal.frequency_id || undefined}
                  onChange={(value: string) =>
                    dispatch(
                      actions.setFrequencyId(
                        value as ERportFrequency
                      )
                    )
                  }
                />
              </Form.Item>
            </div>

            {/* Generation Time */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                label={t(
                  "scheduler.modal.form.generate.date.label"
                )}
                required
                tooltip={t(
                  "scheduler.modal.form.generate.date.tooltip"
                )}
              >
                <DatePicker
                  value={
                    modal.generate_date
                      ? dayjs(modal.generate_date)
                      : undefined
                  }
                  className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                  onChange={(date) => {
                    if (date) {
                      dispatch(
                        actions.setGenerateDate(
                          date.format("YYYY-MM-DD")
                        )
                      );
                    }
                  }}
                  disabledDate={(current) => {
                    return (
                      current &&
                      current < dayjs().startOf("day")
                    );
                  }}
                />
              </Form.Item>

              <Form.Item
                label={t(
                  "scheduler.modal.form.generate.time.label"
                )}
                required
                tooltip={t(
                  "scheduler.modal.form.generate.time.tooltip"
                )}
              >
                <TimePicker
                  value={
                    modal.generate_time
                      ? dayjs(modal.generate_time, "HH:mm")
                      : undefined
                  }
                  className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                  onChange={(time) => {
                    if (time) {
                      dispatch(
                        actions.setGenerateTime(
                          time.format("HH:mm:ss")
                        )
                      );
                    }
                  }}
                  format="HH:mm"
                />
              </Form.Item>
            </div>

            {/* Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                label={t(
                  "scheduler.modal.form.stopIfBlank.label"
                )}
                valuePropName="checked"
                tooltip={t(
                  "scheduler.modal.form.stopIfBlank.tooltip"
                )}
              >
                <Switch
                  checked={modal.stop_if_blank}
                  onChange={(checked) =>
                    dispatch(
                      actions.setStopIfBlank(checked)
                    )
                  }
                />
              </Form.Item>

              <Form.Item
                label={t(
                  "scheduler.modal.form.detailedReport.label"
                )}
                valuePropName="checked"
                tooltip={t(
                  "scheduler.modal.form.detailedReport.tooltip"
                )}
              >
                <Switch
                  checked={modal.detailed_report}
                  onChange={(checked) =>
                    dispatch(
                      actions.setDetailedReport(checked)
                    )
                  }
                />
              </Form.Item>
            </div>

            {/* Status */}
            {modal.mode === "update" && (
              <Form.Item
                label={t(
                  "scheduler.modal.form.active.label"
                )}
                valuePropName="checked"
                tooltip={t(
                  "scheduler.modal.form.active.tooltip"
                )}
              >
                <Switch
                  checked={modal.active}
                  onChange={(checked) =>
                    dispatch(actions.setActive(checked))
                  }
                />
              </Form.Item>
            )}
          </div>
        </div>

        {/* Filter Configuration Section */}
        <div className="mt-8 space-y-4">
          <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
            <h3 className="text-lg font-medium">
              {t("scheduler.modal.sections.filter")}
            </h3>
          </div>
          <div className="p-6 rounded-lg space-y-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
              <Form.Item
                label={t(
                  "scheduler.modal.form.reportType.label"
                )}
                required
                tooltip={t(
                  "scheduler.modal.form.reportType.tooltip"
                )}
              >
                <SelectReportType
                  value={modal.report_type_id}
                  onChange={(value) =>
                    dispatch(
                      actions.setReportTypeId(
                        value as EReportType
                      )
                    )
                  }
                />
              </Form.Item>
            </div>
            {/* Start And End Time */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                label={t(
                  "scheduler.modal.form.startTime.label"
                )}
                tooltip={t(
                  "scheduler.modal.form.startTime.tooltip"
                )}
              >
                <TimePicker
                  format="HH:mm"
                  className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                  value={
                    modal.start_time
                      ? dayjs(
                          `${dayjs().format(
                            "YYYY-MM-DD"
                          )} ${modal.start_time}`
                        )
                      : null
                  }
                  onChange={(time) =>
                    dispatch(
                      actions.setStartTime(
                        time ? time.format("HH:mm") : ""
                      )
                    )
                  }
                />
              </Form.Item>
              <Form.Item
                label={t(
                  "scheduler.modal.form.endTime.label"
                )}
                tooltip={t(
                  "scheduler.modal.form.endTime.tooltip"
                )}
              >
                <TimePicker
                  format="HH:mm"
                  className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                  value={
                    modal.end_time
                      ? dayjs(
                          `${dayjs().format(
                            "YYYY-MM-DD"
                          )} ${modal.end_time}`
                        )
                      : null
                  }
                  onChange={(time) =>
                    dispatch(
                      actions.setEndTime(
                        time ? time.format("HH:mm") : ""
                      )
                    )
                  }
                />
              </Form.Item>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
              <Form.Item
                label={t(
                  "scheduler.modal.form.selectedBranch.label"
                )}
                tooltip={t(
                  "scheduler.modal.form.selectedBranch.tooltip"
                )}
              >
                <GridSelectBranch
                  mode={"select"}
                  clearable={true}
                  value={
                    modal.selected_branch_id || undefined
                  }
                  onChange={(value) => {
                    if (!value) {
                      dispatch(
                        actions.setSelectedBranchId("")
                      );
                    } else if (!Array.isArray(value)) {
                      dispatch(
                        actions.setSelectedBranchId(
                          value.id
                        )
                      );
                    }
                  }}
                />
              </Form.Item>
            </div>
            {/* Report-specific Filters */}
            <SchedulerFormFields
              reportTypeId={modal.report_type_id}
            />
          </div>
        </div>

        {/* Email Configuration Section */}
        <div className="mt-8 space-y-4">
          <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
            <h3 className="text-lg font-medium">
              {t("scheduler.modal.sections.email.title")}
            </h3>
            <Tooltip
              title={t(
                "scheduler.modal.sections.email.tooltip"
              )}
            >
              <InfoCircleOutlined className="text-gray-400" />
            </Tooltip>
          </div>

          <div className="p-6 rounded-lg space-y-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600">
            <Form.Item
              label={t(
                "scheduler.modal.form.subject.label"
              )}
              required
              tooltip={t(
                "scheduler.modal.form.subject.tooltip"
              )}
            >
              <Input
                placeholder={t(
                  "scheduler.modal.form.subject.placeholder"
                )}
                className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                value={modal.subject}
                onChange={(e) =>
                  dispatch(
                    actions.setSubject(e.target.value)
                  )
                }
              />
            </Form.Item>

            <Form.Item
              label={t(
                "scheduler.modal.form.message.label"
              )}
              required
              tooltip={t(
                "scheduler.modal.form.message.tooltip"
              )}
            >
              <TextArea
                rows={4}
                placeholder={t(
                  "scheduler.modal.form.message.placeholder"
                )}
                className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                value={modal.message}
                onChange={(e) =>
                  dispatch(
                    actions.setMessage(e.target.value)
                  )
                }
              />
            </Form.Item>

            <Form.Item
              label={t("scheduler.modal.form.email.label")}
              tooltip={t(
                "scheduler.modal.form.email.tooltip"
              )}
            >
              <>
                <div className="flex flex-wrap gap-2 mb-2">
                  {modal.email.map((email) => (
                    <Tag
                      key={email}
                      closable
                      onClose={() =>
                        handleEmailRemove(email)
                      }
                      className="flex items-center"
                    >
                      {email}
                    </Tag>
                  ))}
                </div>
                <Space.Compact className="w-full">
                  <Input
                    placeholder={t(
                      "scheduler.modal.form.email.placeholder"
                    )}
                    value={emailInput}
                    onChange={handleEmailInputChange}
                    onPressEnter={handleEmailInputConfirm}
                    className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                  />
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleEmailInputConfirm}
                    className="bg-blue-500 hover:bg-blue-600 border-0"
                  >
                    {t(
                      "scheduler.modal.form.email.addButton"
                    )}
                  </Button>
                </Space.Compact>
              </>
            </Form.Item>
          </div>
        </div>
      </Form>

      {modal.errorMessage && (
        <div className="mt-4">
          <ErrorMessage message={modal.errorMessage} />
        </div>
      )}
    </Modal>
  );
};

export default ModalAddScheduler;
