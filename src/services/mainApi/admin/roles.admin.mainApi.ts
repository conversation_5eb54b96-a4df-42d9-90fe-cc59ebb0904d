import baseMain<PERSON>pi from "../base.mainApi";
import { BaseGetRequest } from "../types/base.mainApi.types";
import {
  AdminRoleListResponse,
  AdminRoleDetailResponse,
  CreateUpdateRoleRequest,
} from "./types/roles.admin.mainApi.types";

const rolesAdminMainApi = {
  getRoles: async (params: BaseGetRequest = {}) => {
    const response =
      await baseMainApi.get<AdminRoleListResponse>(
        "/web-api/admin/roles",
        { params }
      );
    return response.data;
  },
  createRoles: async (data: CreateUpdateRoleRequest) => {
    const response = await baseMainApi.post(
      "/web-api/admin/roles",
      data
    );
    return response.data;
  },
  updateRoles: async (
    id: string,
    data: CreateUpdateRoleRequest
  ) => {
    const response = await baseMainApi.put(
      `/web-api/admin/roles/${id}`,
      data
    );
    return response.data;
  },
  getRolesById: async (id: string) => {
    const response =
      await baseMainApi.get<AdminRoleDetailResponse>(
        `/web-api/admin/roles/${id}`
      );
    return response.data;
  },
  deleteRoles: async (id: string) => {
    const response = await baseMainApi.delete(
      `/web-api/admin/roles/${id}`
    );
    return response.data;
  },
};

export default rolesAdminMainApi;
