import {
  createSlice,
  createAsyncThunk,
  PayloadAction,
} from "@reduxjs/toolkit";
import { AdminBranch } from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { branchDetailsLogAdminMainApi } from "../../../services/mainApi/admin/branchDetailsLog.admin.mainApi";
import { AdminBranchDetailsLogBranchDetail } from "../../../services/mainApi/admin/types/branchDetailsLog.admin.mainApi.types";
import { RootState } from "../../index.ts";

export interface BranchDetailsLogFilter {
  branchId: string | null;
}

interface BranchDetailsLogState {
  branchDetailsLogs: AdminBranchDetailsLogBranchDetail[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    total: number;
  };
  filter: BranchDetailsLogFilter;
  selectedBranch: AdminBranch | null;
}

// Initial filter state
const initialFilter: BranchDetailsLogFilter = {
  branchId: null,
};

// Initial state
const initialState: BranchDetailsLogState = {
  branchDetailsLogs: [],
  loading: false,
  error: null,
  pagination: {
    page: 1,
    total: 0,
  },
  filter: initialFilter,
  selectedBranch: null,
};

// Async thunk for fetching branch details logs
export const fetchBranchDetailsLogs = createAsyncThunk(
  "branchDetailsLog/fetchBranchDetailsLogs",
  async (
    params: {
      branchCode: string;
      filter?: BranchDetailsLogFilter;
    },
    { rejectWithValue, getState }
  ) => {
    try {
      const { branchCode } = params;

      const state = getState() as RootState;
      const { filter } = state.branchDetailsLogAdmin;

      const apiParams = {
        branch_id: filter.branchId || undefined,
      };

      // Call the API
      const response =
        await branchDetailsLogAdminMainApi.getBranchDetailsLogs(
          branchCode,
          apiParams
        );

      // Map API response to slice state structure for the new format
      let mappedLogs: AdminBranchDetailsLogBranchDetail[] =
        [];

      if (response.data) {
        mappedLogs = response.data;
      }

      return {
        data: {
          branchDetailsLogs: mappedLogs,
          pagination: {
            page: response.meta.page || 1,
            total: response.meta.total || 0,
          },
        },
      };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error
          ? error.message
          : "An error occurred"
      );
    }
  }
);

// Slice
const branchDetailsLogAdminSlice = createSlice({
  name: "branchDetailsLogAdmin",
  initialState,
  reducers: {
    setSelectedBranch: (
      state,
      action: PayloadAction<AdminBranch | null>
    ) => {
      state.selectedBranch = action.payload;
      state.filter.branchId = action.payload?.id || null;
    },
    clearFilters: (state) => {
      state.filter = {
        ...initialFilter,
      };
      state.selectedBranch = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchBranchDetailsLogs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchBranchDetailsLogs.fulfilled,
        (state, action) => {
          state.loading = false;
          state.branchDetailsLogs =
            action.payload.data.branchDetailsLogs;
        }
      )
      .addCase(
        fetchBranchDetailsLogs.rejected,
        (state, action) => {
          state.loading = false;
          state.error = action.payload as string;
        }
      );
  },
});

export const { actions } = branchDetailsLogAdminSlice;
export default branchDetailsLogAdminSlice;
