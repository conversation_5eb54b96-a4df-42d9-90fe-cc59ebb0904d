import React, { useEffect } from "react";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks";
import {
  fetchSchedulers,
  setFilter,
} from "../../../../store/slices/admin/scheduler.admin.slice";
import { useParams } from "react-router-dom";
import { Empty, Pagination, Spin } from "antd";
import SchedulerListItem from "./SchedulerListItem";
import { useTranslation } from "react-i18next";

const SchedulerList: React.FC = () => {
  const dispatch = useAppDispatch();
  const { schedulers, loading, filter, pagination } =
    useAppSelector((state) => state.schedulerAdmin);
  const { branchCode } = useParams();
  const isLoading = React.useRef(false);
  const { t } = useTranslation();

  const fetch = React.useCallback(async () => {
    if (isLoading.current) return;
    isLoading.current = true;
    await dispatch(fetchSchedulers(branchCode || ""));
    isLoading.current = false;
  }, [branchCode, dispatch]);

  useEffect(() => {
    fetch();
  }, [fetch]);

  const handlePageChange = (
    page: number,
    pageSize: number
  ) => {
    dispatch(
      setFilter({
        page,
        limit: pageSize,
      })
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Spin size="large" />
      </div>
    );
  }

  if (!schedulers.length) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Empty
          description={
            <span className="text-gray-500 dark:text-gray-400">
              {t("scheduler.list.noSchedulersFound")}
            </span>
          }
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Schedulers Grid */}
      <div className="grid grid-cols-1 gap-4">
        {schedulers.map((scheduler) => (
          <SchedulerListItem
            key={scheduler.id}
            scheduler={scheduler}
          />
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <Pagination
          current={filter.page || 1}
          pageSize={filter.limit || 10}
          total={pagination.total}
          onChange={handlePageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              {t("scheduler.list.total", { total })}
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

export default SchedulerList;
