import { Button, Input, Select } from "antd";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {
  PlusOutlined,
  SearchOutlined,
  SortAscendingOutlined,
} from "@ant-design/icons";
import { MdTask } from "react-icons/md";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import { useTranslation } from "react-i18next";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks";
import {
  setFilter,
  fetchTaskAdmin,
} from "../../../store/slices/admin/task.admin.slice";
import AdminTaskList from "./Components/AdminTaskList";
import { useCallback, useRef } from "react";
import _ from "lodash";
import { useParams } from "react-router-dom";
import ModalAddTask from "./Components/ModalAddTask/ModalAddTask";
import addTaskAdminSlice from "../../../store/slices/admin/addTask.admin.slice";

const { Option } = Select;

const AdminTask = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { filter } = useAppSelector(
    (state) => state.taskAdmin
  );
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  const debouncedFetchRef = useRef(
    _.debounce(() => {
      dispatch(fetchTaskAdmin(branchCode || ""));
    }, 500)
  );

  const handleSearch = useCallback(
    (value: string) => {
      dispatch(
        setFilter({ search: value || null, page: 1 })
      );
      debouncedFetchRef.current();
    },
    [dispatch]
  );

  const handleSortChange = useCallback(
    (value: string) => {
      dispatch(setFilter({ orderBy: value }));
    },
    [dispatch]
  );

  const handleSortDirectionToggle = useCallback(() => {
    dispatch(
      setFilter({
        orderDirection:
          filter.orderDirection === "ASC" ? "DESC" : "ASC",
      })
    );
  }, [dispatch, filter.orderDirection]);

  const onAdd = useCallback(() => {
    dispatch(addTaskAdminSlice.actions.open());
  }, [dispatch]);

  return (
    <WebAdminLayout activePage="task">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header Section */}
          <PageHeader
            icon={<MdTask />}
            title={t("task.title")}
            description={t(
              "task.description",
              "Manage your tasks and assignments"
            )}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={onAdd}
              >
                {t("task.addButton")}
              </Button>,
            ]}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder={t(
                  "task.search.placeholder",
                  "Search tasks..."
                )}
                prefix={<SearchOutlined />}
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                size="large"
                onChange={(e) =>
                  handleSearch(e.target.value)
                }
                allowClear
              />
              <div className="flex gap-2">
                <Select
                  defaultValue="created_at"
                  className="min-w-[160px] dark:bg-gray-700"
                  size="large"
                  onChange={handleSortChange}
                  value={filter.orderBy}
                >
                  <Option value="created_at">
                    {t(
                      "task.search.sortOptions.createdAt",
                      "Created Date"
                    )}
                  </Option>
                  <Option value="start_time">
                    {t(
                      "task.search.sortOptions.startTime",
                      "Start Time"
                    )}
                  </Option>
                </Select>
                <Button
                  icon={
                    <SortAscendingOutlined
                      rotate={
                        filter.orderDirection === "DESC"
                          ? 180
                          : 0
                      }
                    />
                  }
                  size="large"
                  onClick={handleSortDirectionToggle}
                  className="dark:border-gray-600 dark:text-gray-300"
                />
              </div>
            </div>
          </div>

          {/* Tasks List Section */}
          <AdminTaskList />
        </div>
      </div>
      <ModalAddTask />
    </WebAdminLayout>
  );
};

export default AdminTask;
