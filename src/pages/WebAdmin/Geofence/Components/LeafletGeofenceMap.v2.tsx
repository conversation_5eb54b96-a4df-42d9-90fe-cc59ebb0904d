import React, { useEffect, useState, useRef } from "react";
import { GeofenceData } from "../../../../services/mainApi/admin/types/geofence.admin.mainApi.types";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, use<PERSON><PERSON>, CircleMarker, useMapEvents } from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import "leaflet-draw/dist/leaflet.draw.css";
import "./LeafletGeofenceMap.v2.css";
import { MdAddCircle, MdDelete } from "react-icons/md";

// Fix for Leaflet marker icon issue in React
import icon from "leaflet/dist/images/marker-icon.png";
import iconShadow from "leaflet/dist/images/marker-shadow.png";

// Import Leaflet Draw
import "leaflet-draw";

const DefaultIcon = L.icon({
  iconUrl: icon,
  shadowUrl: iconShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41]
});

<PERSON><PERSON>.prototype.options.icon = DefaultIcon;

/**
 * Props for LeafletGeofenceMap component
 *
 * Defines the input and output properties for the map component.
 */
interface LeafletMapProps {
  /**
   * Geofence data to display on the map
   * Contains polygon coordinates in the format expected by the API
   */
  geofenceData: GeofenceData;

  /**
   * Callback function triggered when geofence data changes
   * Used to update the parent component with new polygon data
   * @param data - Updated geofence data with new coordinates
   */
  onGeofenceDataChange: (data: GeofenceData) => void;
}

// Default location (Indonesia center) if geolocation is not available
const DEFAULT_CENTER: [number, number] = [-2.5489, 118.0149];
const DEFAULT_ZOOM = 5;

// Create a custom icon for user location
const userLocationIcon = new L.Icon({
  iconUrl: icon,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
});

// Component to center map on geofence data when available
function GeofenceController({ geofenceData }: { geofenceData: GeofenceData }) {
  const map = useMap();
  
  useEffect(() => {
    // Check if there are coordinates in the geofence data
    if (geofenceData && geofenceData.coordinates && geofenceData.coordinates.length > 0) {
      // Create bounds from coordinates
      const bounds = L.latLngBounds(
        geofenceData.coordinates.map(coord => [coord[0], coord[1]])
      );
      
      // Add some padding around the bounds
      map.fitBounds(bounds, {
        padding: [50, 50],
        maxZoom: 18,
        animate: true
      });
    }
  }, [geofenceData, map]);
  
  return null;
}

// Component to handle recenter map to user's location
function LocationMarker() {
  const [position, setPosition] = useState<[number, number] | null>(null);
  const [isLocating, setIsLocating] = useState(true);
  const [accuracy, setAccuracy] = useState<number | null>(null);
  const map = useMap();

  useEffect(() => {
    // Center map immediately when component mounts
    setIsLocating(true);
    
    // Set better options for geolocation
    const locationOptions: L.LocateOptions = {
      setView: true,
      maxZoom: 18,
      enableHighAccuracy: true,
      watch: false,
      timeout: 15000, // Increase timeout for better accuracy
      maximumAge: 0   // Don't use cached location
    };
    
    map.locate(locationOptions);

    const handleLocationFound = (e: L.LocationEvent) => {
      setPosition([e.latlng.lat, e.latlng.lng]);
      setAccuracy(e.accuracy);
      setIsLocating(false);
      
      // Force re-center the map with an appropriate zoom level based on accuracy
      const zoomLevel = calculateZoomBasedOnAccuracy(e.accuracy);
      map.setView(e.latlng, zoomLevel, {
        animate: true,
        duration: 1
      });
      
      // Show accuracy circle
      L.circle(e.latlng, {
        radius: e.accuracy / 2,
        color: '#1890ff',
        fillColor: '#1890ff',
        fillOpacity: 0.1,
        weight: 1
      }).addTo(map);
    };
    
    // Calculate appropriate zoom level based on GPS accuracy
    const calculateZoomBasedOnAccuracy = (accuracyInMeters: number): number => {
      // Higher accuracy (lower meters) = higher zoom level
      if (accuracyInMeters < 10) return 19;      // Very accurate (< 10m)
      else if (accuracyInMeters < 50) return 18; // Accurate (< 50m)
      else if (accuracyInMeters < 100) return 17; // Good (< 100m)
      else if (accuracyInMeters < 500) return 15; // Moderate (< 500m)
      else if (accuracyInMeters < 1000) return 14; // Poor (< 1km)
      else return 13; // Very poor (> 1km)
    };

    const handleLocationError = (e: L.ErrorEvent) => {
      console.log('Location access denied or unavailable', e.message);
      setIsLocating(false);
    };

    map.on('locationfound', handleLocationFound);
    map.on('locationerror', handleLocationError);

    return () => {
      map.off('locationfound', handleLocationFound);
      map.off('locationerror', handleLocationError);
    };
  }, [map]);

  return (
    <>
      {isLocating && (
        <CircleMarker 
          center={map.getCenter()} 
          radius={15}
          pathOptions={{ 
            color: '#1890ff',
            fillColor: '#1890ff',
            fillOpacity: 0.4,
            weight: 2
          }}
          className="location-finding-indicator"
        />
      )}
      {position && (
        <Marker position={position} icon={userLocationIcon}>
          <Popup>
            Your location
            {accuracy && <div>Accuracy: ±{Math.round(accuracy)}m</div>}
          </Popup>
        </Marker>
      )}
    </>
  );
}

// This component forces the map to recenter when the container is resized
function ResizeHandler() {
  const map = useMap();
  
  useEffect(() => {
    // Add a small delay to ensure the map has rendered properly
    const timeout = setTimeout(() => {
      map.invalidateSize();
    }, 100);
    
    return () => clearTimeout(timeout);
  }, [map]);
  
  return null;
}

// Custom control component for map actions
function MapControls({ 
  onCreatePolygon, 
  onDeletePolygon,
  hasPolygon 
}: { 
  onCreatePolygon: () => void, 
  onDeletePolygon: () => void,
  hasPolygon: boolean
}) {
  return (
    <div className="map-custom-controls">
      <button 
        className="map-control-btn create-polygon"
        onClick={onCreatePolygon}
        title="Create Polygon"
      >
        <MdAddCircle size={24} />
      </button>
      
      {hasPolygon && (
        <button 
          className="map-control-btn delete-polygon"
          onClick={onDeletePolygon}
          title="Delete Polygon"
        >
          <MdDelete size={24} />
        </button>
      )}
    </div>
  );
}

// Component for creating a triangle polygon with dynamic sizing
function PolygonCreator({ geofenceData, onGeofenceDataChange }: { geofenceData: GeofenceData, onGeofenceDataChange: (data: GeofenceData) => void }) {
  const map = useMap();
  const editableLayers = useRef(new L.FeatureGroup()).current;
  const [trianglePolygon, setTrianglePolygon] = useState<L.Polygon | null>(null);
  const hasExistingGeofence = useRef(false);

  // Check if geofence data already exists
  useEffect(() => {
    if (geofenceData && geofenceData.coordinates && geofenceData.coordinates.length > 0) {
      hasExistingGeofence.current = true;
    } else {
      hasExistingGeofence.current = false;
    }
  }, [geofenceData]);

  // Add the editable layers to the map
  useEffect(() => {
    map.addLayer(editableLayers);

    // Clean up function to remove the layer when component unmounts
    return () => {
      // Clean up any polygons and their event handlers
      editableLayers.eachLayer((layer) => {
        if (layer instanceof L.Polygon) {
          layer.off('edit');
          layer.off('mouseover');
          layer.off('mouseout');
        }
      });
      
      // Remove the layer group from the map
      map.removeLayer(editableLayers);
    };
  }, [map, editableLayers]);

  // Create a scalable triangle based on current zoom level
  const createTriangle = () => {
    // Clear existing polygons
    editableLayers.clearLayers();
    
    // Get center point and calculate size based on viewport
    const center = map.getCenter();
    
    // Calculate a reasonable size based on viewport bounds
    const bounds = map.getBounds();
    const lngSpan = bounds.getEast() - bounds.getWest();
    const latSpan = bounds.getNorth() - bounds.getSouth();
    
    // Make it smaller for better usability - 10% of viewport
    const width = lngSpan * .7;
    const height = latSpan * .7;
    
    // Create triangle coordinates (isosceles triangle)
    const lat = center.lat;
    const lng = center.lng;
    
    const triangleCoords: L.LatLngExpression[] = [
      [lat - height/2, lng - width/2], // bottom left
      [lat + height/2, lng], // top
      [lat - height/2, lng + width/2], // bottom right
      [lat - height/2, lng - width/2]  // close the polygon
    ];
    
    // Create an editable polygon
    const polygon = L.polygon(triangleCoords, {
      color: '#1890ff',
      fillColor: '#1890ff',
      fillOpacity: 0.3,
    });
    
    // Add to editable layers
    editableLayers.addLayer(polygon);
    setTrianglePolygon(polygon);
    
    // Enable editing with built-in controls
    // @ts-expect-error - This is using Leaflet's internal editing capabilities
    if (polygon.editing) {
      // @ts-expect-error - Enable editing functionality for polygon
      polygon.editing.enable();
    }
    
    // Update the geofence data
    updateGeofenceData(polygon);
    
    // Add event listeners for shape changes
    polygon.on('edit', () => updateGeofenceData(polygon));
    
    // Update the hasExistingGeofence ref since we've now created one
    hasExistingGeofence.current = true;
  };
  
  // Function to delete the current polygon
  const deletePolygon = () => {
    // Clear any existing polygons
    editableLayers.clearLayers();
    
    // Set state to null
    setTrianglePolygon(null);
    
    // Update the hasExistingGeofence ref
    hasExistingGeofence.current = false;
    
    // Call the onGeofenceDataChange callback with empty coordinates
    onGeofenceDataChange({
      type: geofenceData.type || "Polygon",
      coordinates: []
    });
  };
  
  // Update GeofenceData when polygon changes
  const updateGeofenceData = (polygon: L.Polygon) => {
    // Get all latlngs from the polygon
    const allLatLngs = polygon.getLatLngs();
    
    // Handle possible nested array structure
    let latLngs: L.LatLng[];
    if (Array.isArray(allLatLngs[0]) && allLatLngs.length === 1) {
      // Handle case where latlngs are in a nested array (e.g., [[latlng, latlng, ...]])
      latLngs = allLatLngs[0] as L.LatLng[];
    } else {
      // Handle case where latlngs are a simple array (e.g., [latlng, latlng, ...])
      latLngs = allLatLngs as unknown as L.LatLng[];
    }
    
    // Map the LatLng objects to coordinate arrays [lat, lng]
    const coordinates = latLngs.map(point => [point.lat, point.lng]);
    
    // Create GeofenceData object with same type as original
    const updatedGeofenceData: GeofenceData = {
      type: geofenceData.type || "Polygon",
      coordinates: coordinates
    };
    
    // Call the onGeofenceDataChange callback through props
    onGeofenceDataChange(updatedGeofenceData);
  };
  
  // Add map event listener for zoom changes to adjust polygon size
  useMapEvents({
    zoomend: () => {
      if (trianglePolygon) {
        // When zooming, we could update the polygon size, but for now let's keep it as is
        // This ensures the polygon remains editable and draggable after zoom
      }
    }
  });
  
  // Render existing geofence if available
  useEffect(() => {
    if (geofenceData && geofenceData.coordinates && geofenceData.coordinates.length > 0) {
      // Clear any existing polygons
      editableLayers.clearLayers();
      
      // Convert coordinates to LatLng format
      const latLngs = geofenceData.coordinates.map(coord => L.latLng(coord[0], coord[1]));
      
      // Create polygon with the coordinates
      const polygon = L.polygon(latLngs, {
        color: '#1890ff',
        fillColor: '#1890ff',
        fillOpacity: 0.3,
      });
      
      // Add to editable layers
      editableLayers.addLayer(polygon);
      setTrianglePolygon(polygon);
      
      // Enable editing with built-in controls
      // @ts-expect-error - This is using Leaflet's internal editing capabilities
      if (polygon.editing) {
        // @ts-expect-error - Enable editing functionality for polygon
        polygon.editing.enable();
      }
      
      // Add event listeners for shape changes
      polygon.on('edit', () => updateGeofenceData(polygon));
    }
  }, []);
  
  return (
    <MapControls 
      onCreatePolygon={createTriangle} 
      onDeletePolygon={deletePolygon}
      hasPolygon={hasExistingGeofence.current}
    />
  );
}

const LeafletGeofenceMapV2: React.FC<LeafletMapProps> = (props) => {
  // Calculate center point based on geofence data if available
  const calculateInitialCenter = (): [number, number] => {
    if (props.geofenceData && props.geofenceData.coordinates && props.geofenceData.coordinates.length > 0) {
      // Calculate center of all coordinates
      const coords = props.geofenceData.coordinates;
      let sumLat = 0;
      let sumLng = 0;
      
      for (const coord of coords) {
        sumLat += coord[0];
        sumLng += coord[1];
      }
      
      // Return average position
      return [sumLat / coords.length, sumLng / coords.length];
    }
    
    // Fallback to default center
    return DEFAULT_CENTER;
  };
  
  // Get initial center based on geofence data or default
  const initialCenter = calculateInitialCenter();
  
  return (
    <div className="map-wrapper">
      <MapContainer
        center={initialCenter}
        zoom={DEFAULT_ZOOM}
        attributionControl={false} // Remove attribution to save space
        zoomControl={true} // Show zoom controls
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        <GeofenceController geofenceData={props.geofenceData} />
        <LocationMarker />
        <ResizeHandler />
        <PolygonCreator 
          geofenceData={props.geofenceData} 
          onGeofenceDataChange={props.onGeofenceDataChange} 
        />
      </MapContainer>
    </div>
  );
};

export default LeafletGeofenceMapV2; 