import {
  BaseGetRequest,
  BaseResponse,
} from "../../types/base.mainApi.types";
import { AdminLabel } from "./label.admin.mainApi.types";
import { AdminZone } from "./zone.admin.mainApi.types";

export interface AdminGeofenceGetRequest
  extends BaseGetRequest {
  status?: string;
  zone_id?: string;
}

export interface GeofenceData {
  type: string;
  coordinates: number[][];
}

export interface AdminGeofence {
  id: string;
  branch_id: string;
  parent_branch_id: string;
  zone_id: string;
  geofence_name: string;
  geofence_description: string | null;
  active_time_start: string;
  active_time_end: string;
  minimum_stay_duration: string;
  maximum_stay_duration: string;
  geofence_data: GeofenceData;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
  labels: AdminLabel[];
  zone: AdminZone;
}

export interface AdminCreateUpdateGeofenceRequest {
  geofence_name: string;
  geofence_description?: string | null;
  zone_id: string;
  active_from: string;
  active_to: string;
  minimum_stay_duration: string;
  maximum_stay_duration: string;
  geofence_data: GeofenceData;
  active: boolean;
  label_ids: (number | string)[];
}

export type AdminGeofenceListResponse = BaseResponse<
  AdminGeofence[]
>;
