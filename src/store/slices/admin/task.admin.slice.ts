import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import taskAdminMainApi from "../../../services/mainApi/admin/task.admin.mainApi";
import { AdminTask } from "../../../services/mainApi/admin/types/task.admin.mainApi.types";
import {
  BaseGetRequest,
  BasePaginationMeta,
} from "../../../services/mainApi/types/base.mainApi.types";

interface State {
  tasks: AdminTask[];
  loading: boolean;
  errorMessage: string | null;
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;
}

const initState: State = {
  tasks: [],
  loading: false,
  errorMessage: null,
  filter: {
    search: null,
    page: 1,
    limit: 10,
    orderBy: "created_at",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    total_pages: 0,
  },
};

export const fetchTaskAdmin = createAsyncThunk<
  { data: AdminTask[]; meta: BasePaginationMeta },
  string,
  { state: RootState; rejectValue: string }
>(
  "taskAdmin/fetchTasks",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const { filter } = state.taskAdmin;
      const params: BaseGetRequest = {
        search: filter.search || undefined,
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
      };

      const response = await taskAdminMainApi.getTasks(
        branchCode,
        params
      );
      return {
        data: response.data || [],
        meta: response.meta,
      };
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue("Failed to fetch tasks");
    }
  }
);

const taskAdminSlice = createSlice({
  name: "taskAdmin",
  initialState: { ...initState },
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchTaskAdmin.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchTaskAdmin.fulfilled,
        (state, action) => {
          state.loading = false;
          state.tasks = action.payload.data;
          state.pagination = action.payload.meta;
        }
      )
      .addCase(fetchTaskAdmin.rejected, (state, action) => {
        state.loading = false;
        state.errorMessage = action.payload as string;
      });
  },
});

export const { setFilter } = taskAdminSlice.actions;
export default taskAdminSlice;
