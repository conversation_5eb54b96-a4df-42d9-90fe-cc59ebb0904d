import { App, Input, Switch } from "antd";
import {
  useAppSelector,
  useAppDispatch,
} from "../../../../../store/hooks.ts";
import addSubBranchAdminSlice from "../../../../../store/slices/admin/addSubBranch.admin.slice.ts";
import SelectTimezone from "../../../../../components/Timezone/SelectTimezone.tsx";
import { Modal } from "antd";
import React, {
  useCallback,
  useRef,
  useEffect,
} from "react";
import { AdminBranchCreateRequestBody } from "../../../../../services/mainApi/admin/types/branch.admin.mainApi.types.ts";
import branchAdminMainApi from "../../../../../services/mainApi/admin/branch.admin.mainApi.ts";
import axios from "axios";
import { handleApiError } from "../../../../../utils/error.ts";
import ErrorMessage from "../../../../../components/Common/ErrorMessage/ErrorMessage.tsx";
import { fetchBranches } from "../../../../../store/slices/admin/branch.admin.slice.ts";
import { useTranslation } from "react-i18next";

const ModalAddBranch = () => {
  const modalState = useAppSelector(
    (state) => state.modalAddSubBranchAdmin
  );
  const { mode, branchEdit, open, loading } = modalState;
  const dispatch = useAppDispatch();
  const { message, modal } = App.useApp();
  const creating = useRef(false);
  const { t } = useTranslation();

  useEffect(() => {
    if (mode === "update" && branchEdit) {
      dispatch(
        addSubBranchAdminSlice.actions.setBranchName(
          branchEdit.branch_name
        )
      );
      dispatch(
        addSubBranchAdminSlice.actions.setBranchDescription(
          branchEdit.branch_description || ""
        )
      );
      dispatch(
        addSubBranchAdminSlice.actions.setBranchTimezone(
          branchEdit.timezone_id
        )
      );
      dispatch(
        addSubBranchAdminSlice.actions.setActive(
          branchEdit.active
        )
      );
    }
  }, [mode, branchEdit, dispatch]);

  const onCancel = useCallback(() => {
    dispatch(addSubBranchAdminSlice.actions.close());
  }, [dispatch]);

  const handleNameChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(
      addSubBranchAdminSlice.actions.setBranchName(
        e.target.value
      )
    );
  };

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    dispatch(
      addSubBranchAdminSlice.actions.setBranchDescription(
        e.target.value
      )
    );
  };

  const handleTimezoneChange = (value: string) => {
    dispatch(
      addSubBranchAdminSlice.actions.setBranchTimezone(
        value
      )
    );
  };

  const handleActiveChange = (checked: boolean) => {
    dispatch(
      addSubBranchAdminSlice.actions.setActive(checked)
    );
  };

  const onSubmit = useCallback(async () => {
    if (creating.current) return;
    dispatch(
      addSubBranchAdminSlice.actions.setLoading(true)
    );
    dispatch(addSubBranchAdminSlice.actions.setError(null));
    creating.current = true;

    const payload: AdminBranchCreateRequestBody = {
      name: modalState.branchName,
      description: modalState.branchDescription,
      timezone: modalState.branchTimezone || "",
      active: modalState.active,
    };

    try {
      if (mode === "update" && branchEdit) {
        await branchAdminMainApi.updateBranch(
          branchEdit.id,
          payload
        );
        message.success(t("branch.modal.edit.success"));
      } else {
        await branchAdminMainApi.createBranch(payload);
        message.success(t("branch.modal.add.success"));
      }
      dispatch(fetchBranches());
      onCancel();
    } catch (e) {
      let errorMessages: string | string[] = t(
        mode === "update"
          ? "branch.modal.edit.error"
          : "branch.modal.add.error"
      );
      if (axios.isAxiosError(e)) {
        errorMessages = handleApiError(e).errors;
      }
      dispatch(
        addSubBranchAdminSlice.actions.setError(
          errorMessages
        )
      );
      modal.error({
        title: t("error"),
        content: Array.isArray(errorMessages)
          ? errorMessages[0]
          : errorMessages,
        okText: "OK",
        okButtonProps: {
          className:
            "bg-red-500 hover:bg-red-600 border-0 text-white",
        },
      });
    } finally {
      dispatch(
        addSubBranchAdminSlice.actions.setLoading(false)
      );
      creating.current = false;
    }
  }, [
    dispatch,
    message,
    modal,
    modalState,
    mode,
    branchEdit,
    onCancel,
    t,
  ]);

  return (
    <Modal
      title={
        <div className="text-lg font-semibold">
          {mode === "update"
            ? t("branch.modal.edit.title")
            : t("branch.modal.add.title")}
        </div>
      }
      open={open}
      onCancel={onCancel}
      onOk={onSubmit}
      className="rounded-xl"
      okButtonProps={{
        className: "bg-blue-500 hover:bg-blue-600 border-0",
        loading: loading,
      }}
      maskClosable={!loading}
      closable={!loading}
      confirmLoading={loading}
      width={800}
      okText={
        mode === "update"
          ? t("common.update")
          : t("common.create")
      }
      cancelText={t("common.cancel")}
    >
      <div className="mt-4">
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-4">
            {t("branch.modal.sections.branchInfo")}
          </h3>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">
              {t("branch.modal.form.name.label")}{" "}
              <span className="text-red-500">*</span>
            </label>
            <Input
              className="rounded-lg w-full"
              placeholder={t(
                "branch.modal.form.name.placeholder"
              )}
              value={modalState.branchName}
              onChange={handleNameChange}
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">
              {t("branch.modal.form.description.label")}
            </label>
            <Input.TextArea
              className="rounded-lg w-full"
              placeholder={t(
                "branch.modal.form.description.placeholder"
              )}
              rows={4}
              value={modalState.branchDescription}
              onChange={handleDescriptionChange}
            />
          </div>

          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                {t("branch.modal.form.timezone.label")}{" "}
                <span className="text-red-500">*</span>
              </label>
              <SelectTimezone
                className="rounded-lg w-full"
                value={
                  modalState.branchTimezone || undefined
                }
                onChange={handleTimezoneChange}
                placeholder={t(
                  "branch.modal.form.timezone.placeholder"
                )}
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="flex items-center">
              <Switch
                checked={modalState.active}
                onChange={handleActiveChange}
              />
              <span className="ml-2">
                {t("branch.modal.form.status.label")}
              </span>
            </label>
          </div>
        </div>

        {modalState.errorMessage && (
          <ErrorMessage message={modalState.errorMessage} />
        )}
      </div>
    </Modal>
  );
};

export default ModalAddBranch;
