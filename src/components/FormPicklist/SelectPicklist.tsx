import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import formPicklistAdminMainApi from "../../services/mainApi/admin/form-picklist.admin.mainApi";
import { AdminFormPicklist } from "../../services/mainApi/admin/types/form-picklist.admin.mainApi.types";
import { useParams } from "react-router-dom";

interface SelectPicklistProps {
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  clearable?: boolean;
}

export default function SelectPicklist({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select picklist",
  clearable = true,
}: SelectPicklistProps) {
  const [picklists, setPicklists] = useState<
    AdminFormPicklist[]
  >([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);
  const { branchCode } = useParams();

  useEffect(() => {
    const fetchPicklists = async () => {
      if (isFetching.current) return;

      try {
        isFetching.current = true;
        setLoading(true);
        const response =
          await formPicklistAdminMainApi.getFormPicklists(
            branchCode || ""
          );
        setPicklists(response.data || []);
      } catch (error) {
        console.error("Failed to fetch picklists:", error);
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchPicklists();
  }, [branchCode]);

  return (
    <Select
      className={className}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      disabled={disabled}
      loading={loading}
      showSearch
      optionFilterProp="label"
      options={picklists.map((picklist) => ({
        value: picklist.id,
        label: picklist.form_picklist_name,
      }))}
      allowClear={clearable}
    />
  );
}
