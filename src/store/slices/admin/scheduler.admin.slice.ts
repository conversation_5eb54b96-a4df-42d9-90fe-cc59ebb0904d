import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import schedulerAdminMainApi from "../../../services/mainApi/admin/scheduler.admin.mainApi";
import {
  AdminScheduler,
  AdminSchedulerGetRequest,
} from "../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { BasePaginationMeta } from "../../../services/mainApi/types/base.mainApi.types";

interface State {
  schedulers: AdminScheduler[];
  loading: boolean;
  errorMessage: string | null;
  filter: {
    search: string | null;
    report_type_id: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;
}

const initialState: State = {
  schedulers: [],
  loading: false,
  errorMessage: null,
  filter: {
    search: null,
    report_type_id: null,
    page: null,
    limit: null,
    orderBy: "created_at",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
  },
};

export const fetchSchedulers = createAsyncThunk(
  "schedulerAdmin/fetchSchedulers",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const { filter } = state.schedulerAdmin;
      const params: AdminSchedulerGetRequest = {
        search: filter.search || "",
        report_type_id: filter.report_type_id
          ? Number(filter.report_type_id)
          : undefined,
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
      };
      const response =
        await schedulerAdminMainApi.getSchedulers(
          branchCode,
          params
        );
      return {
        data: response.data,
        meta: response.meta,
      };
    } catch {
      return rejectWithValue("Failed to fetch schedulers");
    }
  }
);

export const deleteScheduler = createAsyncThunk(
  "schedulerAdmin/deleteScheduler",
  async (
    {
      branchCode,
      schedulerId,
    }: { branchCode: string; schedulerId: string },
    { rejectWithValue }
  ) => {
    try {
      await schedulerAdminMainApi.deleteScheduler(
        branchCode,
        schedulerId
      );
      return schedulerId;
    } catch {
      return rejectWithValue("Failed to delete scheduler");
    }
  }
);

const schedulerAdminSlice = createSlice({
  name: "schedulerAdmin",
  initialState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch schedulers
      .addCase(fetchSchedulers.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchSchedulers.fulfilled,
        (state, action) => {
          state.loading = false;
          state.schedulers = action.payload.data || [];
          state.pagination = action.payload.meta;
        }
      )
      .addCase(
        fetchSchedulers.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      )
      // Delete scheduler
      .addCase(
        deleteScheduler.fulfilled,
        (state, action) => {
          state.schedulers = state.schedulers.filter(
            (scheduler) => scheduler.id !== action.payload
          );
          state.pagination.total =
            (state.pagination.total || 1) - 1;
        }
      );
  },
});

export const { setFilter } = schedulerAdminSlice.actions;
export default schedulerAdminSlice;
