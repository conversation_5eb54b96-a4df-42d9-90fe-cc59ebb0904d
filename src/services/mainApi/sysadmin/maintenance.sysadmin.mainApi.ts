import baseMainApi from "../base.mainApi";
import {
    UpdateMaintenanceRequest
} from "./types/maintenance.mainApi.types";
import { Setting, SettingListResponse } from "../types/setting.mainApi.types";

const maintenanceSysAdminMainApi = {
    getList: async ({
        search = "",
        page = 1,
        limit = 20,
        order_by = "created_at",
        order_direction = "DESC",
      }: {
        search?: string;
        page?: number;
        limit?: number;
        order_by?: string;
        order_direction?: "ASC" | "DESC";
      }) => {
        const response = await baseMainApi.get<SettingListResponse>('/web-api/sysadmin/maintenance', {
                params: {
                    search,
                    page,
                    limit,
                    order_by,
                    order_direction,
                },
            });
        return response.data;
    },
    update: async (id: string, request: UpdateMaintenanceRequest): Promise<Setting> => {
        const response = await baseMainApi.put(
            `/web-api/sysadmin/maintenance/${id}`,
            request
          );
        return response.data;
    },
};

export default maintenanceSysAdminMainApi;