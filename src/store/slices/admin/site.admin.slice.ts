import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import zoneAdminMainApi from "../../../services/mainApi/admin/zone.admin.mainApi";
import { AdminZone } from "../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import {
  BasePaginationMeta,
  BaseGetRequest,
} from "../../../services/mainApi/types/base.mainApi.types";

interface ZoneGetRequest extends BaseGetRequest {
  status?: string;
}

interface State {
  loading: boolean;
  errorMessage: string | null;
  sites: AdminZone[];
  filter: {
    search: string | null;
    status: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;
}

const initState: State = {
  loading: false,
  errorMessage: null,
  sites: [],
  filter: {
    search: null,
    status: null,
    page: null,
    limit: null,
    orderBy: "created_at",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
  },
};

export const fetchSitesAdmin = createAsyncThunk(
  "siteAdmin/fetchSites",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const { filter } = state.siteAdmin;
      const params: ZoneGetRequest = {
        search: filter.search || "",
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
      };

      if (filter.status) {
        params.status = filter.status;
      }

      const response = await zoneAdminMainApi.getZones(
        branchCode,
        params
      );
      return {
        data: response.data,
        meta: response.meta,
      };
    } catch {
      return rejectWithValue("Failed to fetch sites");
    }
  }
);

const siteAdminSlice = createSlice({
  name: "siteAdmin",
  initialState: { ...initState },
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchSitesAdmin.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchSitesAdmin.fulfilled,
        (state, action) => {
          state.loading = false;
          state.sites = action.payload.data || [];
          state.pagination = action.payload.meta;
        }
      )
      .addCase(
        fetchSitesAdmin.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      );
  },
});

export default siteAdminSlice;
