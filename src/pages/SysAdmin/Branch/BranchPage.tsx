import { useState } from "react";
import { Button, Input, Pagination, Select } from "antd";
import {
  PlusOutlined,
  SearchOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
} from "@ant-design/icons";
import SysAdminLayout from "../../../components/Layout/SysAdminLayout";
import BranchList from "./Components/BranchList";
import AddEditBranchModal from "./Components/AddEditBranchModal";
import DeleteBranchModal from "./Components/DeleteBranchModal";
import { useBranchManagement } from "./Components/hooks/useBranchManagement.ts";
import { Branch } from "../../../services/mainApi/sysadmin/types/branch.mainApi.types.ts";
import { SortField } from "../../../store/slices/sysadmin/branchList.sysadmin.slice.ts";

const { Option } = Select;

export default function BranchPage() {
  const {
    branches,
    loading,
    pagination,
    filters,
    handleSearch,
    handleSort,
    handlePageChange,
  } = useBranchManagement();

  const [showAddEdit, setShowAddEdit] = useState(false);
  const [showDelete, setShowDelete] = useState(false);
  const [selectedBranch, setSelectedBranch] =
    useState<Branch | null>(null);

  const handleAddClick = () => {
    setSelectedBranch(null);
    setShowAddEdit(true);
  };

  const handleEditClick = (branch: Branch) => {
    setSelectedBranch(branch);
    setShowAddEdit(true);
  };

  const handleDeleteClick = (branch: Branch) => {
    setSelectedBranch(branch);
    setShowDelete(true);
  };

  const toggleSortOrder = () => {
    handleSort(
      filters.sortField,
      filters.sortOrder === "asc" ? "desc" : "asc"
    );
  };

  return (
    <SysAdminLayout activePage="branch">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Sticky Header */}
        <div className="sticky top-0 z-10 bg-gray-50 dark:bg-gray-900 pb-6">
          {/* Header Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                  Client Management
                </h1>
                <p className="text-gray-500 dark:text-gray-400 mt-1">
                  Manage your organization branches
                </p>
              </div>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddClick}
                size="large"
                className="bg-blue-500 hover:bg-blue-600 border-0 shadow-md hover:shadow-lg transition-all duration-200"
              >
                Add New Client
              </Button>
            </div>
          </div>

          {/* Search and Sort Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="w-full md:w-1/3">
                <Input
                  placeholder="Search branches..."
                  prefix={
                    <SearchOutlined className="text-gray-400 dark:text-gray-500" />
                  }
                  value={filters.searchQuery}
                  onChange={(e) =>
                    handleSearch(e.target.value)
                  }
                  className="rounded-lg"
                  size="large"
                />
              </div>
              <div className="flex gap-2 items-center">
                <Select
                  value={filters.sortField}
                  onChange={(value: SortField) =>
                    handleSort(value, filters.sortOrder)
                  }
                  className="w-40"
                  size="large"
                >
                  <Option value="created_at">
                    Creation Date
                  </Option>
                  <Option value="branch_name">
                    Branch Name
                  </Option>
                </Select>
                <Button
                  icon={
                    filters.sortOrder === "asc" ? (
                      <SortAscendingOutlined />
                    ) : (
                      <SortDescendingOutlined />
                    )
                  }
                  onClick={toggleSortOrder}
                  size="large"
                  className="flex items-center dark:border-gray-600 dark:text-gray-300"
                >
                  {filters.sortOrder === "asc"
                    ? "Ascending"
                    : "Descending"}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Branch List with Pagination */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm mt-6">
          <BranchList
            branches={branches}
            loading={loading}
            onEdit={handleEditClick}
            onDelete={handleDeleteClick}
          />

          {/* Pagination */}
          <div className="p-6 border-t border-gray-100 dark:border-gray-700">
            <Pagination
              current={pagination.current}
              total={pagination.total}
              pageSize={pagination.pageSize}
              onChange={handlePageChange}
              showTotal={(total) => `Total ${total} items`}
              showSizeChanger={false}
              className="flex justify-end"
            />
          </div>
        </div>

        {/* Modals */}
        <AddEditBranchModal
          visible={showAddEdit}
          branch={selectedBranch}
          onClose={() => setShowAddEdit(false)}
        />

        <DeleteBranchModal
          visible={showDelete}
          branch={selectedBranch}
          onClose={() => setShowDelete(false)}
          onSuccess={async () => {
            if (selectedBranch) {
              setShowDelete(false);
            }
          }}
        />
      </div>
    </SysAdminLayout>
  );
}
