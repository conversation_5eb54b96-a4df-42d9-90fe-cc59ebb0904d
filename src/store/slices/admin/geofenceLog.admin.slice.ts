import {
  createSlice,
  createAsyncThunk,
  PayloadAction,
} from "@reduxjs/toolkit";
import { geofenceLogAdminApi } from "../../../services/mainApi/admin/geofenceLog.admin.mainApi";
import {
  AdminGeofenceLogEntry,
  AdminGeofenceLogListParams,
} from "../../../services/mainApi/admin/types/geofenceLog.admin.mainApi.types";
import { BasePaginationMeta } from "../../../services/mainApi/types/base.mainApi.types";
import { RootState } from "../../../store";
import { AdminRole } from "../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminUser } from "../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminLabel } from "../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminBranch } from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminZone } from "../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import dayjs from "dayjs";

interface State {
  geofenceLogs: AdminGeofenceLogEntry[];
  loading: boolean;
  error: string | null;

  filter: {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    branchId: string | number | null;
    siteId: string | number | null;
    siteLabels: (string | number)[];
    roleId: string | number | null;
    userId: string | number | null;
    userLabels: (string | number)[];
    deviceId: string | number | null;
    deviceLabels: (string | number)[];
    page: number;
    limit: number;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;

  // Filter selection objects
  selectedRole: AdminRole | null;
  selectedUser: AdminUser | null;
  selectedDevice: AdminDevice | null;
  selectedSite: AdminZone | null;
  selectedBranch: AdminBranch | null;
  selectedUserLabels: AdminLabel[];
  selectedDeviceLabels: AdminLabel[];
  selectedSiteLabels: AdminLabel[];
}

const initialState: State = {
  geofenceLogs: [],
  loading: false,
  error: null,

  filter: {
    startDate: dayjs().subtract(1, "day").format("YYYY-MM-DD"),
    endDate: dayjs().subtract(1, "day").format("YYYY-MM-DD"),
    startTime: "00:00:00",
    endTime: "23:59:59",
    branchId: null,
    siteId: null,
    siteLabels: [],
    roleId: null,
    userId: null,
    userLabels: [],
    deviceId: null,
    deviceLabels: [],
    page: 1,
    limit: 10,
    orderBy: "geofence_name",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
  },

  selectedRole: null,
  selectedUser: null,
  selectedDevice: null,
  selectedSite: null,
  selectedBranch: null,
  selectedUserLabels: [],
  selectedDeviceLabels: [],
  selectedSiteLabels: [],
};

export const fetchGeofenceLogs = createAsyncThunk(
  "geofenceLogAdmin/fetchGeofenceLogs",
  async (
    {
      branchCode,
    }: {
      branchCode: string;
    },
    { getState }
  ) => {
    const state = getState() as RootState;
    const geofenceLogAdmin = state.geofenceLogAdmin;

    const params: AdminGeofenceLogListParams = {
      page: geofenceLogAdmin.filter.page,
      limit: geofenceLogAdmin.filter.limit,
      start_date:
        geofenceLogAdmin.filter.startDate || undefined,
      end_date:
        geofenceLogAdmin.filter.endDate || undefined,
      start_time:
        geofenceLogAdmin.filter.startTime || undefined,
      end_time:
        geofenceLogAdmin.filter.endTime || undefined,
      branch_id:
        geofenceLogAdmin.filter.branchId || undefined,
      site_id: geofenceLogAdmin.filter.siteId || undefined,
      site_labels:
        geofenceLogAdmin.filter.siteLabels.length > 0
          ? geofenceLogAdmin.filter.siteLabels.join(",")
          : undefined,
      role_id: geofenceLogAdmin.filter.roleId || undefined,
      user_id: geofenceLogAdmin.filter.userId || undefined,
      user_labels:
        geofenceLogAdmin.filter.userLabels.length > 0
          ? geofenceLogAdmin.filter.userLabels.join(",")
          : undefined,
      device_id:
        geofenceLogAdmin.filter.deviceId || undefined,
      device_labels:
        geofenceLogAdmin.filter.deviceLabels.length > 0
          ? geofenceLogAdmin.filter.deviceLabels.join(",")
          : undefined,
      order_by: geofenceLogAdmin.filter.orderBy,
      order_direction:
        geofenceLogAdmin.filter.orderDirection,
    };

    return await geofenceLogAdminApi.getGeofenceLogs(
      branchCode,
      params
    );
  }
);

const geofenceLogAdminSlice = createSlice({
  name: "geofenceLogAdmin",
  initialState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.filter.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.filter.limit = action.payload;
    },
    setOrderBy: (state, action: PayloadAction<string>) => {
      state.filter.orderBy = action.payload;
    },
    setOrderDirection: (
      state,
      action: PayloadAction<"ASC" | "DESC">
    ) => {
      state.filter.orderDirection = action.payload;
    },
    clearFilters: (state) => {
      state.filter = {
        ...initialState.filter,
        page: state.filter.page,
        limit: state.filter.limit,
      };
      state.selectedRole = null;
      state.selectedUser = null;
      state.selectedDevice = null;
      state.selectedSite = null;
      state.selectedBranch = null;
      state.selectedUserLabels = [];
      state.selectedDeviceLabels = [];
      state.selectedSiteLabels = [];
    },

    // Setting filter selection objects
    setSelectedRole: (
      state,
      action: PayloadAction<AdminRole | null>
    ) => {
      state.selectedRole = action.payload;
      state.filter.roleId = action.payload?.id || null;
    },
    setSelectedUser: (
      state,
      action: PayloadAction<AdminUser | null>
    ) => {
      state.selectedUser = action.payload;
      state.filter.userId = action.payload?.id || null;
    },
    setSelectedDevice: (
      state,
      action: PayloadAction<AdminDevice | null>
    ) => {
      state.selectedDevice = action.payload;
      state.filter.deviceId = action.payload?.id || null;
    },
    setSelectedSite: (
      state,
      action: PayloadAction<AdminZone | null>
    ) => {
      state.selectedSite = action.payload;
      state.filter.siteId = action.payload?.id || null;
    },
    setSelectedBranch: (
      state,
      action: PayloadAction<AdminBranch | null>
    ) => {
      state.selectedBranch = action.payload;
      state.filter.branchId = action.payload?.id || null;
    },
    setSelectedUserLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedUserLabels = action.payload;
      state.filter.userLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setSelectedDeviceLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedDeviceLabels = action.payload;
      state.filter.deviceLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setSelectedSiteLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedSiteLabels = action.payload;
      state.filter.siteLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setStartDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startDate = action.payload;
    },
    setEndDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endDate = action.payload;
    },
    setStartTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startTime = action.payload;
    },
    setEndTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endTime = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Geofence Logs
      .addCase(fetchGeofenceLogs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchGeofenceLogs.fulfilled,
        (state, action) => {
          state.loading = false;
          state.geofenceLogs = action.payload.data || [];
          state.pagination.total =
            action.payload.meta?.total || 0;
          state.pagination.page =
            action.payload.meta?.page || 1;
          state.pagination.limit =
            action.payload.meta?.limit || 10;
        }
      )
      .addCase(
        fetchGeofenceLogs.rejected,
        (state, action) => {
          state.loading = false;
          state.error =
            action.error.message ||
            "Failed to fetch geofence logs";
        }
      );
  },
});

export default geofenceLogAdminSlice;
