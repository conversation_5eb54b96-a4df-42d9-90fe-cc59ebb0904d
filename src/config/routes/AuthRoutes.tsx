import { Route, RouteProps } from "react-router-dom";
import { lazy } from "react";

// Lazy load components
const Login = lazy(() => import("../../components/Auth/LoginRoute"));
const RootRedirect = lazy(() => import("../../components/Auth/RootRedirect"));

export const getAuthRoutes = (): React.ReactElement<RouteProps>[] => {
  return [
    // Home page redirect
    <Route
      key="root"
      path="/"
      element={<RootRedirect />}
    />,

    // Login page
    <Route
      key="login"
      path="/login"
      element={<Login />}
    />
  ];
}; 