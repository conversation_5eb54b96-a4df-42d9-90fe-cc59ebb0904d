import WebAdminLayout from "../../../components/Layout/WebAdminLayout.tsx";
import { App, Input, Spin } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { MdBusiness } from "react-icons/md";
import { useNavigate } from "react-router";
import {
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks.ts";
import _ from "lodash";
import BranchGrid from "./Components/BranchGrid";
import selectBranchAdminSlice, {
  fetchBranches,
} from "../../../store/slices/admin/selectBranch.admin.slice";
import myProfileMainApi from "../../../services/mainApi/myProfile.mainApi";
import authSlice from "../../../store/slices/auth.slice";
import { useTranslation } from "react-i18next";

const SelectBranch = () => {
  const selectBranch = useAppSelector(
    (state) => state.selectBranchAdmin
  );
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { message } = App.useApp();
  const fetching = useRef(false);
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  const handleSelectBranch = async (branchCode: string) => {
    try {
      setLoading(true);
      const response = await myProfileMainApi.getProfile({
        branch_code: branchCode,
      });
      const hasPermission = response.data?.current_branch;

      if (hasPermission) {
        dispatch(authSlice.actions.setUser(response.data));
        setTimeout(() => {
          const next = new URLSearchParams(
            window.location.search
          ).get("next");
          if (next) {
            navigate(`/admin/branch/${branchCode}/${next}`);
          } else {
            navigate(
              `/admin/branch/${branchCode}/dashboard`
            );
          }
        }, 500);
      } else {
        message.error(
          t("selectBranch.errors.noPermission")
        );
        setLoading(false);
      }
    } catch {
      message.error(t("selectBranch.errors.verifyFailed"));
      setLoading(false);
    }
  };

  const fetch = useCallback(async () => {
    if (fetching.current) return;
    fetching.current = true;
    await dispatch(fetchBranches());
    fetching.current = false;
  }, [dispatch]);

  const debouncedFetchRef = useRef(
    _.debounce(async () => {
      if (fetching.current) return;
      fetching.current = true;
      await dispatch(fetchBranches());
      fetching.current = false;
    }, 500)
  );

  const debouncedFetch = useCallback(() => {
    debouncedFetchRef.current();
  }, []);

  const onSearchChange = useCallback(
    (value: string) => {
      dispatch(
        selectBranchAdminSlice.actions.setFilter({
          search: value,
        })
      );
      debouncedFetch();
    },
    [dispatch, debouncedFetch]
  );

  useEffect(() => {
    fetch();
  }, [fetch]);

  // Pisahkan branch menjadi Main Branch dan Non-Main Branch
  const mainBranches = selectBranch.branches.filter(
    (branch) => branch.id === branch.parent_id
  );
  const nonMainBranches = selectBranch.branches.filter(
    (branch) => branch.id !== branch.parent_id
  );

  return (
    <WebAdminLayout hideMenu={true}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto relative">
          {loading && (
            <div className="absolute inset-0 bg-gray-50/50 dark:bg-gray-900/50 z-50 flex items-center justify-center">
              <Spin size="large" />
            </div>
          )}

          {/* Header Section */}
          <div className="text-center mb-8">
            <MdBusiness className="text-5xl text-blue-600 dark:text-blue-500 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-2">
              {t("selectBranch.title")}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {t("selectBranch.description")}
            </p>
          </div>

          {/* Search Section */}
          <div className="mb-6">
            <Input
              placeholder={t(
                "selectBranch.searchPlaceholder"
              )}
              prefix={
                <SearchOutlined className="text-gray-400 dark:text-gray-500" />
              }
              value={selectBranch.filter.search || ""}
              onChange={(e) =>
                onSearchChange(e.target.value)
              }
              size="large"
              className="rounded-lg dark:bg-gray-800 dark:border-gray-700 dark:text-gray-100"
            />
          </div>

          {/* Head Office Section */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
              {t("selectBranch.headOffice", "Main")}
            </h2>
            <BranchGrid
              branches={mainBranches}
              onSelect={handleSelectBranch}
              loading={selectBranch.loading}
            />
          </div>

          {/* Branch Office Section */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
              {t("selectBranch.clients", "Clients")}
            </h2>
            <BranchGrid
              branches={nonMainBranches}
              onSelect={handleSelectBranch}
              loading={selectBranch.loading}
            />
          </div>
        </div>
      </div>
    </WebAdminLayout>
  );
};

export default SelectBranch;
