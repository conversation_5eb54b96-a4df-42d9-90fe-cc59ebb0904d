import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import userAdminMainApi from "../../../services/mainApi/admin/user.admin.mainApi";
import { AdminUser } from "../../../services/mainApi/admin/types/user.admin.mainApi.types";
import {
  BaseGetRequest,
  BasePaginationMeta,
} from "../../../services/mainApi/types/base.mainApi.types";

interface State {
  users: AdminUser[];
  loading: boolean;
  errorMessage: string | null;
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;
}

const initialState: State = {
  users: [],
  loading: false,
  errorMessage: null,
  filter: {
    search: null,
    page: null,
    limit: null,
    orderBy: "created_at",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
  },
};

export const fetchUsers = createAsyncThunk(
  "userAdmin/fetchUsers",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const { filter } = state.userAdmin;
      const params: BaseGetRequest = {
        search: filter.search || "",
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
      };
      const response = await userAdminMainApi.getUsers(
        params,
        branchCode
      );
      return {
        data: response.data,
        meta: response.meta,
      };
    } catch {
      return rejectWithValue("Failed to fetch users");
    }
  }
);

export const deleteUser = createAsyncThunk(
  "userAdmin/deleteUser",
  async (
    {
      branchCode,
      userId,
    }: { branchCode: string; userId: string },
    { rejectWithValue }
  ) => {
    try {
      await userAdminMainApi.deleteUser(branchCode, userId);
      return userId;
    } catch {
      return rejectWithValue("Failed to delete user");
    }
  }
);

const userAdminSlice = createSlice({
  name: "userAdmin",
  initialState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch users
      .addCase(fetchUsers.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.users = action.payload.data || [];
        state.pagination = action.payload.meta;
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.loading = false;
        state.errorMessage = action.payload as string;
      })
      // Delete user
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.users = state.users.filter(
          (user) => user.id !== action.payload
        );
        state.pagination.total =
          state.pagination.total || 1;
      });
  },
});

export const { setFilter } = userAdminSlice.actions;
export default userAdminSlice;
