import { BaseResponse } from "../../types/base.mainApi.types";
import { AdminBranch } from "./branch.admin.mainApi.types";
import { AdminGeofence } from "./geofence.admin.mainApi.types.ts";

export enum ECheckPointType {
  BEACON = "1",
  NFC = "2",
  RFID = "3",
  GEOFENCE = "4",
}

interface CheckpointType {
  id: ECheckPointType;
  checkpoint_type_name: string;
  checkpoint_type_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

interface Zone {
  id: string;
  branch_id: string;
  parent_branch_id: string;
  timezone_id: string;
  zone_name: string;
  zone_description: string | null;
  zone_address: string;
  latitude: number;
  longitude: number;
  active: boolean;
  interval_active: boolean | null;
  interval_start_time: string | null;
  interval_end_time: string | null;
  subject: string | null;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
}

interface Label {
  id: string;
  branch_id: string;
  label_name: string;
  label_description: string;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

export interface AdminCheckpoint {
  id: string;
  branch_id: string;
  parent_branch_id: string;
  checkpoint_type_id: string;
  zone_id: string;
  beacon_id: string | null;
  geofence_id: string | null;
  checkpoint_name: string;
  checkpoint_description: string | null;
  major_value: string | null;
  minor_value: string | null;
  serial_number_hex: string;
  serial_number_dec: string;
  serial_number_second_hex: string | null;
  serial_number_second_dec: string | null;
  latitude: number;
  longitude: number;
  monday: boolean | null;
  monday_count: number | null;
  tuesday: boolean | null;
  tuesday_count: number | null;
  wednesday: boolean | null;
  wednesday_count: number | null;
  thursday: boolean | null;
  thursday_count: number | null;
  friday: boolean | null;
  friday_count: number | null;
  saturday: boolean | null;
  saturday_count: number | null;
  sunday: boolean | null;
  sunday_count: number | null;
  visit_interval: boolean | null;
  rotation_interval: number | null;
  warning_notification: number | null;
  latest_check_time: string | null;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
  branch: AdminBranch;
  parent_branch: AdminBranch;
  zone: Zone;
  checkpoint_type: CheckpointType;
  labels: Label[];
  geofence?: AdminGeofence | null;
  latest_voltage: number | null;
}

export type AdminCheckpointListResponse = BaseResponse<
  AdminCheckpoint[]
>;
export type AdminCheckpointDetailResponse =
  BaseResponse<AdminCheckpoint>;

export interface CreateUpdateCheckpointRequest {
  checkpoint_name: string;
  checkpoint_description: string;
  zone_id: number | string;
  checkpoint_type_id: number | string;
  beacon_id?: number | string;
  geofence_id?: number | string;
  major_value?: number | string;
  minor_value?: number | string;
  serial_number_hex?: string;
  serial_number_dec?: string;
  serial_number_second_hex?: string;
  serial_number_second_dec?: string;
  latitude: number;
  longitude: number;
  monday: boolean;
  monday_count?: number;
  tuesday: boolean;
  tuesday_count?: number;
  wednesday: boolean;
  wednesday_count?: number;
  thursday: boolean;
  thursday_count?: number;
  friday: boolean;
  friday_count?: number;
  saturday: boolean;
  saturday_count?: number;
  sunday: boolean;
  sunday_count?: number;
  visit_interval: boolean;
  rotation_interval: number;
  warning_notification: number;
  label_ids: (number | string)[];
  active: boolean;
}
