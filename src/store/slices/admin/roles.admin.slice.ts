import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { AdminRole } from "../../../services/mainApi/admin/types/roles.admin.mainApi.types.ts";
import rolesAdminMainApi from "../../../services/mainApi/admin/roles.admin.mainApi.ts";
import {
  BaseGetRequest,
  BasePaginationMeta,
} from "../../../services/mainApi/types/base.mainApi.types.ts";
import { RootState } from "../../index.ts";

interface State {
  roles: AdminRole[];
  loading: boolean;
  errorMessage: string | null;
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;
}

const initState: State = {
  roles: [],
  loading: false,
  errorMessage: null,
  filter: {
    search: null,
    page: null,
    limit: null,
    orderBy: "created_at",
    orderDirection: "ASC",
  },
  pagination: {
    total: 0,
  },
};

export const fetchRoles = createAsyncThunk(
  "rolesAdmin/fetchRoles",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const { filter } = state.rolesAdmin;
      const params: BaseGetRequest = {
        search: filter.search || "",
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
      };
      const response = await rolesAdminMainApi.getRoles({
        ...params,
      });
      return {
        data: response.data,
        meta: response.meta,
      };
    } catch {
      return rejectWithValue("Failed to fetch roles");
    }
  }
);

const rolesAdminSlice = createSlice({
  name: "rolesAdmin",
  initialState: {
    ...initState,
  },
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchRoles.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(fetchRoles.fulfilled, (state, action) => {
        state.loading = false;
        state.roles = action.payload.data || [];
        state.pagination = action.payload.meta;
      })
      .addCase(fetchRoles.rejected, (state, action) => {
        state.loading = false;
        state.errorMessage = action.payload as string;
      });
  },
});

export default rolesAdminSlice;
