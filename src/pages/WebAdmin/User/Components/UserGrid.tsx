import React, { useEffect } from "react";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks";
import {
  fetchUsers,
  setFilter,
} from "../../../../store/slices/admin/user.admin.slice";
import { useParams } from "react-router-dom";
import {
  Empty,
  Pagination,
  Spin,
  Tag,
  Tooltip,
  Button,
} from "antd";
import { useTranslation } from "react-i18next";
import { AdminUser } from "../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { FiClock, FiEdit } from "react-icons/fi";
import { MdMail, MdPhone, MdWarning } from "react-icons/md";
import addUserAdminSlice from "../../../../store/slices/admin/addUser.admin.slice";

dayjs.extend(relativeTime);

const UserGrid: React.FC = () => {
  const dispatch = useAppDispatch();
  const { users, loading, filter, pagination } =
    useAppSelector((state) => state.userAdmin);
  const auth = useAppSelector((state) => state.auth);
  const { branchCode } = useParams();
  const isLoading = React.useRef(false);
  const { t } = useTranslation();

  const allowEdit = (user: AdminUser) => {
    const isCurrentUserSuperAdmin =
      auth.user?.role?.super_admin;
    const isTargetUserSuperAdmin = user.role?.super_admin;

    if (!isCurrentUserSuperAdmin) {
      if (isTargetUserSuperAdmin) {
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  };

  const fetch = React.useCallback(async () => {
    if (isLoading.current) return;
    isLoading.current = true;
    await dispatch(fetchUsers(branchCode || ""));
    isLoading.current = false;
  }, [branchCode, dispatch]);

  useEffect(() => {
    fetch();
  }, [fetch]);

  const handlePageChange = (
    page: number,
    pageSize: number
  ) => {
    dispatch(
      setFilter({
        page,
        limit: pageSize,
      })
    );
  };

  const handleEdit = (user: AdminUser) => {
    dispatch(addUserAdminSlice.actions.openEdit(user));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Spin size="large" />
      </div>
    );
  }

  if (!users.length) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Empty
          description={
            <span className="text-gray-500 dark:text-gray-400">
              {t("users.list.noUsersFound")}
            </span>
          }
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Users Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {users.map((user) => (
          <div
            key={user.id}
            className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border ${
              user.active
                ? "border-gray-200 dark:border-gray-700"
                : "border-red-100 dark:border-red-900 bg-red-50/10"
            } overflow-hidden h-full`}
          >
            <div className="p-4 flex flex-col h-full">
              {/* Header: Name, Email, Status */}
              <div className="flex items-start justify-between mb-3 pb-2 border-b dark:border-gray-700">
                <div className="flex-1">
                  <div className="flex items-center gap-2 flex-wrap">
                    <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 truncate">
                      {user.name}
                    </h3>
                    {!user.active && (
                      <Tag
                        icon={
                          <MdWarning className="mr-1" />
                        }
                        className="bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 border-red-200 dark:border-red-800"
                      >
                        {t("users.userItem.inactive")}
                      </Tag>
                    )}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1 mt-1 truncate">
                    <MdMail className="text-gray-400 flex-shrink-0" />
                    <span className="truncate">
                      {user.email}
                    </span>
                  </div>
                </div>
                <div className="flex gap-1">
                  {allowEdit(user) && (
                    <Tooltip
                      title={t("users.modal.actions.edit")}
                    >
                      <Button
                        type="text"
                        icon={
                          <FiEdit className="text-lg" />
                        }
                        onClick={() => handleEdit(user)}
                        className="text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400"
                      />
                    </Tooltip>
                  )}
                </div>
              </div>

              {/* User Info */}
              <div className="flex-grow space-y-2">
                {/* Role & Access */}
                <div className="flex flex-wrap gap-1 mb-2">
                  {user.role && (
                    <Tag className="bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400">
                      {user.role.role_name}
                    </Tag>
                  )}
                  {user.web_access && (
                    <Tag className="bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400">
                      {t("users.userItem.web")}
                    </Tag>
                  )}
                  {user.mobile_access && (
                    <Tag className="bg-purple-50 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400">
                      {t("users.userItem.mobile")}
                    </Tag>
                  )}
                </div>

                {/* Phone */}
                {user.phone && (
                  <div className="text-xs text-gray-600 dark:text-gray-300 flex items-center gap-1">
                    <MdPhone className="text-gray-400" />
                    <span>{user.phone}</span>
                  </div>
                )}

                {/* Branches */}
                {user.branches &&
                  user.branches.length > 0 && (
                    <div className="mt-2">
                      <div className="text-xs text-gray-500 dark:text-gray-400 font-medium mb-1">
                        {t(
                          "users.userItem.branchAssignment"
                        )}
                        :
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-300 line-clamp-2">
                        {user.branches
                          .map(
                            (branch) => branch.branch_name
                          )
                          .join(", ")}
                      </div>
                    </div>
                  )}

                {/* Labels */}
                {user.labels && user.labels.length > 0 && (
                  <div className="mt-2">
                    <div className="text-xs text-gray-500 dark:text-gray-400 font-medium mb-1">
                      {t("users.userItem.labels", "Labels")}
                      :
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {user.labels.map((label) => (
                        <Tag
                          key={label.id}
                          className="bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400"
                        >
                          {label.label_name}
                        </Tag>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Footer with timestamps */}
              <div className="flex flex-wrap items-center gap-2 text-xs text-gray-500 dark:text-gray-400 pt-2 mt-2 border-t dark:border-gray-700">
                {user.created_at && (
                  <Tooltip
                    title={dayjs(user.created_at).format(
                      "DD MMM YYYY HH:mm"
                    )}
                  >
                    <div className="flex items-center gap-1">
                      <FiClock className="text-xs" />
                      <span>
                        {dayjs(user.created_at).fromNow()}
                      </span>
                    </div>
                  </Tooltip>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3">
        <Pagination
          current={filter.page || 1}
          pageSize={filter.limit || 10}
          total={pagination.total}
          onChange={handlePageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              {t("common.pagination.total", { total })}
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

export default UserGrid;
