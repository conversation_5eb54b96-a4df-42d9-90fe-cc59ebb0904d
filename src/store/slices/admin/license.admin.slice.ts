import {
  createAsyncThunk,
  createSlice,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import licenseAdminApi from "../../../services/mainApi/admin/license.admin.mainApi";
import { AdminLicense } from "../../../services/mainApi/admin/types/license.admin.mainApi.types";
import { BasePaginationMeta } from "../../../services/mainApi/types/base.mainApi.types";
interface State {
  licenses: AdminLicense[];
  loading: boolean;
  errorMessage: string | null;
  pagination: BasePaginationMeta;
}

const initState: State = {
  licenses: [],
  loading: false,
  errorMessage: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    total_pages: 0,
  },
};

export const fetchLicenseAdmin = createAsyncThunk<
  { data: AdminLicense[]; meta: BasePaginationMeta },
  string,
  { state: RootState; rejectValue: string }
>(
  "licenseAdmin/fetchLicenses",
  async (branchCode: string, { rejectWithValue }) => {
    try {
      const response =
        await licenseAdminApi.getList(branchCode);
      return {
        data: response.data || [],
        meta: response.meta,
      };
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue("Failed to fetch licenses");
    }
  }
);

const licenseAdminSlice = createSlice({
  name: "licenseAdmin",
  initialState: { ...initState },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchLicenseAdmin.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchLicenseAdmin.fulfilled,
        (state, action) => {
          state.loading = false;
          state.licenses = action.payload.data;
          state.pagination = action.payload.meta;
        }
      )
      .addCase(
        fetchLicenseAdmin.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      );
  },
});

export default licenseAdminSlice;
