import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { taskLogAdminApi } from "../../../services/mainApi/admin/taskLog.admin.mainApi";
import { AdminRole } from "../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminUser } from "../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminBranch } from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import {
  AdminTaskLog,
  AdminTaskLogListParams,
} from "../../../services/mainApi/admin/types/taskLog.admin.mainApi.types";
import { AdminLabel } from "../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminTask } from "../../../services/mainApi/admin/types/task.admin.mainApi.types.ts";
import dayjs from "dayjs";

interface TaskLogAdminState {
  taskLogs: AdminTaskLog[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filter: {
    page: number;
    limit: number;
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    branchId: string | null;
    roleId: string | null;
    userId: string | null;
    userLabels: string[];
    deviceId: string | null;
    deviceLabels: string[];
    taskId: string | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  selectedRole: AdminRole | null;
  selectedUser: AdminUser | null;
  selectedDevice: AdminDevice | null;
  selectedTask: AdminTask | null;
  selectedBranch: AdminBranch | null;
  selectedUserLabels: AdminLabel[];
  selectedDeviceLabels: AdminLabel[];
}

const initialState: TaskLogAdminState = {
  taskLogs: [],
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
  filter: {
    page: 1,
    limit: 10,
    startDate: dayjs()
      .subtract(1, "day")
      .format("YYYY-MM-DD"),
    endDate: dayjs().format("YYYY-MM-DD"),
    startTime: "00:00:00",
    endTime: "23:59:59",
    branchId: null,
    roleId: null,
    userId: null,
    userLabels: [],
    deviceId: null,
    deviceLabels: [],
    taskId: null,
    orderBy: "original_submitted_time",
    orderDirection: "DESC",
  },
  selectedRole: null,
  selectedUser: null,
  selectedDevice: null,
  selectedTask: null,
  selectedBranch: null,
  selectedUserLabels: [],
  selectedDeviceLabels: [],
};

// Async thunk for fetching task logs
export const fetchTaskLogs = createAsyncThunk(
  "taskLogAdmin/fetchTaskLogs",
  async (
    { branchCode }: { branchCode: string },
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as {
        taskLogAdmin: TaskLogAdminState;
      };
      const { filter } = state.taskLogAdmin;

      const params: AdminTaskLogListParams = {
        page: filter.page,
        limit: filter.limit,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
      };

      // Add optional parameters if they exist
      if (filter.startDate)
        params.start_date = filter.startDate;
      if (filter.endDate) params.end_date = filter.endDate;
      if (filter.startTime)
        params.start_time = filter.startTime;
      if (filter.endTime) params.end_time = filter.endTime;
      if (filter.branchId)
        params.branch_id = filter.branchId;
      if (filter.roleId) params.role_id = filter.roleId;
      if (filter.userId) params.user_id = filter.userId;
      if (filter.userLabels.length > 0)
        params.user_labels = filter.userLabels.join(",");
      if (filter.deviceId)
        params.device_id = filter.deviceId;
      if (filter.deviceLabels.length > 0)
        params.device_labels =
          filter.deviceLabels.join(",");
      if (filter.taskId) params.task_id = filter.taskId;

      const response = await taskLogAdminApi.getTaskLogs(
        branchCode,
        params
      );
      return response;
    } catch {
      return rejectWithValue("Failed to fetch task logs");
    }
  }
);

const taskLogAdminSlice = createSlice({
  name: "taskLogAdmin",
  initialState,
  reducers: {
    setPage: (state, action: PayloadAction<number>) => {
      state.filter.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.filter.limit = action.payload;
    },
    setStartDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startDate = action.payload;
    },
    setEndDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endDate = action.payload;
    },
    setStartTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startTime = action.payload;
    },
    setEndTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endTime = action.payload;
    },
    setSelectedRole: (
      state,
      action: PayloadAction<AdminRole | null>
    ) => {
      state.selectedRole = action.payload;
      state.filter.roleId = action.payload?.id || null;
    },
    setSelectedUser: (
      state,
      action: PayloadAction<AdminUser | null>
    ) => {
      state.selectedUser = action.payload;
      state.filter.userId = action.payload?.id || null;
    },
    setSelectedUserLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedUserLabels = action.payload;
      state.filter.userLabels = action.payload.map(
        (label) => label.id
      );
    },
    setSelectedDevice: (
      state,
      action: PayloadAction<AdminDevice | null>
    ) => {
      state.selectedDevice = action.payload;
      state.filter.deviceId = action.payload?.id || null;
    },
    setSelectedDeviceLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedDeviceLabels = action.payload;
      state.filter.deviceLabels = action.payload.map(
        (label) => label.id
      );
    },
    setSelectedTask: (
      state,
      action: PayloadAction<AdminTask | null>
    ) => {
      state.selectedTask = action.payload;
      state.filter.taskId = action.payload?.id || null;
    },
    setSelectedBranch: (
      state,
      action: PayloadAction<AdminBranch | null>
    ) => {
      state.selectedBranch = action.payload;
      state.filter.branchId = action.payload?.id || null;
    },
    clearFilters: (state) => {
      state.filter = {
        ...state.filter,
        startDate: null,
        endDate: null,
        startTime: null,
        endTime: null,
        branchId: null,
        roleId: null,
        userId: null,
        userLabels: [],
        deviceId: null,
        deviceLabels: [],
        taskId: null,
      };
      state.selectedRole = null;
      state.selectedUser = null;
      state.selectedDevice = null;
      state.selectedTask = null;
      state.selectedBranch = null;
      state.selectedUserLabels = [];
      state.selectedDeviceLabels = [];
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchTaskLogs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTaskLogs.fulfilled, (state, action) => {
        state.loading = false;
        state.taskLogs = action.payload.data;
        state.pagination = {
          page: action.payload.meta.page,
          limit: action.payload.meta.limit,
          total: action.payload.meta.total,
          totalPages: action.payload.meta.totalPages,
        };
      })
      .addCase(fetchTaskLogs.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default taskLogAdminSlice;
