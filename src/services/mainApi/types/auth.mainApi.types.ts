import { BaseResponse } from "./base.mainApi.types";

export interface User {
  id: string;
  name: string;
  email: string;
  web_access: boolean;
  mobile_access: boolean;
  system_access: boolean;
}

export interface LoginResponseData {
  access_token: string;
  token_type: string;
  expires_in: number;
  expires_at: string;
  user: User;
}

export type LoginResponse = BaseResponse<LoginResponseData>;

export interface PermissionData {
  has_permission: boolean;
}

export type BranchPermissionResponse =
  BaseResponse<PermissionData>;
export type RolesPermissionResponse =
  BaseResponse<PermissionData>;
