import {
  createSlice,
  createAsyncThunk,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../../../store";
import beaconSysAdminMainApi from "../../../services/mainApi/sysadmin/beacon.sysadmin.mainApi";
import { 
  Beacon, 
  CreateBeaconDto, 
  UpdateBeaconDto 
} from "../../../services/mainApi/sysadmin/types/beacon.mainApi.types";
import { isAxiosError } from "axios";
import { handleApiError } from "../../../utils/error";

interface State {
  open: boolean;
  mode: "create" | "update";
  beaconEdit: Beacon | null;
  loading: boolean;
  errorMessage: string[] | null;

  parent_branch_id: string;
  beacon_name: string;
  beacon_description: string;
  beacon_uuid: string;
  counter_customer_code: string;
  pip_api_key: string;
  active: boolean;
}

const initialState: State = {
  open: false,
  mode: "create",
  beaconEdit: null,
  loading: false,
  errorMessage: null,

  parent_branch_id: "",
  beacon_name: "",
  beacon_description: "",
  beacon_uuid: "",
  counter_customer_code: "",
  pip_api_key: "",
  active: true,
};

export const createBeacon = createAsyncThunk(
  "addBeaconSysadmin/createBeacon",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const beaconData = state.addBeaconSysadmin;
      
      const createData: CreateBeaconDto = {
        parent_branch_id: Number(beaconData.parent_branch_id),
        beacon_name: beaconData.beacon_name,
        beacon_description: beaconData.beacon_description,
        beacon_uuid: beaconData.beacon_uuid,
        counter_customer_code: beaconData.counter_customer_code,
        pip_api_key: beaconData.pip_api_key,
        active: beaconData.active,
      };
      
      const response = await beaconSysAdminMainApi.create(createData);
      return response;
    } catch (error) {
      if (isAxiosError(error)) {
        const e = handleApiError(error);
        return rejectWithValue(e.errors);
      }
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue("An unknown error occurred");
    }
  }
);

export const updateBeacon = createAsyncThunk(
  "addBeaconSysadmin/updateBeacon",
  async (
    beaconId: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const beaconData = state.addBeaconSysadmin;
      
      const updateData: UpdateBeaconDto = {
        parent_branch_id: Number(beaconData.parent_branch_id),
        beacon_name: beaconData.beacon_name,
        beacon_description: beaconData.beacon_description,
        beacon_uuid: beaconData.beacon_uuid,
        counter_customer_code: beaconData.counter_customer_code,
        pip_api_key: beaconData.pip_api_key,
        active: beaconData.active,
      };
      
      const response = await beaconSysAdminMainApi.update(beaconId, updateData);
      return response;
    } catch (error) {
      if (isAxiosError(error)) {
        const e = handleApiError(error);
        return rejectWithValue(e.errors);
      }
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue("An unknown error occurred");
    }
  }
);

const addBeaconSysadminSlice = createSlice({
  name: "addBeaconSysadmin",
  initialState,
  reducers: {
  open: (state) => {
    state.open = true;
    state.mode = "create";
  },
  openEdit: (
    state,
    action: PayloadAction<Beacon>
  ) => {
    state.open = true;
    state.mode = "update";
    state.beaconEdit = action.payload;
    state.parent_branch_id = action.payload.parent_branch_id.toString();
    state.beacon_name = action.payload.beacon_name;
    state.beacon_description = action.payload.beacon_description || "";
    state.beacon_uuid = action.payload.beacon_uuid;
    state.counter_customer_code = action.payload.counter_customer_code || "";
    state.pip_api_key = action.payload.pip_api_key || "";
    state.active = action.payload.active;
  },
  close: () => {
    return {
      ...initialState,
    };
  },
  setError: (
    state,
    action: PayloadAction<string | null>
  ) => {
    state.errorMessage = action.payload ? [action.payload] : null;
  },
  setParentBranchId: (
    state,
    action: PayloadAction<string>
  ) => {
    state.parent_branch_id = action.payload;
  },
  setBeaconName: (
    state,
    action: PayloadAction<string>
  ) => {
    state.beacon_name = action.payload;
  },
  setBeaconDescription: (
    state,
    action: PayloadAction<string>
  ) => {
    state.beacon_description = action.payload;
  },
  setBeaconUuid: (
    state,
    action: PayloadAction<string>
  ) => {
    state.beacon_uuid = action.payload;
  },
  setCounterCustomerCode: (
    state,
    action: PayloadAction<string>
  ) => {
    state.counter_customer_code = action.payload;
  },
  setPipApiKey: (
    state,
    action: PayloadAction<string>
  ) => {
    state.pip_api_key = action.payload;
  },
  setActive: (state, action: PayloadAction<boolean>) => {
    state.active = action.payload;
  },
  },
  extraReducers: (builder) => {
    builder
      // Create Beacon
      .addCase(createBeacon.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(createBeacon.fulfilled, (state) => {
        state.loading = false;
        state.open = false;
      })
      .addCase(createBeacon.rejected, (state, action) => {
        state.loading = false;
        state.errorMessage = action.payload as string[];
      })
      
      // Update Beacon
      .addCase(updateBeacon.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(updateBeacon.fulfilled, (state) => {
        state.loading = false;
        state.open = false;
      })
      .addCase(updateBeacon.rejected, (state, action) => {
        state.loading = false;
        state.errorMessage = action.payload as string[];
      });
  },
});

export default addBeaconSysadminSlice; 