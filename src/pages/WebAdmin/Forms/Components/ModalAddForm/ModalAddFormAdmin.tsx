import {
  Button,
  Form,
  Input,
  Modal,
  Select,
  Switch,
  App,
  Tooltip,
  Space,
} from "antd";
import { useTranslation } from "react-i18next";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks";
import SelectRole from "../../../../../components/Roles/SelectRole";
import GridSelectBranch from "../../../../../components/Branch/GridSelectBranch";
import SelectCheckpoint from "../../../../../components/Checkpoint/SelectCheckpoint";
import {
  DeleteOutlined,
  PlusOutlined,
  InfoCircleOutlined,
  FormOutlined,
} from "@ant-design/icons";
import addFormAdminSlice, {
  createFormAdmin,
  updateFormAdmin,
} from "../../../../../store/slices/admin/addForm.admin.slice";
import { FieldType } from "../../../../../types/FieldType.enum";
import SelectPicklist from "../../../../../components/FormPicklist/SelectPicklist";
import { useParams } from "react-router-dom";
import ErrorMessage from "../../../../../components/Common/ErrorMessage/ErrorMessage";
import { fetchFormsAdmin } from "../../../../../store/slices/admin/form.admin.slice";

const { TextArea } = Input;

const ModalAddFormAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { actions } = addFormAdminSlice;
  const { message } = App.useApp();
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const modal = useAppSelector(
    (state) => state.addFormAdmin
  );

  const handleClose = async () => {
    dispatch(actions.close());
  };

  const onSubmit = async () => {
    dispatch(actions.setError(null));
    try {
      if (modal.mode === "update") {
        const update = await dispatch(
          updateFormAdmin(branchCode || "")
        );
        if (update.meta.requestStatus === "rejected") {
          throw new Error("Failed to update form");
        }
      } else {
        const update = await dispatch(
          createFormAdmin(branchCode || "")
        );
        if (update.meta.requestStatus === "rejected") {
          throw new Error("Failed to create form");
        }
      }
      message.success("Form saved successfully");
      handleClose();
      await dispatch(fetchFormsAdmin(branchCode || ""));
    } catch {
      message.error(
        "An unexpected error occurred while saving the form"
      );
    }
  };

  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <FormOutlined className="text-blue-500" />
          <span>{t("forms.modal.add.title")}</span>
        </div>
      }
      open={modal.open}
      onOk={onSubmit}
      onCancel={handleClose}
      className="rounded-xl"
      okButtonProps={{
        className: "bg-blue-500 hover:bg-blue-600 border-0",
      }}
      confirmLoading={modal.loading}
      width={800}
    >
      <Form
        layout="vertical"
        className="mt-4"
      >
        {/* Basic Information Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
            <h3 className="text-lg font-medium">
              {t(
                "forms.modal.sections.basic",
                "Basic Information"
              )}
            </h3>
            <Tooltip
              title={t(
                "forms.modal.sections.basic.tooltip",
                "Fill in the basic details of your form"
              )}
            >
              <InfoCircleOutlined className="text-gray-400" />
            </Tooltip>
          </div>

          <div className="p-6 rounded-lg space-y-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600">
            {/* Form Name & Description */}
            <div className="grid grid-cols-1 gap-4">
              <Form.Item
                label={t(
                  "forms.modal.form.name.label",
                  "Form Name"
                )}
                required
                tooltip={t(
                  "forms.modal.form.name.tooltip",
                  "Enter a descriptive name for your form"
                )}
              >
                <Input
                  placeholder={t(
                    "forms.modal.form.name.placeholder",
                    "e.g., Daily Inspection Form"
                  )}
                  className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                  value={modal.form_name}
                  onChange={(e) =>
                    dispatch(
                      actions.setName(e.target.value)
                    )
                  }
                />
              </Form.Item>

              <Form.Item
                label={t(
                  "forms.modal.form.description.label",
                  "Description"
                )}
                tooltip={t(
                  "forms.modal.form.description.tooltip",
                  "Provide details about the form's purpose"
                )}
              >
                <TextArea
                  rows={3}
                  placeholder={t(
                    "forms.modal.form.description.placeholder",
                    "Describe the purpose and usage of this form..."
                  )}
                  className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                  value={modal.form_description || ""}
                  onChange={(e) =>
                    dispatch(
                      actions.setDescription(e.target.value)
                    )
                  }
                />
              </Form.Item>
            </div>

            {/* Role, Branch, & Checkpoint Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                label={t(
                  "forms.modal.form.role.label",
                  "Role Access"
                )}
                tooltip={t(
                  "forms.modal.form.role.tooltip",
                  "Select which roles can access this form"
                )}
              >
                <SelectRole
                  value={modal.role_id || undefined}
                  onChange={(value) =>
                    dispatch(actions.setRole(value))
                  }
                />
              </Form.Item>

              <Form.Item
                label={t(
                  "forms.modal.form.checkpoint.label",
                  "Associated Checkpoint"
                )}
                tooltip={t(
                  "forms.modal.form.checkpoint.tooltip",
                  "Link this form to a specific checkpoint"
                )}
              >
                <SelectCheckpoint
                  value={modal.checkpoint_id || undefined}
                  onChange={(value) => {
                    if (!Array.isArray(value)) {
                      dispatch(
                        actions.setCheckpointId(value)
                      );
                    }
                  }}
                />
              </Form.Item>
            </div>

            <Form.Item
              label={t(
                "forms.modal.form.branches.label",
                "Branch Availability"
              )}
              tooltip={t(
                "forms.modal.form.branches.tooltip",
                "Select which branches can use this form"
              )}
              required
            >
              <GridSelectBranch
                multiple
                value={modal.branch_ids || undefined}
                onChange={(value) => {
                  if (Array.isArray(value)) {
                    dispatch(
                      actions.setBranches(
                        value.map((branch) => branch.id)
                      )
                    );
                  }
                }}
              />
            </Form.Item>

            {/* Form Status */}
            <div className="flex items-center justify-between p-3 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300">
                  {t(
                    "forms.modal.form.active.label",
                    "Form Status"
                  )}
                </h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t(
                    "forms.modal.form.active.description",
                    "Enable or disable this form"
                  )}
                </p>
              </div>
              <Switch
                checked={modal.active}
                onChange={(checked) =>
                  dispatch(actions.setActive(checked))
                }
              />
            </div>
          </div>
        </div>

        {/* Fields Section */}
        <div className="mt-8 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
              <h3 className="text-lg font-medium">
                {t(
                  "forms.modal.sections.fields",
                  "Form Fields"
                )}
              </h3>
              <Tooltip
                title={t(
                  "forms.modal.sections.fields.tooltip",
                  "Add and configure form fields"
                )}
              >
                <InfoCircleOutlined className="text-gray-400" />
              </Tooltip>
            </div>
            <Button
              type="dashed"
              icon={<PlusOutlined />}
              onClick={() => dispatch(actions.addField())}
              className="border-blue-500 text-blue-500"
            >
              {t("forms.modal.field.add")}
            </Button>
          </div>

          {modal.fields.map((field, index) => (
            <div
              key={field.rowId}
              className="p-6 rounded-lg bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600"
            >
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-base font-medium text-gray-700 dark:text-gray-200 flex items-center gap-2">
                  <span>
                    {t("forms.modal.field.title")} #
                    {index + 1}
                  </span>
                  {field.form_field_require && (
                    <span className="text-red-500 text-sm">
                      *Required
                    </span>
                  )}
                </h4>
                <Space>
                  <Switch
                    size="small"
                    checked={field.form_field_require}
                    onChange={(checked) =>
                      dispatch(
                        actions.setFieldRequired({
                          rowId: field.rowId,
                          form_field_require: checked,
                        })
                      )
                    }
                    checkedChildren="Required"
                    unCheckedChildren="Optional"
                  />
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() =>
                      dispatch(
                        actions.removeField(field.rowId)
                      )
                    }
                  />
                </Space>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Form.Item
                  label={t(
                    "forms.modal.field.type.label",
                    "Field Type"
                  )}
                  required
                  tooltip={t(
                    "forms.modal.field.type.tooltip",
                    "Select the type of input for this field"
                  )}
                >
                  <Select
                    value={field.field_type_id}
                    onChange={(value) =>
                      dispatch(
                        actions.setFieldType({
                          rowId: field.rowId,
                          field_type_id: value,
                        })
                      )
                    }
                    className="[&_.ant-select-selector]:dark:bg-gray-700 [&_.ant-select-selector]:dark:border-gray-600 [&_.ant-select-selection-item]:dark:text-gray-200"
                    options={[
                      {
                        value: FieldType.INPUT,
                        label: t(
                          "forms.modal.field.type.text",
                          "Text Input"
                        ),
                      },
                      {
                        value: FieldType.SELECT,
                        label: t(
                          "forms.modal.field.type.select",
                          "Dropdown Select"
                        ),
                      },
                      {
                        value: FieldType.CHECKBOX,
                        label: t(
                          "forms.modal.field.type.checkbox",
                          "Checkbox"
                        ),
                      },
                      {
                        value: FieldType.IMAGE,
                        label: t(
                          "forms.modal.field.type.image",
                          "Image Upload"
                        ),
                      },
                      {
                        value: FieldType.SIGNATURE,
                        label: t(
                          "forms.modal.field.type.signature",
                          "Digital Signature"
                        ),
                      },
                    ]}
                  />
                </Form.Item>

                <Form.Item
                  label={t(
                    "forms.modal.field.name.label",
                    "Field Name"
                  )}
                  required
                  tooltip={t(
                    "forms.modal.field.name.tooltip",
                    "Enter a name for this field"
                  )}
                >
                  <Input
                    value={field.form_field_name}
                    onChange={(e) =>
                      dispatch(
                        actions.setFieldName({
                          rowId: field.rowId,
                          form_field_name: e.target.value,
                        })
                      )
                    }
                    placeholder={t(
                      "forms.modal.field.name.placeholder",
                      "e.g., Equipment Status"
                    )}
                    className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                  />
                </Form.Item>
              </div>

              <Form.Item
                label={t(
                  "forms.modal.field.description.label",
                  "Field Description"
                )}
                tooltip={t(
                  "forms.modal.field.description.tooltip",
                  "Add helpful instructions for this field"
                )}
              >
                <TextArea
                  rows={2}
                  value={field.form_field_description || ""}
                  onChange={(e) =>
                    dispatch(
                      actions.setFieldDescription({
                        rowId: field.rowId,
                        form_field_description:
                          e.target.value,
                      })
                    )
                  }
                  className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                  placeholder={t(
                    "forms.modal.field.description.placeholder",
                    "Provide instructions or additional context..."
                  )}
                />
              </Form.Item>

              {field.field_type_id === FieldType.SELECT && (
                <Form.Item
                  label={t(
                    "forms.modal.field.picklist.label",
                    "Picklist"
                  )}
                  required
                >
                  <SelectPicklist
                    value={
                      field.form_picklist_id || undefined
                    }
                    onChange={(value) =>
                      dispatch(
                        actions.setFieldPicklist({
                          rowId: field.rowId,
                          form_picklist_id: value,
                        })
                      )
                    }
                  />
                </Form.Item>
              )}

              <div className="flex gap-4">
                <Form.Item
                  label={t(
                    "forms.modal.field.required.label",
                    "Required"
                  )}
                  valuePropName="checked"
                >
                  <Switch
                    checked={field.form_field_require}
                    onChange={(checked) =>
                      dispatch(
                        actions.setFieldRequired({
                          rowId: field.rowId,
                          form_field_require: checked,
                        })
                      )
                    }
                  />
                </Form.Item>

                <Form.Item
                  label={t(
                    "forms.modal.field.active.label",
                    "Active"
                  )}
                  valuePropName="checked"
                >
                  <Switch
                    checked={field.active}
                    onChange={(checked) =>
                      dispatch(
                        actions.setFieldActive({
                          rowId: field.rowId,
                          active: checked,
                        })
                      )
                    }
                  />
                </Form.Item>
              </div>
            </div>
          ))}

          <Button
            type="dashed"
            onClick={() => dispatch(actions.addField())}
            icon={<PlusOutlined />}
            className="w-full dark:border-gray-600 dark:text-gray-300 dark:hover:border-gray-500 dark:hover:text-gray-200"
          >
            {t("forms.modal.field.add")}
          </Button>
        </div>
      </Form>
      {modal.errorMessage && (
        <div className="mt-4">
          <ErrorMessage message={modal.errorMessage} />
        </div>
      )}
    </Modal>
  );
};

export default ModalAddFormAdmin;
