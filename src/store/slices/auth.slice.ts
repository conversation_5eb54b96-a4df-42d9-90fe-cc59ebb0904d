import {
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import {
  MyProfileResponseData,
  CurrentUserRole,
} from "../../services/mainApi/types/myProfile.mainApi.types.ts";

interface AuthState {
  user: MyProfileResponseData | null;
  isAuthenticated: boolean;
  role: CurrentUserRole | null;
}

const initialState: AuthState = {
  user: null,
  role: null,
  isAuthenticated: false,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setUser: (
      state,
      action: PayloadAction<MyProfileResponseData | null>
    ) => {
      state.user = action.payload;
      state.isAuthenticated = !!action.payload;
      state.role = action.payload?.role || null;
    },
    clearUser: (state) => {
      state.user = null;
      state.role = null;
      state.isAuthenticated = false;
    },
  },
});

export default authSlice;
