import React, { useEffect, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, useMap } from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import "./GpsHeatmap.css";

// Import leaflet.heat without TypeScript definitions
import "leaflet.heat";

// Define a type for the heat layer to avoid 'any'
interface HeatLayer {
  addTo: (map: L.Map) => HeatLayer;
}

// Komponen untuk menangani heatmap layer
const HeatmapLayer: React.FC<{ points: number[][] }> = ({ points }) => {
  const map = useMap();
  const heatLayerRef = useRef<HeatLayer | null>(null);

  useEffect(() => {
    if (!points || points.length === 0) return;

    // Hapus layer sebelumnya jika ada
    if (heatLayerRef.current) {
      map.removeLayer(heatLayerRef.current as unknown as <PERSON><PERSON>er);
    }

    try {
      // Buat layer heatmap dengan warna yang lebih kontras
      // @ts-expect-error - Leaflet heat is added to L namespace but TypeScript doesn't know about it
      heatLayerRef.current = L.heatLayer(points, {
        radius: 25,
        blur: 15,
        maxZoom: 17,
        max: 1.0,
        // Updated gradient with more vibrant and contrasting colors
        gradient: { 
          0.1: '#66ff00',  // Bright green for low density
          0.3: '#ffff00',  // Yellow for low-medium density
          0.5: '#ff9900',  // Orange for medium density
          0.7: '#ff3300',  // Bright red for medium-high density
          0.9: '#990099'   // Purple for highest density
        },
      }).addTo(map);
    } catch (error) {
      console.error("Error creating heatmap layer:", error);
    }

    // Cleanup function
    return () => {
      if (heatLayerRef.current) {
        map.removeLayer(heatLayerRef.current as unknown as L.Layer);
      }
    };
  }, [map, points]);

  return null;
};

// Props untuk komponen GpsHeatmap
interface GpsHeatmapProps {
  points: number[][];
  loading?: boolean;
  initialCenter?: [number, number];
  initialZoom?: number;
}

// Komponen untuk menangani resize map
const MapResizer: React.FC = () => {
  const map = useMap();

  useEffect(() => {
    const handleResize = () => {
      map.invalidateSize();
    };

    window.addEventListener("resize", handleResize);

    // Trigger resize setelah komponen dimount
    setTimeout(() => {
      handleResize();
    }, 100);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [map]);

  return null;
};

// Komponen utama GpsHeatmap
const GpsHeatmap: React.FC<GpsHeatmapProps> = ({
  points,
  loading = false,
  initialCenter = [-6.2088, 106.8456], // Default: Jakarta (Monas)
  initialZoom = 12,
}) => {
  // Hitung center berdasarkan titik-titik yang ada
  const calculateCenter = (): [number, number] => {
    if (points && points.length > 0) {
      const validPoints = points.filter(
        (p) => p.length >= 2 && typeof p[0] === 'number' && typeof p[1] === 'number'
      );

      if (validPoints.length === 0) return initialCenter;

      const sumLat = validPoints.reduce((sum, p) => sum + p[0], 0);
      const sumLng = validPoints.reduce((sum, p) => sum + p[1], 0);

      return [sumLat / validPoints.length, sumLng / validPoints.length];
    }

    return initialCenter;
  };

  const center = calculateCenter();

  return (
    <div className="gps-heatmap-container">
      <MapContainer
        center={center}
        zoom={initialZoom}
        scrollWheelZoom={true}
        className="gps-heatmap"
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        <MapResizer />
        {!loading && points && points.length > 0 && (
          <HeatmapLayer points={points} />
        )}
      </MapContainer>
      {loading && (
        <div className="gps-heatmap-loading">
          <span className="text-lg font-semibold">Loading...</span>
        </div>
      )}
    </div>
  );
};

export default GpsHeatmap;