import React from "react";
import { Modal } from "antd";
import { useTranslation } from "react-i18next";
import AlarmLocationMap from "./AlarmLocationMap";

interface AlarmLocationModalProps {
  isOpen: boolean;
  onClose: () => void;
  latitude: number;
  longitude: number;
  locationTitle?: string;
}

const AlarmLocationModal: React.FC<
  AlarmLocationModalProps
> = ({
  isOpen,
  onClose,
  latitude,
  longitude,
  locationTitle = "Location",
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      title={t(
        "alarms.locationModalTitle",
        "Location Details"
      )}
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={800}
      className="alarm-location-modal"
    >
      <div className="p-4">
        <h4 className="text-base font-medium text-gray-900 dark:text-white mb-4">
          {locationTitle}
        </h4>
        <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
          <p>
            {t("alarms.coordinates", "Coordinates")}:{" "}
            {latitude.toFixed(6)}, {longitude.toFixed(6)}
          </p>
        </div>
        <AlarmLocationMap
          latitude={latitude}
          longitude={longitude}
          popupText={locationTitle}
        />
      </div>
    </Modal>
  );
};

export default AlarmLocationModal;
