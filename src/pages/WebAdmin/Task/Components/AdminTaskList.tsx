import { useCallback, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks";
import {
  fetchTaskAdmin,
  setFilter,
} from "../../../../store/slices/admin/task.admin.slice";
import dayjs from "dayjs";
import { Button, Empty, Pagination, Spin } from "antd";
import { AdminTask } from "../../../../services/mainApi/admin/types/task.admin.mainApi.types";
import addTaskAdminSlice from "../../../../store/slices/admin/addTask.admin.slice";
import { useParams } from "react-router-dom";
import {
  FiClock,
  FiRepeat,
  FiCalendar,
  FiAlertTriangle,
  FiEdit,
  FiUser,
  FiTarget,
  FiShield,
  FiClock as FiClockAlt,
} from "react-icons/fi";

const AdminTaskList = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { tasks, loading, pagination, filter } =
    useAppSelector((state) => state.taskAdmin);
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const isFetching = useRef(false);

  const fetch = useCallback(async () => {
    if (!branchCode) return;

    if (isFetching.current) return;
    isFetching.current = true;
    await dispatch(fetchTaskAdmin(branchCode));
    isFetching.current = false;
  }, [branchCode, dispatch]);

  useEffect(() => {
    fetch();
  }, [
    filter.orderBy,
    filter.orderDirection,
    filter.page,
    fetch,
  ]);

  const handlePageChange = useCallback(
    (page: number, pageSize: number) => {
      dispatch(
        setFilter({
          page,
          limit: pageSize,
        })
      );
    },
    [dispatch]
  );

  const handleEdit = useCallback(
    (task: AdminTask) => {
      dispatch(addTaskAdminSlice.actions.openUpdate(task));
    },
    [dispatch]
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!tasks.length) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8">
        <Empty
          description={t(
            "task.list.empty",
            "No tasks found"
          )}
          className="dark:text-gray-400"
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {tasks.map((task) => (
          <div
            key={task.id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden border border-gray-100 dark:border-gray-700 h-full"
          >
            <div className="p-4">
              {/* Header Section */}
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 line-clamp-1">
                      {task.task_name}
                    </h3>
                    {!task.active && (
                      <div className="flex items-center text-xs px-2 py-0.5 rounded-full bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 gap-1">
                        <FiAlertTriangle className="text-xs" />
                        {t(
                          "task.status.inactive",
                          "Inactive"
                        )}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <FiClockAlt className="text-xs" />
                      {t(
                        "task.list.createdAt",
                        "Created"
                      )}:{" "}
                      {dayjs(task.created_at).format(
                        "DD MMM YYYY"
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mt-2">
                    {task.task_description ||
                      t(
                        "task.list.noDescription",
                        "No description"
                      )}
                  </p>
                </div>
                <Button
                  type="text"
                  icon={<FiEdit className="text-sm" />}
                  onClick={() => handleEdit(task)}
                  className="text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400"
                />
              </div>

              {/* Tags & Time Section */}
              <div className="flex flex-wrap items-center gap-2 mb-3">
                <div
                  className={`
                  inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium gap-1
                  ${
                    task.task_type === "SCHEDULED"
                      ? "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                      : "bg-purple-50 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400"
                  }
                `}
                >
                  {task.task_type === "SCHEDULED" ? (
                    <FiClock className="text-xs" />
                  ) : (
                    <FiRepeat className="text-xs" />
                  )}
                  {task.task_type === "SCHEDULED"
                    ? t("task.type.scheduled")
                    : t("task.type.repeating")}
                </div>
                <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 gap-1">
                  <FiClock className="text-xs" />
                  {task.allowed_time}{" "}
                  {t("task.list.minutes")}
                </div>
              </div>

              {/* Info Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                {/* Time Range */}
                <div>
                  <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 mb-1">
                    <FiCalendar className="text-xs" />
                    {t("task.list.timeRange")}
                  </div>
                  <div className="text-gray-700 dark:text-gray-300">
                    {dayjs(task.start_time).format(
                      "DD MMM HH:mm"
                    )}{" "}
                    -{" "}
                    {dayjs(task.end_time).format(
                      "DD MMM HH:mm"
                    )}
                  </div>
                </div>

                {/* Checkpoint Info */}
                {task.checkpoint && (
                  <div>
                    <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 mb-1">
                      <FiTarget className="text-xs" />
                      {t(
                        "task.list.checkpoint",
                        "Checkpoint"
                      )}
                    </div>
                    <div className="text-gray-700 dark:text-gray-300 line-clamp-1">
                      {task.checkpoint.checkpoint_name}
                    </div>
                  </div>
                )}

                {/* Role Info */}
                {task.role && (
                  <div>
                    <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 mb-1">
                      <FiShield className="text-xs" />
                      {t("task.list.role")}
                    </div>
                    <div className="text-gray-700 dark:text-gray-300 line-clamp-1">
                      {task.role.role_name}
                    </div>
                  </div>
                )}

                {/* Assigned User */}
                {task.user && (
                  <div>
                    <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 mb-1">
                      <FiUser className="text-xs" />
                      {t(
                        "task.list.assignedTo",
                        "Assigned To"
                      )}
                    </div>
                    <div className="text-gray-700 dark:text-gray-300 line-clamp-1">
                      {task.user.name}
                    </div>
                  </div>
                )}
              </div>

              {/* Fields Preview */}
              {task.fields && task.fields.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                  <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 mb-2">
                    {t("task.list.fields")} (
                    {task.fields.length})
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {task.fields.map((field) => (
                      <div
                        key={field.id}
                        className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-gray-600 dark:text-gray-300"
                      >
                        {field.task_field_name}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <Pagination
          current={filter.page || 1}
          pageSize={filter.limit || 10}
          total={pagination.total}
          onChange={handlePageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              Total {total} items
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

export default AdminTaskList;
