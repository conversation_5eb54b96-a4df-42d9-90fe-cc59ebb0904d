import { Card, Skeleton } from "antd";

export function BeaconSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
      {[...Array(6)].map((_, index) => (
        <Card
          key={index}
          className="shadow-sm bg-white dark:bg-gray-800 dark:border-gray-700"
          actions={[
            <Skeleton.Button active key="1" size="small" />,
            <Skeleton.Button active key="2" size="small" />,
          ]}
        >
          <Skeleton active paragraph={{ rows: 3 }} />
        </Card>
      ))}
    </div>
  );
} 