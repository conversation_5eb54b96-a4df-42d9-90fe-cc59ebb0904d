import { Row, Col } from "antd";
import { AdminLogAlert } from "../../../../services/mainApi/admin/types/logAlert.admin.mainApi.types";
import DashboardActivityItem from "./DashboardActivityItem";
import React from "react";

interface DashboardActivityListProps {
  activities: AdminLogAlert[];
  onRefresh: () => Promise<void>;
}

const DashboardActivityList: React.FC<DashboardActivityListProps> = ({ activities, onRefresh }) => {
  return (
    <Row gutter={[16, 16]}>
      {activities.map((activity) => (
        <Col key={activity.id} xs={24} sm={12} md={8} lg={6}>
          <DashboardActivityItem activity={activity} onRefresh={onRefresh} />
        </Col>
      ))}
    </Row>
  );
};

export default DashboardActivityList;
