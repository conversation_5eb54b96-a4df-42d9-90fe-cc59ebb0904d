import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import deviceAdminMainApi from "../../services/mainApi/admin/device.admin.mainApi.ts";
import {
  AdminDevice,
  EDeviceType,
} from "../../services/mainApi/admin/types/device.admin.mainApi.types.ts";
import { useParams } from "react-router";
import { BaseGetRequest } from "../../services/mainApi/types/base.mainApi.types";

// Extend BaseGetRequest for device-specific parameters
interface DeviceGetRequest extends BaseGetRequest {
  uniguard_device_type_id?: EDeviceType;
}

interface SelectDeviceProps {
  value?: string;
  onChange?: (
    value: string | null,
    device: AdminDevice | null
  ) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  deviceTypeId?: EDeviceType;
  clearable?: boolean;
  showImei?: boolean;
}

export default function SelectDevice({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select device",
  deviceTypeId,
  clearable = true,
  showImei = false,
}: SelectDeviceProps) {
  const [devices, setDevices] = useState<AdminDevice[]>([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);
  const { branchCode } = useParams();

  useEffect(() => {
    const fetchDevices = async () => {
      if (isFetching.current) return;

      try {
        isFetching.current = true;
        setLoading(true);

        // Use the extended request type
        const params: DeviceGetRequest = {};
        if (deviceTypeId) {
          params.uniguard_device_type_id = deviceTypeId;
        }

        const response =
          await deviceAdminMainApi.getDevices(
            branchCode || "",
            params
          );
        setDevices(response.data || []);
      } catch (error) {
        console.error("Failed to fetch devices:", error);
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchDevices();
  }, [deviceTypeId, branchCode]);

  const handleChange = (value: string) => {
    if (onChange) {
      const selectedDevice = devices.find(
        (device) => device.id === value
      );
      if (selectedDevice) {
        onChange(value, selectedDevice);
      } else {
        onChange(null, null);
      }
    }
  };

  return (
    <Select
      className={className}
      placeholder={placeholder}
      value={value}
      onChange={handleChange}
      disabled={disabled}
      loading={loading}
      showSearch
      optionFilterProp="label"
      options={devices.map((device) => ({
        value: device.id,
        label: `${device.device_name}${showImei && device.imei ? ` (${device.imei})` : ""}`,
      }))}
      allowClear={clearable}
    />
  );
}
