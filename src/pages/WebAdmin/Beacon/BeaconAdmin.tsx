import { Button, Input, Select } from "antd";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {
  SearchOutlined,
  SortAscendingOutlined,
} from "@ant-design/icons";
import { MdBluetooth } from "react-icons/md";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import { useTranslation } from "react-i18next";
import BeaconList from "./Components/BeaconList";
import { useRef } from "react";
import beaconAdminSlice, {
  fetchBeaconsAdmin,
} from "../../../store/slices/admin/beacon.admin.slice";
import { useAppSelector } from "../../../store/hooks";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import _ from "lodash";
import { useParams } from "react-router-dom";

export default function BeaconAdmin() {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  const isFetching = useRef(false);
  const { actions } = beaconAdminSlice;
  const beaconAdmin = useAppSelector(
    (state) => state.beaconAdmin
  );

  const fetchDebounce = useRef(
    _.debounce(() => {
      fetch();
    }, 500)
  );

  const fetch = async () => {
    if (isFetching.current) return;
    isFetching.current = true;
    await dispatch(fetchBeaconsAdmin(branchCode || ""));
    isFetching.current = false;
  };

  const handleSearch = (value: string) => {
    dispatch(
      actions.setFilter({ search: value || null, page: 1 })
    );
    fetchDebounce.current();
  };

  return (
    <WebAdminLayout activePage="beacon">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdBluetooth />}
            title={t("beacon.title", "Beacons")}
            description={t(
              "beacon.description",
              "Manage your beacon devices"
            )}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex gap-4 flex-wrap">
              <Input
                placeholder={t(
                  "beacon.search.placeholder",
                  "Search beacons..."
                )}
                prefix={<SearchOutlined />}
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                size="large"
                value={beaconAdmin.filter.search || ""}
                onChange={(e) =>
                  handleSearch(e.target.value)
                }
              />
              <div className="flex gap-2">
                <Select
                  defaultValue="created_at"
                  className="min-w-[160px] dark:bg-gray-700"
                  size="large"
                >
                  <Select.Option value="created_at">
                    {t(
                      "beacon.search.sortOptions.createdAt",
                      "Created Date"
                    )}
                  </Select.Option>
                  <Select.Option value="beacon_name">
                    {t(
                      "beacon.search.sortOptions.name",
                      "Name"
                    )}
                  </Select.Option>
                </Select>
                <Button
                  icon={<SortAscendingOutlined />}
                  size="large"
                  className="dark:border-gray-600 dark:text-gray-300"
                />
              </div>
            </div>
          </div>

          {/* Beacon List */}
          <BeaconList />
        </div>
      </div>
    </WebAdminLayout>
  );
}
