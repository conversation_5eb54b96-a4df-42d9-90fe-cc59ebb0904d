import {App, Divider, Input, Modal, Select, Switch,} from "antd";
import {AxiosError} from "axios";
import {useCallback, useEffect, useMemo, useState,} from "react";
import licenseMainApi from "../../../../services/mainApi/license.mainApi.ts";
import branchSysAdminMainApi from "../../../../services/mainApi/sysadmin/branch.sysadmin.mainApi.ts";
import {Branch, UpdateBranchRequest,} from "../../../../services/mainApi/sysadmin/types/branch.mainApi.types.ts";
import timezoneMainApi from "../../../../services/mainApi/timezone.mainApi.ts";
import {License} from "../../../../services/mainApi/types/license.mainApi.types.ts";
import {Timezone} from "../../../../services/mainApi/types/timezone.mainApi.types.ts";
import {useAppDispatch} from "../../../../store/hooks.ts";
import SelectTimezone from "../../../../components/Timezone/SelectTimezone.tsx";
import {useTranslation} from "react-i18next";
import {fetchBranches} from "../../../../store/slices/sysadmin/branchList.sysadmin.slice.ts";
import {z} from "zod";

interface ApiErrorResponse {
  message: string;
}

interface AddEditBranchModalProps {
  visible: boolean;
  branch: Branch | null;
  onClose: () => void;
}

// Define Zod schemas for validation
const userSchema = z.object({
  name: z.string().min(1, {message: "Name is required"}),
  email: z.string().email({message: "Invalid email format"}),
  phone: z.string().min(1, {message: "Phone is required"}).regex(/^\d+$/, {message: "Phone must contain only digits"}),
  password: z.string().optional(),
  confirm_password: z.string().optional()
}).refine((data) => {
  // If password is provided, confirm_password must match
  if (data.password && data.password !== data.confirm_password) {
    return false;
  }
  return true;
}, {
  message: "Passwords do not match",
  path: ["confirm_password"]
});

const branchSchema = z.object({
  name: z.string().min(1, {message: "Branch name is required"}),
  description: z.string().optional(),
  license: z.string().min(1, {message: "License is required"}),
  timezone: z.string().min(1, {message: "Timezone is required"}),
  active: z.boolean().optional().default(false),
  user: userSchema,

});

// Type for form state
interface FormState {
  name: string;
  description: string;
  license: string;
  timezone: string;
  active: boolean;
  user: {
    name: string;
    email: string;
    phone: string;
    password: string;
    confirm_password: string;
  };
}

export default function AddEditBranchModal({
                                             visible,
                                             branch,
                                             onClose,
                                           }: AddEditBranchModalProps) {
  // Replace Form.useForm() with useState
  const [formState, setFormState] = useState<FormState>({
    name: "",
    description: "",
    license: "",
    timezone: "",
    active: false,
    user: {
      name: "",
      email: "",
      phone: "",
      password: "",
      confirm_password: ""
    }
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const [licenses, setLicenses] = useState<License[]>([]);
  const [, setTimezones] = useState<Timezone[]>([]);
  const [loading, setLoading] = useState(false);
  const {message} = App.useApp();
  const dispatch = useAppDispatch();
  const {t} = useTranslation();

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const [licenseResponse, timezoneResponse] =
        await Promise.all([
          licenseMainApi.getList(),
          timezoneMainApi.getList(),
        ]);

      setLicenses(licenseResponse.data || []);
      setTimezones(timezoneResponse.data || []);
    } catch (err) {
      const error = err as AxiosError<ApiErrorResponse>;
      message.error(
        error.response?.data?.message ||
        t("branch.modal.add.error")
      );
    } finally {
      setLoading(false);
    }
  }, [message, t]);

  // Handle input changes
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormState(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error for this field when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => {
        const newErrors = {...prev};
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Handle nested user field changes
  const handleUserInputChange = (field: string, value: string) => {
    setFormState(prev => ({
      ...prev,
      user: {
        ...prev.user,
        [field]: value
      }
    }));
    // Clear error for this field when user starts typing
    if (formErrors[`user.${field}`]) {
      setFormErrors(prev => {
        const newErrors = {...prev};
        delete newErrors[`user.${field}`];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    try {
      // Validate the form data using Zod
      branchSchema.parse(formState);
      setFormErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Convert Zod errors to a more usable format
        const newErrors: Record<string, string> = {};
        error.errors.forEach(err => {
          const path = err.path.join('.');
          newErrors[path] = err.message;
        });
        setFormErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async () => {
    try {
      // Validate form using Zod
      if (!validateForm()) {
        return;
      }

      setLoading(true);

      if (branch) {
        // Update existing branch
        const updatePayload: UpdateBranchRequest = {
          name: formState.name,
          description: formState.description,
          timezone: formState.timezone,
          license: formState.license,
          active: formState.active,
          admin_name: formState.user.name,
          admin_email: formState.user.email,
          admin_phone: formState.user.phone,
          admin_password: formState.user.password || "",
          admin_confirm_password:
            formState.user.confirm_password || "",
        };
        await branchSysAdminMainApi.update(
          branch.id,
          updatePayload
        );
        message.success(t("branch.modal.edit.success"));
      } else {
        // Create new branch with user
        await branchSysAdminMainApi.createWithUser({
          name: formState.name,
          description: formState.description,
          timezone: formState.timezone,
          license: formState.license,
          user: {
            name: formState.user.name,
            email: formState.user.email,
            password: formState.user.password,
            confirm_password: formState.user.confirm_password,
            phone: formState.user.phone,
          },
        });
        message.success(t("branch.modal.add.success"));
      }

      // Refresh branch list
      dispatch(fetchBranches());

      onModalClose();
    } catch (err) {
      const error = err as AxiosError<ApiErrorResponse>;
      message.error(
        error.response?.data?.message ||
        t(
          branch
            ? "branch.modal.edit.error"
            : "branch.modal.add.error"
        )
      );
    } finally {
      setLoading(false);
    }
  };

  const setValue = useCallback(() => {
    setFormState({
      name: branch?.branch_name || "",
      description: branch?.branch_description || "",
      license: branch?.license_id || "",
      timezone: branch?.timezone_id || "",
      active: branch?.active || false,
      user: {
        name: branch?.branch_owner_user?.name || "",
        email: branch?.branch_owner_user?.email || "",
        phone: branch?.branch_owner_user?.phone || "",
        password: "",
        confirm_password: "",
      },
    });
  }, [branch]);

  // Fetch data when modal opens
  useEffect(() => {
    if (visible) {
      fetchData();
      if (branch) {
        setValue();
      } else {
        // Reset form for new branch
        setFormState({
          name: "",
          description: "",
          license: "",
          timezone: "",
          active: false,
          user: {
            name: "",
            email: "",
            phone: "",
            password: "",
            confirm_password: ""
          }
        });
      }
      // Clear any previous errors
      setFormErrors({});
    }
  }, [fetchData, visible, branch, setValue]);

  const onModalClose = useMemo(
    () => () => {
      // Reset form state
      setFormState({
        name: "",
        description: "",
        license: "",
        timezone: "",
        active: false,
        user: {
          name: "",
          email: "",
          phone: "",
          password: "",
          confirm_password: ""
        }
      });
      setFormErrors({});
      onClose();
    },
    [onClose]
  );

  return (
    <Modal
      title={
        <div className="text-lg font-semibold">
          {branch
            ? t("branch.modal.edit.title")
            : t("branch.modal.add.title")}
        </div>
      }
      open={visible}
      onCancel={onModalClose}
      onOk={handleSubmit}
      width={800}
      className="rounded-xl"
      okButtonProps={{
        className: "bg-blue-500 hover:bg-blue-600 border-0",
      }}
      confirmLoading={loading}
    >
      <div className="mt-4">
        {/* Branch Information Section */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-4">
            {t("branch.modal.sections.branchInfo")}
          </h3>

          {/* Branch Name */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">
              {t("branch.modal.form.name.label")}
              <span className="text-red-500">*</span>
            </label>
            <Input
              className="rounded-lg w-full"
              placeholder={t("branch.modal.form.name.placeholder")}
              value={formState.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              status={formErrors["name"] ? "error" : ""}
            />
            {formErrors["name"] && (
              <div className="text-red-500 text-sm mt-1">{formErrors["name"]}</div>
            )}
          </div>

          {/* Branch Description */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">
              {t("branch.modal.form.description.label")}
            </label>
            <Input.TextArea
              className="rounded-lg w-full"
              placeholder={t("branch.modal.form.description.placeholder")}
              rows={4}
              value={formState.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              status={formErrors["description"] ? "error" : ""}
            />
            {formErrors["description"] && (
              <div className="text-red-500 text-sm mt-1">{formErrors["description"]}</div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* License */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                {t("branch.modal.form.license.label")}
                <span className="text-red-500">*</span>
              </label>
              <Select
                className="rounded-lg w-full"
                placeholder={t("branch.modal.form.license.placeholder")}
                options={licenses.map((license) => ({
                  value: license.id,
                  label: license.license_name,
                }))}
                loading={loading}
                value={formState.license}
                onChange={(value) => handleInputChange("license", value)}
                status={formErrors["license"] ? "error" : ""}
              />
              {formErrors["license"] && (
                <div className="text-red-500 text-sm mt-1">{formErrors["license"]}</div>
              )}
            </div>

            {/* Timezone */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                {t("branch.modal.form.timezone.label")}
                <span className="text-red-500">*</span>
              </label>
              <SelectTimezone
                className="rounded-lg w-full"
                value={formState.timezone}
                onChange={(value) => handleInputChange("timezone", value)}
              />
              {formErrors["timezone"] && (
                <div className="text-red-500 text-sm mt-1">{formErrors["timezone"]}</div>
              )}
            </div>
          </div>
        </div>

        {/* Branch Admin Information Section */}
        <Divider/>
        <div className="mb-4">
          <h3 className="text-lg font-medium mb-4">
            {t("branch.modal.sections.adminInfo")}
          </h3>

          {/* Admin Name */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">
              {t("branch.modal.admin.name.label")}
              <span className="text-red-500">*</span>
            </label>
            <Input
              className="rounded-lg w-full"
              placeholder={t("branch.modal.admin.name.placeholder")}
              value={formState.user.name}
              onChange={(e) => handleUserInputChange("name", e.target.value)}
              status={formErrors["user.name"] ? "error" : ""}
            />
            {formErrors["user.name"] && (
              <div className="text-red-500 text-sm mt-1">{formErrors["user.name"]}</div>
            )}
          </div>

          {/* Admin Email */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">
              {t("branch.modal.admin.email.label")}
              <span className="text-red-500">*</span>
            </label>
            <Input
              className="rounded-lg w-full"
              placeholder={t("branch.modal.admin.email.placeholder")}
              value={formState.user.email}
              onChange={(e) => handleUserInputChange("email", e.target.value)}
              status={formErrors["user.email"] ? "error" : ""}
            />
            {formErrors["user.email"] && (
              <div className="text-red-500 text-sm mt-1">{formErrors["user.email"]}</div>
            )}
          </div>

          {/* Admin Phone */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">
              {t("branch.modal.admin.phone.label")}
              <span className="text-red-500">*</span>
            </label>
            <Input
              className="rounded-lg w-full"
              placeholder={t("branch.modal.admin.phone.placeholder")}
              value={formState.user.phone}
              onChange={(e) => handleUserInputChange("phone", e.target.value)}
              status={formErrors["user.phone"] ? "error" : ""}
            />
            {formErrors["user.phone"] && (
              <div className="text-red-500 text-sm mt-1">{formErrors["user.phone"]}</div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Admin Password */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                {t("branch.modal.admin.password.label")}
                {!branch && <span className="text-red-500">*</span>}
              </label>
              <Input.Password
                className="rounded-lg w-full"
                placeholder={
                  branch
                    ? t("branch.modal.admin.password.placeholderUpdate")
                    : t("branch.modal.admin.password.placeholder")
                }
                value={formState.user.password}
                onChange={(e) => handleUserInputChange("password", e.target.value)}
                status={formErrors["user.password"] ? "error" : ""}
              />
              {formErrors["user.password"] && (
                <div className="text-red-500 text-sm mt-1">{formErrors["user.password"]}</div>
              )}
              {branch && (
                <div style={{color: "#888", fontSize: 12}}>
                  {t("branch.modal.admin.password.optionalHint")}
                </div>
              )}
            </div>

            {/* Admin Confirm Password */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                {t("branch.modal.admin.confirmPassword.label")}
                {!branch && <span className="text-red-500">*</span>}
              </label>
              <Input.Password
                className="rounded-lg w-full"
                placeholder={
                  branch
                    ? t("branch.modal.admin.confirmPassword.placeholderUpdate")
                    : t("branch.modal.admin.confirmPassword.placeholder")
                }
                value={formState.user.confirm_password}
                onChange={(e) => handleUserInputChange("confirm_password", e.target.value)}
                status={formErrors["user.confirm_password"] ? "error" : ""}
              />
              {formErrors["user.confirm_password"] && (
                <div className="text-red-500 text-sm mt-1">{formErrors["user.confirm_password"]}</div>
              )}
              {branch && (
                <div style={{color: "#888", fontSize: 12}}>
                  {t("branch.modal.admin.password.optionalHint")}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Active Status */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            {t("branch.modal.form.status.label")}
          </label>
          <Switch
            checkedChildren={t("branch.modal.form.status.active")}
            unCheckedChildren={t("branch.modal.form.status.inactive")}
            className="bg-gray-300"
            checked={formState.active}
            onChange={(checked) => handleInputChange("active", checked)}
          />
        </div>
      </div>
    </Modal>
  );
}
