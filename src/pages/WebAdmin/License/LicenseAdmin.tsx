import { useTranslation } from "react-i18next";
import { <PERSON>d<PERSON><PERSON> } from "react-icons/md";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import LicenseList from "./Components/LicenseList";

export default function LicenseAdmin() {
  const { t } = useTranslation();

  return (
    <WebAdminLayout activePage="license">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdKey />}
            title={t("license.title", "Licenses")}
            description={t(
              "license.description",
              "Manage your licenses and their limitations"
            )}
          />

          {/* Content Section */}
          <LicenseList />
        </div>
      </div>
    </WebAdminLayout>
  );
}
