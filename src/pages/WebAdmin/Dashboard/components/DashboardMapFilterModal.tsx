import React, {useEffect, useState} from "react";
import {<PERSON><PERSON>, DatePicker, Form, Modal, TimePicker,} from "antd";
import {useTranslation} from "react-i18next";
import SelectRole from "../../../../components/Roles/SelectRole";
import SelectUser from "../../../../components/SelectUser/SelectUser";
import {AdminUser} from "../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import {useAppDispatch, useAppSelector} from "../../../../store/hooks";
import dayjs from "dayjs";
import dashboardMapAdminSlice, {
  fetchCheckpointActivitiesDashboard
} from "../../../../store/slices/admin/dashboardMap.admin.slice.ts";

interface DashboardMapFilterModalProps {
  visible: boolean;
  onClose: () => void;
}

const DashboardMapFilterModal: React.FC<DashboardMapFilterModalProps> = ({
                                                                           visible,
                                                                           onClose,
                                                                         }) => {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();

  // Get filter state from Redux store
  const {filters} = useAppSelector((state) => state.dashboardMapAdmin);

  // Local state to manage form values
  const [formState, setFormState] = useState({
    startDate: filters.startDate,
    endDate: filters.endDate,
    startTime: filters.startTime,
    endTime: filters.endTime,
    role: filters.role,
    user: filters.user,
  });

  // Update local state when Redux state changes
  useEffect(() => {
    setFormState({
      startDate: filters.startDate,
      endDate: filters.endDate,
      startTime: filters.startTime,
      endTime: filters.endTime,
      role: filters.role,
      user: filters.user,
    });
  }, [filters]);

  // Handler for user change
  const handleUserChange = (
    _value: string | null,
    adminUser: AdminUser | null
  ) => {
    setFormState((prevState) => ({
      ...prevState,
      user: adminUser,
    }));
  };

  // Handler for applying filters
  const handleApplyFilters = () => {
    // Dispatch actions to update filter state in Redux
    dispatch(dashboardMapAdminSlice.actions.setFilter(formState));

    // Fetch data with new filters
    dispatch(fetchCheckpointActivitiesDashboard());

    // Close the modal
    onClose();
  };

  // Handler for clearing filters
  const handleClearFilters = () => {
    dispatch(dashboardMapAdminSlice.actions.clearFilters());
    onClose();
  };

  return (
    <Modal
      title={t("dashboard.filter.title", "Filter Dashboard Map")}
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="clear" onClick={handleClearFilters}>
          {t("dashboard.filter.clearFilters", "Clear Filters")}
        </Button>,
        <Button key="apply" type="primary" onClick={handleApplyFilters}>
          {t("dashboard.filter.applyFilters", "Apply Filters")}
        </Button>,
      ]}
      width={600}
    >
      <Form layout="vertical" className="space-y-6">
        {/* Date Range */}
        <div className="mb-6">
          <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">
            {t("dashboard.filter.dateRange", "Date Range")}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              label={t("dashboard.filter.startDate", "Start Date")}
              required
            >
              <DatePicker
                value={formState.startDate ? dayjs(formState.startDate) : undefined}
                className="w-full"
                allowClear={false}
                disabledDate={(current) => {
                  // Disable dates after end date
                  if (formState.endDate) {
                    return current && current.isAfter(dayjs(formState.endDate));
                  }
                  return false;
                }}
                onChange={(date) => {
                  if (date) {
                    const dateStr = date.toISOString();
                    setFormState((prevState) => ({
                      ...prevState,
                      startDate: dateStr,
                    }));
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              label={t("dashboard.filter.endDate", "End Date")}
              required
            >
              <DatePicker
                value={formState.endDate ? dayjs(formState.endDate) : undefined}
                className="w-full"
                allowClear={false}
                disabledDate={(current) => {
                  // Disable dates before start date
                  if (formState.startDate) {
                    return current && current.isBefore(dayjs(formState.startDate));
                  }
                  return false;
                }}
                onChange={(date) => {
                  if (date) {
                    const dateStr = date.toISOString();
                    setFormState((prevState) => ({
                      ...prevState,
                      endDate: dateStr,
                    }));
                  }
                }}
              />
            </Form.Item>
          </div>
        </div>

        {/* Time Range */}
        <div className="mb-6">
          <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">
            {t("dashboard.filter.timeRange", "Time Range")}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              label={t("dashboard.filter.startTime", "Start Time")}
              required
            >
              <TimePicker
                format="HH:mm:ss"
                className="w-full"
                value={
                  formState.startTime
                    ? dayjs(`${dayjs().format("YYYY-MM-DD")} ${formState.startTime}`)
                    : null
                }
                onChange={(time) => {
                  const timeStr = time ? time.format("HH:mm:ss") : "00:00:00";
                  setFormState((prevState) => ({
                    ...prevState,
                    startTime: timeStr,
                  }));
                }}
              />
            </Form.Item>
            <Form.Item
              label={t("dashboard.filter.endTime", "End Time")}
              required
            >
              <TimePicker
                format="HH:mm:ss"
                className="w-full"
                value={
                  formState.endTime
                    ? dayjs(`${dayjs().format("YYYY-MM-DD")} ${formState.endTime}`)
                    : null
                }
                onChange={(time) => {
                  const timeStr = time ? time.format("HH:mm:ss") : "23:59:59";
                  setFormState((prevState) => ({
                    ...prevState,
                    endTime: timeStr,
                  }));
                }}
              />
            </Form.Item>
          </div>
        </div>

        {/* Role Selection */}
        <Form.Item
          label={t("dashboard.filter.role", "Role")}
        >
          <SelectRole
            className="w-full"
            value={formState.role?.id || undefined}
            onChange={(_, role) => {
              setFormState((prevState) => ({
                ...prevState,
                role: role || null,
              }));
            }}
            clearable={true}
            placeholder={t("dashboard.filter.allRoles", "All Roles")}
          />
        </Form.Item>

        {/* User Selection */}
        <Form.Item
          label={t("dashboard.filter.user", "User")}
        >
          <SelectUser
            className="w-full"
            value={formState.user?.id || undefined}
            onChange={handleUserChange}
            clearable={true}
            placeholder={t("dashboard.filter.allUsers", "All Users")}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DashboardMapFilterModal;
