import { ReactNode } from "react";
import { Md<PERSON>lose, Md<PERSON>ilterList } from "react-icons/md";
import dayjs from "dayjs";

/**
 * Interface for filter item configuration
 */
export interface FilterItem {
  key: string;
  editKey: string;
  value: string | null;
}

/**
 * Interface for filter group configuration
 */
export interface FilterGroup {
  key: string;
  filters: FilterItem[];
}

/**
 * Props for ActiveFilters component
 */
export interface ActiveFiltersProps<T> {
  /**
   * Configuration for filter groups to display
   */
  filterGroups: FilterGroup[];

  /**
   * Function to handle removing a filter
   */
  onRemoveFilter: (filterKey: keyof T) => void;

  /**
   * Function to handle editing a filter
   */
  onEditFilter: (filterKey: keyof T) => void;

  /**
   * Function to handle clearing all filters
   */
  onClearAll: () => void;

  /**
   * Function to handle showing the filter drawer/modal
   */
  onShowFilters: () => void;

  /**
   * Function to check if there are any active filters
   */
  hasActiveFilters: () => boolean;

  /**
   * Additional element to render in the filter area
   */
  additionalActions?: ReactNode;
}

/**
 * ActiveFilters Component
 *
 * A reusable component that displays active filters with options to edit, remove, or clear all filters.
 *
 * @returns React component
 */
const ActiveFilters = <T,>({
  filterGroups,
  onRemoveFilter,
  onEditFilter,
  onClearAll,
  onShowFilters,
  hasActiveFilters,
  additionalActions,
}: ActiveFiltersProps<T>) => {
  if (!hasActiveFilters()) {
    return null;
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-4 p-3">
      <div className="flex flex-wrap items-center gap-2">
        {filterGroups.map((group) => {
          const activeFilters = group.filters.filter(
            (f) => f.value
          );
          if (activeFilters.length === 0) return null;

          return activeFilters.map((filter) => (
            <div
              key={filter.key}
              onClick={() =>
                onEditFilter(filter.editKey as keyof T)
              }
              className="inline-flex items-center gap-1.5 px-2.5 py-1 bg-gray-100 dark:bg-gray-700
                         hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full text-xs
                         cursor-pointer transition-colors duration-150"
            >
              <span className="text-gray-600 dark:text-gray-300">
                {filter.value}
              </span>
              <MdClose
                className="w-3.5 h-3.5 text-gray-400 dark:text-gray-500
                          hover:text-gray-600 dark:hover:text-gray-300"
                onClick={(e) => {
                  e.stopPropagation();
                  onRemoveFilter(filter.editKey as keyof T);
                }}
              />
            </div>
          ));
        })}

        <div className="flex items-center gap-2 ml-auto">
          <button
            onClick={onClearAll}
            className="inline-flex items-center gap-1.5 px-2.5 py-1
                       bg-gray-50 dark:bg-gray-700 hover:bg-gray-100
                       dark:hover:bg-gray-600 rounded-full text-xs text-gray-600
                       dark:text-gray-300 cursor-pointer transition-colors duration-150"
          >
            <MdClose className="w-3.5 h-3.5" />
            <span>Clear All</span>
          </button>

          <button
            onClick={onShowFilters}
            className="inline-flex items-center gap-1.5 px-2.5 py-1
                       bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100
                       dark:hover:bg-blue-900/40 rounded-full text-xs text-blue-600
                       dark:text-blue-400 cursor-pointer transition-colors duration-150"
          >
            <MdFilterList className="w-3.5 h-3.5" />
            <span>Edit Filters</span>
          </button>

          {additionalActions}
        </div>
      </div>
    </div>
  );
};

/**
 * Helper functions for creating filter configuration
 */
export const createStartDateFilter = (
  startDate: string | null
): FilterItem => ({
  key: "startDate",
  editKey: "startDate",
  value: startDate
    ? `From: ${dayjs(startDate).format("DD MMM YYYY")}`
    : null,
});

export const createEndDateFilter = (
  endDate: string | null
): FilterItem => ({
  key: "endDate",
  editKey: "endDate",
  value: endDate
    ? `To: ${dayjs(endDate).format("DD MMM YYYY")}`
    : null,
});

export const createStartTimeFilter = (
  startTime: string | null
): FilterItem => ({
  key: "startTime",
  editKey: "startTime",
  value: startTime ? `From: ${startTime}` : null,
});

export const createEndTimeFilter = (
  endTime: string | null
): FilterItem => ({
  key: "endTime",
  editKey: "endTime",
  value: endTime ? `To: ${endTime}` : null,
});

export const createLabelFilter = (
  key: string,
  editKey: string,
  labels: unknown[]
): FilterItem => ({
  key,
  editKey,
  value:
    labels?.length > 0 ? `${labels.length} labels` : null,
});

// Using a more flexible approach with generics
export const createSimpleFilter = <T extends object | null>(
  key: string,
  editKey: string,
  value: T,
  displayField: string
): FilterItem => ({
  key,
  editKey,
  value:
    value && displayField in (value as object)
      ? String((value as any)[displayField])
      : null,
});

export default ActiveFilters;
