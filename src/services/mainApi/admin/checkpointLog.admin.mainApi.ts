import baseMainApi from "../base.mainApi";
import {
  AdminCheckpointLogListResponse,
  AdminCheckpointLogListParams,
} from "./types/checkpointLog.admin.mainApi.types";

export const checkpointLogAdminApi = {
  getCheckpointLogs: async (
    branchCode: string,
    params?: AdminCheckpointLogListParams
  ) => {
    const response =
      await baseMainApi.get<AdminCheckpointLogListResponse>(
        `/web-api/admin/${branchCode}/checkpoint-log`,
        { params }
      );
    return response.data;
  },

  exportCheckpointLogById: async (
    branchCode: string,
    id: string,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/checkpoint-log/export/${id}/pdf/buffer`,
      {
        responseType: "arraybuffer",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },

  exportCheckpointLogs: async (
    branchCode: string,
    params: AdminCheckpointLogListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/checkpoint-log/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
};
