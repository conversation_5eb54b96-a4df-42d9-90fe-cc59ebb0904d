import { BaseResponse } from "../../types/base.mainApi.types";

export interface AdminBeacon {
  id: string;
  parent_branch_id: string;
  beacon_name: string;
  beacon_description: string | null;
  beacon_uuid: string;
  counter_customer_code: string;
  pip_api_key: string | null;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
  parent_branch: {
    id: string;
    parent_id: string;
    branch_name: string;
    branch_description: string;
    license_id: string;
    timezone_id: string;
    active: boolean;
    created_at: string;
    created_by: string | null;
    updated_at: string;
    updated_by: string | null;
    branch_code: string;
  };
}

export type AdminBeaconListResponse = BaseResponse<
  AdminBeacon[]
>;
