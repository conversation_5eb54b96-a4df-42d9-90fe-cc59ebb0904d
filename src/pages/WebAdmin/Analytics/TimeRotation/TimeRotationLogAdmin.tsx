import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Md<PERSON><PERSON>rOff } from "react-icons/md";
import { useEffect, useState, useRef } from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import { <PERSON><PERSON>, Drawer } from "antd";
import TimeRotationLogList from "./Components/TimeRotationLogList";
import { useParams } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../../../store/hooks";
import timeRotationAdminSlice, {
  fetchTimeRotationLogs,
} from "../../../../store/slices/admin/timeRotation.admin.slice";
import ActiveFilters, {
  createEndDateFilter,
  createEndTimeFilter,
  createLabelFilter,
  createSimpleFilter,
  createStartDateFilter,
  createStartTimeFilter,
  FilterGroup,
} from "../../../../components/Admin/Common/ActiveFilters";
import AnalyticDrawerForm, {
  AnalyticsFilterState,
} from "../../Analytics/Components/AnalyticDrawerForm";
import { EReportType } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { AdminRole } from "../../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminBranch } from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminZone } from "../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import TimeRotationLogExportButton from "./Components/TimeRotationLogExportButton";
import { AdminLabel } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminUser } from "../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminCheckpoint } from "../../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";

interface FilterState {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: null | AdminBranch;
  zone: null | AdminZone;
  zoneLabels: AdminLabel[];
  checkpoint: null | AdminCheckpoint;
  checkpointLabels: AdminLabel[];
  role: null | AdminRole;
  user: null | AdminUser;
  userLabels: AdminLabel[];
  device: null | AdminDevice;
  deviceLabels: AdminLabel[];
  interval: number | null;
  method: string | null;
}

const TimeRotationLogAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const isFetchingRef = useRef(false);

  // Get branch code from URL params
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  // Redux state
  const timeRotationState = useAppSelector(
    (state) => state.timeRotationAdmin
  );
  const loading = timeRotationState.loading;
  const pagination = timeRotationState.pagination;

  const selectedBranch = timeRotationState.selectedBranch;
  const selectedZone = timeRotationState.selectedZone;
  const selectedZoneLabels = timeRotationState.selectedZoneLabels;
  const selectedCheckpoint = timeRotationState.selectedCheckpoint;
  const selectedCheckpointLabels = timeRotationState.selectedCheckpointLabels;
  const selectedRole = timeRotationState.selectedRole;
  const selectedUser = timeRotationState.selectedUser;
  const selectedUserLabels = timeRotationState.selectedUserLabels;
  const selectedDevice = timeRotationState.selectedDevice;
  const selectedDeviceLabels = timeRotationState.selectedDeviceLabels;
  const filterState = timeRotationState.filter;

  // Load data on mount and when filters change
  useEffect(() => {
    if (branchCode && !isFetchingRef.current) {
      isFetchingRef.current = true;
      dispatch(fetchTimeRotationLogs({ branchCode }))
        .finally(() => {
          isFetchingRef.current = false;
        });
    }
  }, [
    dispatch,
    branchCode,
    filterState.page,
    filterState.limit,
    filterState.startDate,
    filterState.endDate,
    filterState.startTime,
    filterState.endTime,
    filterState.branchId,
    filterState.zoneId,
    filterState.roleId,
    filterState.userId,
    filterState.userLabels,
    filterState.deviceId,
    filterState.deviceLabels,
    filterState.rotationInterval,
    filterState.rotationMethod,
  ]);

  // Handle page change
  const handlePageChange = (
    page: number,
    pageSize?: number
  ) => {
    if (pageSize && pageSize !== filterState.limit) {
      dispatch(timeRotationAdminSlice.actions.setLimit(pageSize));
    }
    dispatch(timeRotationAdminSlice.actions.setPage(page));
  };

  // Convert applied filter state to format expected by drawer
  const getAppliedFilters = (): FilterState => {
    return {
      startDate: filterState.startDate,
      endDate: filterState.endDate,
      startTime: filterState.startTime,
      endTime: filterState.endTime,
      branch: selectedBranch,
      zone: selectedZone,
      zoneLabels: selectedZoneLabels || [],
      checkpoint: selectedCheckpoint,
      checkpointLabels: selectedCheckpointLabels || [],
      role: selectedRole,
      user: selectedUser,
      userLabels: selectedUserLabels || [],
      device: selectedDevice,
      deviceLabels: selectedDeviceLabels || [],
      interval: filterState.rotationInterval,
      method: filterState.rotationMethod,
    };
  };

  // Check if there are any active filters
  const hasActiveFilters = (): boolean => {
    // Check if any filter is active
    return !!(filterState.startDate ||
      filterState.endDate ||
      filterState.startTime ||
      filterState.endTime ||
      filterState.branchId ||
      filterState.zoneId ||
      filterState.zoneLabels?.length > 0 ||
      filterState.checkpointId ||
      filterState.checkpointLabels?.length > 0 ||
      filterState.roleId ||
      filterState.userId ||
      filterState.userLabels?.length > 0 ||
      filterState.deviceId ||
      filterState.deviceLabels?.length > 0 ||
      filterState.rotationInterval ||
      filterState.rotationMethod);
  };

  // Get filter groups configuration for ActiveFilters component
  const getFilterGroups = (): FilterGroup[] => {
    const appliedFilters = getAppliedFilters();

    return [
      {
        key: "date",
        filters: [
          createStartDateFilter(
            appliedFilters.startDate
          ),
          createEndDateFilter(
            appliedFilters.endDate
          ),
        ],
      },
      {
        key: "time",
        filters: [
          createStartTimeFilter(
            appliedFilters.startTime
          ),
          createEndTimeFilter(
            appliedFilters.endTime
          ),
        ],
      },
      {
        key: "user",
        filters: [
          createSimpleFilter(
            "role",
            "role",
            appliedFilters.role,
            "role_name"
          ),
          createSimpleFilter(
            "user",
            "user",
            appliedFilters.user,
            "name"
          ),
          createLabelFilter(
            "userLabels",
            "userLabels",
            appliedFilters.userLabels
          ),
        ],
      },
      {
        key: "device",
        filters: [
          createSimpleFilter(
            "device",
            "device",
            appliedFilters.device,
            "device_name"
          ),
          createLabelFilter(
            "deviceLabels",
            "deviceLabels",
            appliedFilters.deviceLabels
          ),
        ],
      },
      {
        key: "location",
        filters: [
          createSimpleFilter(
            "zone",
            "zone",
            appliedFilters.zone,
            "zone_name"
          ),
          createLabelFilter(
            "zoneLabels",
            "zoneLabels",
            appliedFilters.zoneLabels
          ),
          createSimpleFilter(
            "checkpoint",
            "checkpoint",
            appliedFilters.checkpoint,
            "checkpoint_name"
          ),
          createLabelFilter(
            "checkpointLabels",
            "checkpointLabels",
            appliedFilters.checkpointLabels
          ),
          createSimpleFilter(
            "branch",
            "branch",
            appliedFilters.branch,
            "branch_name"
          ),
        ],
      },
      {
        key: "rotation",
        filters: [
          {
            key: "interval",
            editKey: "interval",
            value: appliedFilters.interval?.toString() || null,
          },
          {
            key: "method",
            editKey: "method",
            value: appliedFilters.method,
          },
        ],
      },
    ];
  };

  const handleClearAll = () => {
    dispatch(timeRotationAdminSlice.actions.clearFilters());
  };

  const handleRemoveFilter = (
    filterKey: keyof FilterState
  ) => {
    switch (filterKey) {
      case "role":
        dispatch(timeRotationAdminSlice.actions.setSelectedRole(null));
        break;
      case "user":
        dispatch(timeRotationAdminSlice.actions.setSelectedUser(null));
        break;
      case "userLabels":
        dispatch(timeRotationAdminSlice.actions.setSelectedUserLabels([]));
        break;
      case "device":
        dispatch(timeRotationAdminSlice.actions.setSelectedDevice(null));
        break;
      case "deviceLabels":
        dispatch(timeRotationAdminSlice.actions.setSelectedDeviceLabels([]));
        break;
      case "zone":
        dispatch(timeRotationAdminSlice.actions.setSelectedZone(null));
        break;
      case "zoneLabels":
        dispatch(timeRotationAdminSlice.actions.setSelectedZoneLabels([]));
        break;
      case "checkpoint":
        dispatch(timeRotationAdminSlice.actions.setSelectedCheckpoint(null));
        break;
      case "checkpointLabels":
        dispatch(timeRotationAdminSlice.actions.setSelectedCheckpointLabels([]));
        break;
      case "branch":
        dispatch(timeRotationAdminSlice.actions.setSelectedBranch(null));
        break;
      case "startDate":
        dispatch(timeRotationAdminSlice.actions.setStartDate(null));
        break;
      case "endDate":
        dispatch(timeRotationAdminSlice.actions.setEndDate(null));
        break;
      case "startTime":
        dispatch(timeRotationAdminSlice.actions.setStartTime(null));
        break;
      case "endTime":
        dispatch(timeRotationAdminSlice.actions.setEndTime(null));
        break;
      case "interval":
        dispatch(timeRotationAdminSlice.actions.setRotationInterval(null));
        break;
      case "method":
        dispatch(timeRotationAdminSlice.actions.setRotationMethod(null));
        break;
    }
  };

  // Handle clicking on an active filter to edit it
  const handleEditFilter = () => {
    // We only need to open the drawer
    setDrawerVisible(true);
  };

  // Convert FilterState to AnalyticsFilterState for the drawer form
  const getInitialDrawerValues = (): Partial<AnalyticsFilterState> => {
    const appliedFilters = getAppliedFilters();

    return {
      startDate: appliedFilters.startDate || "",
      endDate: appliedFilters.endDate || "",
      startTime: appliedFilters.startTime || "",
      endTime: appliedFilters.endTime || "",
      selectedBranch: appliedFilters.branch  || null,
      role: appliedFilters.role,
      site: appliedFilters.zone,
      siteLabels: appliedFilters.zoneLabels,
      checkpoint: appliedFilters.checkpoint,
      checkpointLabels: appliedFilters.checkpointLabels,
      user: appliedFilters.user,
      userLabels: appliedFilters.userLabels,
      device: appliedFilters.device,
      deviceLabels: appliedFilters.deviceLabels,
      rotationInterval: appliedFilters.interval || 10,
      rotationMethod: appliedFilters.method || "COUNT",
    };
  };

  // Get filter info for export button
  const getFilterInfo = () => ({
    role: timeRotationState.selectedRole || null,
    userId: timeRotationState.filter.userId || null,
    userLabels: timeRotationState.selectedUserLabels || [],
    deviceId: timeRotationState.filter.deviceId || null,
    deviceLabels: timeRotationState.selectedDeviceLabels || [],
    startDate: timeRotationState.filter.startDate || null,
    endDate: timeRotationState.filter.endDate || null,
    startTime: timeRotationState.filter.startTime || null,
    endTime: timeRotationState.filter.endTime || null,
    selectedBranch: timeRotationState.selectedBranch || null,
    selectedZone: timeRotationState.selectedZone || null,
    selectedZoneLabels: timeRotationState.selectedZoneLabels || [],
    selectedCheckpoint: timeRotationState.selectedCheckpoint || null,
    selectedCheckpointLabels: timeRotationState.selectedCheckpointLabels || [],
    rotationInterval: timeRotationState.filter.rotationInterval || null,
    rotationMethod: timeRotationState.filter.rotationMethod || null,
  });

  // Apply filters from the drawer
  const handleApplyFilters = (
    newFilters: AnalyticsFilterState
  ) => {
    // Update redux state
    dispatch(timeRotationAdminSlice.actions.setSelectedRole(newFilters.role || null));
    dispatch(timeRotationAdminSlice.actions.setSelectedUser(newFilters.user || null));
    dispatch(timeRotationAdminSlice.actions.setSelectedUserLabels(newFilters.userLabels || []));
    dispatch(timeRotationAdminSlice.actions.setSelectedDevice(newFilters.device || null));
    dispatch(timeRotationAdminSlice.actions.setSelectedDeviceLabels(newFilters.deviceLabels || []));
    dispatch(timeRotationAdminSlice.actions.setSelectedZone(newFilters.site || null));
    dispatch(timeRotationAdminSlice.actions.setSelectedZoneLabels(newFilters.siteLabels || []));
    dispatch(timeRotationAdminSlice.actions.setSelectedCheckpoint(newFilters.checkpoint || null));
    dispatch(timeRotationAdminSlice.actions.setSelectedCheckpointLabels(newFilters.checkpointLabels || []));
    dispatch(timeRotationAdminSlice.actions.setSelectedBranch(newFilters.selectedBranch || null));
    dispatch(timeRotationAdminSlice.actions.setStartDate(newFilters.startDate || null));
    dispatch(timeRotationAdminSlice.actions.setEndDate(newFilters.endDate || null));
    dispatch(timeRotationAdminSlice.actions.setStartTime(newFilters.startTime || null));
    dispatch(timeRotationAdminSlice.actions.setEndTime(newFilters.endTime || null));
    dispatch(timeRotationAdminSlice.actions.setRotationInterval(newFilters.rotationInterval || null));
    dispatch(timeRotationAdminSlice.actions.setRotationMethod(newFilters.rotationMethod || null));

    // Reset page to 1
    dispatch(timeRotationAdminSlice.actions.setPage(1));
    setDrawerVisible(false);
  };

  return (
    <WebAdminLayout activePage="time-rotation-log">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdTimerOff />}
            title={t(
              "timeRotationLog.title",
              "Time & Rotation Log"
            )}
            description={t(
              "timeRotationLog.description",
              "View and analyze patrol rotation times and delays"
            )}
            actions={[
              // Desktop view - show all buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <TimeRotationLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                />
                <Button
                  key="filter"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button and dropdown for downloads
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <TimeRotationLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                  buttonText={t(
                    "common.download",
                    "Download"
                  )}
                />
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters using the reusable component */}
          <ActiveFilters<FilterState>
            filterGroups={getFilterGroups()}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAll}
            onShowFilters={() => {
              setDrawerVisible(true);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Time Rotation Log List */}
          <TimeRotationLogList
            timeRotationLogs={timeRotationState.timeRotationLogs}
            loading={loading}
            total={pagination.total}
            page={pagination.page}
            limit={pagination.limit}
            onPageChange={handlePageChange}
          />
        </div>

        {/* Filter Drawer */}
        <Drawer
          title={t(
            "timeRotationLog.filters.title",
            "Filter Time Rotation Log"
          )}
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          className="dark:bg-gray-800"
        >
          <AnalyticDrawerForm
            reportType={EReportType.TIME_AND_ROTATION}
            onApply={handleApplyFilters}
            onClose={() => setDrawerVisible(false)}
            initialValues={getInitialDrawerValues()}
          />
        </Drawer>
      </div>
    </WebAdminLayout>
  );
};

export default TimeRotationLogAdmin;
