import baseMainApi from "../base.mainApi";
import {
  AdminSchedulerListResponse,
  AdminSchedulerDetailResponse,
  AdminSchedulerGetRequest,
  CreateSchedulerRequest,
  UpdateSchedulerRequest,
} from "./types/scheduler.admin.mainApi.types";

const schedulerAdminMainApi = {
  getSchedulers: async (
    branchCode: string,
    params?: AdminSchedulerGetRequest
  ) => {
    const response =
      await baseMainApi.get<AdminSchedulerListResponse>(
        `/web-api/admin/${branchCode}/scheduler`,
        { params }
      );
    return response.data;
  },

  getSchedulerById: async (
    branchCode: string,
    id: string
  ) => {
    const response =
      await baseMainApi.get<AdminSchedulerDetailResponse>(
        `/web-api/admin/${branchCode}/scheduler/${id}`
      );
    return response.data;
  },

  createScheduler: async (
    branchCode: string,
    data: CreateSchedulerRequest
  ) => {
    const response =
      await baseMainApi.post<AdminSchedulerDetailResponse>(
        `/web-api/admin/${branchCode}/scheduler`,
        data
      );
    return response.data;
  },

  updateScheduler: async (
    branchCode: string,
    id: string,
    data: UpdateSchedulerRequest
  ) => {
    const response =
      await baseMainApi.put<AdminSchedulerDetailResponse>(
        `/web-api/admin/${branchCode}/scheduler/${id}`,
        data
      );
    return response.data;
  },

  deleteScheduler: async (
    branchCode: string,
    id: string
  ) => {
    const response =
      await baseMainApi.delete<AdminSchedulerDetailResponse>(
        `/web-api/admin/${branchCode}/scheduler/${id}`
      );
    return response.data;
  },
};

export default schedulerAdminMainApi;
