import { AdminBranch } from "./branch.admin.mainApi.types";
import { AdminRole } from "./roles.admin.mainApi.types";
import { AdminUser } from "./user.admin.mainApi.types";
import { PaginationMeta } from "../../types/common.mainApi.types";

export interface AdminTaskLog {
  id: string;
  branch_id: string;
  parent_branch_id: string;
  check_point_id: string | null;
  role_id: string | null;
  user_id: string | null;
  task_type: string;
  task_name: string;
  task_description: string;
  start_time: string;
  end_time: string;
  allowed_time: number;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
}

export interface FieldType {
  id: number;
  field_type_name: string;
  field_type_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

export interface TaskField {
  id: string;
  log_task_id: string;
  field_type_id: string;
  field_type_name: string;
  task_field_name: string;
  field_type_value: string | null;
  field_type: FieldType;
}

export interface AdminTaskLog {
  id: string;
  uuid: string;
  parent_branch_id: string;
  branch_id: string;
  branch_name: string;
  task_id: string;
  task_name: string;
  task_type: string;
  task_start_time: string;
  task_end_time: string;
  task_allowed_time: number;
  role_id: string | null;
  role_name: string;
  user_id: string | null;
  user_name: string;
  device_id: string | null;
  device_name: string | null;
  timezone_id: string | null;
  timezone_name: string | null;
  latitude: number;
  longitude: number;
  event_time: string;
  original_submitted_time: string;
  branch: AdminBranch;
  task: AdminTaskLog;
  role: AdminRole;
  user: AdminUser;
  fields: TaskField[];
}

export interface AdminTaskLogListParams {
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  branch_id?: string | number;
  page?: number;
  limit?: number;
  order_by?: string;
  order_direction?: "ASC" | "DESC";
  role_id?: string | number;
  user_id?: string | number;
  user_labels?: string | string[];
  device_id?: string | number;
  device_labels?: string | string[];
  task_id?: string | number;
}

export interface AdminTaskLogListResponse {
  success: boolean;
  message: string;
  data: AdminTaskLog[];
  meta: PaginationMeta;
  error: null | string;
}
