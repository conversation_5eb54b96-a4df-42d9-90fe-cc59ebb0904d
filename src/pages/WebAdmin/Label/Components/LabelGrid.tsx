import React, { useCallback, useEffect } from "react";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks.ts";
import labelAdminSlice, {
  fetchLabelsAdmin,
} from "../../../../store/slices/admin/label.admin.slice.ts";
import { useParams } from "react-router-dom";
import { Empty, Pagination, Spin } from "antd";
import { AdminLabel } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types.ts";
import {
  DeleteOutlined,
  EditOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { openEdit } from "../../../../store/slices/admin/addLabel.admin.slice.ts";
import { useTranslation } from "react-i18next";

const LabelGrid: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { labels, loading, filter, pagination } =
    useAppSelector((state) => state.labelAdmin);
  const { branchCode } = useParams();
  const isLoading = React.useRef(false);

  const fetch = React.useCallback(async () => {
    if (isLoading.current) return;
    isLoading.current = true;
    await dispatch(fetchLabelsAdmin(branchCode || ""));
    isLoading.current = false;
  }, [branchCode, dispatch]);

  useEffect(() => {
    fetch();
  }, [fetch]);

  const handlePageChange = (
    page: number,
    pageSize: number
  ) => {
    dispatch(
      labelAdminSlice.actions.setFilter({
        page,
        limit: pageSize,
      })
    );
    fetch();
  };

  const handleEdit = useCallback(
    (label: AdminLabel) => {
      dispatch(openEdit(label));
    },
    [dispatch]
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Spin size="large" />
      </div>
    );
  }

  if (!labels?.length) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Empty
          description={
            <span className="text-gray-500 dark:text-gray-400">
              {t("label.noLabelsFound", "No labels found")}
            </span>
          }
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Labels Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {labels.map((label) => (
          <div
            key={label.id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow group"
          >
            {/* Label Content */}
            <div className="flex flex-col h-full">
              <div className="flex-grow">
                <h3
                  className="text-base font-medium text-gray-900 dark:text-gray-100 truncate mb-1"
                  title={label.label_name}
                >
                  {label.label_name}
                </h3>
                <p
                  className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2 mb-2"
                  title={label.label_description}
                >
                  {label.label_description ||
                    t(
                      "label.noDescription",
                      "No description"
                    )}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  {dayjs(label.created_at).format(
                    "DD MMM YYYY"
                  )}
                </p>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-1 pt-2 mt-2 border-t border-gray-100 dark:border-gray-700">
                <button
                  onClick={() => handleEdit(label)}
                  className="p-1.5 text-blue-600 hover:bg-blue-50 rounded-md dark:text-blue-400 dark:hover:bg-blue-900/20"
                  title={t("common.edit")}
                >
                  <EditOutlined />
                </button>
                <button
                  onClick={() => {
                    console.log("Delete label:", label.id);
                  }}
                  className="p-1.5 text-red-600 hover:bg-red-50 rounded-md dark:text-red-400 dark:hover:bg-red-900/20"
                  title={t("common.remove")}
                >
                  <DeleteOutlined />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3">
        <Pagination
          current={filter.page || 1}
          pageSize={filter.limit || 10}
          total={pagination.total}
          onChange={handlePageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              {t("common.pagination.total", { total })}
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

export default LabelGrid;
