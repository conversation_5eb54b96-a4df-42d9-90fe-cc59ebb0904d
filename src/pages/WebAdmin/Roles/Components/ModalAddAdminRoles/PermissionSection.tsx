import { Module } from "../../../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { Permission, PermissionType } from "./types";
import PermissionItem from "./PermissionItem";
import { Button, Checkbox } from "antd";

interface PermissionSectionProps {
  title: string;
  modules: Module[];
  permissions: Permission[];
  onPermissionChange: (
    moduleCode: string,
    moduleType: "MODULE" | "REPORT",
    permission: PermissionType,
    value: boolean
  ) => void;
}

const PermissionSection = ({
  title,
  modules,
  permissions,
  onPermissionChange,
}: PermissionSectionProps) => {
  // Check if all permissions for all modules are checked
  const isAllChecked = () => {
    return modules.every((module) => {
      const p = permissions.find((perm) => perm.module_code === module.module_code);
      return p && p.allow_view && p.allow_create && p.allow_update && p.allow_delete;
    });
  };

  // Handler to check/uncheck all permissions for all modules
  const handleCheckAll = (checked: boolean) => {
    modules.forEach((module) => {
      ["allow_view", "allow_create", "allow_update", "allow_delete"].forEach((perm) => {
        onPermissionChange(
          module.module_code,
          module.module_type,
          perm as PermissionType,
          checked
        );
      });
    });
  };

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100">{title}</h3>
        <Button
          type="default"
          size="small"
          onClick={() => handleCheckAll(!isAllChecked())}
          className="flex items-center gap-2"
        >
          <Checkbox checked={isAllChecked()} className="pointer-events-none" />
          {isAllChecked() ? "Uncheck All" : "Check All"}
        </Button>
      </div>
      <div className="rounded-lg border dark:border-gray-700 bg-white dark:bg-gray-800 divide-y dark:divide-gray-700 px-4">
        {modules.length === 0 ? (
          <div className="p-4 text-gray-500 dark:text-gray-400 text-center">
            No modules available
          </div>
        ) : (
          modules.map((module) => (
            <PermissionItem
              key={module.id}
              module={module}
              permission={
                permissions.find(
                  (p) =>
                    p.module_code === module.module_code
                ) || {
                  module_code: module.module_code,
                  allow_view: false,
                  allow_create: false,
                  allow_update: false,
                  allow_delete: false,
                }
              }
              onChange={onPermissionChange}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default PermissionSection;
