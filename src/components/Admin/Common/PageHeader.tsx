import { ReactNode } from "react";

interface PageHeaderProps {
  icon: ReactNode;
  title: string;
  description: string;
  actions?: ReactNode[];
}

export default function PageHeader({
  icon,
  title,
  description,
  actions,
}: PageHeaderProps) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-3">
          <span className="text-3xl text-blue-500">
            {icon}
          </span>
          <div>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100">
              {title}
            </h1>
            <p className="text-gray-500 dark:text-gray-400">
              {description}
            </p>
          </div>
        </div>
        {actions && actions.length > 0 && (
          <div className="flex items-center gap-2">
            {actions.map((action, index) => (
              <span key={index}>{action}</span>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
