import baseMainApi from "../base.mainApi";
import { License, LicenseListResponse } from "../types/license.mainApi.types";
import { LicenseCreateRequest, LicenseUpdateRequest } from "./types/license.sysadmin.mainApi.types";

const licenseSysAdminMainApi = {
  getList: async ({
    search = "",
    active,
    page = 1,
    limit = 20,
    order_by = "created_at",
    order_direction = "DESC",
  }: {
    search?: string;
    active?: boolean;
    page?: number;
    limit?: number;
    order_by?: string;
    order_direction?: "ASC" | "DESC";
  }) => {
    const response = await baseMainApi.get<LicenseListResponse>(
      "/web-api/sysadmin/license",
      {
        params: {
          search,
          active: active !== undefined ? active : undefined,
          page,
          limit,
          order_by,
          order_direction,
        },
      }
    );
    return response.data;
  },

  createLicense: async (data: LicenseCreateRequest): Promise<License> => {
    const response = await baseMainApi.post(
      `/web-api/sysadmin/license`,
      data
    );
    return response.data.data;
  },

  updateLicense: async (id: string, data: LicenseUpdateRequest): Promise<License> => {
    const response = await baseMainApi.put(
      `/web-api/sysadmin/license/${id}`,
      data
    );
    return response.data.data;
  },
};

export default licenseSysAdminMainApi;
