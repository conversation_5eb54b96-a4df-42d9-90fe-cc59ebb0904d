import { useCallback, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks";
import siteAdminSlice, {
  fetchSitesAdmin,
} from "../../../../store/slices/admin/site.admin.slice";
import { Empty, Pagination, Spin } from "antd";
import SiteListItem from "./SiteListItem";
import { useParams } from "react-router";

const SiteList = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { sites, loading, pagination, filter } =
    useAppSelector((state) => state.siteAdmin);
  const { branchCode } = useParams();
  const isFetching = useRef(false);

  const fetch = useCallback(() => {
    if (isFetching.current) return;
    isFetching.current = true;
    dispatch(fetchSitesAdmin(branchCode || ""));
    isFetching.current = false;
  }, [dispatch, branchCode]);

  const handlePageChange = useCallback(
    (page: number, pageSize: number) => {
      dispatch(
        siteAdminSlice.actions.setFilter({
          page,
          limit: pageSize,
        })
      );
      fetch();
    },
    [dispatch, fetch]
  );

  useEffect(() => {
    fetch();
  }, [fetch]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!sites.length) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8">
        <Empty
          description={t(
            "site.list.empty",
            "No sites found"
          )}
          className="dark:text-gray-400"
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4">
        {sites.map((site) => (
          <SiteListItem
            key={site.id}
            site={site}
          />
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <Pagination
          current={filter.page || 1}
          pageSize={filter.limit || 10}
          total={pagination.total}
          onChange={handlePageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              {t(
                "common.totalItems",
                "Total {{total}} items",
                { total }
              )}
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

export default SiteList;
