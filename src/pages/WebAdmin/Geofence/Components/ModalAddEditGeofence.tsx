import React, {useEffect, useRef} from "react";
import {<PERSON>pp, Button, Form, Input, Modal, Spin, Switch, Tabs, TimePicker,} from "antd";
import {useTranslation} from "react-i18next";
import {useAppDispatch, useAppSelector,} from "../../../../store/hooks";
import {useParams} from "react-router-dom";
import addEditGeofenceAdminSlice, {
  createGeofence,
  fetchGeofenceDetail,
  updateGeofence,
} from "../../../../store/slices/admin/addEditGeofence.admin.slice";
import {fetchGeofencesAdmin} from "../../../../store/slices/admin/geofence.admin.slice";
import dayjs from "dayjs";
import ErrorMessage from "../../../../components/Common/ErrorMessage/ErrorMessage";
import SelectZone from "../../../../components/Zone/SelectZone.tsx";
import SelectLabel from "../../../../components/Label/SelectLabel.tsx";
import LeafletGeofenceMapV2 from "./LeafletGeofenceMap.v2.tsx";
import {GeofenceData} from "../../../../services/mainApi/admin/types/geofence.admin.mainApi.types.ts";

const {TabPane} = Tabs;

const ModalAddEditGeofence: React.FC = () => {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const {message} = App.useApp();
  const {branchCode} = useParams<{
    branchCode: string;
  }>();

  const {
    open,
    mode,
    loading,
    submitting,
    errorMessage,
    geofenceName,
    geofenceDescription,
    zoneId,
    activeFrom,
    activeTo,
    minimumStayDuration,
    maximumStayDuration,
    active,
    labelIds,
    geofenceEdit,
    geofenceData,
  } = useAppSelector((state) => state.addEditGeofenceAdmin);

  // Get actions from slice
  const actions = addEditGeofenceAdminSlice.actions;

  // Load geofence details when editing
  useEffect(() => {
    if (
      open &&
      mode === "update" &&
      geofenceEdit &&
      branchCode
    ) {
      dispatch(
        fetchGeofenceDetail({
          branchCode,
          geofenceId: geofenceEdit.id,
        })
      );
    }
  }, [dispatch, open, mode, geofenceEdit, branchCode]);

  const handleSubmit = async () => {
    if (!branchCode) {
      message.error(t("checkpoint.error.noBranchCode"));
      return;
    }

    try {
      if (mode === "create") {
        const result = await dispatch(
          createGeofence(branchCode)
        );
        if (result.meta.requestStatus === "fulfilled") {
          message.success(
            t(
              "geofence.modal.add.success",
              "Geofence created successfully"
            )
          );
          // Refresh geofence list
          dispatch(fetchGeofencesAdmin(branchCode));
        }
      } else if (mode === "update" && geofenceEdit) {
        const result = await dispatch(
          updateGeofence({
            branchCode,
            geofenceId: geofenceEdit.id,
          })
        );
        if (result.meta.requestStatus === "fulfilled") {
          message.success(
            t(
              "geofence.modal.edit.success",
              "Geofence updated successfully"
            )
          );
          // Refresh geofence list
          dispatch(fetchGeofencesAdmin(branchCode));
        }
      }
    } catch (error) {
      console.error("Error submitting geofence:", error);
      message.error(
        t("geofence.modal.error", "Failed to save geofence")
      );
    }
  };

  const handleCancel = () => {
    dispatch(actions.close());
  };

  return (
    <Modal
      title={
        mode === "create"
          ? t("geofence.modal.add.title", "Add Geofence")
          : t("geofence.modal.edit.title", "Edit Geofence")
      }
      open={open}
      onCancel={handleCancel}
      width={800}
      footer={[
        <Button
          key="cancel"
          onClick={handleCancel}
        >
          {t("common.cancel")}
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={submitting}
          onClick={handleSubmit}
          className="bg-blue-500 hover:bg-blue-600"
        >
          {mode === "create"
            ? t("common.create")
            : t("common.update")}
        </Button>,
      ]}
    >
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <Spin size="large"/>
        </div>
      ) : (
        <Tabs defaultActiveKey="basic">
          <TabPane
            tab={t(
              "geofence.modal.sections.basic",
              "Basic Information"
            )}
            key="basic"
          >
            <Form
              layout="vertical"
              className="space-y-3"
            >
              <Form.Item
                label={t(
                  "geofence.modal.form.name.label",
                  "Name"
                )}
                required
              >
                <Input
                  value={geofenceName}
                  onChange={(e) =>
                    dispatch(
                      actions.setGeofenceName(
                        e.target.value
                      )
                    )
                  }
                  placeholder={t(
                    "geofence.modal.form.name.placeholder",
                    "Enter geofence name"
                  )}
                />
              </Form.Item>

              <Form.Item
                label={t(
                  "geofence.modal.form.description.label",
                  "Description"
                )}
              >
                <Input.TextArea
                  value={geofenceDescription}
                  onChange={(e) =>
                    dispatch(
                      actions.setGeofenceDescription(
                        e.target.value
                      )
                    )
                  }
                  placeholder={t(
                    "geofence.modal.form.description.placeholder",
                    "Enter description"
                  )}
                  rows={3}
                />
              </Form.Item>

              <Form.Item
                label={t(
                  "geofence.modal.form.zone.label",
                  "Zone"
                )}
                required
              >
                <SelectZone
                  value={zoneId}
                  onChange={(value) =>
                    dispatch(actions.setZoneId(value))
                  }
                />
              </Form.Item>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Form.Item
                  label={t(
                    "geofence.modal.form.activeTime.start",
                    "Active From"
                  )}
                >
                  <TimePicker
                    format="HH:mm"
                    value={
                      activeFrom
                        ? dayjs(activeFrom, "HH:mm")
                        : undefined
                    }
                    onChange={(time) =>
                      dispatch(
                        actions.setActiveFrom(
                          time ? time.format("HH:mm") : ""
                        )
                      )
                    }
                    className="w-full"
                  />
                </Form.Item>

                <Form.Item
                  label={t(
                    "geofence.modal.form.activeTime.end",
                    "Active To"
                  )}
                >
                  <TimePicker
                    format="HH:mm"
                    value={
                      activeTo
                        ? dayjs(activeTo, "HH:mm")
                        : undefined
                    }
                    onChange={(time) =>
                      dispatch(
                        actions.setActiveTo(
                          time ? time.format("HH:mm") : ""
                        )
                      )
                    }
                    className="w-full"
                  />
                </Form.Item>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Form.Item
                  label={t(
                    "geofence.modal.form.minStayDuration",
                    "Minimum Stay Duration"
                  )}
                >
                  <TimePicker
                    format="HH:mm"
                    value={
                      minimumStayDuration
                        ? dayjs(
                          minimumStayDuration,
                          "HH:mm"
                        )
                        : undefined
                    }
                    onChange={(time) =>
                      dispatch(
                        actions.setMinimumStayDuration(
                          time ? time.format("HH:mm") : ""
                        )
                      )
                    }
                    className="w-full"
                  />
                </Form.Item>

                <Form.Item
                  label={t(
                    "geofence.modal.form.maxStayDuration",
                    "Maximum Stay Duration"
                  )}
                >
                  <TimePicker
                    format="HH:mm"
                    value={
                      maximumStayDuration
                        ? dayjs(
                          maximumStayDuration,
                          "HH:mm"
                        )
                        : undefined
                    }
                    onChange={(time) =>
                      dispatch(
                        actions.setMaximumStayDuration(
                          time ? time.format("HH:mm") : ""
                        )
                      )
                    }
                    className="w-full"
                  />
                </Form.Item>
              </div>

              <Form.Item label={t("common.labels")}>
                <SelectLabel
                  mode="multiple"
                  value={labelIds || []}
                  onChange={(value) =>
                    dispatch(
                      actions.setLabelIds(value as string[])
                    )
                  }
                />
              </Form.Item>

              <Form.Item
                label={t("checkpoint.modal.form.active")}
              >
                <Switch
                  checked={active}
                  onChange={(checked) =>
                    dispatch(actions.setActive(checked))
                  }
                />
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane
            tab={t("geofence.modal.tabs.map", "Map")}
            key="map"
            destroyInactiveTabPane={true}
          >
            <div className="p-2">
              <div
                ref={mapContainerRef}
                className="h-[500px] w-full rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700"
              >
                <LeafletGeofenceMapV2
                  geofenceData={geofenceData}
                  onGeofenceDataChange={(data: GeofenceData) =>
                    dispatch(
                      actions.setGeofenceData(data)
                    )
                  }
                />
              </div>
            </div>
          </TabPane>
        </Tabs>
      )}

      {errorMessage && (
        <div className="mt-4">
          <ErrorMessage message={errorMessage}/>
        </div>
      )}
    </Modal>
  );
};

export default ModalAddEditGeofence;
