import { BaseResponse } from "./base.mainApi.types";

export interface Module {
  id: string;
  module_code: string;
  module_type: string;
}

export interface Permission {
  id: string;
  module: Module;
  allow_create: boolean;
  allow_update: boolean;
  allow_view: boolean;
  allow_delete: boolean;
}

export interface CurrentUserRole {
  id: string;
  role_name: string;
  super_admin: boolean;
  permissions: Permission[];
}

export interface MyProfileResponseData {
  id: string;
  name: string;
  email: string;
  parent_branch: {
    id: string;
    branch_name: string;
    branch_description: string;
    branch_code: string;
  };
  current_branch: {
    id: string;
    branch_name: string;
    branch_description: string;
    branch_code: string;
  };
  access: {
    web_access: boolean;
    mobile_access: boolean;
    system_access: boolean;
  };
  role: CurrentUserRole;
}

export type MyProfileResponse =
  BaseResponse<MyProfileResponseData>;
