import {useEffect, useRef} from "react";
import Map, {MapRef} from "../../../../components/Map/Map";
import {useParams} from "react-router-dom";
import {useAppDispatch, useAppSelector} from "../../../../store/hooks";
import dashboardMapAdminSlice, {
  fetchCheckpointActivitiesDashboard,
  fetchGeofencesDashboard
} from "../../../../store/slices/admin/dashboardMap.admin.slice.ts";
import ActivityList from "./ActivityList";
import DashboardMapFilterModal from "./DashboardMapFilterModal";

const DashboardMap = () => {
  const mapRef = useRef<MapRef>(null);
  const {branchCode} = useParams<{ branchCode: string }>();
  const dispatch = useAppDispatch();
  const {
    geofences,
    fetchingGeofences,
    checkpointActivities,
    fetchingCheckpointActivities,
    error,
    filterModal
  } = useAppSelector(
    (state) => state.dashboardMapAdmin
  );

  const checkpointFetching = useRef(false);
  const geofenceFetching = useRef(false);

  useEffect(() => {
    if (branchCode) {
      if (!geofenceFetching.current) {
        geofenceFetching.current = true;
        dispatch(fetchGeofencesDashboard())
          .finally(() => {
            geofenceFetching.current = false;
          });
      }

      if (!checkpointFetching.current) {
        checkpointFetching.current = true;
        dispatch(fetchCheckpointActivitiesDashboard())
          .finally(() => {
            checkpointFetching.current = false;
          });
      }
    }
  }, [branchCode, dispatch]);

  const transformedGeofences = geofences.map(geofence => {
    const coordinates = geofence.geofence_data.coordinates;
    return {
      id: geofence.id,
      geofence: coordinates as [number, number][],
    };
  });

  const transformedCheckpointActivities = checkpointActivities.filter(activity => activity.latitude && activity.longitude).map(activity => {
    return {
      id: activity.id,
      position: [activity.latitude, activity.longitude] as [number, number],
      title: activity.userName,
      description: activity.eventName,
      date: activity.eventSubmittedTime,
    };
  });

  const handleUserActivityClick = (id: string) => {
    mapRef.current?.goToMarkerById(id);
  };

  const handleToggleFilterModal = () => {
    dispatch(dashboardMapAdminSlice.actions.toggleFilterModal());
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm h-[calc(100vh-180px)] relative">

      <div className="absolute top-10 right-10 z-[1000] w-80">
        <ActivityList
          activities={checkpointActivities}
          loading={fetchingCheckpointActivities}
          onActivityClick={handleUserActivityClick}
        />
      </div>

      <DashboardMapFilterModal
        visible={filterModal.visible}
        onClose={handleToggleFilterModal}
      />

      <div className="map-container h-full">
        {error && (
          <div className="text-red-500 mb-2">
            {Array.isArray(error) ? error.join(', ') : error}
          </div>
        )}
        {(fetchingGeofences || fetchingCheckpointActivities) && (
          <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center z-10">
            Loading data...
          </div>
        )}
        {
          !fetchingGeofences && !fetchingCheckpointActivities && (
            <Map
              ref={mapRef}
              geofences={transformedGeofences}
              checkpointActivityMarkers={transformedCheckpointActivities}
              geolocationActivityMarkers={[]}
            />
          )
        }
      </div>
    </div>
  );
};

export default DashboardMap;
