import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import beaconSysAdminMainApi from "../../../services/mainApi/sysadmin/beacon.sysadmin.mainApi";
import { Beacon } from "../../../services/mainApi/sysadmin/types/beacon.mainApi.types";

export type SortField = "created_at" | "beacon_name";
export type SortOrder = "asc" | "desc";

// Types
interface BeaconState {
  items: Beacon[];
  loading: boolean;
  error: string | null;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  filters: {
    searchQuery: string;
    sortField: SortField;
    sortOrder: SortOrder;
    active: boolean | undefined;
  };
}

// Initial state
const initialState: BeaconState = {
  items: [],
  loading: false,
  error: null,
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0,
  },
  filters: {
    searchQuery: "",
    sortField: "created_at",
    sortOrder: "desc",
    active: undefined,
  },
};

// Async thunks
export const fetchBeacons = createAsyncThunk(
  "beacon/fetchBeacons",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const { pagination, filters } = state.beaconSysadmin;

      const response = await beaconSysAdminMainApi.getList({
        search: filters.searchQuery,
        active: filters.active,
        page: pagination.current,
        limit: pagination.pageSize,
        order_by: filters.sortField,
        order_direction: filters.sortOrder.toUpperCase() as "ASC" | "DESC",
      });

      return {
        data: response.data,
        total: response.meta.total,
      };
    } catch {
      return rejectWithValue("Failed to fetch beacons");
    }
  }
);

// Slice
const beaconListSysadminSlice = createSlice({
  name: "beacon",
  initialState,
  reducers: {
    setSearchQuery: (
      state,
      action: PayloadAction<string>
    ) => {
      state.filters.searchQuery = action.payload;
      state.pagination.current = 1; // Reset to first page
    },
    setActiveFilter: (
      state, 
      action: PayloadAction<boolean | undefined>
    ) => {
      state.filters.active = action.payload;
      state.pagination.current = 1; // Reset to first page
    },
    setSort: (
      state,
      action: PayloadAction<{
        field: SortField;
        order: SortOrder;
      }>
    ) => {
      state.filters.sortField = action.payload.field;
      state.filters.sortOrder = action.payload.order;
      state.pagination.current = 1; // Reset to first page
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.pagination.current = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchBeacons.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBeacons.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.data;
        state.pagination.total = action.payload.total;
      })
      .addCase(fetchBeacons.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Actions
export const { setSearchQuery, setActiveFilter, setSort, setPage } =
  beaconListSysadminSlice.actions;

// Selectors
export const selectBeacons = (state: RootState) =>
  state.beaconSysadmin.items;
export const selectBeaconLoading = (state: RootState) =>
  state.beaconSysadmin.loading;
export const selectBeaconError = (state: RootState) =>
  state.beaconSysadmin.error;
export const selectBeaconPagination = (state: RootState) =>
  state.beaconSysadmin.pagination;
export const selectBeaconFilters = (state: RootState) =>
  state.beaconSysadmin.filters;

export default beaconListSysadminSlice; 