import React from "react";
import {<PERSON><PERSON>, Tooltip} from "antd";
import {<PERSON><PERSON>lock, FiLogIn, FiLogOut, FiTarget, FiUser,} from "react-icons/fi";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import relativeTime from "dayjs/plugin/relativeTime";
import {useTranslation} from "react-i18next";
import {AdminGeofenceLogEntry} from "../../../../../services/mainApi/admin/types/geofenceLog.admin.mainApi.types";

dayjs.extend(relativeTime);
dayjs.extend(duration);

interface GeofenceLogItemProps {
  geofenceLog: AdminGeofenceLogEntry;
}

const GeofenceLogItem: React.FC<GeofenceLogItemProps> = ({
                                                           geofenceLog,
                                                         }) => {
  const {t} = useTranslation();

  // Format duration in hours and minutes
  const formatDuration = (durationInSeconds: number) => {
    const durationObj = dayjs.duration(durationInSeconds, 'seconds');
    const hours = Math.floor(durationObj.asHours());
    const minutes = Math.floor(durationObj.minutes());
    const seconds = Math.floor(durationObj.seconds());

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-0 p-4 mb-4">
      {/* Header - with gradient background */}
      <div
        className="mb-3 -mx-4 -mt-4 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 rounded-t-xl border-b border-gray-100 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center gap-2">
              {geofenceLog.geofence_name}
              {geofenceLog.violation && (
                <Badge
                  status="error"
                  text={
                    <span className="text-red-500">
                      {t("geofenceLog.violation", "Violation")}
                    </span>
                  }
                />
              )}
            </h3>
          </div>
          <Tooltip
            title={dayjs(geofenceLog.entry).format(
              "DD MMM YYYY HH:mm:ss"
            )}
          >
            <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
              <FiClock className="inline"/>
              {dayjs(geofenceLog.entry).fromNow()}
            </span>
          </Tooltip>
        </div>
      </div>

      {/* Content - Compact Two Column Layout */}
      <div className="grid grid-cols-2 gap-3 px-1">
        {/* Left Column */}
        <div className="space-y-2">
          {/* User Info - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
              <FiUser className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("geofenceLog.user", "User")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {geofenceLog.user_name}
                </span>
              </div>
            </div>
          </div>

          {/* Device Info - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
              <FiTarget className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("geofenceLog.device", "Device")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {geofenceLog.device_name}
                </span>
              </div>
            </div>
          </div>

          {/* Entry Time - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
              <FiLogIn className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("geofenceLog.entry", "Entry")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {dayjs(geofenceLog.entry).format("DD MMM YYYY HH:mm:ss")}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-2">
          {/* Geofence Information - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-orange-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 flex-shrink-0">
              <FiTarget className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("geofenceLog.geofence", "Geofence")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {geofenceLog.geofence_name}
                </span>
              </div>
            </div>
          </div>

          {/* Exit Time - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-red-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300 flex-shrink-0">
              <FiLogOut className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("geofenceLog.exit", "Exit")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {dayjs(geofenceLog.exit).format("DD MMM YYYY HH:mm:ss")}
                </span>
              </div>
            </div>
          </div>

          {/* Actual Duration - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-indigo-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex-shrink-0">
              <FiClock className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("geofenceLog.duration", "Duration")}:
                </span>
                <Badge color="blue" count={formatDuration(geofenceLog.duration)} className="font-medium text-xs"/>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
};

export default GeofenceLogItem;
