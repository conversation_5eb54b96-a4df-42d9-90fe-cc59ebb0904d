import { BaseResponse } from "../../types/base.mainApi.types";

interface AdminActivityBranch {
  id: string;
  parent_id: string;
  branch_name: string;
  branch_description: string;
  license_id: string;
  timezone_id: string;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
  branch_code: string;
}

export interface AdminActivity {
  id: string;
  branch_id: string;
  parent_branch_id: string;
  activity_name: string;
  activity_description: string;
  gps_required: boolean;
  photo_required: boolean;
  comment_required: boolean;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
  branch: AdminActivityBranch;
}

export interface CreateActivityRequest {
  activity_name: string;
  activity_description: string;
  gps_required: boolean;
  photo_required: boolean;
  comment_required: boolean;
}

export interface UpdateActivityRequest
  extends CreateActivityRequest {
  active: boolean;
}

export type AdminActivityListResponse = BaseResponse<
  AdminActivity[]
>;
export type AdminActivityDetailResponse =
  BaseResponse<AdminActivity>;
