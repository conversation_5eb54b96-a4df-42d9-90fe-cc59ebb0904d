import React from "react";
import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FiMapPin} from "react-icons/fi";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import {useTranslation} from "react-i18next";
import {
  AdminBatteryLevelLogEntry
} from "../../../../../services/mainApi/admin/types/batteryLevelLog.admin.mainApi.types.ts";
import {formatDateTime} from "../../../../../utils/dateUtils.ts";

dayjs.extend(relativeTime);

// Interface for the battery log item props
interface CheckpointBatteryLogItemProps {
  batteryLog: AdminBatteryLevelLogEntry;
}

const CheckpointBatteryLogItem: React.FC<CheckpointBatteryLogItemProps> = ({batteryLog}) => {
  const {t} = useTranslation();

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-0 p-4 h-full flex flex-col">
      {/* Header - with gradient background */}
      <div
        className="mb-3 -mx-4 -mt-4 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 rounded-t-xl border-b border-gray-100 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center gap-2 truncate">
            <FiBattery className="text-blue-500 flex-shrink-0"/>
            <span className="truncate">{batteryLog.checkpoint_name}</span>
          </h3>
        </div>
      </div>

      {/* Content - Single Column for Grid Layout */}
      <div className="flex-grow">
        <div className="space-y-2">
          {/* Checkpoint Info */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
              <FiMapPin className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("checkpointBatteryLog.checkpointName", "Checkpoint")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {batteryLog.checkpoint_name}
                </span>
              </div>
            </div>
          </div>

          {/* Battery Level Info */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-indigo-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex-shrink-0">
              <FiBattery className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("checkpointBatteryLog.batteryLevel", "Battery Level")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {batteryLog.voltage.toFixed(2)}%
                </span>
              </div>
            </div>
          </div>

          {/* Recorded At Info */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-purple-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 flex-shrink-0">
              <FiClock className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("checkpointBatteryLog.recordedAt", "Recorded At")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {formatDateTime(batteryLog.original_submitted_time, "DD MMM YYYY HH:mm", batteryLog.timezone_name)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckpointBatteryLogItem;
