import {
  App,
  Checkbox,
  Divider,
  Form,
  Input,
  Modal,
} from "antd";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks.ts";
import addUserAdminSlice, {
  createUser,
  fetchDetailUser,
  updateUser,
} from "../../../../../store/slices/admin/addUser.admin.slice.ts";
import GridSelectBranch from "../../../../../components/Branch/GridSelectBranch.tsx";
import ErrorMessage from "../../../../../components/Common/ErrorMessage/ErrorMessage.tsx";
import SelectRole from "../../../../../components/Roles/SelectRole.tsx";
import SelectLabel from "../../../../../components/Label/SelectLabel.tsx";
import { useParams } from "react-router-dom";
import { useEffect } from "react";
import { fetchUsers } from "../../../../../store/slices/admin/user.admin.slice.ts";
import { useTranslation } from "react-i18next";

// Modal component for adding/editing users
const ModalAddUser = () => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  // Get modal state from Redux store
  const modalState = useAppSelector(
    (state) => state.addUserAdmin
  );
  const {
    mode,
    open,
    submitting,
    name,
    email,
    phone,
    password,
    roleId,
    labels,
    branches,
    errorMessage,
    confirmPassword,
    allowMobileAccess,
    allowWebAccess,
  } = modalState;
  const { branchCode } = useParams() || {};
  const { message } = App.useApp();

  // Handler to close the modal
  const handleClose = () => {
    dispatch(addUserAdminSlice.actions.close());
  };

  // Handle form submission
  const onSubmit = async () => {
    let result;
    if (mode === "update") {
      result = await dispatch(updateUser(branchCode || ""));
    } else {
      result = await dispatch(createUser(branchCode || ""));
    }

    if (result.meta.requestStatus === "fulfilled") {
      dispatch(fetchUsers(branchCode || ""));
      message.success(t("users.modal.saveSuccess"));
      handleClose();
    }
  };

  // Fetch user details when editing
  useEffect(() => {
    if (mode === "update" && open) {
      dispatch(fetchDetailUser(branchCode || ""));
    }
  }, [branchCode, dispatch, mode, open]);

  return (
    <Modal
      title={
        <div className="text-lg font-semibold">
          {mode === "update"
            ? t("users.modal.edit.title")
            : t("users.modal.add.title")}
        </div>
      }
      open={open}
      onCancel={handleClose}
      onOk={onSubmit}
      confirmLoading={submitting}
      className="rounded-xl"
      okButtonProps={{
        className: "bg-blue-500 hover:bg-blue-600 border-0",
      }}
      width={800}
    >
      {/* User form */}
      <Form
        layout="vertical"
        className="mt-4"
      >
        {/* Email field - shown in create mode as editable, in update mode as readonly */}
        <Form.Item
          label={t("users.modal.form.email.label")}
          required={mode === "create"}
        >
          <Input
            placeholder={t(
              "users.modal.form.email.placeholder"
            )}
            type="email"
            value={email}
            onChange={(e) =>
              dispatch(
                addUserAdminSlice.actions.setEmail(
                  e.target.value
                )
              )
            }
            className="rounded-lg"
            readOnly={mode === "update"}
            disabled={mode === "update"}
          />
        </Form.Item>

        {/* Name field */}
        <Form.Item
          label={t("users.modal.form.name.label")}
          required
        >
          <Input
            placeholder={t(
              "users.modal.form.name.placeholder"
            )}
            value={name}
            onChange={(e) =>
              dispatch(
                addUserAdminSlice.actions.setName(
                  e.target.value
                )
              )
            }
            className="rounded-lg"
          />
        </Form.Item>

        {/* Phone field */}
        <Form.Item
          label={t("users.modal.form.phone.label")}
        >
          <Input
            placeholder={t(
              "users.modal.form.phone.placeholder"
            )}
            value={phone}
            onChange={(e) =>
              dispatch(
                addUserAdminSlice.actions.setPhone(
                  e.target.value
                )
              )
            }
            className="rounded-lg"
          />
        </Form.Item>

        {/* Password fields - always shown */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            label={t("users.modal.form.password.label")}
            required={mode === "create"}
          >
            <Input.Password
              placeholder={
                mode === "create"
                  ? t(
                      "users.modal.form.password.placeholder"
                    )
                  : t(
                      "users.modal.form.password.placeholderUpdate"
                    )
              }
              value={password}
              onChange={(e) =>
                dispatch(
                  addUserAdminSlice.actions.setPassword(
                    e.target.value
                  )
                )
              }
              className="rounded-lg"
            />
          </Form.Item>
          <Form.Item
            label={t(
              "users.modal.form.confirmPassword.label"
            )}
            required={mode === "create"}
          >
            <Input.Password
              placeholder={
                mode === "create"
                  ? t(
                      "users.modal.form.confirmPassword.placeholder"
                    )
                  : t(
                      "users.modal.form.confirmPassword.placeholderUpdate"
                    )
              }
              value={confirmPassword}
              onChange={(e) =>
                dispatch(
                  addUserAdminSlice.actions.setConfirmPassword(
                    e.target.value
                  )
                )
              }
              className="rounded-lg"
            />
          </Form.Item>
        </div>
        <Divider />
        {/* Access control checkboxes */}
        <Form.Item>
          <Checkbox
            checked={allowMobileAccess}
            onChange={(e) =>
              dispatch(
                addUserAdminSlice.actions.setAllowMobileAccess(
                  e.target.checked
                )
              )
            }
          >
            {t("users.modal.form.allowMobileAccess")}
          </Checkbox>
        </Form.Item>
        <Form.Item>
          <Checkbox
            checked={allowWebAccess}
            onChange={(e) =>
              dispatch(
                addUserAdminSlice.actions.setAllowWebAccess(
                  e.target.checked
                )
              )
            }
          >
            {t("users.modal.form.allowWebAccess")}
          </Checkbox>
        </Form.Item>
        <Divider />
        {/* Role selection */}
        <Form.Item
          label={t("users.modal.form.role.label")}
          required={true}
        >
          <SelectRole
            value={roleId || undefined}
            onChange={(roleId) =>
              dispatch(
                addUserAdminSlice.actions.setRoleId(roleId)
              )
            }
          />
        </Form.Item>
        {/* Label selection */}
        <Form.Item
          label={t("users.modal.form.label.label")}
        >
          <SelectLabel
            mode={"multiple"}
            value={labels}
            onChange={(labels) => {
              if (Array.isArray(labels)) {
                dispatch(
                  addUserAdminSlice.actions.setLabels(
                    labels
                  )
                );
              }
            }}
          />
        </Form.Item>
        <Divider />
        {/* Branch selection */}
        <Form.Item
          label={t("users.modal.form.client.label")}
        >
          <GridSelectBranch
            multiple={true}
            maxHeight={800}
            value={branches}
            onChange={(branches) => {
              if (Array.isArray(branches)) {
                dispatch(
                  addUserAdminSlice.actions.setBranches(
                    branches.map((branch) => branch.id)
                  )
                );
              }
            }}
          />
        </Form.Item>
        <Divider />
      </Form>
      {/*  Use error message component*/}
      <div>
        {errorMessage && (
          <ErrorMessage message={errorMessage} />
        )}
      </div>
    </Modal>
  );
};

export default ModalAddUser;
