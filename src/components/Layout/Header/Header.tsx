import { Button } from "antd";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from "@ant-design/icons";
import React from "react";

interface HeaderProps {
  collapsed?: boolean;
  onToggleCollapse?: () => void;
  children?: React.ReactNode;
}

export default function Header({
  collapsed,
  onToggleCollapse,
  children,
}: HeaderProps) {
  return (
    <header
      className={`main-header border-b bg-white dark:bg-gray-800 dark:border-gray-700 ${collapsed ? "main-header-collapsed" : "main-header-expanded"}`}
    >
      <div className="flex justify-between items-center h-full px-6">
        <Button
          type="text"
          icon={
            collapsed ? (
              <MenuUnfoldOutlined />
            ) : (
              <MenuFoldOutlined />
            )
          }
          onClick={onToggleCollapse}
        />
        {children}
      </div>
    </header>
  );
}
