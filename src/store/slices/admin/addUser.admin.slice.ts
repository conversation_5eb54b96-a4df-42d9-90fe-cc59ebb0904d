import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import {
  AdminUser,
  UpdateUserRequest,
} from "../../../services/mainApi/admin/types/user.admin.mainApi.types.ts";
import userAdminMainApi from "../../../services/mainApi/admin/user.admin.mainApi.ts";
import { RootState } from "../../index.ts";
import Axios from "axios";
import { handleApiError } from "../../../utils/error.ts";

interface State {
  mode: "create" | "update";
  userEdit: AdminUser | null;
  open: boolean;
  loading: boolean;
  errorMessage: string | string[] | null;
  submitting: boolean;

  // Form fields
  name: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  allowMobileAccess: boolean;
  allowWebAccess: boolean;
  roleId: string | null;
  labels: string[];
  branches: string[];
  active: boolean;
}

const initialState: State = {
  mode: "create",
  userEdit: null,
  open: false,
  loading: false,
  errorMessage: null,
  submitting: false,

  name: "",
  email: "",
  phone: "",
  password: "",
  confirmPassword: "",
  roleId: null,
  allowMobileAccess: true,
  allowWebAccess: true,
  labels: [],
  branches: [],
  active: true,
};

export const fetchDetailUser = createAsyncThunk(
  "addUserAdmin/fetchDetailUser",
  async (
    branchCode: string,
    { rejectWithValue, getState }
  ) => {
    const state = getState() as RootState;
    const { userEdit } = state.addUserAdmin;
    const id = userEdit?.id || "";
    try {
      const response = await userAdminMainApi.getUserById(
        branchCode,
        id
      );
      return response.data;
    } catch (error: unknown) {
      if (Axios.isAxiosError(error)) {
        const { errors } = handleApiError(error);
        return rejectWithValue(errors);
      }
      return rejectWithValue("Failed to fetch detail user");
    }
  }
);

export const createUser = createAsyncThunk(
  "addUserAdmin/createUser",
  async (
    branchCode: string,
    { rejectWithValue, getState }
  ) => {
    const state = getState() as RootState;
    const {
      name,
      email,
      phone,
      password,
      confirmPassword,
      roleId,
      allowMobileAccess,
      allowWebAccess,
      labels,
      branches,
    } = state.addUserAdmin;

    try {
      const response = await userAdminMainApi.createUser(
        {
          name,
          email,
          phone,
          password,
          confirm_password: confirmPassword,
          role_id: roleId,
          web_access: allowWebAccess,
          mobile_access: allowMobileAccess,
          label_ids: labels,
          branch_ids: branches,
        },
        branchCode
      );
      return response.data;
    } catch (error: unknown) {
      if (Axios.isAxiosError(error)) {
        const { errors } = handleApiError(error);
        return rejectWithValue(errors);
      }
      return rejectWithValue("Failed to create user");
    }
  }
);

export const updateUser = createAsyncThunk(
  "addUserAdmin/updateUser",
  async (
    branchCode: string,
    { rejectWithValue, getState }
  ) => {
    const state = getState() as RootState;
    const {
      userEdit,
      name,
      phone,
      password,
      confirmPassword,
      roleId,
      allowMobileAccess,
      allowWebAccess,
      labels,
      branches,
      active,
    } = state.addUserAdmin;

    if (!userEdit) {
      return rejectWithValue("No user selected for update");
    }

    try {
      // Prepare update payload
      const updateData: UpdateUserRequest = {
        name,
        phone,
        role_id: roleId,
        web_access: allowWebAccess,
        mobile_access: allowMobileAccess,
        active,
        label_ids: labels,
        branch_ids: branches,
        password,
        confirm_password: confirmPassword,
      };

      // Only include password fields if they are provided
      if (password && confirmPassword) {
        Object.assign(updateData, {
          password,
          confirm_password: confirmPassword,
        });
      }

      const response = await userAdminMainApi.updateUser(
        updateData,
        branchCode,
        userEdit.id
      );
      return response.data;
    } catch (error: unknown) {
      if (Axios.isAxiosError(error)) {
        const { errors } = handleApiError(error);
        return rejectWithValue(errors);
      }
      return rejectWithValue("Failed to update user");
    }
  }
);

const addUserAdminSlice = createSlice({
  name: "addUserAdmin",
  initialState,
  reducers: {
    open: (state) => {
      state.open = true;
      state.errorMessage = null;
    },
    openEdit: (state, action: PayloadAction<AdminUser>) => {
      state.open = true;
      state.mode = "update";
      state.userEdit = action.payload;
      state.name = action.payload.name;
      state.email = action.payload.email;
      state.phone = action.payload.phone;
      state.roleId = action.payload.role_id;
      state.active = action.payload.active;
      state.allowMobileAccess =
        action.payload.mobile_access;
      state.allowWebAccess = action.payload.web_access;
      state.labels = action.payload.labels.map(
        (label) => label.id
      );
      state.branches =
        action.payload.branches?.map(
          (branch) => branch.id
        ) || [];
    },
    close: () => {
      return initialState;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setName: (state, action: PayloadAction<string>) => {
      state.name = action.payload;
    },
    setEmail: (state, action: PayloadAction<string>) => {
      state.email = action.payload;
    },
    setPhone: (state, action: PayloadAction<string>) => {
      state.phone = action.payload;
    },
    setPassword: (state, action: PayloadAction<string>) => {
      state.password = action.payload;
    },
    setConfirmPassword: (
      state,
      action: PayloadAction<string>
    ) => {
      state.confirmPassword = action.payload;
    },
    setRoleId: (state, action: PayloadAction<string>) => {
      state.roleId = action.payload || "";
    },
    setAllowMobileAccess: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.allowMobileAccess = action.payload;
    },
    setAllowWebAccess: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.allowWebAccess = action.payload;
    },
    setLabels: (state, action: PayloadAction<string[]>) => {
      state.labels = action.payload;
    },
    setBranches: (
      state,
      action: PayloadAction<string[]>
    ) => {
      state.branches = action.payload;
    },
    setActive: (state, action: PayloadAction<boolean>) => {
      state.active = action.payload;
    },
    setError: (
      state,
      action: PayloadAction<State["errorMessage"]>
    ) => {
      state.errorMessage = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDetailUser.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchDetailUser.fulfilled,
        (state, action) => {
          state.loading = false;
          if (action.payload) {
            state.name = action.payload.name;
            state.email = action.payload.email;
            state.roleId = action.payload.role_id;
            state.active = action.payload.active;
            state.allowMobileAccess =
              action.payload.mobile_access;
            state.allowWebAccess =
              action.payload.web_access;
            state.labels = action.payload.labels.map(
              (label) => label.id
            );
            state.branches =
              action.payload.branches?.map(
                (branch) => branch.id
              ) || [];
          }
        }
      )
      .addCase(
        fetchDetailUser.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      )
      .addCase(updateUser.pending, (state) => {
        state.submitting = true;
        state.errorMessage = null;
      })
      .addCase(updateUser.fulfilled, (state) => {
        state.submitting = false;
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.submitting = false;
        state.errorMessage = action.payload as string;
      })
      .addCase(createUser.pending, (state) => {
        state.submitting = true;
        state.errorMessage = null;
      })
      .addCase(createUser.fulfilled, (state) => {
        state.submitting = false;
      })
      .addCase(createUser.rejected, (state, action) => {
        state.submitting = false;
        state.errorMessage = action.payload as string;
      });
  },
});

export default addUserAdminSlice;
