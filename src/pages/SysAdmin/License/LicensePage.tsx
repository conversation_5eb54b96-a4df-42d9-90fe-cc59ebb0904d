import { Button, Pagination, Input, Select } from "antd";
import { useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>d<PERSON><PERSON> } from "react-icons/md";
import { SearchOutlined, SortDescendingOutlined, SortAscendingOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchLicenses,
  selectLicenses,
  selectLicenseLoading,
  setPage,
  selectLicensePagination,
  selectLicenseFilters,
  setSort,
  setSearchQuery
} from "../../../store/slices/sysadmin/licenseList.sysadmin.slice.ts";
import addLicenseSysadminSlice from "../../../store/slices/sysadmin/addLicense.sysadmin.slice.ts";
import SysAdminLayout from "../../../components/Layout/SysAdminLayout.tsx";
import AddEditLicenseModal from "./Components/AddEditLicenseModal.tsx";
import LicenseList from "./Components/LicenseList.tsx";
import type { AppDispatch } from "../../../store";
import type { License } from "../../../services/mainApi/types/license.mainApi.types";
import type { SortField } from "../../../store/slices/sysadmin/licenseList.sysadmin.slice";

const { Option } = Select;

export default function LicensePage() {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();

  // Get license data from Redux store
  const licenses = useSelector(selectLicenses);
  const loading = useSelector(selectLicenseLoading);
  const pagination = useSelector(selectLicensePagination);
  const filters = useSelector(selectLicenseFilters);

  useEffect(() => {
    // Fetch licenses when component mounts
    dispatch(fetchLicenses());
  }, [dispatch]);

  // Open modal for adding a new license
  const handleAddLicense = () => {
    dispatch(addLicenseSysadminSlice.actions.open());
  };

  // Open modal for editing an existing license
  const handleEditLicense = (license: License) => {
    dispatch(addLicenseSysadminSlice.actions.openEdit(license));
  };

  // Handle search input change
  const handleSearch = (value: string) => {
    dispatch(setSearchQuery(value));
  };

  // Handle pagination change
  const handlePageChange = (page: number) => {
    dispatch(setPage(page));
  };

  // Toggle sort order
  const toggleSortOrder = () => {
    dispatch(setSort({
      field: filters.sortField,
      order: filters.sortOrder === "asc" ? "desc" : "asc"
    }));
  };

  // Handle sort field change
  const handleSortFieldChange = (field: SortField) => {
    dispatch(setSort({
      field,
      order: filters.sortOrder
    }));
  };

  return (
    <SysAdminLayout activePage={"license"}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Sticky Header */}
        <div className="sticky top-0 z-10 bg-gray-50 dark:bg-gray-900 pb-6">
          {/* Header Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6 backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90 transition-all duration-300">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <span className="bg-blue-500 text-white p-2 rounded-lg mr-3 shadow-md">
                    <MdKey className="text-xl" />
                  </span>
                  {t("license.title")}
                </h1>
                <p className="text-gray-500 dark:text-gray-400 mt-2 ml-12">
                  {t("license.description", "Manage license packages")}
                </p>
              </div>
              <Button
                type="primary"
                icon={<MdAdd className="text-xl" />}
                onClick={handleAddLicense}
                size="large"
                className="bg-blue-500 hover:bg-blue-600 border-0 shadow-md hover:shadow-lg transition-all duration-300 rounded-lg"
              >
                {t("license.add")}
              </Button>
            </div>
          </div>

          {/* Search and Sort Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90 transition-all duration-300">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="w-full md:w-1/3">
                <Input
                  placeholder={t("license.searchPlaceholder", "Search licenses...")}
                  prefix={
                    <SearchOutlined className="text-gray-400 dark:text-gray-500" />
                  }
                  value={filters.searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="rounded-lg hover:border-blue-400 focus:border-blue-500 transition-all duration-300"
                  size="large"
                  allowClear
                />
              </div>
              <div className="flex flex-wrap gap-3 items-center">
                <Select
                  value={filters.sortField}
                  onChange={handleSortFieldChange}
                  className="w-48 rounded-lg"
                  size="large"
                  popupClassName="rounded-lg overflow-hidden"
                  dropdownStyle={{ borderRadius: '0.5rem' }}
                >
                  <Option value="created_at">
                    {t("common.creationDate", "Creation Date")}
                  </Option>
                  <Option value="license_name">
                    {t("license.table.name")}
                  </Option>
                </Select>
                <Button
                  icon={
                    filters.sortOrder === "desc" ? (
                      <SortDescendingOutlined />
                    ) : (
                      <SortAscendingOutlined />
                    )
                  }
                  onClick={toggleSortOrder}
                  size="large"
                  className="flex items-center dark:border-gray-600 dark:text-gray-300 hover:bg-blue-50 hover:text-blue-500 dark:hover:bg-blue-900/20 dark:hover:text-blue-400 transition-all duration-300"
                >
                  {filters.sortOrder === "desc"
                    ? t("common.sortDesc", "Descending")
                    : t("common.sortAsc", "Ascending")}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* License List with Pagination */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm mt-6 overflow-hidden transition-all duration-300 hover:shadow-md">
          <LicenseList
            licenses={licenses}
            loading={loading}
            onEdit={handleEditLicense}
          />

          {/* Pagination */}
          <div className="p-6 border-t border-gray-100 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50">
            <Pagination
              current={pagination.current}
              total={pagination.total}
              pageSize={pagination.pageSize}
              onChange={handlePageChange}
              showTotal={(total) => `${t("common.total")} ${total} ${t("common.items")}`}
              showSizeChanger={false}
              className="flex justify-end"
              itemRender={(page, type, originalElement) => {
                if (type === 'page' && page === pagination.current) {
                  return <div className="bg-blue-500 text-white flex items-center justify-center h-8 w-8 rounded-md">{page}</div>;
                }
                return originalElement;
              }}
            />
          </div>
        </div>

        {/* Modals */}
        <AddEditLicenseModal />
      </div>
    </SysAdminLayout>
  );
}
