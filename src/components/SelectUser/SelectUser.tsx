import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import userAdminMainApi from "../../services/mainApi/admin/user.admin.mainApi.ts";
import { AdminUser } from "../../services/mainApi/admin/types/user.admin.mainApi.types.ts";
import { useParams } from "react-router";

interface SelectUserProps {
  value?: string;
  onChange?: (
    value: string | null,
    adminUser: AdminUser | null
  ) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  clearable?: boolean;
  showEmail?: boolean;
  roleId?: string;
  branchId?: string;

}

export default function SelectUser({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select user",
  roleId,
  clearable = true,
  branchId,
}: SelectUserProps) {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);
  const { branchCode } = useParams();

  useEffect(() => {
    const fetchUsers = async () => {
      if (isFetching.current) return;

      try {
        isFetching.current = true;
        setLoading(true);
        const response = await userAdminMainApi.getUsers(
          {
            role_id: roleId,
            branch_id: branchId,
          },
          branchCode || ""
        );
        setUsers(response.data || []);
      } catch (error) {
        console.error("Failed to fetch users:", error);
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchUsers();
  }, [roleId, branchCode, branchId]);

  const handleChange = (value: string) => {
    if (onChange) {
      const selectedUser = users.find(
        (user) => user.id === value
      );
      if (selectedUser) {
        onChange(value, selectedUser);
      } else {
        onChange(null, null);
      }
    }
  };

  return (
    <Select
      className={className}
      placeholder={placeholder}
      value={value}
      onChange={handleChange}
      disabled={disabled}
      loading={loading}
      showSearch
      optionFilterProp="label"
      options={users.map((user) => ({
        value: user.id,
        label: `${user.name}${user.role ? ` - ${user.role.role_name}` : ""}`,
      }))}
      allowClear={clearable}
    />
  );
}
