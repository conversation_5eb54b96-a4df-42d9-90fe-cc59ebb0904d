import {
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { AdminLabel } from "../../../services/mainApi/admin/types/label.admin.mainApi.types";

interface State {
  isOpen: boolean;
  mode: "create" | "edit";
  selectedLabel: AdminLabel | null;
}

const initialState: State = {
  isOpen: false,
  mode: "create",
  selectedLabel: null,
};

const addLabelAdminSlice = createSlice({
  name: "addLabelAdmin",
  initialState,
  reducers: {
    openCreate: (state) => {
      state.isOpen = true;
      state.mode = "create";
      state.selectedLabel = null;
    },
    openEdit: (
      state,
      action: PayloadAction<AdminLabel>
    ) => {
      state.isOpen = true;
      state.mode = "edit";
      state.selectedLabel = action.payload;
    },
    close: (state) => {
      state.isOpen = false;
      state.selectedLabel = null;
    },
  },
});

export const { openCreate, openEdit, close } =
  addLabelAdminSlice.actions;
export default addLabelAdminSlice;
