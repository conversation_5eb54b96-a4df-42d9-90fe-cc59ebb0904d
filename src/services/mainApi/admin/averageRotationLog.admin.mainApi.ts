import baseMainApi from "../base.mainApi";
import {
  AdminAverageRotationLogListParams,
  AdminAverageRotationLogListResponse,
} from "./types/averageRotationLog.admin.mainApi.types";

export const averageRotationLogAdminMainApi = {
  getAverageRotationLogs: async (
    branchCode: string,
    params?: AdminAverageRotationLogListParams
  ) => {
    const response = await baseMainApi.get<AdminAverageRotationLogListResponse>(
      `/web-api/admin/${branchCode}/average-rotation-log`,
      { params }
    );
    return response.data;
  },

  exportAverageRotationLogs: async (
    branchCode: string,
    params: AdminAverageRotationLogListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/average-rotation-log/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
};
