import { useCallback, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Button, Input, Select } from "antd";
import { FiPlus } from "react-icons/fi";
import {
  SearchOutlined,
  SortAscendingOutlined,
} from "@ant-design/icons";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks";
import {
  fetchCheckpointAdmin,
  setFilter,
} from "../../../store/slices/admin/checkpoint.admin.slice";
import CheckpointList from "./Components/CheckpointList";
import ModalAddCheckpoint from "./Components/ModalAddCheckpoint/ModalAddCheckpoint";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import { MdLocationOn } from "react-icons/md";
import _ from "lodash";
import addCheckpointAdminSlice from "../../../store/slices/admin/addCheckpoint.admin.slice.ts";
import { useParams } from "react-router";

const { Option } = Select;

const CheckpointAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { filter } = useAppSelector(
    (state) => state.checkpointAdmin
  );
  const { branchCode } = useParams();
  const isFetching = useRef(false);

  const fetch = useCallback(() => {
    if (isFetching.current) return;
    isFetching.current = true;
    dispatch(fetchCheckpointAdmin(branchCode || ""));
    isFetching.current = false;
  }, [dispatch, branchCode]);

  const debouncedSearchRef = useRef(
    _.debounce(() => {
      fetch();
    }, 500)
  );

  const handleAddClick = useCallback(() => {
    dispatch(addCheckpointAdminSlice.actions.open());
  }, [dispatch]);

  const handleSearch = useCallback(
    (value: string) => {
      dispatch(
        setFilter({ search: value || null, page: 1 })
      );
      debouncedSearchRef.current();
    },
    [dispatch]
  );

  const handleSortChange = useCallback(
    (value: string) => {
      dispatch(setFilter({ orderBy: value }));
    },
    [dispatch]
  );

  const handleSortDirectionToggle = useCallback(() => {
    dispatch(
      setFilter({
        orderDirection:
          filter.orderDirection === "ASC" ? "DESC" : "ASC",
      })
    );
  }, [dispatch, filter.orderDirection]);

  return (
    <WebAdminLayout activePage="checkpoint">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header Section */}
          <PageHeader
            icon={<MdLocationOn />}
            title={t("checkpoint.title")}
            description={t("checkpoint.description")}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<FiPlus />}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={handleAddClick}
              >
                {t("checkpoint.add")}
              </Button>,
            ]}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder={t(
                  "checkpoint.search.placeholder"
                )}
                prefix={<SearchOutlined />}
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                size="large"
                onChange={(e) =>
                  handleSearch(e.target.value)
                }
                allowClear
              />
              <div className="flex gap-2">
                <Select
                  defaultValue="created_at"
                  className="min-w-[160px] dark:bg-gray-700"
                  size="large"
                  onChange={handleSortChange}
                  value={filter.orderBy}
                >
                  <Option value="created_at">
                    {t("checkpoint.sort.createdAt")}
                  </Option>
                  <Option value="checkpoint_name">
                    {t("checkpoint.sort.name")}
                  </Option>
                </Select>
                <Button
                  icon={
                    <SortAscendingOutlined
                      rotate={
                        filter.orderDirection === "DESC"
                          ? 180
                          : 0
                      }
                    />
                  }
                  size="large"
                  onClick={handleSortDirectionToggle}
                  className="dark:border-gray-600 dark:text-gray-300"
                />
              </div>
            </div>
          </div>

          {/* Content Section */}
          <CheckpointList />
        </div>
      </div>
      <ModalAddCheckpoint />
    </WebAdminLayout>
  );
};

export default CheckpointAdmin;
