import {Empty, Pagination, Spin, Tooltip} from "antd";
import {useCallback, useEffect, useRef} from "react";
import branchAdminSlice, {fetchBranches,} from "../../../../store/slices/admin/branch.admin.slice.ts";
import {useAppDispatch, useAppSelector,} from "../../../../store/hooks.ts";
import {useTranslation} from "react-i18next";
import {MdAccessTime, MdCalendarToday, MdEdit, MdRemoveRedEye, MdVerified,} from "react-icons/md";
import dayjs from "dayjs";
import viewBranchAdminSlice from "../../../../store/slices/admin/viewBranch.admin.slice.ts";
import addSubBranchAdminSlice from "../../../../store/slices/admin/addSubBranch.admin.slice.ts";
import {AdminBranch} from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types.ts";

const BranchGrid = () => {
  const dispatch = useAppDispatch();
  const branch = useAppSelector((s) => s.branchAdmin);
  const {branches, loading, pagination, filter} = branch;
  const isLoading = useRef(false);
  const {t} = useTranslation();

  const fetch = useCallback(async () => {
    if (isLoading.current) return;
    isLoading.current = true;
    await dispatch(fetchBranches());
    isLoading.current = false;
  }, [dispatch]);

  const onPageChange = useCallback(
    (page: number) => {
      dispatch(
        branchAdminSlice.actions.setFilter({
          page: page,
        })
      );
      fetch();
    },
    [dispatch, fetch]
  );

  useEffect(() => {
    if (isLoading.current) return;
    fetch();
  }, [fetch]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Spin size="large"/>
      </div>
    );
  }

  if (!branches.length) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Empty
          description={
            <span className="text-gray-500 dark:text-gray-400">
              {t("branch.grid.empty")}
            </span>
          }
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Branches Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {branches.map((branch: AdminBranch) => {
          const isMainBranch = branch.id === branch.parent_id;

          const handleView = () => {
            dispatch(viewBranchAdminSlice.actions.open(branch));
          };

          const handleEdit = () => {
            dispatch(addSubBranchAdminSlice.actions.openEdit(branch));
          };

          return (
            <div
              key={branch.id}
              className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow group ${!branch.active ? "opacity-75" : ""}`}
            >
              {/* Branch Content */}
              <div className="flex flex-col h-full">
                <div className="flex-grow">
                  {/* Header with name and status */}
                  <div className="flex items-center justify-between mb-2">
                    <h3
                      className="text-base font-medium text-gray-900 dark:text-gray-100 truncate"
                      title={branch.branch_name}
                    >
                      {branch.branch_name}
                    </h3>
                    <div className="flex items-center gap-2">
                      <div className="flex gap-1">
                        {!branch.active && (
                          <div
                            className="bg-red-100 text-red-800 text-xs px-2 py-0.5 rounded-full dark:bg-red-900 dark:text-red-200">
                            {t("branch.grid.status.inactive")}
                          </div>
                        )}
                        {isMainBranch && (
                          <div
                            className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-200">
                            {t("branch.grid.status.mainBranch")}
                          </div>
                        )}
                      </div>
                      {/* Actions */}
                      <div className="flex space-x-1">
                        <Tooltip title={t("branch.grid.actions.view")}>
                          <button
                            onClick={handleView}
                            className="p-1 text-blue-600 hover:bg-blue-50 rounded-md dark:text-blue-400 dark:hover:bg-blue-900/20"
                          >
                            <MdRemoveRedEye size={16} />
                          </button>
                        </Tooltip>
                        <Tooltip title={t("branch.grid.actions.edit")}>
                          <button
                            onClick={handleEdit}
                            className="p-1 text-green-600 hover:bg-green-50 rounded-md dark:text-green-400 dark:hover:bg-green-900/20"
                          >
                            <MdEdit size={16} />
                          </button>
                        </Tooltip>
                      </div>
                    </div>
                  </div>

                  {/* Branch code */}
                  <p className="text-xs text-gray-600 dark:text-gray-300 mb-2">
                    <span className="font-medium">{t("branch.grid.columns.code")}:</span>{" "}
                    {branch.branch_code}
                  </p>

                  {/* Branch information */}
                  <div className="space-y-1 text-xs text-gray-600 dark:text-gray-300">
                    <p className="flex items-center gap-1">
                      <MdVerified className="text-blue-500 dark:text-blue-400"/>
                      {t("branch.grid.license")}:{" "}
                      {branch.license.license_name}
                    </p>
                    <p className="flex items-center gap-1">
                      <MdAccessTime className="text-green-500 dark:text-green-400"/>
                      {branch.timezone.timezone_name} ({branch.timezone.gmt_offset})
                    </p>
                    <p className="flex items-center gap-1">
                      <MdCalendarToday className="text-gray-500 dark:text-gray-400"/>
                      {t("branch.grid.columns.createdAt")}:{" "}
                      {dayjs(branch.created_at).format("DD MMM YYYY")}
                    </p>
                    <p className="flex items-center gap-1">
                      <MdAccessTime className="text-gray-500 dark:text-gray-400"/>
                      {t("branch.grid.columns.updatedAt")}:{" "}
                      {dayjs(branch.updated_at).format("DD MMM YYYY")}
                    </p>
                  </div>
                </div>

                {/* Footer space */}
                <div className="pt-2 mt-2 border-t border-gray-100 dark:border-gray-700"></div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Pagination */}
      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3">
        <Pagination
          current={filter.page || 1}
          total={pagination.total}
          pageSize={filter.limit || 10}
          onChange={onPageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              {t("branch.grid.total", {total})}
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

export default BranchGrid;
