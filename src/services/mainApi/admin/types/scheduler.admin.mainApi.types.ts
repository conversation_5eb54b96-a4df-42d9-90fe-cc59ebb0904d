import {BaseGetRequest, BaseResponse,} from "../../types/base.mainApi.types";
import {AdminBranch} from "./branch.admin.mainApi.types";
import {AdminUser} from "./user.admin.mainApi.types";
import {AdminDevice} from "./device.admin.mainApi.types.ts";
import {AdminRole} from "./roles.admin.mainApi.types.ts";
import {AdminZone} from "./zone.admin.mainApi.types.ts";
import {AdminCheckpoint} from "./checkpoint.admin.mainApi.types.ts";
import {AdminForm} from "./form.admin.mainApi.types.ts";
import {AdminActivity} from "./activity.admin.mainApi.types.ts";
import {AdminTask} from "./task.admin.mainApi.types.ts";
import { AdminLabelDetail } from "./label.admin.mainApi.types.ts";

export enum EReportType {
  ACTIVITY_LOG = "1",
  ALARMS = "2",
  AVERAGE_ROTATION = "3",
  BRANCH_DETAILS = "4",
  CHECKPOINT_ACTIVITY = "5",
  CHECKPOINT_BATTERY = "6",
  EXCEPTION = "7",
  EXCEPTION_DETAILED = "8",
  FORMS = "9",
  GEOFENCE = "10",
  GPS_HEATMAP = "11",
  MISSED_ZONE = "12",
  SIGN_ON_OFF = "13",
  TASKS = "14",
  TIME_AND_ROTATION = "15",
  TIME_ON_ZONE = "16",
  UNKNOWN = "17",
}

export enum ERportFrequency {
  DAILY = "1",
  WEEKLY = "2",
  MONTHLY = "3",
  YEARLY = "4",
  TWO_DAYS = "5",
}

export enum EReportFormatType {
  PDF = "PDF",
  SPREADSHEET = "SPREADSHEET",
}

export enum ERotationMethod {
  COUNT = "COUNT",
}

interface ReportType {
  id: string;
  report_type_name: string;
  report_type_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

interface Frequency {
  id: string;
  frequency_name: string;
  frequency_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
}

export interface AdminSchedulerGetRequest
  extends BaseGetRequest {
  report_type_id?: number;
}

export interface CreateSchedulerRequest {
  scheduler_name: string;
  scheduler_description: string;
  report_type_id: number;
  report_format_type: EReportFormatType | string;
  frequency_id: number;
  generate_date: string;
  generate_time: string;
  stop_if_blank: boolean;
  detailed_report: boolean;
  start_time: string;
  end_time: string;
  selected_branch_id: number | null;
  role_id?: number | null;
  user_id: number | null;
  user_label_ids: number[];
  device_id?: number | null;
  device_label_ids: number[];
  zone_id?: number | null;
  zone_label_ids: number[];
  checkpoint_id?: number | null;
  checkpoint_label_ids: number[];
  form_id?: number | null;
  activity_id?: number | null;
  rotation_interval: number;
  rotation_method: ERotationMethod | string;
  subject: string;
  message: string;
  email: string[];
  active?: boolean;
}

export type UpdateSchedulerRequest = CreateSchedulerRequest;

interface SchedulerRecipient {
  id: string;
  schedule_id: string;
  recipient_type: "EMAIL";
  recipient_contact: string;
  created_at: string;
  created_by: string;
}

export interface AdminScheduler {
  id: string;
  branch_id: string;
  parent_branch_id: string;
  selected_branch_id: string | null;
  role_id: string | null;
  user_id: string | null;
  report_type_id: string;
  device_id: string | null;
  zone_id: string | null;
  checkpoint_id: string | null;
  form_id: string | null;
  activity_id: string | null;
  task_id: string | null;
  frequency_id: string;
  report_format_type: "PDF" | "SPREADSHEET" | string;
  scheduler_name: string;
  scheduler_description: string | null;
  start_period: string | null;
  end_period: string | null;
  next_run_time: string | null;
  generate_date: string;
  generate_time: string;
  generate_time_utc: string | null;
  start_time: string;
  end_time: string;
  stop_if_blank: boolean;
  detailed_report: boolean;
  subject: string;
  message: string;
  active: boolean;
  rotation_method: "COUNT" | string;
  rotation_interval: number;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
  branch: AdminBranch | null;
  role: AdminRole | null;
  user: AdminUser | null;
  report_type: ReportType;
  device: AdminDevice | null;
  zone: AdminZone | null;
  checkpoint: AdminCheckpoint | null;
  form: AdminForm | null;
  activity: AdminActivity | null;
  task: AdminTask | null;
  frequency: Frequency;
  recipients: SchedulerRecipient[];
  checkpoint_labels: Omit<AdminLabelDetail, "branch">[];
  device_labels: Omit<AdminLabelDetail, "branch">[];
  user_labels: Omit<AdminLabelDetail, "branch">[];
  zone_labels: Omit<AdminLabelDetail, "branch">[];
}

export type AdminSchedulerListResponse = BaseResponse<
  AdminScheduler[]
>;
export type AdminSchedulerDetailResponse =
  BaseResponse<AdminScheduler>;
