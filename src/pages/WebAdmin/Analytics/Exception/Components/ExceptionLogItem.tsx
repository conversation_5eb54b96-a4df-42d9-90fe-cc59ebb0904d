import React from "react";
import { Badge } from "antd";
import { FiMapPin, FiCheckCircle, FiXCircle, FiClock, FiSmartphone } from "react-icons/fi";
import { useTranslation } from "react-i18next";
import { AdminExceptionReportLogEntry } from "../../../../../services/mainApi/admin/types/exceptionReportLog.admin.mainApi.types";

interface ExceptionLogItemProps {
  exceptionLog: AdminExceptionReportLogEntry;
}

const ExceptionLogItem: React.FC<ExceptionLogItemProps> = ({ exceptionLog }) => {
  const { t } = useTranslation();

  // Calculate completion percentage
  const completionPercentage = exceptionLog.expected_visit_time > 0
    ? Math.round((exceptionLog.actual_visit_time / exceptionLog.expected_visit_time) * 100)
    : 0;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-0 p-4 mb-4">
      {/* Header - Checkpoint Name with gradient background */}
      <div className="mb-3 -mx-4 -mt-4 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 rounded-t-xl border-b border-gray-100 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center gap-2">
          <FiMapPin className="text-red-500" />
          {exceptionLog.checkpoint_name}
        </h3>
        {/* Device Info */}
        <div className="flex items-center gap-2 mt-1 text-sm text-gray-600 dark:text-gray-400">
          <FiSmartphone className="text-blue-500" />
          <span>{t("exceptionLog.device", "Device")}: {exceptionLog.serial_number_hex}</span>
        </div>
      </div>

      {/* Content - Compact Two Column Layout */}
      <div className="grid grid-cols-2 gap-3 px-1">
        {/* Left Column */}
        <div className="space-y-2">
          {/* Expected Visits - compact */}
          <div className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
              <FiClock className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("exceptionLog.expectedVisits", "Expected")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {exceptionLog.expected_visit_time}
                </span>
              </div>
            </div>
          </div>

          {/* Actual Visits - compact */}
          <div className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
              <FiCheckCircle className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("exceptionLog.actualVisits", "Actual")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {exceptionLog.actual_visit_time}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-2">
          {/* Missed Visits - compact */}
          <div className="group flex items-center gap-2 text-sm hover:bg-red-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300 flex-shrink-0">
              <FiXCircle className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("exceptionLog.missedVisits", "Missed")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {exceptionLog.missed_visit_time}
                </span>
              </div>
            </div>
          </div>

          {/* Completion Rate - compact */}
          <div className="group flex items-center gap-2 text-sm hover:bg-indigo-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex-shrink-0">
              <FiClock className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("exceptionLog.completionRate", "Completion")}:
                </span>
                <Badge
                  color={exceptionLog.missed_visit_time > 0 ? "red" : "green"}
                  count={`${completionPercentage}%`}
                  className="font-medium text-xs"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExceptionLogItem;
