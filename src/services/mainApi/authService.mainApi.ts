import baseMainApi from "./base.mainApi";
import {
  BranchPermissionResponse,
  LoginResponse,
} from "./types/auth.mainApi.types";

const authServiceMainApi = {
  login: async (email: string, password: string) => {
    const response = await baseMainApi.post<LoginResponse>(
      "/web-api/auth/login",
      {
        email,
        password,
      }
    );
    return response.data;
  },
  logout: async () => {
    const response = await baseMainApi.delete(
      "/web-api/auth/logout"
    );
    return response.data;
  },
  checkBranchPermission: async (branchCode: string) => {
    const response =
      await baseMainApi.get<BranchPermissionResponse>(
        "/web-api/auth/check-branch-permission",
        {
          params: {
            branch_code: branchCode,
          },
        }
      );
    return response.data;
  },
};

export default authServiceMainApi;
