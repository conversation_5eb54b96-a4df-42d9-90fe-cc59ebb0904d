import React, { useState } from "react";
import { Button, Tag } from "antd";
import {
  FiE<PERSON>,
  <PERSON><PERSON><PERSON>t<PERSON><PERSON>gle,
  <PERSON><PERSON>ell,
} from "react-icons/fi";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useTranslation } from "react-i18next";
import { useAppDispatch } from "../../../../store/hooks";
import { AlertAdmin } from "../../../../services/mainApi/admin/types/alert.admin.mainApi.types";
import addAlertAdminSlice from "../../../../store/slices/admin/addAlert.admin.slice";

dayjs.extend(relativeTime);

interface AlertItemProps {
  alert: AlertAdmin;
}

const AlertItem: React.FC<AlertItemProps> = ({ alert }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { actions } = addAlertAdminSlice;
  const [expandDescription, setExpandDescription] =
    useState(false);

  const handleEdit = () => {
    dispatch(actions.openUpdate(alert));
  };

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border 
        ${
          alert.active
            ? "border-gray-100 dark:border-gray-700"
            : "border-red-100 dark:border-red-900 bg-red-50/10"
        } overflow-hidden`}
    >
      <div className="p-4">
        {/* Header: Alert Title and Active/Inactive Status */}
        <div className="flex items-start justify-between mb-3 pb-3 border-b dark:border-gray-700">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <div className="mr-2">
                <FiBell
                  className="text-gray-500 dark:text-gray-400"
                  size={20}
                />
              </div>
              <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">
                {alert.alert_name}
              </h3>
              {!alert.active && (
                <Tag
                  icon={
                    <FiAlertTriangle className="mr-1" />
                  }
                  className="bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 border-red-200 dark:border-red-800"
                >
                  {t("alert.status.inactive")}
                </Tag>
              )}
            </div>

            {/* Description with expand/collapse functionality */}
            {alert.alert_description && (
              <div className="mt-1 mb-2">
                <p
                  className={`text-sm text-gray-600 dark:text-gray-300 ${
                    !expandDescription ? "line-clamp-2" : ""
                  }`}
                >
                  {alert.alert_description}
                </p>
                {alert.alert_description.length > 120 && (
                  <button
                    onClick={() =>
                      setExpandDescription(
                        !expandDescription
                      )
                    }
                    className="text-xs text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 mt-1 focus:outline-none"
                  >
                    {expandDescription
                      ? t("common.showLess")
                      : t("common.showMore")}
                  </button>
                )}
              </div>
            )}
          </div>

          <Button
            icon={<FiEdit />}
            type="text"
            className="text-gray-400 hover:text-blue-500 dark:text-gray-500 dark:hover:text-blue-400"
            onClick={handleEdit}
          />
        </div>

        {/* Alert Details */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t("alert.labels.event")}
            </div>
            <div className="mt-1 text-sm dark:text-gray-300">
              {alert.alert_event.event_name}
            </div>
          </div>

          <div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t("alert.labels.action")}
            </div>
            <div className="mt-1 text-sm dark:text-gray-300">
              {alert.alert_action.action_name}
            </div>
          </div>

          <div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t("alert.labels.createdAt")}
            </div>
            <div className="mt-1 text-sm dark:text-gray-300">
              {dayjs(alert.created_at).format(
                "MMM D, YYYY"
              )}
            </div>
          </div>

          <div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t("alert.labels.updatedAt")}
            </div>
            <div className="mt-1 text-sm dark:text-gray-300">
              {dayjs(alert.updated_at).fromNow()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AlertItem;
