import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router";
import { Provider } from "react-redux";
import { App as AntApp } from "antd";
import { AntdProvider } from "./providers/StyleProvider";
import { store } from "./store";
import "./index.css";
import App from "./App.tsx";
import { ThemeProvider } from "./context/ThemeContext";
import { I18nProvider } from "./components/I18nProvider";
import "./i18n";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <Provider store={store}>
      <I18nProvider>
        <BrowserRouter>
          <ThemeProvider>
            <AntdProvider>
              <AntApp>
                <App />
              </AntApp>
            </AntdProvider>
          </ThemeProvider>
        </BrowserRouter>
      </I18nProvider>
    </Provider>
  </StrictMode>
);
