import React, { useState } from "react";
import { But<PERSON>, Dropdown, Spin, Tag, Tooltip } from "antd";
import {
  FiCalendar,
  FiClock,
  FiDownload,
  FiFileText,
  FiGrid,
  FiMapPin,
  FiMonitor,
  FiUser,
  FiInfo,
} from "react-icons/fi";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useTranslation } from "react-i18next";
import { AdminSignInOutLog } from "../../../../../services/mainApi/admin/types/signInOutLog.admin.mainApi.types";
import { signInOutLogAdminApi } from "../../../../../services/mainApi/admin/signInOutLog.admin.mainApi";
import { useParams } from "react-router-dom";
import { formatDateTime, formatTimeAgo } from "../../../../../utils/dateUtils";

dayjs.extend(relativeTime);

interface SignInOutLogItemProps {
  signInOutLog: AdminSignInOutLog;
}

const SignInOutLogItem: React.FC<SignInOutLogItemProps> = ({
  signInOutLog,
}) => {
  const { t } = useTranslation();
  const [isDownloading, setIsDownloading] = useState(false);
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  // Add the handler functions
  const handleDownloadSpreadsheet = async (
    signInOutLog: AdminSignInOutLog
  ) => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const blob =
        await signInOutLogAdminApi.exportSignInOutLogById(
          branchCode,
          signInOutLog.id,
          "spreadsheet",
          "buffer"
        );

      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${signInOutLog.id}_sign_in_out_log.xlsx`;
      link.click();

      URL.revokeObjectURL(url);
      setIsDownloading(false);
    } catch (error) {
      console.error(
        "Error downloading spreadsheet:",
        error
      );
    } finally {
      setIsDownloading(false);
    }
  };

  const downloadPdf = async () => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const blob =
        await signInOutLogAdminApi.exportSignInOutLogById(
          branchCode,
          signInOutLog.id,
          "pdf",
          "buffer"
        );

      // Create URL and open in new tab instead of downloading
      const url = URL.createObjectURL(blob);
      window.open(url, "_blank");

      // Still need to revoke the URL when done, but we need to delay it
      // to ensure the new tab has time to load the resource
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 5000); // 5 seconds delay before cleanup
    } catch (error) {
      console.error("Error opening PDF:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  const exportMenu = {
    items: [
      {
        key: "spreadsheet",
        icon: <FiGrid className="text-lg" />,
        label: t(
          "signInOutLog.downloadSpreadsheet",
          "Export as Spreadsheet"
        ),
        onClick: () =>
          handleDownloadSpreadsheet(signInOutLog),
      },
      {
        key: "pdf",
        icon: <FiFileText className="text-lg" />,
        label: t(
          "signInOutLog.downloadPdf",
          "Export as PDF"
        ),
        onClick: downloadPdf,
      },
    ],
  };

  // Helper function to get log type tag color
  const getLogTypeTagColor = (type: string) => {
    switch (type) {
      case "SIGNIN":
        return "green";
      case "SIGNOUT":
        return "blue";
      case "FORCE_SIGNOUT":
        return "red";
      default:
        return "default";
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-0 p-4 mb-4">
      {/* Header - with gradient background */}
      <div className="mb-3 -mx-4 -mt-4 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 rounded-t-xl border-b border-gray-100 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center gap-2">
            <FiInfo className="text-blue-500" />
            {t("signInOutLog.logTitle", "Sign On/Off Log")} #{signInOutLog.id}
            <Tag color={getLogTypeTagColor(signInOutLog.user_log_type)} className="ml-2">
              {t(
                `signInOutLog.type.${signInOutLog.user_log_type.toLowerCase()}`,
                signInOutLog.user_log_type
              )}
            </Tag>
          </h3>
          <Tooltip
            title={formatDateTime(signInOutLog.event_time, "DD MMM YYYY HH:mm:ss")}
          >
            <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
              <FiClock className="inline" />
              {formatTimeAgo(signInOutLog.event_time)}
            </span>
          </Tooltip>
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {signInOutLog.parent_branch.branch_name}
        </p>
      </div>

      {/* Content - Compact Two Column Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 px-1">
        {/* Left Column */}
        <div className="space-y-2">
          {/* User Info - compact */}
          <div className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
              <FiUser className="w-4 h-4" />
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("signInOutLog.user", "User")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {signInOutLog.user_name}
                </span>
              </div>
            </div>
          </div>

          {/* Log Type - compact */}
          <div className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
              <FiInfo className="w-4 h-4" />
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("signInOutLog.logType", "Log Type")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {t(
                    `signInOutLog.type.${signInOutLog.user_log_type.toLowerCase()}`,
                    signInOutLog.user_log_type
                  )}
                </span>
              </div>
            </div>
          </div>

          {/* Location - compact (if available) */}
          {signInOutLog.latitude && signInOutLog.longitude && (
            <div className="group flex items-center gap-2 text-sm hover:bg-red-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
              <div className="flex items-center justify-center w-7 h-7 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300 flex-shrink-0">
                <FiMapPin className="w-4 h-4" />
              </div>
              <div className="flex flex-col min-w-0">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t("signInOutLog.location", "Location")}:
                  </span>
                  <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                    {signInOutLog.latitude.toFixed(6)}, {signInOutLog.longitude.toFixed(6)}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Right Column */}
        <div className="space-y-2">
          {/* Branch Info - compact */}
          <div className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
              <FiGrid className="w-4 h-4" />
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("signInOutLog.branch", "Branch")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {signInOutLog.parent_branch.branch_name}
                </span>
              </div>
            </div>
          </div>

          {/* Device Info - compact */}
          <div className="group flex items-center gap-2 text-sm hover:bg-purple-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 flex-shrink-0">
              <FiMonitor className="w-4 h-4" />
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("signInOutLog.device", "Device")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {signInOutLog.device
                    ? signInOutLog.device_name
                    : t("signInOutLog.noDevice", "No device")}
                </span>
                {signInOutLog.device_type && (
                  <Tag color="purple" className="ml-1 text-xs">
                    {signInOutLog.device_type}
                  </Tag>
                )}
              </div>
            </div>
          </div>

          {/* Date/Time - compact */}
          <div className="group flex items-center gap-2 text-sm hover:bg-amber-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div className="flex items-center justify-center w-7 h-7 rounded-full bg-amber-100 dark:bg-amber-900 text-amber-600 dark:text-amber-300 flex-shrink-0">
              <FiCalendar className="w-4 h-4" />
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("signInOutLog.dateTime", "Date/Time")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {formatDateTime(signInOutLog.event_time, "DD MMM YYYY HH:mm:ss")}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer Section with modern styling - matching ActivityLogItem */}
      <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center bg-gray-50 dark:bg-gray-800 -mx-4 -mb-4 px-4 py-3 rounded-b-xl">
        <div className="text-xs text-gray-500 dark:text-gray-400 flex flex-wrap gap-2">
          <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md">
            {t("signInOutLog.id", "ID")}: {signInOutLog.uuid}
          </span>
          <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md">
            {t("signInOutLog.submitted", "Submitted")}: {formatDateTime(signInOutLog.event_time, "DD MMM YYYY HH:mm:ss")}
          </span>
        </div>
        <div className="flex gap-2">
          <Dropdown
            menu={exportMenu}
            placement="bottomRight"
          >
            <Button
              icon={
                isDownloading ? (
                  <Spin size="small" />
                ) : (
                  <FiDownload />
                )
              }
              disabled={isDownloading}
              className="flex items-center gap-2 transition-all duration-300"
            >
              {t("common.export", "Export")}
            </Button>
          </Dropdown>
        </div>
      </div>
    </div>
  );
};

export default SignInOutLogItem;
