import {
  Button,
  Checkbox,
  Empty,
  Radio,
  Select,
  Skeleton,
} from "antd";
import branchAdminMainApi from "../../services/mainApi/admin/branch.admin.mainApi";
import { Branch } from "../../services/mainApi/sysadmin/types/branch.mainApi.types";
import { useEffect, useRef, useState } from "react";
import { MdBusiness } from "react-icons/md";

interface GridSelectBranchProps {
  multiple?: boolean;
  value?: string | string[];
  onChange?: (value: Branch | Branch[]) => void;
  maxHeight?: number | string;
  mode?: "grid" | "select";
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  clearable?: boolean; // Add new prop
}

const GridSelectBranch = ({
  multiple = false,
  value,
  onChange,
  maxHeight = 400,
  mode = "grid",
  className = "",
  placeholder = "Select branch",
  disabled = false,
  clearable = true, // Add default value
}: GridSelectBranchProps) => {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);

  const fetchBranch = async () => {
    if (isFetching.current) return;
    isFetching.current = true;
    setLoading(true);
    try {
      const response =
        await branchAdminMainApi.selectBranch({
          show_all_branches: true,
        });
      const { data } = response;
      setBranches(data || []);
    } catch (error) {
      console.error("Failed to fetch branches:", error);
    } finally {
      setLoading(false);
      isFetching.current = false;
    }
  };

  useEffect(() => {
    fetchBranch();
  }, []);

  const handleSelect = (branchId: string) => {
    if (!onChange) return;

    if (multiple) {
      const findBranch = branches.find(
        (branch) => branch.id === branchId
      );
      if (!findBranch) return;

      const isSelected = (value as string[])?.includes(
        branchId
      );
      const values = isSelected
        ? (value as string[]).filter(
            (id) => id !== branchId
          )
        : [...((value as string[]) || []), branchId];

      const selectedBranches = branches.filter((branch) => {
        return values.includes(branch.id);
      });
      onChange(selectedBranches);
    } else {
      const findBranch = branches.find(
        (branch) => branch.id === branchId
      );
      if (!findBranch) return;
      onChange(findBranch);
    }
  };

  const handleCheckAll = () => {
    if (!onChange || !multiple) return;

    const currentValue = (value as string[]) || [];
    const allBranchIds = branches.map(
      (branch) => branch.id
    );

    // If all branches are selected, unselect all. Otherwise, select all
    const isAllSelected = allBranchIds.every((id) =>
      currentValue.includes(id)
    );

    if (isAllSelected) {
      onChange([]);
    } else {
      onChange(branches);
    }
  };

  if (loading) {
    return mode === "select" ? (
      <Select
        loading
        className={className}
      />
    ) : (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-3">
        {[...Array(6)].map((_, index) => (
          <div
            key={index}
            className="bg-white rounded-lg border p-3 dark:bg-gray-800 dark:border-gray-700"
          >
            <Skeleton
              active
              paragraph={{ rows: 1 }}
            />
          </div>
        ))}
      </div>
    );
  }

  if (!branches.length) {
    return mode === "select" ? (
      <Select
        notFoundContent={
          <Empty
            description="No branches found"
            className="dark:text-gray-400"
          />
        }
        className={className}
      />
    ) : (
      <Empty
        description="No branches found"
        className="dark:text-gray-400"
      />
    );
  }

  if (mode === "select") {
    return (
      <Select
        mode={multiple ? "multiple" : undefined}
        value={value}
        onChange={(selectedValue) => {
          if (!selectedValue) {
            // Handle clear case
            onChange?.(multiple ? [] : (undefined as any));
            return;
          }

          if (multiple) {
            const selectedBranches = branches.filter(
              (branch) =>
                (selectedValue as string[]).includes(
                  branch.id
                )
            );
            onChange?.(selectedBranches);
          } else {
            const selectedBranch = branches.find(
              (branch) => branch.id === selectedValue
            );
            if (selectedBranch) onChange?.(selectedBranch);
          }
        }}
        className={`[&_.ant-select-selector]:dark:bg-gray-700 [&_.ant-select-selector]:dark:border-gray-600 [&_.ant-select-selection-item]:dark:text-gray-200 ${className}`}
        placeholder={placeholder}
        disabled={disabled}
        loading={loading}
        optionLabelProp="label"
        allowClear={clearable} // Add allowClear prop
      >
        {branches.map((branch) => (
          <Select.Option
            key={branch.id}
            value={branch.id}
            label={branch.branch_name}
          >
            <div className="flex items-center gap-2">
              <MdBusiness className="flex-shrink-0" />
              <div>
                <div className="font-medium">
                  {branch.branch_name}
                </div>
                <div className="text-xs text-gray-500">
                  {branch.branch_code}
                </div>
              </div>
              {branch.id === branch.parent_id && (
                <span className="ml-auto text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-0.5 rounded-full">
                  Main
                </span>
              )}
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }

  const SelectComponent = multiple ? Checkbox : Radio;

  // Check if all branches are selected
  const isAllSelected =
    multiple &&
    branches.length > 0 &&
    branches.every((branch) =>
      (value as string[])?.includes(branch.id)
    );

  return (
    <div>
      {multiple && (
        <div className="mb-4 flex justify-end">
          <Button
            type="default"
            onClick={handleCheckAll}
            className="flex items-center gap-2"
          >
            <Checkbox
              checked={isAllSelected}
              className="pointer-events-none"
            />
            {isAllSelected ? "Uncheck All" : "Check All"}
          </Button>
        </div>
      )}
      <div
        className="overflow-auto"
        style={{ maxHeight }}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-3 p-0.5">
          {branches.map((branch) => (
            <div
              key={branch.id}
              className="bg-white rounded-lg border hover:shadow-md transition-shadow duration-200 cursor-pointer group dark:bg-gray-800 dark:border-gray-700"
              onClick={() => handleSelect(branch.id)}
            >
              <div className="flex items-center p-3">
                <SelectComponent
                  checked={
                    multiple
                      ? (
                          (value as string[]) || []
                        ).includes(branch.id)
                      : value === branch.id
                  }
                  onClick={(e) => e.stopPropagation()}
                  onChange={() => handleSelect(branch.id)}
                  className="flex-shrink-0"
                />
                <div className="ml-3 flex-grow min-w-0">
                  <div className="text-sm font-medium text-gray-800 dark:text-gray-100 truncate">
                    {branch.branch_name}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <MdBusiness className="flex-shrink-0" />
                    <span className="truncate">
                      {branch.branch_code}
                    </span>
                  </div>
                </div>
                {branch.id === branch.parent_id && (
                  <span className="flex-shrink-0 ml-2 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-0.5 rounded-full">
                    Main
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default GridSelectBranch;
