interface License {
  id: string;
  license_name: string;
  max_subbranch: number;
  max_user: number;
  max_label: number;
  max_scheduler: number;
  max_alert: number;
  max_device: number;
  max_site: number;
  max_checkpoint: number;
  max_geofence: number;
  max_form: number;
  max_task: number;
  max_activity: number;
  max_beacon: number;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

interface Timezone {
  id: string;
  timezone_name: string;
  gmt_offset: string;
  country_code: string;
  active: boolean;
}

export interface BranchOwnerUser {
  id: string;
  parent_branch_id: string;
  role_id: string;
  name: string;
  email: string;
  phone: string;
  system_access: boolean;
  web_access: boolean;
  mobile_access: boolean;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Branch {
  id: string;
  parent_id: string | null;
  timezone_id: string;
  license_id: string;
  reseller_id: string | null;
  branch_code: string;
  branch_name: string;
  branch_description: string | null;
  branch_logo: string;
  branch_colour: string;
  gps_tracking_enabled: boolean;
  gps_interval: number;
  active: boolean | null;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
  branch_owner_user_id: string;
  license: License;
  timezone: Timezone;
  parent: Branch | null;
  branch_owner_user?: BranchOwnerUser;
}

export interface BranchListParams {
  search?: string;
  active?: boolean;
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  orderBy?: string;
  orderDirection?: "asc" | "desc";
}

export interface BranchListResponseMeta {
  timestamp: string;
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface BranchListResponse {
  data: Branch[];
  meta: BranchListResponseMeta;
}

export interface CreateBranchUserRequest {
  name: string;
  email: string;
  password: string;
  confirm_password: string;
  phone: string;
}

export interface CreateBranchWithUserRequest {
  name: string;
  description: string;
  timezone: number | string;
  license: number | string;
  user: CreateBranchUserRequest;
}

export interface CreateBranchWithUserResponse {
  data: Branch;
  meta: {
    timestamp: string;
  };
}

export interface UpdateBranchRequest {
  name: string;
  description: string;
  timezone: number | string;
  license: number | string;
  active: boolean;

  admin_name: string;
  admin_email: string;
  admin_phone: string;
  admin_password: string;
  admin_confirm_password: string;
}

export interface UpdateBranchResponse {
  data: Branch;
  meta: {
    timestamp: string;
  };
}
