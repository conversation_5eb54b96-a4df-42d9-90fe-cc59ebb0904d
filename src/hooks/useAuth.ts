import {
  useLocation,
  useNavigate,
  useParams,
} from "react-router-dom";
import {
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { getTokens } from "../utils/storage";
import myProfileMainApi from "../services/mainApi/myProfile.mainApi";
import {
  useAppDispatch,
  useAppSelector,
} from "../store/hooks";
import authSlice from "../store/slices/auth.slice.ts";

export function useAuth() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const authReducer = useAppSelector((state) => state.auth);
  const [loading, setLoading] = useState(true);
  const isFetching = useRef(false);
  const tokens = getTokens();
  const { branchCode } = useParams();
  const currentLocation = useLocation();

  const accessToken = tokens?.accessToken;

  const fetchProfile = useCallback(async () => {
    if (isFetching.current) {
      return;
    }

    if (!accessToken) {
      setLoading(false);
      if (currentLocation.pathname !== "/login") {
        navigate("/login");
      }
      return;
    }

    try {
      isFetching.current = true;
      const response = await myProfileMainApi.getProfile({
        branch_code: branchCode,
      });
      dispatch(authSlice.actions.setUser(response.data));
    } catch {
      dispatch(authSlice.actions.setUser(null));
      if (currentLocation.pathname !== "/login") {
        navigate("/login");
      }
    } finally {
      isFetching.current = false;
      setTimeout(() => {
        setLoading(false);
      }, 300);
    }
  }, [
    accessToken,
    branchCode,
    currentLocation.pathname,
    dispatch,
    navigate,
  ]);

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  return {
    userData: authReducer.user,
    loading: loading,
    isAuthenticated: Boolean(
      accessToken && authReducer.user
    ),
    hasSystemAccess: Boolean(
      authReducer.user?.access.system_access
    ),
    hasWebAccess: Boolean(
      authReducer.user?.access.web_access
    ),
    hasMobileAccess: Boolean(
      authReducer.user?.access.mobile_access
    ),
  };
}
