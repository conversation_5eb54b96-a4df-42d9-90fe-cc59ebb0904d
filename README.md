# Uniguard Frontend

## Overview
Uniguard is a comprehensive frontend application for monitoring and managing beacons, geofences, zones, and checkpoints. It provides an intuitive and powerful interface for administrators to manage these assets across multiple branches.

> **Note:** While this project is referred to as "Uniguard Frontend" in this documentation, the package.json uses "react-vite" as the package name. This is for historical reasons and does not affect the functionality of the application.

## Features

### Multi-Role Support
- System Administrator (SysAdmin) for global system management
- Web Administrator (WebAdmin) for branch-specific management
- Role-based dashboard and access control

### Technology Stack
- React 19.0.0 for UI components
- Redux Toolkit 2.5.1 for centralized state management
- TypeScript 5.7.2 for type safety
- Ant Design 5.14.1 and Tailwind CSS 3.4.1 for UI
- React Router 7.1.5 for navigation
- Firebase for hosting and authentication
- Axios for HTTP requests
- i18next for internationalization
- Zod for form validation
- Leaflet for interactive maps
- ExcelJS and React-PDF for document generation
- Dayjs for date manipulation

### Authentication and Security
- JWT-based authentication
- Role-based access control
- Session management
- Secure cookie handling

### User Interface
- Dark/Light theme support
- Responsive design for all devices
- Multi-language support (English, Indonesian, Chinese)
- Interactive maps with Leaflet
- Form validation with Zod
- Customized Ant Design theme
- Tailwind utility classes for consistent spacing and layout

### Development Features
- Hot Module Replacement (HMR)
- TypeScript strict mode with comprehensive type checking
- ESLint configuration with modern JavaScript rules
- Environment variable management
- Build optimization with Vite
- Code formatting with Prettier
- Modular code organization
- Component-driven development

## Prerequisites
- Node.js 18 or higher
- NPM 9 or higher

## Installation

Clone the repository:
```bash
git clone https://github.com/yourusername/ugz-vite-2.git
cd ugz-vite-2
```

Install dependencies:
```bash
npm install
```

Configure environment variables:
```bash
cp .env.example .env
```
Then edit `.env` with your specific environment configuration. The main environment variable is:
- `VITE_API_URL`: The base URL for API requests (default: http://localhost:3000/)

## Building and Development

Start the development server:
```bash
npm run dev
```

Build for production:
```bash
npm run build
```

Preview the production build:
```bash
npm run preview
```

Lint the code:
```bash
npm run lint
```

Format the code:
```bash
npm run format
```

Check if code is formatted according to Prettier rules:
```bash
npm run format:check
```

## Deployment

This project is configured for deployment on Firebase Hosting:

```bash
npm run build
firebase deploy
```

## Project Structure

```
ugz-vite-2/
├── public/                  # Static assets
├── src/
│   ├── assets/              # Images, fonts, etc.
│   ├── components/          # Reusable components
│   │   ├── Common/          # Common components
│   │   ├── Layout/          # Layout components
│   │   └── [Component]/     # Component folder structure
│   │       ├── index.tsx    # Main component file
│   │       ├── styles.ts    # Styled components/styles
│   │       ├── types.ts     # Component types
│   │       ├── utils.ts     # Component-specific utilities
│   │       ├── constants.ts # Component-specific constants
│   │       └── hooks.ts     # Component-specific hooks
│   ├── config/              # Configuration files
│   ├── hooks/               # Custom React hooks
│   ├── locales/             # i18n translations
│   ├── pages/               # Page components
│   │   ├── Auth/            # Authentication pages
│   │   ├── SysAdmin/        # System Admin pages
│   │   └── WebAdmin/        # Web Admin pages
│   ├── services/            # API services
│   │   └── mainApi/         # Main API services
│   │       ├── admin/       # Admin API services
│   │       ├── sysadmin/    # SysAdmin API services
│   │       └── types/       # API types
│   ├── store/               # Redux store setup
│   │   ├── slices/          # Redux slices
│   │   │   ├── admin/       # Admin slices
│   │   │   └── sysadmin/    # SysAdmin slices
│   │   └── index.ts         # Store configuration
│   ├── styles/              # Global styles
│   ├── types/               # TypeScript types
│   ├── utils/               # Utility functions
│   ├── App.tsx              # Main App component
│   └── index.tsx            # Entry point
├── .prettierrc.json         # Prettier configuration
├── eslint.config.js         # ESLint configuration
├── tsconfig.json            # TypeScript configuration
├── tsconfig.app.json        # App TypeScript configuration
├── tsconfig.node.json       # Node TypeScript configuration
└── vite.config.ts           # Vite bundler config
```

## Main Modules

### SysAdmin
- Branch Management
- License Management
- Label Management
- System Configuration
- User Management
- Role Management

### WebAdmin
- Beacon Management
- Geofence Management
- Zone Management
- Checkpoint Management
- Site Management
- Device Management
- Task Management
- Activity Management
- Form Management
- Alert Management
- Scheduler Management
- Log Management (Activity, Alarm, Task, Checkpoint, Form, Geofence, Sign In/Out, Average Rotation)

## Code Standards and Patterns

### File Naming Conventions
- Components: PascalCase (e.g., `Button.tsx`, `UserCard.tsx`)
- Pages: PascalCase (e.g., `Login.tsx`, `Dashboard.tsx`)
- Hooks: camelCase with 'use' prefix (e.g., `useAuth.ts`, `useTheme.ts`)
- Utils: camelCase (e.g., `formatDate.ts`, `validateInput.ts`)
- Types: PascalCase with '.types.ts' suffix (e.g., `User.types.ts`)
- Constants: SCREAMING_SNAKE_CASE (e.g., `API_ENDPOINTS.ts`)
- Services: camelCase with '.services.ts' suffix (e.g., `auth.services.ts`)
- Redux Slices: camelCase with '.slice.ts' suffix (e.g., `auth.slice.ts`)

### Component Structure
Components should follow this structure:
```
ComponentName/
├── index.tsx      # Main component file
├── styles.ts      # Styled components/styles
├── types.ts       # Component types
├── utils.ts       # Component-specific utilities
├── constants.ts   # Component-specific constants
└── hooks.ts       # Component-specific hooks
```

### State Management

#### Redux Store Structure
The Redux store is organized by modules and roles:

```
store/
├── slices/
│   ├── auth.slice.ts                    # Authentication slice
│   ├── admin/                           # WebAdmin slices
│   │   ├── branch.admin.slice.ts
│   │   ├── user.admin.slice.ts
│   │   └── ...
│   └── sysadmin/                        # SysAdmin slices
│       ├── branchList.sysadmin.slice.ts
│       └── ...
└── index.ts                             # Store configuration
```

#### Slice Pattern
All slices follow this pattern:
```typescript
interface SliceState {}
const initialState: SliceState = {};
export const slice = createSlice({
  name: 'sliceName',
  initialState,
  reducers: {},
});
```

### Page Creation Guidelines

#### WebAdmin Pages
1. Create page component in `src/pages/WebAdmin/<ModuleName>/<PageName>.tsx`
2. Add route in `src/config/routes.tsx` under admin section
3. Create slice in `src/store/slices/webAdmin/<moduleName>.webAdmin.slice.ts`
4. Create API service in `src/services/mainApi/webAdmin/<moduleName>.webAdmin.mainApi.ts`
5. Add types in `src/services/mainApi/webAdmin/types/<moduleName>.webAdmin.mainApi.types.ts`
6. Add translations in `src/locales/<lang>/<moduleName>.json`

#### SysAdmin Pages
1. Create page component in `src/pages/SysAdmin/<ModuleName>/<PageName>.tsx`
2. Add route in `src/config/routes.tsx` under sysAdmin section
3. Create slice in `src/store/slices/sysAdmin/<moduleName>.sysAdmin.slice.ts`
4. Create API service in `src/services/mainApi/sysAdmin/<moduleName>.sysAdmin.mainApi.ts`
5. Add types in `src/services/mainApi/sysAdmin/types/<moduleName>.sysAdmin.mainApi.types.ts`
6. Add translations in `src/locales/<lang>/<moduleName>.json`

### Documentation Requirements
Components should be documented with:
- Description
- Props
- Usage examples

```typescript
/**
 * @description Component description
 * @param {PropType} props - Props description
 * @example
 *   <Component prop="value" />
 */
```

### Styling Guidelines

#### Tailwind CSS
- Use custom classes for consistent layouts: `container`, `wrapper`, `section`
- Use spacing utilities: `space-y-4`, `space-x-4`
- Use responsive prefixes: `sm:`, `md:`, `lg:`, `xl:`
- Prefer Tailwind utilities over custom CSS when possible

#### Ant Design
- Theme customization with these primary colors:
  - Primary: `#1890ff`
  - Success: `#52c41a`
  - Warning: `#faad14`
  - Error: `#f5222d`
- Use Ant Design components for complex UI elements
- Customize components using the theme provider

### TypeScript Configuration

#### Compiler Options
- Target: ES2020/ES2022
- Strict mode enabled
- No unused locals/parameters
- No unchecked side effect imports
- No fallthrough cases in switch

### Performance Guidelines
- Use React.memo for expensive components
- Implement virtualization for long lists
- Lazy load routes and heavy components
- Optimize images and assets
- Use proper key props in lists
- Minimize component re-renders
- Use useMemo and useCallback for optimizing performance
- Prefer server-side pagination for large datasets

### Testing Requirements

#### Components
- Render test
- User interaction
- Props validation

#### Hooks
- Initialization
- State changes
- Cleanup

### Git Commit Standards
Commit messages should follow the pattern: `<type>(<scope>): <description>`

Types:
- feat: New feature
- fix: Bug fix
- docs: Documentation
- style: Formatting
- refactor: Code restructure
- test: Tests
- chore: Maintenance

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes following the commit standards
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request
