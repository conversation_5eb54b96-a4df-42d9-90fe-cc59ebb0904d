import { BaseResponse } from "../../types/base.mainApi.types";

// New interfaces for the updated response format
export interface AdminBranchDetailsLogCheckpoint {
  id: string;
  checkpoint_name: string;
  latest_voltage: string | null;
  latest_check_time: string | null;
  created_at: string;
  updated_at: string;
}

export interface AdminBranchDetailsLogZoneDetail {
  zone_id: string;
  zone_name: string;
  checkpoint_count: number;
  checkpoints: AdminBranchDetailsLogCheckpoint[];
}

export interface AdminBranchDetailsLogBranchDetail {
  branch_id: string;
  branch_name: string;
  total_checkpoints: number;
  total_zones: number;
  zones: AdminBranchDetailsLogZoneDetail[];
}

export interface AdminBranchDetailsLogListParams {
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  page?: number;
  limit?: number;
  branch_id?: string | number;
  order_by?: string;
  order_direction?: "ASC" | "DESC";
}

// Updated response type to use the new format
export type AdminBranchDetailsLogListResponse = BaseResponse<
  AdminBranchDetailsLogBranchDetail[]
>;
