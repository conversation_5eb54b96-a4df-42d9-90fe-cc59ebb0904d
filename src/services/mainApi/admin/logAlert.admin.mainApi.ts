import baseMainApi from "../base.mainApi";
import {
  AdminLogAlertListParams,
  AdminLogAlertListResponse,
} from "./types/logAlert.admin.mainApi.types";

export const logAlertAdminApi = {
  getLogAlerts: async (
    branchCode: string,
    params?: AdminLogAlertListParams
  ) => {
    const response =
      await baseMainApi.get<AdminLogAlertListResponse>(
        `/web-api/admin/${branchCode}/log-alert`,
        { params }
      );
    return response.data;
  },

  deleteFromDashboard: async (branchCode: string, id: string) => {
    const response =
      await baseMainApi.delete<AdminLogAlertListResponse>(
        `/web-api/admin/${branchCode}/log-alert/${id}`
      );
    return response.data;
  },
};
