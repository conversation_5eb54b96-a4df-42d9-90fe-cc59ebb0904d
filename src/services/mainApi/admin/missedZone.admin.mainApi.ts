import baseMainApi from "../base.mainApi";
import {
  AdminMissedZoneLogListResponse,
  AdminMissedZoneLogListParams,
} from "./types/missedZone.admin.mainApi.types";

export const missedZoneAdminApi = {
  getMissedZoneLogs: async (
    branchCode: string,
    params?: AdminMissedZoneLogListParams
  ) => {
    const response =
      await baseMainApi.get<AdminMissedZoneLogListResponse>(
        `/web-api/admin/${branchCode}/missed-zone`,
        { params }
      );
    return response.data;
  },

  exportMissedZoneLogs: async (
    branchCode: string,
    params: AdminMissedZoneLogListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/missed-zone/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
};
