import { BaseResponse } from "../../types/base.mainApi.types";
import { AdminBranch } from "./branch.admin.mainApi.types";
import { AdminRole } from "./roles.admin.mainApi.types";
import { AdminZone } from "./zone.admin.mainApi.types";
import { AdminGeofence } from "./geofence.admin.mainApi.types";

// Timezone interface
interface AdminTimezone {
  id: string;
  timezone_name: string;
  gmt_offset: string;
  country_code: string;
  active: boolean;
}

// User interface specific to geofence log
interface GeofenceLogUser {
  id: string;
  parent_branch_id: string;
  role_id: string;
  name: string;
  email: string;
  phone: string;
  system_access: boolean;
  web_access: boolean;
  mobile_access: boolean;
  active: boolean;
  created_at: string;
  updated_at: string;
}

// Device interface for geofence log (can be null)
interface GeofenceLogDevice {
  id: string;
  device_name: string;
  [key: string]:
    | string
    | number
    | boolean
    | null
    | undefined;
}

// Original detailed geofence log entry
export interface AdminGeofenceLogDetailEntry {
  id: string;
  uuid: string;
  parent_branch_id: string;
  branch_id: string;
  branch_name: string;
  role_id: string;
  role_name: string;
  user_id: string;
  user_name: string;
  device_id: string | null;
  device_name: string | null;
  timezone_id: string;
  timezone_name: string;
  latitude: number;
  longitude: number;
  checkpoint_id: string | null;
  checkpoint_name: string | null;
  zone_id: string;
  zone_name: string;
  gps_tracking_enabled: boolean | null;
  gps_interval: number | null;
  geofence_id: string;
  geofence_name: string;
  active_time_start: string | null;
  active_time_end: string | null;
  minimum_stay_duration: string | null;
  maximum_stay_duration: string | null;
  geofence_data: {
    type: string;
    coordinates: number[];
  };
  original_submitted_time: string;
  event_time: string;

  // Relations
  branch: AdminBranch;
  parent_branch: AdminBranch;
  role: AdminRole;
  user: GeofenceLogUser;
  device: GeofenceLogDevice | null;
  timezone: AdminTimezone;
  checkpoint: null;
  zone: AdminZone;
  geofence: AdminGeofence;
}

// Simplified geofence log entry for list view as per the issue description
export interface AdminGeofenceLogEntry {
  geofence_id: string;
  geofence_name: string;
  device_id: string;
  device_name: string;
  user_id: string;
  user_name: string;
  zone_id: string;
  zone_name: string;
  maximum_stay_duration: string;
  minimum_stay_duration: string;
  entry: string;
  exit: string;
  duration: number;
  violation: boolean;
}

export interface AdminGeofenceLogListParams {
  page?: number;
  limit?: number;
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  branch_id?: string | number;
  site_id?: string | number;
  site_labels?: string | number | (string | number)[];
  role_id?: string | number;
  user_id?: string | number;
  user_labels?: string | number | (string | number)[];
  device_id?: string | number;
  device_labels?: string | number | (string | number)[];
  order_by?: string;
  order_direction?: "ASC" | "DESC";
}

export type AdminGeofenceLogListResponse = BaseResponse<
  AdminGeofenceLogEntry[]
>;
export type AdminGeofenceLogDetailResponse =
  BaseResponse<AdminGeofenceLogDetailEntry>;
