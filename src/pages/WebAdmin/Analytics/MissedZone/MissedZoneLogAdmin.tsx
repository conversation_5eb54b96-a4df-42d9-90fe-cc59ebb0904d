import { useTranslation } from "react-i18next";
import { MdMissed<PERSON><PERSON>oCall, MdFilterList } from "react-icons/md";
import { useCallback, useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { <PERSON><PERSON>, Drawer } from "antd";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import MissedZoneLogList from "./Components/MissedZoneLogList";
import MissedZoneLogExportButton from "./Components/MissedZoneLogExportButton";
import { useAppDispatch, useAppSelector } from "../../../../store/hooks";
import missedZoneAdminSlice, { fetchMissedZoneLogs } from "../../../../store/slices/admin/missedZone.admin.slice";
import ActiveFilters, {
  createEndDateFilter,
  createEndTimeFilter,
  createLabelFilter,
  createSimpleFilter,
  createStartDateFilter,
  createStartTimeFilter,
  FilterGroup,
} from "../../../../components/Admin/Common/ActiveFilters";
import AnalyticDrawerForm, { AnalyticsFilterState } from "../../Analytics/Components/AnalyticDrawerForm";
import { EReportType } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { AdminZone } from "../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { AdminLabel } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminBranch } from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";

// Interface for filter state
interface FilterState {
  site: AdminZone | null;
  siteLabels: AdminLabel[];
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  selectedBranch: AdminBranch | null;
}

const MissedZoneLogAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const isFetching = useRef(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  // Get branch code from URL params
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  // Redux state
  const missedZoneState = useAppSelector(
    (state) => state.missedZoneAdmin
  );
  const loading = missedZoneState.loading;
  const pagination = missedZoneState.pagination;
  const missedZoneLogs = missedZoneState.missedZoneLogs;
  const filterState = missedZoneState.filter;
  const { actions } = missedZoneAdminSlice;

  const fetch = useCallback(async () => {
    if (isFetching.current) return;
    isFetching.current = true;
    await dispatch(fetchMissedZoneLogs({ branchCode: branchCode || "" }));
    isFetching.current = false;
  }, [branchCode, dispatch]);

  // Load data on mount and when filters change
  useEffect(() => {
    if (branchCode) {
      fetch().then();
    }
  }, [
    fetch,
    branchCode,
    filterState.page,
    filterState.limit,
    filterState.startDate,
    filterState.endDate,
    filterState.startTime,
    filterState.endTime,
    filterState.branchId,
    filterState.siteId,
    filterState.siteLabels,
    filterState.orderBy,
    filterState.orderDirection,
  ]);

  // Convert applied filter state to format expected by drawer
  const getAppliedFilters = (): FilterState => {
    return {
      site: missedZoneState.selectedSite,
      siteLabels: missedZoneState.selectedSiteLabels || [],
      startDate: filterState.startDate,
      endDate: filterState.endDate,
      startTime: filterState.startTime,
      endTime: filterState.endTime,
      selectedBranch: missedZoneState.selectedBranch,
    };
  };

  // Apply filters from the drawer
  const handleApplyFilters = (
    newFilters: AnalyticsFilterState
  ) => {
    // Update redux state
    dispatch(
      actions.setSelectedSite(newFilters.site || null)
    );
    dispatch(
      actions.setSelectedSiteLabels(
        newFilters.siteLabels || []
      )
    );
    dispatch(
      actions.setSelectedBranch(
        newFilters.selectedBranch || null
      )
    );
    dispatch(
      actions.setStartDate(newFilters.startDate || null)
    );
    dispatch(
      actions.setEndDate(newFilters.endDate || null)
    );
    dispatch(
      actions.setStartTime(newFilters.startTime || null)
    );
    dispatch(
      actions.setEndTime(newFilters.endTime || null)
    );

    // Reset page to 1
    dispatch(actions.setPage(1));

    setDrawerVisible(false);
  };

  // Handle page change
  const handlePageChange = (page: number, pageSize?: number) => {
    dispatch(actions.setPage(page));
    if (pageSize) {
      dispatch(actions.setLimit(pageSize));
    }
  };

  // Check if there are any active filters
  const hasActiveFilters = (): boolean => {
    // Check if any filter is active
    if (
      filterState.startDate ||
      filterState.endDate ||
      filterState.startTime ||
      filterState.endTime ||
      filterState.branchId ||
      filterState.siteId ||
      filterState.siteLabels.length > 0
    ) {
      return true;
    }

    return false;
  };

  // Get filter groups configuration for ActiveFilters component
  const getFilterGroups = (): FilterGroup[] => {
    const appliedFilters = getAppliedFilters();

    return [
      {
        key: "datetime",
        filters: [
          createStartDateFilter(appliedFilters.startDate),
          createEndDateFilter(appliedFilters.endDate),
          createStartTimeFilter(appliedFilters.startTime),
          createEndTimeFilter(appliedFilters.endTime),
        ],
      },
      {
        key: "site",
        filters: [
          createSimpleFilter(
            "site",
            "site",
            appliedFilters.site,
            "zone_name"
          ),
          createLabelFilter(
            "siteLabels",
            "siteLabels",
            appliedFilters.siteLabels
          ),
        ],
      },
      {
        key: "other",
        filters: [
          createSimpleFilter(
            "selectedBranch",
            "selectedBranch",
            appliedFilters.selectedBranch,
            "branch_name"
          ),
        ],
      },
    ];
  };

  const handleClearAll = () => {
    dispatch(actions.clearFilters());
  };

  const handleRemoveFilter = (
    filterKey: keyof FilterState
  ) => {
    switch (filterKey) {
      case "site":
        dispatch(actions.setSelectedSite(null));
        break;
      case "siteLabels":
        dispatch(actions.setSelectedSiteLabels([]));
        break;
      case "selectedBranch":
        dispatch(actions.setSelectedBranch(null));
        break;
      case "startDate":
        dispatch(actions.setStartDate(null));
        break;
      case "endDate":
        dispatch(actions.setEndDate(null));
        break;
      case "startTime":
        dispatch(actions.setStartTime(null));
        break;
      case "endTime":
        dispatch(actions.setEndTime(null));
        break;
    }
  };

  // Handle clicking on an active filter to edit it
  const handleEditFilter = () => {
    // We only need to open the drawer
    setDrawerVisible(true);
  };

  // Convert FilterState to AnalyticsFilterState for the drawer form
  const getInitialDrawerValues =
    (): Partial<AnalyticsFilterState> => {
      const appliedFilters = getAppliedFilters();

      return {
        startDate: appliedFilters.startDate || "",
        endDate: appliedFilters.endDate || "",
        startTime: appliedFilters.startTime || "",
        endTime: appliedFilters.endTime || "",
        selectedBranch:
          appliedFilters.selectedBranch || null,
        site: appliedFilters.site,
        siteLabels: appliedFilters.siteLabels,
      };
    };

  return (
    <WebAdminLayout activePage="missed-zone-log">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdMissedVideoCall />}
            title={t(
              "missedZoneLog.title",
              "Missed Zone Log"
            )}
            description={t(
              "missedZoneLog.description",
              "View and manage missed zone logs"
            )}
            actions={[
              // Desktop view - show all buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <MissedZoneLogExportButton
                  filters={getAppliedFilters()}
                  showPdf={true}
                  showSpreadsheet={true}
                  className="mr-2"
                />
                <Button
                  key="filter"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button and export button
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <MissedZoneLogExportButton
                  filters={getAppliedFilters()}
                  showPdf={true}
                  showSpreadsheet={true}
                  className="mr-2"
                  buttonText={t(
                    "common.download",
                    "Download"
                  )}
                />
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList />}
                  size="large"
                  type="primary"
                  className="bg-indigo-600 hover:bg-indigo-700 border-0 shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters using the reusable component */}
          <ActiveFilters<FilterState>
            filterGroups={getFilterGroups()}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAll}
            onShowFilters={() => {
              setDrawerVisible(true);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Missed Zone Log List */}
          <MissedZoneLogList
            missedZoneLogs={missedZoneLogs}
            loading={loading}
            onPageChange={handlePageChange}
            total={pagination.total || 0}
            page={pagination.page || 1}
            limit={pagination.limit || 10}
          />
        </div>

        {/* Filter Drawer with modern styling */}
        <Drawer
          title={
            <div className="text-lg font-semibold text-gray-800 dark:text-white">
              {t(
                "missedZoneLog.filters.title",
                "Filter Missed Zone Log"
              )}
            </div>
          }
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          className="dark:bg-gray-800"
        >
          <AnalyticDrawerForm
            reportType={EReportType.MISSED_ZONE}
            onApply={handleApplyFilters}
            onClose={() => setDrawerVisible(false)}
            initialValues={getInitialDrawerValues()}
          />
        </Drawer>
      </div>
    </WebAdminLayout>
  );
};

export default MissedZoneLogAdmin;
