import { FC, useState } from "react";
import { AdminForm } from "../../../../services/mainApi/admin/types/form.admin.mainApi.types";
import { <PERSON><PERSON>, Tooltip, Tag } from "antd";
import { useTranslation } from "react-i18next";
import { MdEdit } from "react-icons/md";
import { BsListCheck } from "react-icons/bs";
import {
  FiShield,
  FiChevronDown,
  FiChevronUp,
  FiCheckCircle
} from "react-icons/fi";
import dayjs from "dayjs";
import { useAppDispatch } from "../../../../store/hooks";
import addFormAdminSlice from "../../../../store/slices/admin/addForm.admin.slice";

interface FormListItemProps {
  form: AdminForm;
}

const FormListItem: FC<FormListItemProps> = ({ form }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [isFieldsExpanded, setIsFieldsExpanded] = useState(false);

  const formatDate = (dateString: string) => {
    try {
      return dayjs(dateString).format("DD MMM YYYY, HH:mm");
    } catch {
      return dateString;
    }
  };

  const onEdit = () => {
    dispatch(addFormAdminSlice.actions.openUpdate(form));
  };

  // Function to truncate text and add ellipsis
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return "-";
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  // Function to render the fields list
  const renderFieldsList = () => {
    if (form.fields.length === 0) {
      return (
        <div className="text-sm text-gray-500 dark:text-gray-400 italic">
          {t("forms.noFields", "No fields defined")}
        </div>
      );
    }

    return (
      <div className="space-y-2 mt-2">
        {form.fields.map((field) => (
          <div
            key={field.id}
            className="p-2 bg-gray-50 dark:bg-gray-700/30 rounded border border-gray-100 dark:border-gray-700"
          >
            <div className="flex justify-between items-start">
              <div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {field.form_field_name}
                </span>
                {field.form_field_require && (
                  <Tag color="red" className="ml-2 text-xs">
                    {t("forms.required", "Required")}
                  </Tag>
                )}
              </div>
              <Tag color="blue" className="text-xs">
                {field.field_type.field_type_name}
              </Tag>
            </div>
            {field.form_field_description && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {truncateText(field.form_field_description, 100)}
              </p>
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Header section */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <BsListCheck className="text-blue-500" />
            {form.form_name}
            {form.active === false && (
              <Tag
                color="red"
                className="ml-2"
              >
                {t("common.inactive", "Inactive")}
              </Tag>
            )}
          </h3>
          <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            <p>
              {form.form_description || "-"}
            </p>
          </div>
        </div>
        <div>
          <Tooltip title={t("common.edit")}>
            <Button
              type="text"
              icon={<MdEdit className="text-lg" />}
              onClick={onEdit}
              className="text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400"
            />
          </Tooltip>
        </div>
      </div>

      {/* Content section */}
      <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Left column */}
        <div className="space-y-4">
          {/* Checkpoint section */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
              <FiCheckCircle className="text-green-500" />
              {t("forms.checkpoint")}
            </h4>
            <div className="text-sm text-gray-700 dark:text-gray-300">
              {form.checkpoint?.checkpoint_name || t("forms.noCheckpoint")}
            </div>
          </div>
        </div>

        {/* Right column */}
        <div className="space-y-4">
          {/* Role section */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
              <FiShield className="text-blue-500" />
              {t("forms.role")}
            </h4>
            <div className="text-sm text-gray-700 dark:text-gray-300">
              {form.role?.role_name || t("forms.noRole")}
            </div>
          </div>
        </div>
      </div>

      {/* Fields section with collapsible functionality */}
      <div className="px-4 pb-4">
        <div className="flex justify-between items-center mb-2">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-1">
            <BsListCheck className="text-blue-500" />
            {t("forms.fields")} ({form.fields.length})
          </h4>
          <button
            onClick={() => setIsFieldsExpanded(!isFieldsExpanded)}
            className="text-blue-500 hover:text-blue-600 flex items-center text-xs font-medium"
          >
            {isFieldsExpanded
              ? <><FiChevronUp className="mr-1" /> {t("common.showLess", "Show Less")}</>
              : <><FiChevronDown className="mr-1" /> {t("common.showMore", "Show More")}</>
            }
          </button>
        </div>

        {isFieldsExpanded && renderFieldsList()}
      </div>

      {/* Footer section */}
      <div className="px-4 py-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400">
        <div className="flex flex-wrap gap-x-4">
          <span>
            {t("common.createdAt")}: {formatDate(form.created_at)}
          </span>
          <span>
            {t("common.updatedAt")}: {formatDate(form.updated_at)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default FormListItem;
