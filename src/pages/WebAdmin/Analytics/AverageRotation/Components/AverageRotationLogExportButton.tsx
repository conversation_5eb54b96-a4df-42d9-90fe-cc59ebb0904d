import React, { useState } from "react";
import { But<PERSON>, Dropdown, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { FiDownload, FiGrid, FiFileText } from "react-icons/fi";
import { useParams } from "react-router-dom";
import { AdminBranch } from "../../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { averageRotationLogAdminMainApi } from "../../../../../services/mainApi/admin/averageRotationLog.admin.mainApi";
import type { MenuProps } from "antd";

interface AverageRotationLogExportButtonProps {
  filters: {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    selectedBranch: AdminBranch | null;
    zoneId: string | number | null;
    branchId: string | number | null;
  };
  className?: string;
  buttonText?: string;
  showPdf?: boolean;
  showSpreadsheet?: boolean;
}

type ExportFormat = "pdf" | "spreadsheet";

const AverageRotationLogExportButton: React.FC<AverageRotationLogExportButtonProps> = ({
  filters,
  className,
  buttonText,
  showPdf = true,
  showSpreadsheet = true,
}) => {
  const { t } = useTranslation();
  const [isDownloading, setIsDownloading] = useState(false);
  const { branchCode } = useParams<{ branchCode: string }>();

  const handleDownload = async (format: ExportFormat) => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const blob = await averageRotationLogAdminMainApi.exportAverageRotationLogs(
        branchCode,
        {
          start_date: filters.startDate || undefined,
          end_date: filters.endDate || undefined,
          start_time: filters.startTime || undefined,
          end_time: filters.endTime || undefined,
          branch_id: filters.branchId?.toString() || undefined,
          zone_id: filters.zoneId?.toString() || undefined,
        },
        format,
        "buffer"
      );

      if (format === "pdf") {
        // Create URL and open in new tab
        const url = URL.createObjectURL(blob);
        window.open(url, "_blank");

        // Delay URL revocation to ensure the new tab has time to load
        setTimeout(() => {
          URL.revokeObjectURL(url);
        }, 5000);
      } else {
        // Create URL and trigger download
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `average_rotation_logs_${new Date().toISOString().slice(0, 10)}.xlsx`;
        link.click();

        // Clean up
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error(`Error downloading ${format}:`, error);
    } finally {
      setIsDownloading(false);
    }
  };

  // If only one format is shown, render a simple button
  if (
    (showPdf && !showSpreadsheet) ||
    (!showPdf && showSpreadsheet)
  ) {
    const format = showPdf ? "pdf" : "spreadsheet";
    const tooltipText = showPdf
      ? t("averageRotationLog.downloadPdf", "Download as PDF")
      : t(
          "averageRotationLog.downloadSpreadsheet",
          "Download as Spreadsheet"
        );

    return (
      <Tooltip title={tooltipText}>
        <Button
          icon={<FiDownload />}
          loading={isDownloading}
          onClick={() => handleDownload(format)}
          size="large"
          className={className}
        >
          {buttonText ||
            (showPdf
              ? t("averageRotationLog.pdf", "PDF")
              : t(
                  "averageRotationLog.spreadsheet",
                  "Spreadsheet"
                ))}
        </Button>
      </Tooltip>
    );
  }

  // Otherwise, render a dropdown with both options
  const items: MenuProps["items"] = [
    {
      key: "pdf",
      label: t("averageRotationLog.exportPdf", "Export as PDF"),
      icon: <FiFileText className="text-lg" />,
      onClick: () => handleDownload("pdf"),
      disabled: !showPdf,
    },
    {
      key: "spreadsheet",
      label: t(
        "averageRotationLog.exportSpreadsheet",
        "Export as Spreadsheet"
      ),
      icon: <FiGrid className="text-lg" />,
      onClick: () => handleDownload("spreadsheet"),
      disabled: !showSpreadsheet,
    },
  ];

  return (
    <Dropdown
      menu={{ items }}
      disabled={isDownloading}
      placement="bottomRight"
    >
      <Button
        icon={<FiDownload />}
        loading={isDownloading}
        size="large"
        className={className}
      >
        {buttonText || t("averageRotationLog.export", "Export")}
      </Button>
    </Dropdown>
  );
};

export default AverageRotationLogExportButton;
