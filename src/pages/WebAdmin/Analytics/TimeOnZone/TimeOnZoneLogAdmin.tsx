import {useTranslation} from "react-i18next";
import {<PERSON>d<PERSON><PERSON><PERSON><PERSON><PERSON>, MdT<PERSON>rOff} from "react-icons/md";
import {useEffect, useState, useRef} from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import {<PERSON><PERSON>, Drawer} from "antd";
import TimeOnZoneLogList from "./Components/TimeOnZoneLogList";
import {useParams} from "react-router-dom";
import {useAppDispatch, useAppSelector} from "../../../../store/hooks";
import timeOnZoneAdminSlice, {fetchTimeOnZoneData,} from "../../../../store/slices/admin/timeOnZone.admin.slice";
import ActiveFilters, {
  createEndDateFilter,
  createEndTimeFilter,
  createLabel<PERSON>ilter,
  createSimpleFilter,
  createStartDateFilter,
  createStartTimeFilter,
  FilterGroup,
} from "../../../../components/Admin/Common/ActiveFilters";
import AnalyticDrawerForm, {AnalyticsFilterState,} from "../../Analytics/Components/AnalyticDrawerForm";
import {EReportType} from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import {AdminBranch} from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import {AdminZone} from "../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import TimeOnZoneLogExportButton from "./Components/TimeOnZoneLogExportButton";
import {AdminLabel} from "../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import {AdminUser} from "../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import {AdminDevice} from "../../../../services/mainApi/admin/types/device.admin.mainApi.types";

interface FilterState {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: null | AdminBranch;
  zone: null | AdminZone;
  zoneLabels: AdminLabel[];
  user: null | AdminUser;
  userLabels: AdminLabel[];
  device: null | AdminDevice;
  deviceLabels: AdminLabel[];
}

const TimeOnZoneLogAdmin = () => {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const isFetchingRef = useRef(false);

  // Get branch code from URL params
  const {branchCode} = useParams<{
    branchCode: string;
  }>();

  // Redux state
  const timeOnZoneState = useAppSelector(
    (state) => state.timeOnZoneAdmin
  );
  const loading = timeOnZoneState.loading;
  const pagination = timeOnZoneState.pagination;

  const selectedBranch = timeOnZoneState.selectedBranch;
  const selectedZone = timeOnZoneState.selectedZone;
  const selectedZoneLabels = timeOnZoneState.selectedZoneLabels;
  const selectedUser = timeOnZoneState.selectedUser;
  const selectedUserLabels = timeOnZoneState.selectedUserLabels;
  const selectedDevice = timeOnZoneState.selectedDevice;
  const selectedDeviceLabels = timeOnZoneState.selectedDeviceLabels;
  const filterState = timeOnZoneState.filter;
  // Load data on mount and when filters change
  useEffect(() => {
    if (branchCode && !isFetchingRef.current) {
      isFetchingRef.current = true;
      dispatch(fetchTimeOnZoneData({branchCode}))
        .finally(() => {
          isFetchingRef.current = false;
        });
    }
  }, [
    dispatch,
    branchCode,
    filterState.page,
    filterState.limit,
    filterState.startDate,
    filterState.endDate,
    filterState.startTime,
    filterState.endTime,
    filterState.branchId,
    filterState.zoneId,
    filterState.zoneLabels,
    filterState.userId,
    filterState.userLabels,
    filterState.deviceId,
    filterState.deviceLabels,
  ]);

  // Handle page change
  const handlePageChange = (
    page: number,
    pageSize?: number
  ) => {
    if (pageSize && pageSize !== filterState.limit) {
      dispatch(timeOnZoneAdminSlice.actions.setLimit(pageSize));
    }
    dispatch(timeOnZoneAdminSlice.actions.setPage(page));
  };

  // Convert applied filter state to format expected by drawer
  const getAppliedFilters = (): FilterState => {
    return {
      startDate: filterState.startDate,
      endDate: filterState.endDate,
      startTime: filterState.startTime,
      endTime: filterState.endTime,
      branch: selectedBranch,
      zone: selectedZone,
      zoneLabels: selectedZoneLabels || [],
      user: selectedUser,
      userLabels: selectedUserLabels || [],
      device: selectedDevice,
      deviceLabels: selectedDeviceLabels || [],
    };
  };

  // Check if there are any active filters
  const hasActiveFilters = (): boolean => {
    // Check if any filter is active
    return !!(filterState.startDate ||
      filterState.endDate ||
      filterState.startTime ||
      filterState.endTime ||
      filterState.branchId ||
      filterState.zoneId ||
      filterState.zoneLabels?.length > 0 ||
      filterState.userId ||
      filterState.userLabels?.length > 0 ||
      filterState.deviceId ||
      filterState.deviceLabels?.length > 0);
  };

  // Get filter groups configuration for ActiveFilters component
  const getFilterGroups = (): FilterGroup[] => {
    const appliedFilters = getAppliedFilters();

    return [
      {
        key: "date",
        filters: [
          createStartDateFilter(
            appliedFilters.startDate
          ),
          createEndDateFilter(
            appliedFilters.endDate
          ),
        ],
      },
      {
        key: "time",
        filters: [
          createStartTimeFilter(
            appliedFilters.startTime
          ),
          createEndTimeFilter(
            appliedFilters.endTime
          ),
        ],
      },
      {
        key: "user",
        filters: [
          createSimpleFilter(
            "user",
            "user",
            appliedFilters.user,
            "name"
          ),
          createLabelFilter(
            "userLabels",
            "userLabels",
            appliedFilters.userLabels
          ),
        ],
      },
      {
        key: "device",
        filters: [
          createSimpleFilter(
            "device",
            "device",
            appliedFilters.device,
            "device_name"
          ),
          createLabelFilter(
            "deviceLabels",
            "deviceLabels",
            appliedFilters.deviceLabels
          ),
        ],
      },
      {
        key: "location",
        filters: [
          createSimpleFilter(
            "zone",
            "zone",
            appliedFilters.zone,
            "zone_name"
          ),
          createLabelFilter(
            "zoneLabels",
            "zoneLabels",
            appliedFilters.zoneLabels
          ),
          createSimpleFilter(
            "branch",
            "branch",
            appliedFilters.branch,
            "branch_name"
          ),
        ],
      },
    ];
  };

  const handleClearAll = () => {
    dispatch(timeOnZoneAdminSlice.actions.resetFilters());
  };

  const handleRemoveFilter = (
    filterKey: keyof FilterState
  ) => {
    switch (filterKey) {
      case "user":
        dispatch(timeOnZoneAdminSlice.actions.setSelectedUser(null));
        break;
      case "userLabels":
        dispatch(timeOnZoneAdminSlice.actions.setSelectedUserLabels([]));
        break;
      case "device":
        dispatch(timeOnZoneAdminSlice.actions.setSelectedDevice(null));
        break;
      case "deviceLabels":
        dispatch(timeOnZoneAdminSlice.actions.setSelectedDeviceLabels([]));
        break;
      case "zone":
        dispatch(timeOnZoneAdminSlice.actions.setSelectedZone(null));
        break;
      case "zoneLabels":
        dispatch(timeOnZoneAdminSlice.actions.setSelectedZoneLabels([]));
        break;
      case "branch":
        dispatch(timeOnZoneAdminSlice.actions.setSelectedBranch(null));
        break;
      case "startDate":
        dispatch(timeOnZoneAdminSlice.actions.setStartDate(null));
        break;
      case "endDate":
        dispatch(timeOnZoneAdminSlice.actions.setEndDate(null));
        break;
      case "startTime":
        dispatch(timeOnZoneAdminSlice.actions.setStartTime(null));
        break;
      case "endTime":
        dispatch(timeOnZoneAdminSlice.actions.setEndTime(null));
        break;
    }
  };

  // Handle clicking on an active filter to edit it
  const handleEditFilter = () => {
    // We only need to open the drawer
    setDrawerVisible(true);
  };

  // Convert FilterState to AnalyticsFilterState for the drawer form
  const getInitialDrawerValues = (): Partial<AnalyticsFilterState> => {
    const appliedFilters = getAppliedFilters();

    return {
      startDate: appliedFilters.startDate || "",
      endDate: appliedFilters.endDate || "",
      startTime: appliedFilters.startTime || "",
      endTime: appliedFilters.endTime || "",
      selectedBranch: appliedFilters.branch || null,
      site: appliedFilters.zone,
      siteLabels: appliedFilters.zoneLabels,
      user: appliedFilters.user,
      userLabels: appliedFilters.userLabels,
      device: appliedFilters.device,
      deviceLabels: appliedFilters.deviceLabels,
    };
  };

  // Get filter info for export button
  const getFilterInfo = () => ({
    startDate: timeOnZoneState.filter.startDate || null,
    endDate: timeOnZoneState.filter.endDate || null,
    startTime: timeOnZoneState.filter.startTime || null,
    endTime: timeOnZoneState.filter.endTime || null,
    selectedBranch: timeOnZoneState.selectedBranch || null,
    site: timeOnZoneState.selectedZone || null,
    siteLabels: timeOnZoneState.selectedZoneLabels || [],
    user: timeOnZoneState.selectedUser || null,
    userLabels: timeOnZoneState.selectedUserLabels || [],
    device: timeOnZoneState.selectedDevice || null,
    deviceLabels: timeOnZoneState.selectedDeviceLabels || [],
  });

  // Apply filters from the drawer
  const handleApplyFilters = (
    newFilters: AnalyticsFilterState
  ) => {
    // Update redux state
    dispatch(timeOnZoneAdminSlice.actions.setSelectedUser(newFilters.user || null));
    dispatch(timeOnZoneAdminSlice.actions.setSelectedUserLabels(newFilters.userLabels || []));
    dispatch(timeOnZoneAdminSlice.actions.setSelectedDevice(newFilters.device || null));
    dispatch(timeOnZoneAdminSlice.actions.setSelectedDeviceLabels(newFilters.deviceLabels || []));
    dispatch(timeOnZoneAdminSlice.actions.setSelectedZone(newFilters.site || null));
    dispatch(timeOnZoneAdminSlice.actions.setSelectedZoneLabels(newFilters.siteLabels || []));
    dispatch(timeOnZoneAdminSlice.actions.setSelectedBranch(newFilters.selectedBranch || null));
    dispatch(timeOnZoneAdminSlice.actions.setStartDate(newFilters.startDate || null));
    dispatch(timeOnZoneAdminSlice.actions.setEndDate(newFilters.endDate || null));
    dispatch(timeOnZoneAdminSlice.actions.setStartTime(newFilters.startTime || null));
    dispatch(timeOnZoneAdminSlice.actions.setEndTime(newFilters.endTime || null));

    // Reset page to 1
    dispatch(timeOnZoneAdminSlice.actions.setPage(1));
    setDrawerVisible(false);
  };

  return (
    <WebAdminLayout activePage="time-on-zone-log">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdTimerOff/>}
            title={t(
              "timeOnZoneLog.title",
              "Time On Zone Log"
            )}
            description={t(
              "timeOnZoneLog.description",
              "View and analyze time spent in zones"
            )}
            actions={[
              // Desktop view - show all buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <TimeOnZoneLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                />
                <Button
                  key="filter"
                  icon={<MdFilterList/>}
                  size="large"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button and dropdown for downloads
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <TimeOnZoneLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                  buttonText={t(
                    "common.download",
                    "Download"
                  )}
                />
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList/>}
                  size="large"
                  type="primary"
                  className="bg-indigo-600 hover:bg-indigo-700 border-0 shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters using the reusable component */}
          <ActiveFilters<FilterState>
            filterGroups={getFilterGroups()}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAll}
            onShowFilters={() => {
              setDrawerVisible(true);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Time On Zone Log List */}
          <TimeOnZoneLogList
            timeOnZoneLogs={timeOnZoneState.timeOnZoneData}
            loading={loading}
            total={pagination.total || 0}
            page={pagination.page || 1}
            limit={pagination.limit || 10}
            onPageChange={handlePageChange}
          />
        </div>

        {/* Filter Drawer with modern styling */}
        <Drawer
          title={
            <div className="text-lg font-semibold text-gray-800 dark:text-white">
              {t(
                "timeOnZoneLog.filters.title",
                "Filter Time On Zone Log"
              )}
            </div>
          }
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          className="dark:bg-gray-800"
        >
          <AnalyticDrawerForm
            reportType={EReportType.TIME_ON_ZONE}
            onApply={handleApplyFilters}
            onClose={() => setDrawerVisible(false)}
            initialValues={getInitialDrawerValues()}
          />
        </Drawer>
      </div>
    </WebAdminLayout>
  );
};

export default TimeOnZoneLogAdmin;
