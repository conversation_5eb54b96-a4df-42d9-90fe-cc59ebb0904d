import {Button, Input, Select} from "antd";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {PlusOutlined, SearchOutlined, SortAscendingOutlined, SortDescendingOutlined,} from "@ant-design/icons";
import {MdDescription} from "react-icons/md";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import {useTranslation} from "react-i18next";
import {useDispatch, useSelector} from "react-redux";
import {AppDispatch, RootState} from "../../../store";
import {useParams} from "react-router-dom";
import {useCallback, useEffect, useRef} from "react";
import formAdminSlice, {debouncedFetchForms, fetchFormsAdmin} from "../../../store/slices/admin/form.admin.slice";
import addFormAdminSlice from "../../../store/slices/admin/addForm.admin.slice";
import FormsList from "./Components/FormsList.tsx";

export default function FormsAdmin() {
  const {t} = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const {branchCode} = useParams<{
    branchCode: string;
  }>();
  const {forms, loading, filter, pagination} = useSelector(
    (state: RootState) => state.formAdmin
  );
  const {actions} = formAdminSlice;

  const isFetching = useRef(false);

  const fetch = useCallback(() => {
    if (isFetching.current) return;
    isFetching.current = true;
    dispatch(fetchFormsAdmin(branchCode || ""));
    isFetching.current = false;
  }, [dispatch, branchCode]);

  // Handle search input change with debounce
  const handleSearchChange = (value: string) => {
    dispatch(actions.setFilter({search: value}));
    if (branchCode) {
      debouncedFetchForms(dispatch, branchCode);
    }
  };

  // Handle order by change
  const handleOrderByChange = (value: string) => {
    dispatch(actions.setFilter({orderBy: value}));
  };

  // Handle order direction toggle
  const handleOrderDirectionToggle = useCallback(() => {
    const newDirection =
      filter.orderDirection === "ASC" ? "DESC" : "ASC";
    dispatch(
      actions.setFilter({orderDirection: newDirection})
    );
  }, [actions, dispatch, filter.orderDirection]);

  // Handle page change
  const handlePageChange = (
    page: number,
    pageSize?: number
  ) => {
    dispatch(
      actions.setFilter({
        page: page,
        limit: pageSize || filter.limit,
      })
    );
  };

  // Fetch forms initially
  useEffect(() => {
    if (branchCode) {
      fetch()
    }
  }, [
    fetch,
    filter.page,
    filter.limit,
    filter.orderBy,
    filter.orderDirection,
  ]);

  return (
    <WebAdminLayout activePage="forms">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdDescription/>}
            title={t("forms.title")}
            description={t(
              "forms.description",
              "Manage your forms and templates"
            )}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined/>}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={() => {
                  dispatch(
                    addFormAdminSlice.actions.open()
                  );
                }}
              >
                {t("forms.addButton")}
              </Button>,
            ]}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex gap-4 flex-wrap">
              <Input
                placeholder={t(
                  "forms.search.placeholder",
                  "Search forms..."
                )}
                prefix={<SearchOutlined/>}
                size="large"
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                onChange={(e) =>
                  handleSearchChange(e.target.value)
                }
                value={filter.search || ""}
              />
              <div className="flex gap-2">
                <Select
                  value={filter.orderBy}
                  onChange={handleOrderByChange}
                  className="min-w-[160px] dark:bg-gray-700"
                  size="large"
                >
                  <Select.Option value="created_at">
                    {t(
                      "forms.search.sortOptions.createdAt",
                      "Created Date"
                    )}
                  </Select.Option>
                  <Select.Option value="form_name">
                    {t(
                      "forms.search.sortOptions.name",
                      "Name"
                    )}
                  </Select.Option>
                </Select>
                <Button
                  icon={
                    filter.orderDirection === "ASC" ? (
                      <SortAscendingOutlined/>
                    ) : (
                      <SortDescendingOutlined/>
                    )
                  }
                  size="large"
                  className="dark:border-gray-600 dark:text-gray-300"
                  onClick={handleOrderDirectionToggle}
                />
              </div>
            </div>
          </div>

          {/* Forms List */}
          <FormsList
            forms={forms}
            loading={loading}
            filter={filter}
            pagination={pagination}
            onPageChange={handlePageChange}
          />
        </div>
      </div>
    </WebAdminLayout>
  );
}
