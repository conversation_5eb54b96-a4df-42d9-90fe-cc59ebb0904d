import { Module } from "../../../../../services/mainApi/admin/types/roles.admin.mainApi.types.ts";

export type ModuleType = "MODULE" | "REPORT";

export type PermissionType =
  | "allow_view"
  | "allow_create"
  | "allow_update"
  | "allow_delete";

export interface Permission {
  module_code: string;
  allow_view: boolean;
  allow_create: boolean;
  allow_update: boolean;
  allow_delete: boolean;
}

export interface PermissionsTableProps {
  availableModules: Module[];
  permissions: Permission[];
  onPermissionChange: (
    moduleCode: string,
    moduleType: ModuleType,
    permission: PermissionType,
    value: boolean
  ) => void;
}
