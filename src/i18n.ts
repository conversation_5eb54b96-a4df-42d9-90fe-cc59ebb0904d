import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";

// Import langsung file terjemahan
import translationEN from "./locales/en-US/translation.json";
import translationID from "./locales/id-ID/translation.json";
import translationCN from "./locales/zh-CN/translation.json";
import translationMT from "./locales/mt-MT/translation.json";

const resources = {
  "en-US": {
    translation: translationEN,
  },
  "id-ID": {
    translation: translationID,
  },
  "zh-CN": {
    translation: translationCN,
  },
  "mt-MT": {
    translation: translationMT,
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: {
      zh: ["zh-CN"],
      en: ["en-US"],
      id: ["id-ID"],
      mt: ["mt-MT"],
      default: ["en-US"],
    },
    supportedLngs: ["en-US", "id-ID", "zh-CN", "mt-MT"],
    // debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ["localStorage", "navigator"],
      lookupLocalStorage: "i18nextLng",
      caches: ["localStorage"],
    },
    react: {
      useSuspense: false,
      bindI18n: "languageChanged loaded",
    },
    returnNull: false,
    returnEmptyString: false,
    returnObjects: true,
    saveMissing: true,
    fallbackNS: "translation",
  });

export default i18n;
