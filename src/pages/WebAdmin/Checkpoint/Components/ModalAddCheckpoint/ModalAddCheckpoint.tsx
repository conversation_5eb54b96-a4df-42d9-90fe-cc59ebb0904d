import {
  <PERSON><PERSON>,
  But<PERSON>,
  Divider,
  Form,
  Input,
  InputNumber,
  Modal,
  Select,
  Spin,
  Switch,
} from "antd";
import {
  BellOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks.ts";
import CheckpointMap from "./CheckpointMap.tsx";
import SelectZone from "../../../../../components/Zone/SelectZone.tsx";
import { ECheckPointType } from "../../../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types.ts";
import SelectLabel from "../../../../../components/Label/SelectLabel.tsx";
import addCheckpointAdminSlice, {
  createCheckpoint,
  updateCheckpoint,
} from "../../../../../store/slices/admin/addCheckpoint.admin.slice.ts";
import SelectGeofence from "../../../../../components/Geofence/SelectGeofence.tsx";
import { useParams } from "react-router-dom";
import { useCallback } from "react";
import { fetchCheckpointAdmin } from "../../../../../store/slices/admin/checkpoint.admin.slice.ts";
import ErrorMessage from "../../../../../components/Common/ErrorMessage/ErrorMessage.tsx";
import SelectBeacon from "../../../../../components/Beacon/SelectBeacon.tsx";

const { Option } = Select;

const ModalAddCheckpoint = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { message, modal } = App.useApp();
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const addCheckpointState = useAppSelector(
    (state) => state.addCheckpointAdmin
  );
  const { actions } = addCheckpointAdminSlice;

  const handleCheckpointTypeChange = (
    value: ECheckPointType
  ) => {
    dispatch(actions.setCheckpointType(value));
  };

  const onClose = () => {
    dispatch(actions.close());
  };

  const onSubmit = useCallback(async () => {
    if (!branchCode) {
      message.error(t("checkpoint.error.noBranchCode"));
      return;
    }

    try {
      let result;

      if (addCheckpointState.mode === "update") {
        result = await dispatch(
          updateCheckpoint(branchCode)
        );
      } else {
        result = await dispatch(
          createCheckpoint(branchCode)
        );
      }

      if (result.meta.requestStatus === "fulfilled") {
        message.success(
          t(
            addCheckpointState.mode === "update"
              ? "checkpoint.modal.edit.success"
              : "checkpoint.modal.add.success"
          )
        );
        dispatch(fetchCheckpointAdmin(branchCode));
      } else if (
        result.meta.requestStatus === "rejected" &&
        result.payload
      ) {
        const errorMsg =
          typeof result.payload === "string"
            ? result.payload
            : t(
                addCheckpointState.mode === "update"
                  ? "checkpoint.error.updateFailed"
                  : "checkpoint.error.createFailed"
              );
        modal.error({
          title: "Error",
          content: errorMsg,
        });
      }
    } catch (error) {
      console.error("Error submitting checkpoint:", error);
      message.error(
        t(
          addCheckpointState.mode === "update"
            ? "checkpoint.error.updateFailed"
            : "checkpoint.error.createFailed"
        )
      );
    }
  }, [
    addCheckpointState.mode,
    branchCode,
    dispatch,
    message,
    modal,
    t,
  ]);

  return (
    <Modal
      title={t(
        addCheckpointState.mode === "update"
          ? "checkpoint.modal.edit.title"
          : "checkpoint.modal.create.title",
        addCheckpointState.mode === "update"
          ? "Edit Checkpoint"
          : "Create New Checkpoint"
      )}
      open={addCheckpointState.open}
      onCancel={onClose}
      onOk={onSubmit}
      width={800}
      className="dark:bg-gray-800"
      confirmLoading={addCheckpointState.loading}
    >
      <Form layout="vertical">
        {/* Basic Information */}
        <Divider>
          {t(
            "checkpoint.form.sections.basic",
            "Basic Information"
          )}
        </Divider>
        <Form.Item
          label={t(
            "checkpoint.form.name.label",
            "Checkpoint Name"
          )}
          required
        >
          <Input
            placeholder={t(
              "checkpoint.form.name.placeholder",
              "Enter checkpoint name"
            )}
            value={addCheckpointState.checkpoint_name}
            onChange={(e) => {
              dispatch(
                actions.setCheckpointName(e.target.value)
              );
            }}
          />
        </Form.Item>

        <Form.Item
          label={t(
            "checkpoint.form.description.label",
            "Description"
          )}
          required
        >
          <Input.TextArea
            rows={4}
            placeholder={t(
              "checkpoint.form.description.placeholder",
              "Enter checkpoint description"
            )}
            value={
              addCheckpointState.checkpoint_description
            }
            onChange={(e) => {
              dispatch(
                actions.setCheckpointDescription(
                  e.target.value
                )
              );
            }}
          />
        </Form.Item>

        <Form.Item
          label={t("checkpoint.form.zone.label", "Zone")}
          required
        >
          <SelectZone
            value={addCheckpointState.zone_id || undefined}
            onChange={(value) => {
              dispatch(actions.setGeofence(value));
            }}
          />
        </Form.Item>

        {/* Identification Section */}
        <Divider>
          {t(
            "checkpoint.form.sections.identification",
            "Identification"
          )}
        </Divider>
        <Form.Item
          label={t(
            "checkpoint.form.type.label",
            "Checkpoint Type"
          )}
          required
        >
          <Select
            placeholder={t(
              "checkpoint.form.type.placeholder",
              "Select type"
            )}
            onChange={handleCheckpointTypeChange}
            value={addCheckpointState.checkpoint_type_id}
          >
            <Option value={ECheckPointType.BEACON}>
              Beacon
            </Option>
            <Option value={ECheckPointType.NFC}>NFC</Option>
            <Option value={ECheckPointType.RFID}>
              RFID
            </Option>
            <Option value={ECheckPointType.GEOFENCE}>
              Geofence
            </Option>
          </Select>
        </Form.Item>

        {/* Conditional rendering based on checkpoint type */}
        {addCheckpointState.checkpoint_type_id ===
          ECheckPointType.BEACON && (
          <>
            <Form.Item
              label={t(
                "checkpoint.form.beacon.label",
                "Beacon"
              )}
              required
            >
              <SelectBeacon
                value={
                  addCheckpointState.beacon_id || undefined
                }
                onChange={(value) => {
                  dispatch(actions.setBeaconId(value));
                }}
              />
            </Form.Item>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                label={t(
                  "checkpoint.form.major.label",
                  "Major Value"
                )}
                required
              >
                <Input
                  value={
                    addCheckpointState.major_value?.toString() ||
                    ""
                  }
                  onChange={(e) => {
                    dispatch(
                      actions.setMajorValue(e.target.value)
                    );
                  }}
                  placeholder={t(
                    "checkpoint.form.major.placeholder",
                    "Enter major value"
                  )}
                />
              </Form.Item>

              <Form.Item
                label={t(
                  "checkpoint.form.minor.label",
                  "Minor Value"
                )}
                required
              >
                <Input
                  value={
                    addCheckpointState.minor_value?.toString() ||
                    ""
                  }
                  onChange={(e) => {
                    dispatch(
                      actions.setMinorValue(e.target.value)
                    );
                  }}
                  placeholder={t(
                    "checkpoint.form.minor.placeholder",
                    "Enter minor value"
                  )}
                />
              </Form.Item>
            </div>
          </>
        )}

        {/* Serial Number Section - Improved Layout */}
        {(addCheckpointState.checkpoint_type_id ===
          ECheckPointType.NFC ||
          addCheckpointState.checkpoint_type_id ===
            ECheckPointType.RFID) && (
          <>
            <Divider>
              {t(
                "checkpoint.form.sections.serialNumbers",
                "Serial Numbers"
              )}
            </Divider>

            <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg mb-4">
              {/* Primary Serial Number Group */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t(
                    "checkpoint.form.primarySerial.groupLabel",
                    "Primary Serial Number"
                  )}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Form.Item
                    label={t(
                      "checkpoint.form.primarySerialHex.label",
                      "HEX Format"
                    )}
                    className="mb-0"
                  >
                    <Input
                      value={
                        addCheckpointState.serial_number_hex
                      }
                      onChange={(e) => {
                        dispatch(
                          actions.setSerialNumberHex(
                            e.target.value
                          )
                        );
                      }}
                      placeholder={t(
                        "checkpoint.form.primarySerialHex.placeholder",
                        "Enter serial number"
                      )}
                      addonBefore="HEX"
                    />
                  </Form.Item>

                  <Form.Item
                    label={t(
                      "checkpoint.form.primarySerialDec.label",
                      "DEC Format"
                    )}
                    className="mb-0"
                  >
                    <Input
                      value={
                        addCheckpointState.serial_number_dec
                      }
                      onChange={(e) => {
                        dispatch(
                          actions.setSerialNumberDec(
                            e.target.value
                          )
                        );
                      }}
                      placeholder={t(
                        "checkpoint.form.primarySerialDec.placeholder",
                        "Enter serial number"
                      )}
                      addonBefore="DEC"
                    />
                  </Form.Item>
                </div>
              </div>

              {/* Secondary Serial Number Group */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t(
                    "checkpoint.form.secondarySerial.groupLabel",
                    "Secondary Serial Number (Optional)"
                  )}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Form.Item
                    label={t(
                      "checkpoint.form.secondarySerialHex.label",
                      "HEX Format"
                    )}
                    className="mb-0"
                  >
                    <Input
                      value={
                        addCheckpointState.serial_number_second_hex
                      }
                      onChange={(e) => {
                        dispatch(
                          actions.setSerialNumberSecondHex(
                            e.target.value
                          )
                        );
                      }}
                      placeholder={t(
                        "checkpoint.form.secondarySerialHex.placeholder",
                        "Enter serial number"
                      )}
                      addonBefore="HEX"
                    />
                  </Form.Item>

                  <Form.Item
                    label={t(
                      "checkpoint.form.secondarySerialDec.label",
                      "DEC Format"
                    )}
                    className="mb-0"
                  >
                    <Input
                      value={
                        addCheckpointState.serial_number_second_dec
                      }
                      onChange={(e) => {
                        dispatch(
                          actions.setSerialNumberSecondDec(
                            e.target.value
                          )
                        );
                      }}
                      placeholder={t(
                        "checkpoint.form.secondarySerialDec.placeholder",
                        "Enter serial number"
                      )}
                      addonBefore="DEC"
                    />
                  </Form.Item>
                </div>
              </div>
            </div>
          </>
        )}

        {addCheckpointState.checkpoint_type_id ===
          ECheckPointType.GEOFENCE && (
          <Form.Item
            label={t(
              "checkpoint.form.geofence.label",
              "Geofence"
            )}
            required
          >
            <SelectGeofence
              value={
                addCheckpointState.geofence_id || undefined
              }
              onChange={(value) => {
                dispatch(actions.setGeofenceId(value));
              }}
            />
          </Form.Item>
        )}

        {/* Location Section - Improved Layout */}
        <Divider>
          {t(
            "checkpoint.form.sections.location",
            "Location"
          )}
        </Divider>

        <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg mb-4">
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
            {t(
              "checkpoint.form.location.description",
              "Set the geographic location of this checkpoint by selecting a point on the map."
            )}
          </p>

          {addCheckpointState.open ? (
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
              <CheckpointMap
                initialLocation={
                  addCheckpointState.latitude &&
                  addCheckpointState.longitude
                    ? {
                        latitude:
                          addCheckpointState.latitude,
                        longitude:
                          addCheckpointState.longitude,
                      }
                    : undefined
                }
                onLocationSelect={(locationInfo) => {
                  dispatch(
                    actions.setLatitude(
                      locationInfo.latitude
                    )
                  );
                  dispatch(
                    actions.setLongitude(
                      locationInfo.longitude
                    )
                  );
                }}
              />
            </div>
          ) : (
            <div className="flex justify-center items-center py-12">
              <Spin size="large" />
            </div>
          )}

          {/* Display coordinates if available */}
          {addCheckpointState.latitude &&
            addCheckpointState.longitude && (
              <div className="mt-3 grid grid-cols-2 gap-4">
                <div className="bg-white dark:bg-gray-700 p-2 rounded border border-gray-200 dark:border-gray-600">
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {t(
                      "checkpoint.form.latitude.label",
                      "Latitude"
                    )}
                  </div>
                  <div className="font-mono text-sm">
                    {addCheckpointState.latitude}
                  </div>
                </div>
                <div className="bg-white dark:bg-gray-700 p-2 rounded border border-gray-200 dark:border-gray-600">
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {t(
                      "checkpoint.form.longitude.label",
                      "Longitude"
                    )}
                  </div>
                  <div className="font-mono text-sm">
                    {addCheckpointState.longitude}
                  </div>
                </div>
              </div>
            )}
        </div>

        {/* Schedule Section */}
        <Divider>
          {t(
            "checkpoint.form.sections.schedule",
            "Schedule"
          )}
        </Divider>

        <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg mb-4">
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
            {t(
              "checkpoint.form.schedule.description",
              "Select which days this checkpoint is active and how many visits are required each day."
            )}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[
              "monday",
              "tuesday",
              "wednesday",
              "thursday",
              "friday",
              "saturday",
              "sunday",
            ].map((day) => (
              <div
                key={day}
                className={`flex items-center gap-3 p-3 rounded-lg border ${
                  (addCheckpointState[
                    day as keyof typeof addCheckpointState
                  ] as boolean)
                    ? "border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20"
                    : "border-gray-200 dark:border-gray-700"
                }`}
              >
                <Switch
                  checked={
                    addCheckpointState[
                      day as keyof typeof addCheckpointState
                    ] as boolean
                  }
                  onChange={(checked) => {
                    dispatch(
                      actions.setDayActive({
                        data: day as
                          | "monday"
                          | "tuesday"
                          | "wednesday"
                          | "thursday"
                          | "friday"
                          | "saturday"
                          | "sunday",
                        active: checked,
                      })
                    );
                  }}
                  className="mr-1"
                />
                <div className="flex-1">
                  <label className="font-medium text-gray-700 dark:text-gray-300">
                    {t(
                      `checkpoint.form.${day}.label`,
                      day.charAt(0).toUpperCase() +
                        day.slice(1)
                    )}
                  </label>
                </div>
                <div className="w-32">
                  <InputNumber
                    className="w-full"
                    min={0}
                    placeholder={t(
                      "checkpoint.form.visitCount.placeholder",
                      "Visits"
                    )}
                    value={
                      addCheckpointState[
                        `${day}_count` as keyof typeof addCheckpointState
                      ] as number
                    }
                    onChange={(value) => {
                      dispatch(
                        actions.setDayCount({
                          data: day as
                            | "monday"
                            | "tuesday"
                            | "wednesday"
                            | "thursday"
                            | "friday"
                            | "saturday"
                            | "sunday",
                          count: value as number,
                        })
                      );
                    }}
                    disabled={
                      !(addCheckpointState[
                        day as keyof typeof addCheckpointState
                      ] as boolean)
                    }
                    addonAfter={t(
                      "checkpoint.form.visits",
                      "visits"
                    )}
                  />
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4 flex justify-between">
            <Button
              type="default"
              onClick={() => {
                [
                  "monday",
                  "tuesday",
                  "wednesday",
                  "thursday",
                  "friday",
                  "saturday",
                  "sunday",
                ].forEach((day) => {
                  dispatch(
                    actions.setDayActive({
                      data: day as any,
                      active: true,
                    })
                  );
                });
              }}
              size="small"
            >
              {t(
                "checkpoint.form.selectAll",
                "Select All Days"
              )}
            </Button>
            <Button
              type="default"
              onClick={() => {
                [
                  "monday",
                  "tuesday",
                  "wednesday",
                  "thursday",
                  "friday",
                  "saturday",
                  "sunday",
                ].forEach((day) => {
                  dispatch(
                    actions.setDayActive({
                      data: day as any,
                      active: false,
                    })
                  );
                });
              }}
              size="small"
            >
              {t(
                "checkpoint.form.clearAll",
                "Clear All Days"
              )}
            </Button>
          </div>
        </div>

        {/* Settings Section - Improved Layout */}
        <Divider>
          {t(
            "checkpoint.form.sections.settings",
            "Settings"
          )}
        </Divider>

        <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg mb-4">
          {/* Visit Interval Toggle with Description */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <label className="font-medium text-gray-700 dark:text-gray-300">
                {t(
                  "checkpoint.form.visitInterval.label",
                  "Visit Interval"
                )}
              </label>
              <Switch
                checked={addCheckpointState.visit_interval}
                onChange={(checked) => {
                  dispatch(
                    actions.setVisitInterval(checked)
                  );
                }}
              />
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {t(
                "checkpoint.form.visitInterval.description",
                "Enable to set rotation interval and warning notifications for this checkpoint."
              )}
            </p>
          </div>

          {/* Conditional Settings when Visit Interval is enabled */}
          {addCheckpointState.visit_interval && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <Form.Item
                label={t(
                  "checkpoint.form.rotationInterval.label",
                  "Rotation Interval"
                )}
                className="mb-0"
              >
                <Select
                  allowClear={true}
                  className="w-full"
                  value={
                    addCheckpointState.rotation_interval
                  }
                  placeholder={t(
                    "checkpoint.form.rotationInterval.placeholder",
                    "Select rotation interval"
                  )}
                  onChange={(value) => {
                    dispatch(
                      actions.setRotationInterval(value)
                    );
                  }}
                  suffixIcon={<ClockCircleOutlined />}
                >
                  {[
                    90, 85, 80, 75, 70, 65, 60, 55, 50, 45,
                    40, 35, 30, 25, 20, 15, 10, 5,
                  ].map((i) => (
                    <Option
                      key={i}
                      value={i}
                    >
                      {i}{" "}
                      {t(
                        "checkpoint.form.minutes",
                        "minutes"
                      )}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label={t(
                  "checkpoint.form.warningNotification.label",
                  "Warning Notification"
                )}
                className="mb-0"
              >
                <Select
                  allowClear={true}
                  className="w-full"
                  placeholder={t(
                    "checkpoint.form.warningNotification.placeholder",
                    "Select warning notification"
                  )}
                  value={
                    addCheckpointState.warning_notification
                  }
                  onChange={(value) => {
                    dispatch(
                      actions.setWarningNotification(value)
                    );
                  }}
                  suffixIcon={<BellOutlined />}
                >
                  {[0, 5, 10, 15, 20, 25, 30].map((i) => (
                    <Option
                      key={i}
                      value={i}
                    >
                      {i}{" "}
                      {t(
                        "checkpoint.form.minutes",
                        "minutes"
                      )}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          )}

          {/* Active Status (only for update mode) */}
          {addCheckpointState.mode === "update" && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <label className="font-medium text-gray-700 dark:text-gray-300">
                    {t(
                      "checkpoint.form.active.label",
                      "Active Status"
                    )}
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {t(
                      "checkpoint.form.active.description",
                      "Enable or disable this checkpoint"
                    )}
                  </p>
                </div>
                <Switch
                  checked={addCheckpointState.active}
                  onChange={(checked) => {
                    dispatch(
                      actions.setCheckpointActive(checked)
                    );
                  }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Labels Section */}
        <Divider>
          {t("checkpoint.form.sections.labels", "Labels")}
        </Divider>
        <Form.Item
          label={t(
            "checkpoint.form.labels.label",
            "Labels"
          )}
          tooltip={t(
            "checkpoint.form.labels.tooltip",
            "Assign labels to categorize this checkpoint"
          )}
        >
          <SelectLabel
            mode="multiple"
            value={addCheckpointState.label_ids || []}
            onChange={(value) => {
              dispatch(
                actions.setLabels(value as string[])
              );
            }}
            placeholder={t(
              "checkpoint.form.labels.placeholder",
              "Select labels"
            )}
          />
        </Form.Item>
      </Form>
      {addCheckpointState.errorMessage && (
        <ErrorMessage
          message={addCheckpointState.errorMessage}
        />
      )}
    </Modal>
  );
};

export default ModalAddCheckpoint;
