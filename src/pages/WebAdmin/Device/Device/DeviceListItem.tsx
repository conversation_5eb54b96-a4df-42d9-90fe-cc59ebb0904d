import { <PERSON><PERSON>, <PERSON>, Tooltip } from "antd";
import { AdminDevice } from "../../../../services/mainApi/admin/types/device.admin.mainApi.types.ts";
import { EditOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { formatDateWithTimeUTC } from "../../../../utils/dateUtils.ts";
import { useDispatch } from "react-redux";
import deviceModalSlice from "../../../../store/slices/admin/deviceModal.admin.slice";

interface DeviceListItemProps {
  device: AdminDevice;
}

export default function DeviceListItem({
  device,
}: DeviceListItemProps) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { actions } = deviceModalSlice;

  const onEdit = () => {
    dispatch(actions.openUpdateModal(device));
  };

  const deviceTypeLabel = () => {
    return device.uniguard_device_type?.name || "Unknown";
  };

  return (
    <div
      className={`
      rounded-xl border transition-all duration-200 hover:shadow-lg
      bg-white dark:bg-gray-800 border-gray-100 dark:border-gray-700
      h-full flex flex-col
    `}
    >
      <div className="p-5 flex-1 flex flex-col">
        {/* Header */}
        <div className="flex items-start justify-between mb-3 pb-3 border-b dark:border-gray-700">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">
                {device.device_name}
              </h3>
              {device.uniguard_device_type && (
                <Tag color="blue">{deviceTypeLabel()}</Tag>
              )}
              {device.type && (
                <Tag color="geekblue">{device.type}</Tag>
              )}
            </div>
            {device.device_description && (
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {device.device_description}
              </p>
            )}
          </div>
          <Tooltip title={t("common.edit")}>
            <Button
              type="text"
              icon={<EditOutlined className="text-lg" />}
              onClick={onEdit}
              className="text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400"
            />
          </Tooltip>
        </div>

        {/* Device Info */}
        <div className="grid grid-cols-1 gap-4 flex-1">
          <div className="p-2 bg-gray-50 dark:bg-gray-800/80 rounded">
            <h4 className="text-xs uppercase text-gray-500 dark:text-gray-400 mb-2 font-medium">
              {t("device.information")}
            </h4>
            <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs">
              <span className="text-gray-500 dark:text-gray-400">
                {t("device.imei")}
              </span>
              <span className="text-gray-700 dark:text-gray-300">
                {device.imei || "-"}
              </span>

              {device.serial_number && (
                <>
                  <span className="text-gray-500 dark:text-gray-400">
                    {t("device.serialNumber")}
                  </span>
                  <span className="text-gray-700 dark:text-gray-300">
                    {device.serial_number}
                  </span>
                </>
              )}

              <span className="text-gray-500 dark:text-gray-400">
                {t("common.created")}
              </span>
              <span className="text-gray-700 dark:text-gray-300">
                {formatDateWithTimeUTC(device.created_at)}
              </span>
            </div>
          </div>
        </div>

        {/* Labels */}
        {device.labels && device.labels.length > 0 && (
          <div className="flex flex-wrap gap-2 pt-4 mt-4 border-t dark:border-gray-700">
            <span className="text-xs text-gray-500 dark:text-gray-400 mr-2">
              {t("common.labels")}:
            </span>
            {device.labels.slice(0, 3).map((label) => (
              <Tag
                key={label.id}
                className="rounded-full m-0"
                color={
                  label.active ? "processing" : "default"
                }
              >
                {label.label_name}
              </Tag>
            ))}
            {device.labels.length > 3 && (
              <Tooltip
                title={device.labels
                  .slice(3)
                  .map((l) => l.label_name)
                  .join(", ")}
              >
                <Tag
                  className="rounded-full m-0"
                  color="default"
                >
                  +{device.labels.length - 3}
                </Tag>
              </Tooltip>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
