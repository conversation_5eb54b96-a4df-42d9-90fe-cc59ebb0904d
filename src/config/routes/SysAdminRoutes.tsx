import { Route, RouteProps } from "react-router-dom";
import { ProtectedRoute } from "../../components/Auth/ProtectedRoute";
import { routeConfig } from "./routes.tsx";

export const getSysAdminRoutes = (): React.ReactElement<RouteProps> => {
  return (
    <Route
      key="sysadmin"
      path="/sysadmin"
      element={<ProtectedRoute guard="sysadmin" />}
    >
      <Route
        path={routeConfig.sysAdmin.dashboard.path}
        element={routeConfig.sysAdmin.dashboard.component}
      />
      <Route
        path={routeConfig.sysAdmin.branch.path}
        element={routeConfig.sysAdmin.branch.component}
      />
      <Route
        path={routeConfig.sysAdmin.beacon.path}
        element={routeConfig.sysAdmin.beacon.component}
      />
      <Route
        path={routeConfig.sysAdmin.license.path}
        element={routeConfig.sysAdmin.license.component}
      />
      <Route
        path={routeConfig.sysAdmin.settings.path}
        element={routeConfig.sysAdmin.settings.component}
      />
    </Route>
  );
}; 