import { BaseResponse } from "../../types/base.mainApi.types";

export enum EDeviceType {
  APP = "1",
  I_BUTTON = "2",
  RFID = "3",
  GPS = "4",
}

export interface AdminDevice {
  id: string;
  parent_branch_id: string;
  uniguard_device_type_id: EDeviceType;
  device_name: string;
  device_description: string | null;
  imei: string;
  type: string;
  serial_number: string | null;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
  parent_branch: {
    id: string;
    parent_id: string;
    branch_name: string;
    branch_description: string;
    license_id: string;
    timezone_id: string;
    active: boolean;
    created_at: string;
    created_by: string | null;
    updated_at: string;
    updated_by: string | null;
    branch_code: string;
  };
  uniguard_device_type: {
    id: string;
    name: string;
    description: string | null;
    active: boolean;
    created_at: string;
    created_by: string | null;
    updated_at: string;
    updated_by: string | null;
  };
  labels: Array<{
    id: string;
    branch_id: string;
    label_name: string;
    label_description: string;
    active: boolean;
    created_at: string;
    created_by: string | null;
    updated_at: string;
    updated_by: string | null;
  }>;
}

export interface CreateDeviceRequest {
  device_name: string;
  uniguard_device_type_id: EDeviceType;
  serial_number?: string;
  imei?: string;
  device_description?: string;
  label_ids?: string[];
}

export type UpdateDeviceRequest = CreateDeviceRequest;

export interface UpdateGpsTrackingIntervalRequest {
  interval: number;
  active: boolean;
}

export type GetGpsTrackingIntervalResponse = BaseResponse<{
  interval: number;
  active: boolean;
}>;
