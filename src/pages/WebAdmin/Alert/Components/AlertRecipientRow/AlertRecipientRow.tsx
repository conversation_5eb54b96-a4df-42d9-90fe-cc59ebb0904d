import { Button, Input } from "antd";
import { DeleteOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import addAlertAdminSlice, {
  AddAlertAdminState,
} from "../../../../../store/slices/admin/addAlert.admin.slice";
import { useAppDispatch } from "../../../../../store/hooks";

interface AlertRecipientRowProps {
  rowId: string;
  recipient: AddAlertAdminState["recipients"][number];
}

const AlertRecipientRow: React.FC<
  AlertRecipientRowProps
> = ({ rowId, recipient }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { actions } = addAlertAdminSlice;

  const onRemove = () => {
    dispatch(actions.removeRecipient(rowId));
  };

  return (
    <div className="grid grid-cols-12 gap-3 items-start">
      <div className="col-span-11">
        <Input
          placeholder={t(
            "alert.form.recipient.contact.placeholder",
            "Contact information"
          )}
          value={recipient?.value || ""}
          onChange={(e) =>
            dispatch(
              actions.changeRecipient({
                rowUuid: rowId,
                value: e.target.value,
              })
            )
          }
        />
      </div>

      <div className="col-span-1 flex items-center justify-end">
        <Button
          icon={<DeleteOutlined />}
          danger
          type="text"
          onClick={onRemove}
        />
      </div>
    </div>
  );
};

export default AlertRecipientRow;
