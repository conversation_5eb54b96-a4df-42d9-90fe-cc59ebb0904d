import { Branch } from "./branch.mainApi.types";

export interface Beacon {
  id: string;
  parent_branch_id: string;
  beacon_name: string;
  beacon_description: string | null;
  beacon_uuid: string;
  counter_customer_code: string;
  pip_api_key: string | null;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
  parent_branch: Branch;
}

export interface BeaconListParams {
  search?: string;
  active?: boolean;
  page?: number;
  limit?: number;
  start_date?: string;
  end_date?: string;
  order_by?: string;
  order_direction?: "ASC" | "DESC";
}

export interface BeaconListResponseMeta {
  timestamp: string;
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface BeaconListResponse {
  success: boolean;
  message: string;
  data: Beacon[];
  meta: BeaconListResponseMeta;
  error: null | string;
}

export interface CreateBeaconDto {
  parent_branch_id: number;
  beacon_name: string;
  beacon_description: string;
  beacon_uuid: string;
  counter_customer_code?: string;
  pip_api_key?: string;
  active: boolean;
}

export interface CreateBeaconResponse {
  success: boolean;
  message: string;
  data: Beacon;
  error: null | string;
}

export interface UpdateBeaconDto {
  parent_branch_id?: number;
  beacon_name?: string;
  beacon_description?: string;
  beacon_uuid?: string;
  counter_customer_code?: string;
  pip_api_key?: string;
  active?: boolean;
}

export interface UpdateBeaconResponse {
  success: boolean;
  message: string;
  data: Beacon;
  error: null | string;
} 