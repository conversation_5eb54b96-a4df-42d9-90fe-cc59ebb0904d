import { memo, useCallback } from "react";
import { <PERSON><PERSON>, Spin, Button } from "antd";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks.ts";
import viewBranchAdminSlice from "../../../../../store/slices/admin/viewBranch.admin.slice.ts";
import {
  MdAccessTime,
  MdBusiness,
  MdCalendarToday,
  MdDelete,
  MdEdit,
  MdError,
  MdVpnKey,
} from "react-icons/md";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import addSubBranchAdminSlice from "../../../../../store/slices/admin/addSubBranch.admin.slice.ts";

const ModalViewBranch = memo(() => {
  const dispatch = useAppDispatch();
  const { open, branchView, loading, errorMessage } =
    useAppSelector((state) => state.viewBranchAdmin);
  const { t } = useTranslation();

  const handleClose = useCallback(() => {
    dispatch(viewBranchAdminSlice.actions.close());
  }, [dispatch]);

  const handleEdit = useCallback(() => {
    if (branchView) {
      dispatch(viewBranchAdminSlice.actions.close());
      dispatch(
        addSubBranchAdminSlice.actions.openEdit(branchView)
      );
    }
  }, [dispatch, branchView]);

  const handleDelete = useCallback(() => {
    if (branchView) {
      // Implement delete functionality
      console.log("Delete branch:", branchView.id);
    }
  }, [branchView]);

  if (!branchView && !loading && !errorMessage) return null;

  return (
    <Modal
      title={
        <div className="text-lg font-semibold dark:text-white">
          {t("branch.modal.view.title")}
        </div>
      }
      open={open}
      onCancel={handleClose}
      footer={
        branchView ? (
          <div className="flex gap-2 justify-end">
            <Button
              icon={<MdDelete className="text-lg" />}
              danger
              onClick={handleDelete}
              className="flex items-center"
              disabled
            >
              {t("branch.modal.view.delete")}
            </Button>
            <Button
              type="primary"
              icon={<MdEdit className="text-lg" />}
              onClick={handleEdit}
              className="flex items-center bg-blue-500 hover:bg-blue-600 border-0"
            >
              {t("branch.modal.view.edit")}
            </Button>
          </div>
        ) : null
      }
      className="rounded-xl"
      width={800}
    >
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <Spin size="large" />
        </div>
      ) : errorMessage ? (
        <div className="flex items-center gap-2 text-red-500 py-4">
          <MdError className="text-xl" />
          <span>{errorMessage}</span>
        </div>
      ) : (
        branchView && (
          <div className="mt-4">
            {/* Branch Basic Information */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-4 dark:text-white">
                {t("branch.modal.sections.branchInfo")}
              </h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {t("branch.grid.columns.name")}
                    </p>
                    <p className="text-base font-medium dark:text-white">
                      {branchView.branch_name}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {t("branch.grid.columns.code")}
                    </p>
                    <p className="text-base font-medium dark:text-white">
                      {branchView.branch_code}
                    </p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {t(
                      "branch.modal.form.description.label"
                    )}
                  </p>
                  <p className="text-base dark:text-white">
                    {branchView.branch_description || "-"}
                  </p>
                </div>
              </div>
            </div>

            {/* License Information */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-4 dark:text-white">
                {t("branch.modal.sections.licenseDetails")}
              </h3>
              <div className="flex items-start gap-3">
                <MdVpnKey className="text-xl text-blue-500 mt-0.5" />
                <div>
                  <p className="font-medium dark:text-white">
                    {branchView.license.license_name}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {t("branch.grid.license")}
                  </p>
                </div>
              </div>
            </div>

            {/* Timezone Information */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-4 dark:text-white">
                {t("branch.modal.sections.timezoneConfig")}
              </h3>
              <div className="flex items-start gap-3">
                <MdAccessTime className="text-xl text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium dark:text-white">
                    {branchView.timezone.timezone_name}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {branchView.timezone.gmt_offset}
                  </p>
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div>
              <h3 className="text-lg font-medium mb-4 dark:text-white">
                {t("branch.modal.sections.additionalInfo")}
              </h3>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <MdBusiness className="text-xl text-purple-500 mt-0.5" />
                  <div>
                    <p className="font-medium dark:text-white">
                      {branchView.parent?.branch_name ||
                        "-"}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {t("roles.modal.fields.parentBranch")}
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <MdCalendarToday className="text-xl text-orange-500 mt-0.5" />
                  <div>
                    <p className="font-medium dark:text-white">
                      {dayjs(branchView.created_at).format(
                        "DD MMM YYYY HH:mm"
                      )}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {t("roles.modal.fields.createdAt")}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      )}
    </Modal>
  );
});

ModalViewBranch.displayName = "ModalViewBranch";

export default ModalViewBranch;
