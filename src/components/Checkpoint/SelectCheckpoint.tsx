import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import checkpointAdminMainApi from "../../services/mainApi/admin/checkpoint.admin.mainApi";
import { AdminCheckpoint } from "../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";
import { useParams } from "react-router";

interface SelectCheckpointProps {
  value?: string;
  onChange?: (
    value: string,
    checkpoint?: AdminCheckpoint
  ) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  clearable?: boolean;
}

export default function SelectCheckpoint({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select checkpoint",
  clearable = true,
}: SelectCheckpointProps) {
  const [checkpoints, setCheckpoints] = useState<
    AdminCheckpoint[]
  >([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);
  const { branchCode } = useParams();

  useEffect(() => {
    const fetchCheckpoints = async () => {
      if (isFetching.current || !branchCode) return;

      try {
        isFetching.current = true;
        setLoading(true);
        const response =
          await checkpointAdminMainApi.getCheckpoints(
            branchCode
          );
        setCheckpoints(response.data || []);
      } catch (error) {
        console.error(
          "Failed to fetch checkpoints:",
          error
        );
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchCheckpoints();
  }, [branchCode]);

  const handleChange = (selectedValue: string) => {
    if (!onChange) return;

    const selectedCheckpoint = checkpoints.find(
      (checkpoint) => checkpoint.id === selectedValue
    );

    onChange(selectedValue, selectedCheckpoint);
  };

  return (
    <Select
      className={className}
      placeholder={placeholder}
      value={value}
      onChange={handleChange}
      disabled={disabled}
      loading={loading}
      allowClear={clearable}
      showSearch
      optionFilterProp="label"
      options={checkpoints.map((checkpoint) => ({
        value: checkpoint.id,
        label: checkpoint.checkpoint_name,
      }))}
    />
  );
}
