import { Empty, Pagination, Spin } from "antd";
import { useTranslation } from "react-i18next";
import PicklistListItem from "./PicklistListItem";
import { AdminFormPicklist } from "../../../../services/mainApi/admin/types/formPicklist.admin.mainApi.types";

interface PicklistListProps {
  picklists: AdminFormPicklist[];
  loading: boolean;
  total: number;
  page: number;
  limit: number;
  onPageChange: (page: number, pageSize?: number) => void;
}

const PicklistList = ({
  picklists,
  loading,
  total,
  page,
  limit,
  onPageChange
}: PicklistListProps) => {
  const { t } = useTranslation();

  return (
    <div className="space-y-6">
      {/* Picklists Grid */}
      <div className="grid grid-cols-1 gap-4">
        {loading ? (
          <div className="flex justify-center py-10">
            <Spin size="large" />
          </div>
        ) : picklists.length === 0 ? (
          <Empty
            description={t(
              "formPicklist.noPicklistsFound",
              "No picklists found"
            )}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8"
          />
        ) : (
          picklists.map((picklist) => (
            <PicklistListItem
              key={picklist.id}
              picklist={picklist}
            />
          ))
        )}
      </div>

      {/* Pagination */}
      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <Pagination
          current={page || 1}
          pageSize={limit || 10}
          total={total}
          onChange={onPageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              {t("common.pagination.total", { total })}
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

export default PicklistList;
