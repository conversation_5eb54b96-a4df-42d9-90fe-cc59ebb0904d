import React, { useState } from "react";
import { <PERSON><PERSON>, Tag, Tooltip } from "antd";
import {
  FiAlertTriangle,
  FiClock,
  FiEdit,
} from "react-icons/fi";
import { MdFormatListBulleted } from "react-icons/md";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { AdminFormPicklist } from "../../../../services/mainApi/admin/types/formPicklist.admin.mainApi.types";
import addFormPicklistAdminSlice from "../../../../store/slices/admin/addFormPicklist.admin.slice.ts";
import { useAppDispatch } from "../../../../store/hooks.ts";

interface PicklistListItemProps {
  picklist: AdminFormPicklist;
}

const PicklistListItem: React.FC<PicklistListItemProps> = ({
  picklist,
}) => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const [expandDescription, setExpandDescription] =
    useState(false);

  const handleEdit = () => {
    dispatch(
      addFormPicklistAdminSlice.actions.openUpdate(picklist)
    );
  };

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-xl border transition-all duration-200 hover:shadow-lg ${
        picklist.active
          ? "border-gray-100 dark:border-gray-700"
          : "border-red-100 dark:border-red-900 bg-red-50/10"
      }`}
    >
      <div className="p-5">
        {/* Header Section */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                {picklist.form_picklist_name}
              </h3>
              {!picklist.active && (
                <Tag
                  icon={
                    <FiAlertTriangle className="mr-1" />
                  }
                  className="bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 border-red-200 dark:border-red-800"
                >
                  {t(
                    "formPicklist.list.status.inactive",
                    "Inactive"
                  )}
                </Tag>
              )}
            </div>

            {/* Description with expand/collapse functionality */}
            {picklist.form_picklist_description && (
              <div className="mt-2">
                <p
                  className={`text-sm text-gray-600 dark:text-gray-300 ${
                    !expandDescription ? "line-clamp-2" : ""
                  }`}
                >
                  {picklist.form_picklist_description}
                </p>
                {picklist.form_picklist_description.length >
                  120 && (
                  <button
                    onClick={() =>
                      setExpandDescription(
                        !expandDescription
                      )
                    }
                    className="text-xs text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 mt-1 focus:outline-none"
                  >
                    {expandDescription
                      ? t(
                          "formPicklist.list.showLess",
                          "Show less"
                        )
                      : t(
                          "formPicklist.list.showMore",
                          "Show more"
                        )}
                  </button>
                )}
              </div>
            )}
          </div>

          <Tooltip
            title={t(
              "formPicklist.list.tooltips.edit",
              "Edit picklist"
            )}
          >
            <Button
              type="text"
              icon={<FiEdit className="text-lg" />}
              onClick={handleEdit}
              className="text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400"
            />
          </Tooltip>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          {/* Options List */}
          <div className="p-3 bg-gray-50 dark:bg-gray-800/80 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm text-gray-700 dark:text-gray-300 font-medium flex items-center">
                <MdFormatListBulleted className="mr-2" />
                {t(
                  "formPicklist.sections.options",
                  "Options"
                )}
              </h4>
              <Tag className="bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400">
                {t("formPicklist.list.optionsCount", {
                  count: picklist.options.length,
                })}
              </Tag>
            </div>

            <div className="space-y-2 max-h-40 overflow-y-auto pr-2">
              {picklist.options.map((option) => (
                <div
                  key={option.id}
                  className={`p-2.5 rounded-lg ${
                    option.active
                      ? "bg-white dark:bg-gray-700"
                      : "bg-red-50 dark:bg-red-900/20"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-800 dark:text-gray-200">
                      {option.form_picklist_option_name}
                    </span>
                    {!option.active && (
                      <Tag className="text-xs bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400">
                        {t(
                          "formPicklist.list.status.inactive",
                          "Inactive"
                        )}
                      </Tag>
                    )}
                  </div>
                  {option.form_picklist_option_description && (
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1.5 line-clamp-2">
                      {
                        option.form_picklist_option_description
                      }
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Creation Info */}
          <div className="p-3 bg-gray-50 dark:bg-gray-800/80 rounded-lg">
            <h4 className="text-sm text-gray-700 dark:text-gray-300 font-medium mb-3">
              {t(
                "formPicklist.list.creationInfo",
                "Creation Information"
              )}
            </h4>
            <div className="space-y-3">
              <Tooltip
                title={t(
                  "formPicklist.list.tooltips.createdAt",
                  {
                    time: dayjs(picklist.created_at).format(
                      t("formPicklist.list.timeFormat")
                    ),
                  }
                )}
              >
                <div className="flex items-center gap-2 text-sm">
                  <FiClock className="text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-300">
                    {t(
                      "formPicklist.list.created",
                      "Created"
                    )}{" "}
                    <span className="font-medium">
                      {dayjs(picklist.created_at).format(t("formPicklist.list.timeFormat"))}
                    </span>
                  </span>
                </div>
              </Tooltip>

              <Tooltip
                title={t(
                  "formPicklist.list.tooltips.updatedAt",
                  {
                    time: dayjs(picklist.updated_at).format(
                      t("formPicklist.list.timeFormat")
                    ),
                  }
                )}
              >
                <div className="flex items-center gap-2 text-sm">
                  <FiClock className="text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-300">
                    {t(
                      "formPicklist.list.updated",
                      "Updated"
                    )}{" "}
                    <span className="font-medium">
                      {dayjs(picklist.updated_at).format(t("formPicklist.list.timeFormat"))}
                    </span>
                  </span>
                </div>
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PicklistListItem;
