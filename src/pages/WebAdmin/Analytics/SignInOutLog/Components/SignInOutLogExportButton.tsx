import React, { useState } from "react";
import type { MenuProps } from "antd";
import { <PERSON><PERSON>, Dropdown, Tooltip } from "antd";
import { FiDownload, FiFileText, FiGrid } from "react-icons/fi";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { signInOutLogAdminApi } from "../../../../../services/mainApi/admin/signInOutLog.admin.mainApi";
import { AdminRole } from "../../../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminUser } from "../../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminLabel } from "../../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminDevice } from "../../../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminBranch } from "../../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminSignInOutLogListParams } from "../../../../../services/mainApi/admin/types/signInOutLog.admin.mainApi.types";

interface SignInOutLogExportButtonProps {
  filters: {
    role: AdminRole | null;
    user: AdminUser | null;
    userLabels: AdminLabel[];
    device: AdminDevice | null;
    deviceLabels: AdminLabel[];
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    selectedBranch: AdminBranch | null;
  };
  className?: string;
  buttonText?: string;
  showPdf?: boolean;
  showSpreadsheet?: boolean;
}

type ExportFormat = "pdf" | "spreadsheet";

const SignInOutLogExportButton: React.FC<
  SignInOutLogExportButtonProps
> = ({
  filters,
  className,
  buttonText,
  showPdf = true,
  showSpreadsheet = true,
}) => {
  const { t } = useTranslation();
  const [isDownloading, setIsDownloading] = useState(false);
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  const handleDownload = async (format: ExportFormat) => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const filterParams: AdminSignInOutLogListParams = {
        start_date: filters.startDate || undefined,
        end_date: filters.endDate || undefined,
        start_time: filters.startTime || undefined,
        end_time: filters.endTime || undefined,
        branch_id: filters.selectedBranch?.id || undefined,
        user_id: filters.user?.id || undefined,
        user_labels:
          filters.userLabels
            ?.map((label) => label.id)
            .join(",") || undefined,
        device_id: filters.device?.id || undefined,
        device_labels:
          filters.deviceLabels
            ?.map((label) => label.id)
            .join(",") || undefined,
      };

      const blob =
        await signInOutLogAdminApi.exportSignInOutLogs(
          branchCode,
          filterParams,
          format,
          "buffer"
        );

      // If it's a PDF, open in new tab
      if (format === "pdf") {
        const url = URL.createObjectURL(blob);
        window.open(url, "_blank");

        // Cleanup after delay to ensure the new tab has time to load
        setTimeout(() => {
          URL.revokeObjectURL(url);
        }, 5000);
      } else {
        // For spreadsheet, trigger download
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `sign_in_out_logs_${new Date().toISOString()}.xlsx`;
        link.click();
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Error exporting logs:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  // Define dropdown items
  const items: MenuProps["items"] = [
    ...(showSpreadsheet
      ? [
        {
          key: "spreadsheet",
          icon: <FiGrid className="w-4 h-4" />,
          label: t(
            "signInOutLog.downloadSpreadsheet",
            "Export as Spreadsheet"
          ),
          onClick: () => handleDownload("spreadsheet"),
        },
      ]
      : []),
    ...(showPdf
      ? [
        {
          key: "pdf",
          icon: <FiFileText className="w-4 h-4" />,
          label: t(
            "signInOutLog.downloadPdf",
            "Export as PDF"
          ),
          onClick: () => handleDownload("pdf"),
        },
      ]
      : []),
  ];

  return (
    <Dropdown menu={{ items }} placement="bottomRight">
      <Tooltip
        title={t("common.export", "Export")}
        placement="top"
      >
        <Button
          type="default"
          icon={<FiDownload className="w-4 h-4" />}
          loading={isDownloading}
          className={className}
          size="large"
        >
          {buttonText || t("common.export", "Export")}
        </Button>
      </Tooltip>
    </Dropdown>
  );
};

export default SignInOutLogExportButton;