import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import labelAdminMainApi from "../../services/mainApi/admin/label.admin.mainApi.ts";
import { AdminLabel } from "../../services/mainApi/admin/types/label.admin.mainApi.types.ts";
import { useParams } from "react-router-dom";

interface SelectLabelProps {
  value?: string | string[];
  onChange?: (
    value: string | string[],
    labels: AdminLabel[] | null
  ) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  mode?: "multiple" | "tags";
}

export default function SelectLabel({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select label",
  mode,
}: SelectLabelProps) {
  const [labels, setLabels] = useState<AdminLabel[]>([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);
  const { branchCode } = useParams();

  useEffect(() => {
    const fetchLabels = async () => {
      if (isFetching.current) return;

      try {
        isFetching.current = true;
        setLoading(true);
        const response = await labelAdminMainApi.getLabels(
          branchCode || ""
        );
        setLabels(response.data || []);
      } catch (error) {
        console.error("Failed to fetch labels:", error);
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchLabels();
  }, [branchCode]);

  const handleChange = (
    selectedValues: string | string[]
  ) => {
    if (!onChange) return;

    if (
      !selectedValues ||
      (Array.isArray(selectedValues) &&
        selectedValues.length === 0)
    ) {
      onChange(selectedValues || "", null);
      return;
    }

    if (Array.isArray(selectedValues)) {
      const selectedLabels = labels.filter((label) =>
        selectedValues.includes(label.id)
      );
      onChange(selectedValues, selectedLabels);
    } else {
      const selectedLabel = labels.find(
        (label) => label.id === selectedValues
      );
      onChange(
        selectedValues,
        selectedLabel ? [selectedLabel] : null
      );
    }
  };

  return (
    <Select
      className={className}
      placeholder={placeholder}
      value={value}
      onChange={handleChange}
      disabled={disabled}
      loading={loading}
      showSearch
      mode={mode}
      optionFilterProp="label"
      options={labels.map((label) => ({
        value: label.id,
        label: label.label_name,
      }))}
    />
  );
}
