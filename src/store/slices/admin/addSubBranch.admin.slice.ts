import {
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { AdminBranch } from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";

interface State {
  mode: "create" | "update";
  branchEdit: AdminBranch | null;
  open: boolean;
  loading: boolean;
  errorMessage: string | string[] | null;
  creating: boolean;

  branchName: string;
  branchDescription: string;
  branchLicense: string | null;
  branchTimezone: string | null;
  active: boolean;
}

const initialState: State = {
  mode: "create",
  branchEdit: null,
  open: false,
  loading: false,
  errorMessage: null,
  creating: false,

  branchName: "",
  branchDescription: "",
  branchLicense: null,
  branchTimezone: null,
  active: true,
};

const addSubBranchAdminSlice = createSlice({
  name: "addSubBranchAdmin",
  initialState: {
    ...initialState,
  },
  reducers: {
    open: (state) => {
      state.open = true;
    },
    openEdit: (
      state,
      action: PayloadAction<AdminBranch>
    ) => {
      state.open = true;
      state.mode = "update";
      state.branchEdit = action.payload;
    },
    close: () => {
      return {
        ...initialState,
      };
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setBranchName: (state, action) => {
      state.branchName = action.payload;
    },
    setBranchDescription: (state, action) => {
      state.branchDescription = action.payload;
    },
    setBranchLicense: (state, action) => {
      state.branchLicense = action.payload;
    },
    setBranchTimezone: (state, action) => {
      state.branchTimezone = action.payload;
    },
    setActive: (state, action) => {
      state.active = action.payload;
    },
    setError: (
      state,
      action: PayloadAction<State["errorMessage"]>
    ) => {
      state.errorMessage = action.payload;
    },
  },
});

export default addSubBranchAdminSlice;
