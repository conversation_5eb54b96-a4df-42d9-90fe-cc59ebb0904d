import {
  BaseGetRequest,
  BaseResponse,
} from "../../types/base.mainApi.types";
import { Timezone } from "../../types/timezone.mainApi.types";
import { AdminLabel } from "./label.admin.mainApi.types";

export interface AdminZoneGetRequest
  extends BaseGetRequest {
  status?: string;
  assigned_to_geofence?: boolean;
}

interface ZoneRecipient {
  id: string;
  zone_id: string;
  recipient_type: string;
  recipient_contact: string;
  created_at: string;
  created_by: string;
}

export interface AdminCreateUpdateZoneRequest {
  zone_name: string;
  zone_description: string | null;
  zone_address: string;
  timezone_id: number | string;
  latitude: number;
  longitude: number;
  interval_active: boolean;
  interval_start_time: string;
  interval_end_time: string;
  subject: string | null;
  recipients: {
    recipient_type: string;
    recipient_contact: string;
  }[];
  label_ids: (number | string)[];
}

export interface AdminZone {
  id: string;
  branch_id: string;
  parent_branch_id: string;
  timezone_id: string;
  zone_name: string;
  zone_description: string | null;
  zone_address: string;
  latitude: number;
  longitude: number;
  active: boolean;
  interval_active: boolean | null;
  interval_start_time: string | null;
  interval_end_time: string | null;
  subject: string | null;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
  labels: AdminLabel[];
  timezone: Timezone;
  recipients: ZoneRecipient[];
}

export type AdminZoneListResponse = BaseResponse<
  AdminZone[]
>;
