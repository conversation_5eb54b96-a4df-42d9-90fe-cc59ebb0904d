import { App, Input, Modal, Tabs } from "antd";
import { useCallback, useEffect } from "react";
import {
  CreateUpdateRoleRequest,
  RolePermissionRequest,
} from "../../../../../services/mainApi/admin/types/roles.admin.mainApi.types.ts";
import rolesAdminMainApi from "../../../../../services/mainApi/admin/roles.admin.mainApi.ts";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks.ts";
import { fetchRoles } from "../../../../../store/slices/admin/roles.admin.slice.ts";
import addRoleAdminSlice, {
  fetchDetailEditAdminRole,
  fetchModules,
} from "../../../../../store/slices/admin/addRole.admin.slice.ts";
import PermissionSection from "./PermissionSection";
import { useTranslation } from "react-i18next";

const ModalAddAdminRoles = () => {
  const dispatch = useAppDispatch();
  const { message } = App.useApp();
  const { close, setRoleName, setPermission } =
    addRoleAdminSlice.actions;
  const { t } = useTranslation();

  const {
    open,
    loading,
    submitting,
    availableModules,
    roleName,
    permissions,
    errorMessage,
    mode,
    selectedAdminRole,
  } = useAppSelector((state) => state.addRoleAdmin);

  useEffect(() => {
    if (open) {
      dispatch(fetchModules());
    }
  }, [dispatch, open]);

  useEffect(() => {
    if (
      selectedAdminRole &&
      mode === "update" &&
      open &&
      availableModules.length
    ) {
      if (availableModules.length > 1) {
        dispatch(fetchDetailEditAdminRole());
      }
    }
  }, [
    dispatch,
    selectedAdminRole,
    mode,
    open,
    availableModules.length,
  ]);

  const handleSubmit = async () => {
    if (!roleName.trim()) {
      message.error(t("roles.modal.form.name.required"));
      return;
    }

    try {
      const permissionRequests: RolePermissionRequest[] =
        permissions;

      const payload: CreateUpdateRoleRequest = {
        role_name: roleName,
        permissions: permissionRequests,
      };

      if (mode === "update" && selectedAdminRole) {
        await rolesAdminMainApi.updateRoles(
          selectedAdminRole.id,
          payload
        );
        message.success(t("roles.modal.edit.success"));
      } else {
        await rolesAdminMainApi.createRoles(payload);
        message.success(t("roles.modal.add.success"));
      }

      dispatch(fetchRoles());
      dispatch(close());
    } catch {
      message.error(
        t(
          mode === "update"
            ? "roles.modal.edit.error"
            : "roles.modal.add.error"
        )
      );
    }
  };

  const handlePermissionChange = useCallback(
    (
      moduleCode: string,
      moduleType: "MODULE" | "REPORT",
      permission:
        | "allow_view"
        | "allow_create"
        | "allow_update"
        | "allow_delete",
      value: boolean
    ) => {
      dispatch(
        setPermission({
          moduleCode,
          moduleType,
          permission,
          value,
        })
      );
    },
    [dispatch, setPermission]
  );

  // Memisahkan modules berdasarkan type
  const modulePermissions = availableModules.filter(
    (m) => m.module_type === "MODULE"
  );
  const reportPermissions = availableModules.filter(
    (m) => m.module_type === "REPORT"
  );

  const items = [
    {
      key: "modules",
      label: (
        <span className="flex items-center gap-2">
          {t("roles.modal.permissions.module.label")}
        </span>
      ),
      children: (
        <PermissionSection
          title={t("roles.modal.permissions.module.title")}
          modules={modulePermissions}
          permissions={permissions.filter((p) =>
            modulePermissions.some(
              (m) =>
                m.module_code === p.module_code &&
                p.module_type === "MODULE"
            )
          )}
          onPermissionChange={handlePermissionChange}
        />
      ),
    },
    {
      key: "reports",
      label: (
        <span className="flex items-center gap-2">
          {t("roles.modal.permissions.report.label")}
        </span>
      ),
      children: (
        <PermissionSection
          title={t("roles.modal.permissions.report.title")}
          modules={reportPermissions}
          permissions={permissions.filter((p) =>
            reportPermissions.some(
              (m) =>
                m.module_code === p.module_code &&
                p.module_type === "REPORT"
            )
          )}
          onPermissionChange={handlePermissionChange}
        />
      ),
    },
  ];

  return (
    <Modal
      title={
        <div className="text-lg font-semibold">
          {mode === "update"
            ? t("roles.modal.edit.title")
            : t("roles.modal.add.title")}
        </div>
      }
      open={open}
      onOk={handleSubmit}
      onCancel={() => dispatch(close())}
      confirmLoading={loading || submitting}
      className="rounded-xl"
      style={{
        maxWidth: "98vw",
        margin: "0 auto",
      }}
      okButtonProps={{
        className: "bg-blue-500 hover:bg-blue-600 border-0",
      }}
      width={800}
    >
      <div className="mt-4">
        <div className="mb-6">
          <div className="mb-2">
            <label className="block text-sm font-medium">
              {t("roles.modal.form.name.label")}{" "}
              <span className="text-red-500">*</span>
            </label>
          </div>
          <Input
            placeholder={t(
              "roles.modal.form.name.placeholder"
            )}
            className="rounded-lg"
            onChange={(e) =>
              dispatch(setRoleName(e.target.value))
            }
            value={roleName}
            status={
              !roleName.trim() && errorMessage
                ? "error"
                : ""
            }
          />
          {!roleName.trim() && errorMessage && (
            <div className="text-red-500 text-sm mt-1">
              {t("roles.modal.form.name.required")}
            </div>
          )}
        </div>
        <Tabs
          items={items}
          className="permissions-tabs"
          type="card"
        />
      </div>
    </Modal>
  );
};

ModalAddAdminRoles.displayName = "ModalAddAdminRoles";

export default ModalAddAdminRoles;
