import React, { useState } from "react";
import { <PERSON><PERSON>, Dropdown, Toolt<PERSON> } from "antd";
import { FiDownload } from "react-icons/fi";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { timeRotationAdmin<PERSON>ainApi } from "../../../../../services/mainApi/admin/timeRotation.admin.mainApi";
import { AdminRole } from "../../../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminBranch } from "../../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminZone } from "../../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { AdminCheckpoint } from "../../../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";
import { AdminTimeRotationListParams } from "../../../../../services/mainApi/admin/types/timeRotation.admin.mainApi.types";
import type { MenuProps } from "antd";
import { AdminLabel } from "../../../../../services/mainApi/admin/types/label.admin.mainApi.types";

interface TimeRotationLogExportButtonProps {
  filters: {
    role: AdminRole | null;
    userId: string | null;
    userLabels: AdminLabel[];
    deviceId: string | null;
    deviceLabels: AdminLabel[];
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    selectedBranch: AdminBranch | null;
    selectedZone: AdminZone | null;
    selectedZoneLabels: AdminLabel[];
    selectedCheckpoint: AdminCheckpoint | null;
    selectedCheckpointLabels: AdminLabel[];
    rotationInterval: number | null;
    rotationMethod: string | null;
  };
  className?: string;
  buttonText?: string;
  showPdf?: boolean;
  showSpreadsheet?: boolean;
}

type ExportFormat = "pdf" | "spreadsheet";

const TimeRotationLogExportButton: React.FC<
  TimeRotationLogExportButtonProps
> = ({
  filters,
  className,
  buttonText,
  showPdf = true,
  showSpreadsheet = true,
}) => {
  const { t } = useTranslation();
  const [isDownloading, setIsDownloading] = useState(false);
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  const handleDownload = async (format: ExportFormat) => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const filterParams: AdminTimeRotationListParams = {
        start_date: filters.startDate || undefined,
        end_date: filters.endDate || undefined,
        start_time: filters.startTime || undefined,
        end_time: filters.endTime || undefined,
        branch_id: filters.selectedBranch?.id || undefined,
        zone_id: filters.selectedZone?.id || undefined,
        zone_labels: filters.selectedZoneLabels.length > 0 ? filters.selectedZoneLabels.map(label => label.id).join(',') : undefined,
        checkpoint_id: filters.selectedCheckpoint?.id || undefined,
        checkpoint_labels: filters.selectedCheckpointLabels.length > 0 ? filters.selectedCheckpointLabels.map(label => label.id).join(',') : undefined,
        role_id: filters.role?.id || undefined,
        user_id: filters.userId || undefined,
        user_labels: filters.userLabels.length > 0 ? filters.userLabels.map(label => label.id).join(',') : undefined,
        device_id: filters.deviceId || undefined,
        device_labels: filters.deviceLabels.length > 0 ? filters.deviceLabels.map(label => label.id).join(',') : undefined,
        rotation_interval: filters.rotationInterval || undefined,
        rotation_method: filters.rotationMethod || undefined,
      };

      const blob =
        await timeRotationAdminMainApi.exportTimeRotation(
          branchCode,
          filterParams,
          format,
          "buffer"
        );

      if (format === "pdf") {
        // Create URL and open in new tab
        const url = URL.createObjectURL(blob);
        window.open(url, "_blank");

        // Delay URL revocation to ensure the new tab has time to load
        setTimeout(() => {
          URL.revokeObjectURL(url);
        }, 5000);
      } else {
        // Create URL and trigger download
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `time_rotation_logs_${new Date().toISOString().slice(0, 10)}.xlsx`;
        link.click();

        // Clean up
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error(`Error downloading ${format}:`, error);
    } finally {
      setIsDownloading(false);
    }
  };

  // If only one format is shown, render a simple button
  if (
    (showPdf && !showSpreadsheet) ||
    (!showPdf && showSpreadsheet)
  ) {
    const format = showPdf ? "pdf" : "spreadsheet";
    const tooltipText = showPdf
      ? t("timeRotationLog.downloadPdf", "Download as PDF")
      : t(
          "timeRotationLog.downloadSpreadsheet",
          "Download as Spreadsheet"
        );

    return (
      <Tooltip title={tooltipText}>
        <Button
          icon={<FiDownload />}
          loading={isDownloading}
          onClick={() => handleDownload(format)}
          size="large"
          className={className}
        >
          {buttonText ||
            (showPdf
              ? t("timeRotationLog.pdf", "PDF")
              : t(
                  "timeRotationLog.spreadsheet",
                  "Spreadsheet"
                ))}
        </Button>
      </Tooltip>
    );
  }

  // Otherwise, render a dropdown with both options
  const items: MenuProps["items"] = [
    {
      key: "pdf",
      label: t("timeRotationLog.exportPdf", "Export as PDF"),
      onClick: () => handleDownload("pdf"),
      disabled: !showPdf,
    },
    {
      key: "spreadsheet",
      label: t(
        "timeRotationLog.exportSpreadsheet",
        "Export as Spreadsheet"
      ),
      onClick: () => handleDownload("spreadsheet"),
      disabled: !showSpreadsheet,
    },
  ];

  return (
    <Dropdown
      menu={{ items }}
      disabled={isDownloading}
    >
      <Button
        icon={<FiDownload />}
        loading={isDownloading}
        size="large"
        className={className}
      >
        {buttonText || t("timeRotationLog.export", "Export")}
      </Button>
    </Dropdown>
  );
};

export default TimeRotationLogExportButton;
