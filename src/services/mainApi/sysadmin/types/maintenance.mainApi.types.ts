export interface Maintenance {
    id: string;
    is_maintenance: boolean;
    description: string;
    updated_at: string;
    updated_by: string | null;
}

export interface MaintenanceListParams {
    search?: string;
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
    order_by?: string;
    order_direction?: "asc" | "desc";
}

export interface MaintenanceListResponseMeta {
  timestamp: string;
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface MaintenanceListResponse {
  data: Maintenance[];
  meta: MaintenanceListResponseMeta;
}

export interface UpdateMaintenanceRequest {
  is_maintenance: boolean;
  description: string;
}

export interface UpdateMaintenanceResponse {
  data: Maintenance;
  meta: {
    timestamp: string;
  };
}