import { Empty, Pagination, Spin } from "antd";
import FormListItem from "./FormListItem";
import { useTranslation } from "react-i18next";
import { AdminForm } from "../../../../services/mainApi/admin/types/form.admin.mainApi.types";
import { BasePaginationMeta } from "../../../../services/mainApi/types/base.mainApi.types";
import ModalAddFormAdmin from "./ModalAddForm/ModalAddFormAdmin";

interface FormsListProps {
  forms: AdminForm[];
  loading: boolean;
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
    ignoreActiveStatus: boolean;
  };
  pagination: BasePaginationMeta;
  onPageChange: (page: number, pageSize?: number) => void;
}

const FormsList = ({
  forms,
  loading,
  filter,
  pagination,
  onPageChange
}: FormsListProps) => {
  const { t } = useTranslation();

  return (
    <div>
      <div className="grid grid-cols-1 gap-4">
        {loading ? (
          <div className="flex justify-center py-10">
            <Spin size="large" />
          </div>
        ) : forms.length === 0 ? (
          <Empty
            description={t(
              "forms.noFormsFound",
              "No forms found"
            )}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8"
          />
        ) : (
          forms.map((form) => (
            <FormListItem
              key={form.id}
              form={form}
            />
          ))
        )}
      </div>

      {forms.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden mt-4">
          <Pagination
            current={filter.page || 1}
            pageSize={filter.limit || 10}
            total={pagination.total || 0}
            onChange={onPageChange}
            showSizeChanger={false}
            showTotal={(total) => (
              <span className="text-gray-500 dark:text-gray-400">
                {t("common.total")} {total}{" "}
                {t("common.items")}
              </span>
            )}
            className="flex justify-end p-4"
          />
        </div>
      )}
      <ModalAddFormAdmin />
    </div>
  );
};

export default FormsList;
