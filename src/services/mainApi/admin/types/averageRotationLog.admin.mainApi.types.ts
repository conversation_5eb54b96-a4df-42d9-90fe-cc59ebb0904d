import { BaseResponse } from "../../types/base.mainApi.types";

export interface AdminAverageRotationLogEntry {
  checkpoint_id: string;
  checkpoint_name: string;
  zone_id: string;
  zone_name: string;
  serial_number: string;
  major_value: string;
  minor_value: string;
  is_beacon: boolean;
  scan_count: number;
  period_minutes: number;
  avg_rotation_minutes: number;
  first_scan: string;
  last_scan: string;
}

export interface AdminAverageRotationLogListParams {
  page?: number;
  limit?: number;
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  zone_id?: string | number;
  branch_id?: string | number;
  zone_labels?: string | number | (string | number)[];
  order_by?: string;
  order_direction?: "ASC" | "DESC";
}

export type AdminAverageRotationLogListResponse = BaseResponse<
  AdminAverageRotationLogEntry[]
>;
