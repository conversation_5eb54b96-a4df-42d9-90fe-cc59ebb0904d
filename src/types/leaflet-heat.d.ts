// Type definitions for leaflet.heat
// Project: https://github.com/Leaflet/Leaflet.heat
// Definitions by: <PERSON><PERSON>

import * as L from 'leaflet';

declare module 'leaflet' {
  namespace HeatLayer {
    interface HeatLayerOptions {
      minOpacity?: number;
      maxZoom?: number;
      max?: number;
      radius?: number;
      blur?: number;
      gradient?: {[key: number]: string};
      pane?: string;
    }
  }

  class HeatLayer extends L.Layer {
    constructor(latlngs: Array<[number, number] | [number, number, number] | L.LatLng>, options?: HeatLayer.HeatLayerOptions);
    setOptions(options: HeatLayer.HeatLayerOptions): this;
    addLatLng(latlng: L.LatLng | [number, number] | [number, number, number]): this;
    setLatLngs(latlngs: Array<L.LatLng | [number, number] | [number, number, number]>): this;
    redraw(): this;
  }

  function heatLayer(latlngs: Array<[number, number] | [number, number, number] | L.LatLng>, options?: HeatLayer.HeatLayerOptions): HeatLayer;
}

declare module 'leaflet.heat' {
  export = L;
}
