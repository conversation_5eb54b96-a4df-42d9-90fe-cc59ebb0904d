import { Navigate, Route, RouteProps } from "react-router-dom";
import { routeConfig } from "./routes.tsx";

export const getNotFoundRoutes = (): React.ReactElement<RouteProps>[] => {
  return [
    // 404 page
    <Route
      key="404"
      path={routeConfig.notFound.path}
      element={routeConfig.notFound.component}
    />,
    <Route
      key="catchAll"
      path="*"
      element={<Navigate to={routeConfig.notFound.path} replace />}
    />
  ];
}; 