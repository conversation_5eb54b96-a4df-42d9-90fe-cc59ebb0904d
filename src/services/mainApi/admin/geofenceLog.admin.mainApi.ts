import baseMainApi from "../base.mainApi";
import {
  AdminGeofenceLogListResponse,
  AdminGeofenceLogListParams,
} from "./types/geofenceLog.admin.mainApi.types";

export const geofenceLogAdminApi = {
  getGeofenceLogs: async (
    branchCode: string,
    params?: AdminGeofenceLogListParams
  ) => {
    const response =
      await baseMainApi.get<AdminGeofenceLogListResponse>(
        `/web-api/admin/${branchCode}/geofence-log`,
        { params }
      );
    return response.data;
  },

  exportGeofenceLogs: async (
    branchCode: string,
    params: AdminGeofenceLogListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/geofence-log/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
};
