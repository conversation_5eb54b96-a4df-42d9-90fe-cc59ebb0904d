import {
  Navigate,
  Outlet,
  useParams,
} from "react-router-dom";
import { useAuth } from "../../hooks/useAuth";
import { Spin } from "antd";

interface ProtectedRouteProps {
  guard: "sysadmin" | "admin";
}

export const ProtectedRoute = ({
  guard,
}: ProtectedRouteProps) => {
  const auth = useAuth();
  const { branchCode } = useParams();

  if (auth.loading) {
    return (
      <div className="h-screen w-screen flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  if (!auth.isAuthenticated) {
    return (
      <Navigate
        to="/login"
        replace
      />
    );
  }

  if (guard === "sysadmin" && !auth.hasSystemAccess) {
    return (
      <Navigate
        to="/login"
        replace
      />
    );
  }

  if (guard === "admin" && !auth.hasWebAccess) {
    return (
      <Navigate
        to="/login"
        replace
      />
    );
  }

  if (
    branchCode &&
    guard === "admin" &&
    auth.hasWebAccess &&
    !auth.userData?.current_branch
  ) {
    return (
      <Navigate
        to="/admin/branch"
        replace
      />
    );
  }

  return <Outlet />;
};
