import { Button, Input, Select } from "antd";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {
  PlusOutlined,
  SearchOutlined,
  SortAscendingOutlined,
  EnvironmentOutlined,
} from "@ant-design/icons";
import { MdDevices } from "react-icons/md";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import { useTranslation } from "react-i18next";
import DeviceList from "./Device/DeviceList";
import { useCallback, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import deviceAdminSlice, {
  fetchDevicesAdmin,
} from "../../../store/slices/admin/device.admin.slice";
import deviceModalSlice from "../../../store/slices/admin/deviceModal.admin.slice";
import { debounce } from "lodash";
import { useParams } from "react-router-dom";
import GpsTrackingIntervalModal from "./Components/GpsTrackingIntervalModal";
import updateGpsTrackingIntervalAdminSlice from "../../../store/slices/admin/updateGpsTrackingInterval.admin.slice";

const { Option } = Select;

export default function DeviceAdmin() {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const { filter } = useSelector(
    (state: RootState) => state.deviceAdmin
  );
  const isLoading = useRef(false);
  const debouncedSearch = useRef(
    debounce(() => {
      fetch();
    }, 500)
  );

  const { actions: deviceAdminActions } = deviceAdminSlice;
  const { actions: deviceModalActions } = deviceModalSlice;
  const { actions: gpsTrackingIntervalActions } = updateGpsTrackingIntervalAdminSlice;

  const fetch = useCallback(async () => {
    if (!branchCode || isLoading.current) return;
    isLoading.current = true;
    await dispatch(fetchDevicesAdmin(branchCode));
    isLoading.current = false;
  }, [dispatch, branchCode]);

  const handleSearch = (value: string) => {
    dispatch(
      deviceAdminActions.setFilter({
        search: value || null,
        page: 1,
      })
    );
    debouncedSearch.current();
  };

  const handleAdd = () => {
    dispatch(deviceModalActions.openCreateModal());
  };

  const handleTracking = () => {
    if (branchCode) {
      dispatch(gpsTrackingIntervalActions.setOpen(true));
    }
  };

  const handleSortChange = (value: string) => {
    dispatch(
      deviceAdminActions.setFilter({
        orderBy: value,
      })
    );
  };

  const handleSortDirectionToggle = () => {
    dispatch(
      deviceAdminActions.setFilter({
        orderDirection:
          filter.orderDirection === "ASC" ? "DESC" : "ASC",
      })
    );
  };

  return (
    <WebAdminLayout activePage="device">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdDevices className="text-2xl" />}
            title={t("device.title")}
            description={t(
              "device.description",
              "Kelola perangkat Anda"
            )}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
              >
                {t("device.addButton")}
              </Button>,
              <Button
                key="tracking"
                type="primary"
                icon={<EnvironmentOutlined />}
                onClick={handleTracking}
                size="large"
                className="bg-green-500 hover:bg-green-600"
              >
                {t("device.trackingButton", "Tracking")}
              </Button>,
            ]}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <Input
                placeholder={t(
                  "device.search.placeholder",
                  "Cari perangkat..."
                )}
                prefix={
                  <SearchOutlined className="text-gray-400" />
                }
                onChange={(e) =>
                  handleSearch(e.target.value)
                }
                value={filter.search || ""}
                size="large"
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
              />
              <div className="flex gap-2">
                <Select
                  value={filter.orderBy}
                  size="large"
                  style={{ minWidth: 120 }}
                  onChange={handleSortChange}
                  className="dark:bg-gray-700"
                >
                  <Option value="created_at">
                    {t(
                      "device.sort.createdAt",
                      "Tanggal Dibuat"
                    )}
                  </Option>
                  <Option value="device_name">
                    {t("device.search.sortOptions.name")}
                  </Option>
                </Select>
                <Button
                  icon={
                    <SortAscendingOutlined
                      rotate={
                        filter.orderDirection === "DESC"
                          ? 180
                          : 0
                      }
                    />
                  }
                  size="large"
                  className="dark:border-gray-600 dark:text-gray-300"
                  onClick={handleSortDirectionToggle}
                />
              </div>
            </div>
          </div>

          {/* Device List */}
          <DeviceList />
          
          {/* GPS Tracking Interval Modal */}
          <GpsTrackingIntervalModal />
        </div>
      </div>
    </WebAdminLayout>
  );
}
