import {
  Modal,
  Form,
  Input,
  Switch,
  Button,
  message,
  Divider,
} from "antd";
import { useTranslation } from "react-i18next";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks.ts";
import addActivityAdminSlice, {
  createActivity,
  updateActivity,
} from "../../../../../store/slices/admin/addActivity.admin.slice.ts";
import { useParams } from "react-router-dom";
import { fetchActivities } from "../../../../../store/slices/admin/activity.admin.slice.ts";

const ModalAddActivity = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const { loading } = useAppSelector(
    (state) => state.activityAdmin
  );
  const modal = useAppSelector(
    (state) => state.addActivityAdmin
  );
  const actions = addActivityAdminSlice.actions;

  const onClose = () => {
    dispatch(actions.close());
  };

  const handleSubmit = async () => {
    if (!modal.activity_name.trim()) {
      message.error(t("activity.form.nameRequired"));
      return;
    }

    if (!modal.activity_description.trim()) {
      message.error(t("activity.form.descriptionRequired"));
      return;
    }

    try {
      let result;

      if (modal.mode === "update") {
        result = await dispatch(
          updateActivity(branchCode || "")
        );
      } else {
        result = await dispatch(
          createActivity(branchCode || "")
        );
      }

      if (result.meta.requestStatus === "fulfilled") {
        message.success(
          t(
            modal.mode === "update"
              ? "activity.modal.edit.success"
              : "activity.modal.add.success"
          )
        );
        dispatch(
          fetchActivities({ branchCode: branchCode || "" })
        );
        onClose();
      } else if (result.meta.requestStatus === "rejected") {
        message.error(
          t(
            modal.mode === "update"
              ? "activity.modal.edit.error"
              : "activity.modal.add.error"
          )
        );
      }
    } catch (error) {
      console.log(error);
      message.error(
        t(
          modal.mode === "update"
            ? "activity.modal.edit.error"
            : "activity.modal.add.error"
        )
      );
    }
  };

  return (
    <Modal
      title={
        modal.mode === "update"
          ? t("activity.modal.edit.title")
          : t("activity.modal.add.title")
      }
      open={modal.open}
      onCancel={onClose}
      footer={null}
      destroyOnClose
    >
      <Form
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          label={t("activity.form.name")}
          required
        >
          <Input
            value={modal.activity_name}
            onChange={(e) =>
              dispatch(
                actions.setActivityName(e.target.value)
              )
            }
          />
        </Form.Item>

        <Form.Item
          label={t("activity.form.description")}
          required
        >
          <Input.TextArea
            rows={4}
            value={modal.activity_description}
            onChange={(e) =>
              dispatch(
                actions.setActivityDescription(
                  e.target.value
                )
              )
            }
          />
        </Form.Item>

        <Divider
          orientation="left"
          className="text-gray-500 dark:text-gray-400"
        >
          {t("activity.form.requirements")}
        </Divider>

        <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg space-y-4">
          <Form.Item
            label={t("activity.form.gpsRequired")}
            className="mb-0"
          >
            <Switch
              checked={modal.gps_required}
              onChange={(checked) =>
                dispatch(actions.setGpsRequired(checked))
              }
            />
          </Form.Item>

          <Form.Item
            label={t("activity.form.photoRequired")}
            className="mb-0"
          >
            <Switch
              checked={modal.photo_required}
              onChange={(checked) =>
                dispatch(actions.setPhotoRequired(checked))
              }
            />
          </Form.Item>

          <Form.Item
            label={t("activity.form.commentRequired")}
            className="mb-0"
          >
            <Switch
              checked={modal.comment_required}
              onChange={(checked) =>
                dispatch(
                  actions.setCommentRequired(checked)
                )
              }
            />
          </Form.Item>
        </div>

        {modal.mode === "update" && (
          <>
            <Divider
              orientation="left"
              className="text-gray-500 dark:text-gray-400"
            >
              {t("activity.form.status")}
            </Divider>

            <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
              <Form.Item
                label={t("activity.form.active")}
                className="mb-0"
              >
                <Switch
                  checked={modal.active}
                  onChange={(checked) =>
                    dispatch(actions.setActive(checked))
                  }
                />
              </Form.Item>
            </div>
          </>
        )}

        <Form.Item className="mb-0 text-right mt-6">
          <Button
            onClick={onClose}
            className="mr-2"
          >
            {t("common.cancel")}
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
          >
            {modal.mode === "update"
              ? t("common.update")
              : t("common.create")}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ModalAddActivity;
