import React, { useState } from "react";
import { <PERSON><PERSON>, Dropdown, Tooltip } from "antd";
import { FiDownload } from "react-icons/fi";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { batteryLevelLogAdminApi } from "../../../../../services/mainApi/admin/batteryLevelLog.admin.mainApi";
import { AdminBranch } from "../../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminCheckpoint } from "../../../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";
import { AdminZone } from "../../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { AdminLabel } from "../../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import type { MenuProps } from "antd";

interface CheckpointBatteryLogExportButtonProps {
  filters: {
    startDate: string | null;
    endDate: string | null;
    selectedBranch: AdminBranch | null;
    site: AdminZone | null;
    siteLabels: AdminLabel[];
    checkpoint: AdminCheckpoint | null;
    checkpointLabels: AdminLabel[];
  };
  className?: string;
  buttonText?: string;
  showPdf?: boolean;
  showSpreadsheet?: boolean;
}

type ExportFormat = "pdf" | "spreadsheet";

const CheckpointBatteryLogExportButton: React.FC<
  CheckpointBatteryLogExportButtonProps
> = ({
  filters,
  className,
  buttonText,
  showPdf = true,
  showSpreadsheet = true,
}) => {
  const { t } = useTranslation();
  const [isDownloading, setIsDownloading] = useState(false);
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  const handleDownload = async (format: ExportFormat) => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const filterParams: Record<string, string | string[] | number | number[]> = {};

      if (filters.startDate) filterParams.start_date = filters.startDate;
      if (filters.endDate) filterParams.end_date = filters.endDate;
      if (filters.selectedBranch?.id) filterParams.branch_id = filters.selectedBranch.id;
      if (filters.site?.id) filterParams.site_id = filters.site.id;
      if (filters.siteLabels?.length > 0) 
        filterParams.site_labels = filters.siteLabels.map(label => label.id).join(",");
      if (filters.checkpoint?.id) filterParams.checkpoint_id = filters.checkpoint.id;
      if (filters.checkpointLabels?.length > 0) 
        filterParams.checkpoint_labels = filters.checkpointLabels.map(label => label.id).join(",");

      const blob = await batteryLevelLogAdminApi.exportBatteryLevelLogs(
        branchCode,
        filterParams,
        format,
        "buffer"
      );

      if (format === "pdf") {
        // Create URL and open in new tab
        const url = URL.createObjectURL(blob);
        window.open(url, "_blank");

        // Delay URL revocation to ensure the new tab has time to load
        setTimeout(() => {
          URL.revokeObjectURL(url);
        }, 5000);
      } else {
        // Create URL and trigger download
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `battery_level_report_${new Date().toISOString().slice(0, 10)}.xlsx`;
        link.click();

        // Clean up
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error(`Error downloading ${format}:`, error);
    } finally {
      setIsDownloading(false);
    }
  };

  // If only one format is shown, render a simple button
  if (
    (showPdf && !showSpreadsheet) ||
    (!showPdf && showSpreadsheet)
  ) {
    const format = showPdf ? "pdf" : "spreadsheet";
    const tooltipText = showPdf
      ? t("export.downloadPdf", "Download as PDF")
      : t(
          "export.downloadSpreadsheet",
          "Download as Spreadsheet"
        );

    return (
      <Tooltip title={tooltipText}>
        <Button
          icon={<FiDownload />}
          loading={isDownloading}
          onClick={() => handleDownload(format)}
          size="large"
          className={className}
        >
          {buttonText ||
            (showPdf
              ? t("export.pdf", "PDF")
              : t(
                  "export.spreadsheet",
                  "Spreadsheet"
                ))}
        </Button>
      </Tooltip>
    );
  }

  // Otherwise, render a dropdown with both options
  const items: MenuProps["items"] = [
    {
      key: "pdf",
      label: t("export.exportPdf", "Export as PDF"),
      onClick: () => handleDownload("pdf"),
      disabled: !showPdf,
    },
    {
      key: "spreadsheet",
      label: t(
        "export.exportSpreadsheet",
        "Export as Spreadsheet"
      ),
      onClick: () => handleDownload("spreadsheet"),
      disabled: !showSpreadsheet,
    },
  ];

  return (
    <Dropdown
      menu={{ items }}
      disabled={isDownloading}
    >
      <Button
        icon={<FiDownload />}
        loading={isDownloading}
        size="large"
        className={className}
      >
        {buttonText || t("export.export", "Export")}
      </Button>
    </Dropdown>
  );
};

export default CheckpointBatteryLogExportButton; 