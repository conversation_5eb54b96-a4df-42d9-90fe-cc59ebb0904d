import { Card, Skeleton } from "antd";

export function LicenseSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
      {[...Array(6)].map((_, index) => (
        <Card
          key={index}
          className="shadow-sm bg-white dark:bg-gray-800 dark:border-gray-700 rounded-lg h-full"
          bodyStyle={{ padding: '20px' }}
          actions={[
            <Skeleton.Button active key="1" size="small" />,
          ]}
        >
          <div className="space-y-4">
            {/* Header skeleton */}
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <Skeleton.Input active size="small" style={{ width: 150 }} />
                <div className="mt-2">
                  <Skeleton.Input active size="small" style={{ width: 100 }} />
                </div>
              </div>
              <Skeleton.Button active size="small" shape="circle" />
            </div>

            {/* Status tag skeleton */}
            <Skeleton.Button active size="small" style={{ width: 80, height: 24 }} />

            {/* Content skeleton */}
            <div className="grid grid-cols-2 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="flex flex-col space-y-1">
                  <Skeleton.Input active size="small" style={{ width: 80 }} />
                  <Skeleton.Input active size="small" style={{ width: 30 }} />
                </div>
              ))}
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}