import {createAsyncThunk, createSlice, PayloadAction,} from "@reduxjs/toolkit";
import {AdminForm, CreateFormRequest,} from "../../../services/mainApi/admin/types/form.admin.mainApi.types";
import {FieldType} from "../../../types/FieldType.enum";
import {v4 as uuidv4} from "uuid";
import {RootState} from "../..";
import formAdminMainApi from "../../../services/mainApi/admin/form.admin.mainApi";
import {isAxiosError} from "axios";
import {handleApiError} from "../../../utils/error";

interface FormField {
  rowId: string;
  field_type_id: FieldType;
  form_field_name: string;
  form_field_description: string | null;
  form_field_require: boolean;
  form_picklist_id: string | null;
  active: boolean;
}

interface State {
  open: boolean;
  mode: "create" | "update";
  loading: boolean;
  submitting: boolean;
  errorMessage: string[] | null;
  selectedForm: AdminForm | null;

  // Form fields
  form_name: string;
  form_description: string | null;
  role_id: string | null;
  branch_ids: string[];
  active: boolean;
  checkpoint_id: string | null;
  fields: FormField[];
}

const initialState: State = {
  open: false,
  mode: "create",
  loading: false,
  submitting: false,
  errorMessage: null,
  selectedForm: null,

  form_name: "",
  form_description: "",
  role_id: null,
  branch_ids: [],
  active: true,
  checkpoint_id: null,
  fields: [],
};

export const createFormAdmin = createAsyncThunk(
  "addFormAdmin/createForm",
  async (
    branchCode: string,
    {getState, rejectWithValue}
  ) => {
    const state = getState() as RootState;
    const addFormState = state.addFormAdmin;

    const payload: CreateFormRequest = {
      form_name: addFormState.form_name,
      form_description: addFormState.form_description,
      role_id: addFormState.role_id || null,
      branch_ids: addFormState.branch_ids,
      active: addFormState.active,
      checkpoint_id: addFormState.checkpoint_id || null,
      fields: addFormState.fields.map((field) => ({
        field_type_id: field.field_type_id,
        form_field_name: field.form_field_name,
        form_field_description:
        field.form_field_description,
        form_field_require: field.form_field_require,
        form_picklist_id: field.form_picklist_id || null,
        active: field.active,
      })),
    };

    try {
      const response = await formAdminMainApi.createForm(
        branchCode,
        payload
      );
      return response.data;
    } catch (e) {
      if (isAxiosError(e)) {
        const {errors} = handleApiError(e);
        return rejectWithValue(errors);
      }
      return rejectWithValue("Failed to create form");
    }
  }
);

export const updateFormAdmin = createAsyncThunk(
  "addFormAdmin/updateForm",
  async (
    branchCode: string,
    {getState, rejectWithValue}
  ) => {
    const state = getState() as RootState;
    const addFormState = state.addFormAdmin;

    if (!addFormState.selectedForm) {
      return rejectWithValue([
        "No form selected for update",
      ]);
    }

    const payload: CreateFormRequest = {
      form_name: addFormState.form_name,
      form_description: addFormState.form_description,
      role_id: addFormState.role_id || null,
      branch_ids: addFormState.branch_ids,
      active: addFormState.active,
      checkpoint_id: addFormState.checkpoint_id || null,
      fields: addFormState.fields.map((field) => ({
        field_type_id: field.field_type_id,
        form_field_name: field.form_field_name,
        form_field_description:
        field.form_field_description,
        form_field_require: field.form_field_require,
        form_picklist_id: field.form_picklist_id || null,
        active: field.active,
      })),
    };

    try {
      const response = await formAdminMainApi.updateForm(
        branchCode,
        addFormState.selectedForm.id,
        payload
      );
      return response.data;
    } catch (e) {
      if (isAxiosError(e)) {
        const {errors} = handleApiError(e);
        return rejectWithValue(errors);
      }
      return rejectWithValue("Failed to update form");
    }
  }
);

const addFormAdminSlice = createSlice({
  name: "addFormAdmin",
  initialState: {...initialState},
  reducers: {
    onClose: () => {
      return {...initialState};
    },
    setError: (
      state,
      action: PayloadAction<string[] | null>
    ) => {
      state.errorMessage = action.payload;
    },
    open: (state) => {
      state.open = true;
      state.mode = "create";
    },
    openUpdate: (
      state,
      action: PayloadAction<AdminForm>
    ) => {
      state.open = true;
      state.mode = "update";
      state.selectedForm = action.payload;
      state.errorMessage = null;

      // Populate form fields
      state.form_name = action.payload.form_name;
      state.form_description =
        action.payload.form_description || "";
      state.role_id = action.payload.role_id;
      state.branch_ids = action.payload.branches.map(
        (branch) => branch.id
      );
      state.active = action.payload.active;
      state.checkpoint_id = action.payload.checkpoint_id;
      state.fields = action.payload.fields.map((field) => {
        const id = uuidv4();
        return {
          rowId: id,
          field_type_id: field.field_type_id,
          form_field_name: field.form_field_name,
          form_field_description:
            field.form_field_description || "",
          form_field_require: field.form_field_require,
          form_picklist_id: field.form_picklist_id,
          active: field.active,
        };
      });
    },
    close: () => {
      return {
        ...initialState,
      };
    },
    setName: (state, action: PayloadAction<string>) => {
      state.form_name = action.payload;
    },
    setDescription: (
      state,
      action: PayloadAction<string>
    ) => {
      state.form_description = action.payload;
    },
    setRole: (state, action: PayloadAction<string>) => {
      state.role_id = action.payload;
    },
    setBranches: (
      state,
      action: PayloadAction<string[]>
    ) => {
      state.branch_ids = action.payload;
    },
    setActive: (state, action: PayloadAction<boolean>) => {
      state.active = action.payload;
    },
    setCheckpointId: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.checkpoint_id = action.payload;
    },

    addField: (state) => {
      const id = uuidv4();
      state.fields.push({
        rowId: id,
        field_type_id: FieldType.INPUT,
        form_field_name: "",
        form_field_description: "",
        form_field_require: false,
        form_picklist_id: null,
        active: true,
      });
    },
    removeField: (state, action: PayloadAction<string>) => {
      state.fields = state.fields.filter(
        (field) => field.rowId !== action.payload
      );
    },
    setFieldType: (
      state,
      action: PayloadAction<{
        rowId: string;
        field_type_id: FieldType;
      }>
    ) => {
      const {rowId, field_type_id} = action.payload;
      const field = state.fields.find(
        (field) => field.rowId === rowId
      );
      if (field) {
        field.field_type_id = field_type_id;
        if (field_type_id !== FieldType.SELECT) {
          field.form_picklist_id = null;
        }
      }
    },
    setFieldName: (
      state,
      action: PayloadAction<{
        rowId: string;
        form_field_name: string;
      }>
    ) => {
      const {rowId, form_field_name} = action.payload;
      const field = state.fields.find(
        (field) => field.rowId === rowId
      );
      if (field) {
        field.form_field_name = form_field_name;
      }
    },
    setFieldDescription: (
      state,
      action: PayloadAction<{
        rowId: string;
        form_field_description: string;
      }>
    ) => {
      const {rowId, form_field_description} =
        action.payload;
      const field = state.fields.find(
        (field) => field.rowId === rowId
      );
      if (field) {
        field.form_field_description =
          form_field_description;
      }
    },
    setFieldRequired: (
      state,
      action: PayloadAction<{
        rowId: string;
        form_field_require: boolean;
      }>
    ) => {
      const {rowId, form_field_require} = action.payload;
      const field = state.fields.find(
        (field) => field.rowId === rowId
      );
      if (field) {
        field.form_field_require = form_field_require;
      }
    },
    setFieldPicklist: (
      state,
      action: PayloadAction<{
        rowId: string;
        form_picklist_id: string | null;
      }>
    ) => {
      const {rowId, form_picklist_id} = action.payload;
      const field = state.fields.find(
        (field) => field.rowId === rowId
      );
      if (field) {
        field.form_picklist_id = form_picklist_id;
      }
    },
    setFieldActive: (
      state,
      action: PayloadAction<{
        rowId: string;
        active: boolean;
      }>
    ) => {
      const {rowId, active} = action.payload;
      const field = state.fields.find(
        (field) => field.rowId === rowId
      );
      if (field) {
        field.active = active;
      }
    },
  },
  extraReducers: (builder) => {
    // Create form cases
    builder
      .addCase(createFormAdmin.pending, (state) => {
        state.submitting = true;
        state.errorMessage = null;
      })
      .addCase(createFormAdmin.fulfilled, (state) => {
        state.submitting = false;
      })
      .addCase(
        createFormAdmin.rejected,
        (state, action) => {
          state.submitting = false;
          state.errorMessage = action.payload as string[];
        }
      )

      // Update form cases
      .addCase(updateFormAdmin.pending, (state) => {
        state.submitting = true;
        state.errorMessage = null;
      })
      .addCase(updateFormAdmin.fulfilled, (state) => {
        state.submitting = false;
      })
      .addCase(
        updateFormAdmin.rejected,
        (state, action) => {
          state.submitting = false;
          state.errorMessage = action.payload as string[];
        }
      );
  },
});

export default addFormAdminSlice;
