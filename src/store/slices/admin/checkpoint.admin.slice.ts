import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import checkpointAdminMainApi from "../../../services/mainApi/admin/checkpoint.admin.mainApi";
import { AdminCheckpoint } from "../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";
import {
  BaseGetRequest,
  BasePaginationMeta,
} from "../../../services/mainApi/types/base.mainApi.types";

interface State {
  checkpoints: AdminCheckpoint[];
  loading: boolean;
  errorMessage: string | null;
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;
}

const initState: State = {
  checkpoints: [],
  loading: false,
  errorMessage: null,
  filter: {
    search: null,
    page: 1,
    limit: 10,
    orderBy: "created_at",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    total_pages: 0,
  },
};

export const fetchCheckpointAdmin = createAsyncThunk<
  { data: AdminCheckpoint[]; meta: BasePaginationMeta },
  string,
  { state: RootState; rejectValue: string }
>(
  "checkpointAdmin/fetchCheckpoints",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const { filter } = state.checkpointAdmin;
      const params: BaseGetRequest = {
        search: filter.search || undefined,
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
      };

      const response =
        await checkpointAdminMainApi.getCheckpoints(
          branchCode,
          params
        );
      return {
        data: response.data || [],
        meta: response.meta,
      };
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue("Failed to fetch checkpoints");
    }
  }
);

const checkpointAdminSlice = createSlice({
  name: "checkpointAdmin",
  initialState: { ...initState },
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCheckpointAdmin.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchCheckpointAdmin.fulfilled,
        (state, action) => {
          state.loading = false;
          state.checkpoints = action.payload.data;
          state.pagination = action.payload.meta;
        }
      )
      .addCase(
        fetchCheckpointAdmin.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      );
  },
});

export const { setFilter } = checkpointAdminSlice.actions;
export default checkpointAdminSlice;
