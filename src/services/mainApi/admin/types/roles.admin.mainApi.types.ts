import { BaseResponse } from "../../types/base.mainApi.types";

export interface Module {
  id: string;
  module_type: "MODULE" | "REPORT";
  module_code: string;
  module_name: string;
  module_description: string;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

interface RolePermission {
  id: string;
  role_id: string;
  module_id: string;
  allow_create: boolean;
  allow_update: boolean;
  allow_view: boolean;
  allow_delete: boolean;
  module: Module;
}

interface ParentBranch {
  id: string;
  parent_id: string;
  branch_name: string;
  branch_description: string;
  license_id: string;
  timezone_id: string;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
  branch_code: string;
}

export interface AdminRole {
  id: string;
  parent_branch_id: string;
  role_name: string;
  super_admin: boolean;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

export interface AdminRoleDetail extends AdminRole {
  parent_branch: ParentBranch;
  permissions: RolePermission[];
}

export type AdminRoleListResponse = BaseResponse<
  AdminRole[]
>;
export type AdminRoleDetailResponse =
  BaseResponse<AdminRoleDetail>;

export interface RolePermissionRequest {
  module_code: string;
  module_type: string;
  allow_create: boolean;
  allow_update: boolean;
  allow_view: boolean;
  allow_delete: boolean;
}

export interface CreateUpdateRoleRequest {
  role_name: string;
  permissions: RolePermissionRequest[];
}
