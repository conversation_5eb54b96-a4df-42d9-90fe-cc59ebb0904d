import baseMainApi from "../base.mainApi";
import {
  AdminAlarmLogListParams,
  AdminAlarmLogListResponse,
} from "./types/alarmLog.admin.mainApi.types";

export const alarmLogAdminApi = {
  getAlarmLogs: async (
    branchCode: string,
    params?: AdminAlarmLogListParams
  ) => {
    const response =
      await baseMainApi.get<AdminAlarmLogListResponse>(
        `/web-api/admin/${branchCode}/alarm-log`,
        { params }
      );
    return response.data;
  },

  exportAlarmLogs: async (
    branchCode: string,
    params: AdminAlarmLogListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/alarm-log/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },

  exportAlarmLogById: async (
    branchCode: string,
    id: string,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/alarm-log/export/${id}/${format}/${saveType}`,
      {
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
};
