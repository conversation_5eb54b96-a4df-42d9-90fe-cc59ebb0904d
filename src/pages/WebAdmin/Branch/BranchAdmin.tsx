import WebAdminLayout from "../../../components/Layout/WebAdminLayout.tsx";
import { Button, Input, Select } from "antd";
import {
  PlusOutlined,
  SearchOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
} from "@ant-design/icons";
import { MdBusinessCenter } from "react-icons/md";
import BranchGrid from "./Components/BranchGrid.tsx";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks.ts";
import React, { useCallback, useRef } from "react";
import _ from "lodash";
import branchAdminSlice, {
  fetchBranches,
} from "../../../store/slices/admin/branch.admin.slice.ts";
import ModalAddBranch from "./Components/ModalAddBranch/ModalAddBranch.tsx";
import addSubBranchAdminSlice from "../../../store/slices/admin/addSubBranch.admin.slice.ts";
import ModalViewBranch from "./Components/ModalViewBranch/ModalViewBranch.tsx";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import { useTranslation } from "react-i18next";

const { Option } = Select;

const BranchAdmin = () => {
  const dispatch = useAppDispatch();
  const { filter } = useAppSelector(
    (state) => state.branchAdmin
  );
  const { t } = useTranslation();

  // Create debounced search function using useRef
  const debouncedSearchRef = useRef(
    _.debounce((value: string) => {
      dispatch(
        branchAdminSlice.actions.setFilter({
          search: value,
          page: 1,
        })
      );
      dispatch(fetchBranches());
    }, 500)
  );

  // Handle search input change
  const handleSearch = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      debouncedSearchRef.current(e.target.value);
    },
    []
  );

  // Handle sort field change
  const handleSortFieldChange = useCallback(
    (value: string) => {
      dispatch(
        branchAdminSlice.actions.setFilter({
          orderBy: value,
          page: 1,
        })
      );
      dispatch(fetchBranches());
    },
    [dispatch]
  );

  // Handle sort direction toggle
  const handleSortDirectionToggle = useCallback(() => {
    dispatch(
      branchAdminSlice.actions.setFilter({
        orderDirection:
          filter.orderDirection === "ASC" ? "DESC" : "ASC",
        page: 1,
      })
    );
    dispatch(fetchBranches());
  }, [dispatch, filter.orderDirection]);

  // Handle add branch button click
  const onAddBranch = useCallback(() => {
    dispatch(addSubBranchAdminSlice.actions.open());
  }, [dispatch]);

  return (
    <WebAdminLayout activePage="client">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdBusinessCenter />}
            title={t("branch.title")}
            description={t("branch.description")}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={onAddBranch}
              >
                {t("branch.addButton")}
              </Button>,
            ]}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder={t("branch.search.placeholder")}
                prefix={<SearchOutlined />}
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                size="large"
                onChange={handleSearch}
                defaultValue={filter.search || ""}
              />
              <div className="flex gap-2">
                <Select
                  value={filter.orderBy}
                  className="dark:bg-gray-700"
                  size="large"
                  style={{ minWidth: 120 }}
                  onChange={handleSortFieldChange}
                >
                  <Option value="created_at">
                    {t(
                      "branch.search.sortOptions.createdAt"
                    )}
                  </Option>
                  <Option value="branch_name">
                    {t(
                      "branch.search.sortOptions.branchName"
                    )}
                  </Option>
                </Select>
                <Button
                  icon={
                    filter.orderDirection === "ASC" ? (
                      <SortAscendingOutlined />
                    ) : (
                      <SortDescendingOutlined />
                    )
                  }
                  size="large"
                  onClick={handleSortDirectionToggle}
                  className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100 dark:hover:bg-gray-600"
                >
                  {filter.orderDirection === "ASC"
                    ? t("branch.search.sortDirection.asc")
                    : t("branch.search.sortDirection.desc")}
                </Button>
              </div>
            </div>
          </div>

          {/* Branch Grid */}
          <BranchGrid />
        </div>
      </div>
      <ModalAddBranch />
      <ModalViewBranch />
    </WebAdminLayout>
  );
};

export default BranchAdmin;
