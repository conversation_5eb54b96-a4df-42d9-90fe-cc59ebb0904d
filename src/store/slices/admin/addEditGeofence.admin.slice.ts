import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import {
  AdminCreateUpdateGeofenceRequest,
  AdminGeofence,
  GeofenceData,
} from "../../../services/mainApi/admin/types/geofence.admin.mainApi.types";
import { RootState } from "../../index";
import geofenceAdminMainApi from "../../../services/mainApi/admin/geofence.admin.mainApi";
import { AdminZone } from "../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { AdminLabel } from "../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { isAxiosError } from "axios";
import { handleApiError } from "../../../utils/error";

interface State {
  open: boolean;
  mode: "create" | "update";
  geofenceEdit: AdminGeofence | null;
  loading: boolean;
  submitting: boolean;
  errorMessage: string[] | string | null;

  // Form fields
  geofenceName: string;
  geofenceDescription: string;
  zoneId: string;
  activeFrom: string;
  activeTo: string;
  minimumStayDuration: string;
  maximumStayDuration: string;
  active: boolean;
  geofenceData: GeofenceData;
  labelIds: string[];

  // Available data for dropdowns
  zones: AdminZone[];
  labels: AdminLabel[];
}

const initialState: State = {
  open: false,
  mode: "create",
  geofenceEdit: null,
  loading: false,
  submitting: false,
  errorMessage: null,

  // Form fields
  geofenceName: "",
  geofenceDescription: "",
  zoneId: "",
  activeFrom: "",
  activeTo: "",
  minimumStayDuration: "",
  maximumStayDuration: "",
  active: true,
  geofenceData: {
    type: "Polygon",
    coordinates: [],
  },
  labelIds: [],

  // Available data for dropdowns
  zones: [],
  labels: [],
};

export const fetchGeofenceDetail = createAsyncThunk(
  "addEditGeofenceAdmin/fetchGeofenceDetail",
  async (
    params: {
      branchCode: string;
      geofenceId: string | number;
    },
    { rejectWithValue }
  ) => {
    try {
      const response =
        await geofenceAdminMainApi.getGeofenceById(
          params.branchCode,
          params.geofenceId
        );
      return response.data;
    } catch (error) {
      if (isAxiosError(error)) {
        return rejectWithValue(
          handleApiError(error).errors
        );
      }
      console.error(
        "Error fetching geofence detail:",
        error
      );
      return rejectWithValue(
        "Failed to fetch geofence detail"
      );
    }
  }
);

export const createGeofence = createAsyncThunk(
  "addEditGeofenceAdmin/createGeofence",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = (getState() as RootState)
        .addEditGeofenceAdmin;
      if (!state) {
        return rejectWithValue("State not found");
      }

      const {
        geofenceName,
        geofenceDescription,
        zoneId,
        activeFrom,
        activeTo,
        minimumStayDuration,
        maximumStayDuration,
        active,
        geofenceData,
        labelIds,
      } = state;

      const data: AdminCreateUpdateGeofenceRequest = {
        geofence_name: geofenceName,
        geofence_description: geofenceDescription || null,
        zone_id: zoneId,
        active_from: activeFrom,
        active_to: activeTo,
        minimum_stay_duration: minimumStayDuration,
        maximum_stay_duration: maximumStayDuration,
        active,
        geofence_data: geofenceData,
        label_ids: labelIds,
      };

      return await geofenceAdminMainApi.createGeofence(
        branchCode,
        data
      );
    } catch (error) {
      if (isAxiosError(error)) {
        return rejectWithValue(
          handleApiError(error).errors
        );
      }
      return rejectWithValue("Failed to create geofence");
    }
  }
);

export const updateGeofence = createAsyncThunk(
  "addEditGeofenceAdmin/updateGeofence",
  async (
    params: {
      branchCode: string;
      geofenceId: string | number;
    },
    { getState, rejectWithValue }
  ) => {
    try {
      const state = (getState() as RootState)
        .addEditGeofenceAdmin;
      if (!state) {
        return rejectWithValue("State not found");
      }

      const {
        geofenceName,
        geofenceDescription,
        zoneId,
        activeFrom,
        activeTo,
        minimumStayDuration,
        maximumStayDuration,
        active,
        geofenceData,
        labelIds,
      } = state;

      const data: AdminCreateUpdateGeofenceRequest = {
        geofence_name: geofenceName,
        geofence_description: geofenceDescription || null,
        zone_id: zoneId,
        active_from: activeFrom,
        active_to: activeTo,
        minimum_stay_duration: minimumStayDuration,
        maximum_stay_duration: maximumStayDuration,
        active,
        geofence_data: geofenceData,
        label_ids: labelIds,
      };

      return await geofenceAdminMainApi.updateGeofence(
        params.branchCode,
        params.geofenceId,
        data
      );
    } catch (error) {
      if (isAxiosError(error)) {
        return rejectWithValue(
          handleApiError(error).errors
        );
      }
      return rejectWithValue("Failed to update geofence");
    }
  }
);

const addEditGeofenceAdminSlice = createSlice({
  name: "addEditGeofenceAdmin",
  initialState: {
    ...initialState,
  },
  reducers: {
    open: (state) => {
      state.open = true;
      state.mode = "create";
      state.errorMessage = null;
      state.geofenceEdit = null;
      // Keep other form fields default values
    },
    openEdit: (
      state,
      action: PayloadAction<AdminGeofence>
    ) => {
      state.open = true;
      state.mode = "update";
      state.geofenceEdit = action.payload;

      // Set form values from geofence data
      state.geofenceName = action.payload.geofence_name;
      state.geofenceDescription =
        action.payload.geofence_description || "";
      state.zoneId = action.payload.zone_id;
      state.activeFrom = action.payload.active_time_start;
      state.activeTo = action.payload.active_time_end;
      state.minimumStayDuration =
        action.payload.minimum_stay_duration;
      state.maximumStayDuration =
        action.payload.maximum_stay_duration;
      state.active = action.payload.active;
      state.geofenceData = action.payload.geofence_data;
      state.labelIds = action.payload.labels.map(
        (label) => label.id
      );
    },
    close: () => {
      return {
        ...initialState
      };
    },
    setGeofenceName: (
      state,
      action: PayloadAction<string>
    ) => {
      state.geofenceName = action.payload;
    },
    setGeofenceDescription: (
      state,
      action: PayloadAction<string>
    ) => {
      state.geofenceDescription = action.payload;
    },
    setZoneId: (state, action: PayloadAction<string>) => {
      state.zoneId = action.payload;
    },
    setActiveFrom: (
      state,
      action: PayloadAction<string>
    ) => {
      console.log(action.payload);
      state.activeFrom = action.payload;
    },
    setActiveTo: (state, action: PayloadAction<string>) => {
      state.activeTo = action.payload;
    },
    setMinimumStayDuration: (
      state,
      action: PayloadAction<string>
    ) => {
      state.minimumStayDuration = action.payload;
    },
    setMaximumStayDuration: (
      state,
      action: PayloadAction<string>
    ) => {
      state.maximumStayDuration = action.payload;
    },
    setActive: (state, action: PayloadAction<boolean>) => {
      state.active = action.payload;
    },
    setGeofenceData: (
      state,
      action: PayloadAction<GeofenceData>
    ) => {
      console.log(action.payload);
      state.geofenceData = action.payload;
    },
    setLabelIds: (
      state,
      action: PayloadAction<string[]>
    ) => {
      state.labelIds = action.payload;
    },
    clearCoordinates: (state) => {
      state.geofenceData.coordinates = [[]];
    },
  },
  extraReducers: (builder) => {
    builder

      // Fetch geofence detail
      .addCase(fetchGeofenceDetail.pending, (state) => {
        state.loading = true;
      })
      .addCase(
        fetchGeofenceDetail.fulfilled,
        (state, action) => {
          state.loading = false;
          const geofence = action.payload;
          if (geofence) {
            // Set form values from fetched geofence
            state.geofenceName = geofence.geofence_name;
            state.geofenceDescription =
              geofence.geofence_description || "";
            state.zoneId = geofence.zone_id;
            state.activeFrom = geofence.active_time_start;
            state.activeTo = geofence.active_time_end;
            state.minimumStayDuration =
              geofence.minimum_stay_duration;
            state.maximumStayDuration =
              geofence.maximum_stay_duration;
            state.active = geofence.active;
            state.geofenceData = geofence.geofence_data;
            state.labelIds = geofence.labels.map(
              (label) => label.id
            );
          }
        }
      )
      .addCase(
        fetchGeofenceDetail.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      )

      // Create geofence
      .addCase(createGeofence.pending, (state) => {
        state.submitting = true;
      })
      .addCase(createGeofence.fulfilled, (state) => {
        state.submitting = false;
        state.open = false;
      })
      .addCase(createGeofence.rejected, (state, action) => {
        state.submitting = false;
        state.errorMessage = action.payload as string;
      })

      // Update geofence
      .addCase(updateGeofence.pending, (state) => {
        state.submitting = true;
      })
      .addCase(updateGeofence.fulfilled, (state) => {
        state.submitting = false;
        state.open = false;
      })
      .addCase(updateGeofence.rejected, (state, action) => {
        console.log(action.payload);
        state.submitting = false;
        state.errorMessage = action.payload as string;
      });
  },
});

export default addEditGeofenceAdminSlice;
