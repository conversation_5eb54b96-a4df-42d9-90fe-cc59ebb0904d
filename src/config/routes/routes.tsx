import { lazy } from "react";

// Lazy load semua komponen
const Login = lazy(() => import("../../pages/Auth/Login.tsx"));
const BranchPage = lazy(
  () => import("../../pages/SysAdmin/Branch/BranchPage.tsx")
);
const BeaconPage = lazy(
  () => import("../../pages/SysAdmin/Beacon/BeaconPage.tsx")
);
const NotFound = lazy(
  () => import("../../pages/NotFound/NotFound.tsx")
);
const DashboardSysAdmin = lazy(
  () => import("../../pages/SysAdmin/Dashboard/DashboardPage.tsx")
);
const DashboardWebAdmin = lazy(
  () => import("../../pages/WebAdmin/Dashboard/Dashboard.tsx")
);
const SelectBranch = lazy(
  () => import("../../pages/WebAdmin/SelectBranch/SelectBranch.tsx")
);
const LicensePage = lazy(
  () => import("../../pages/SysAdmin/License/LicensePage.tsx")
);
const SettingPage = lazy(
  () => import("../../pages/SysAdmin/Maintenance/MaintenancePage.tsx")
);

// Konversi komponen yang belum lazy
const BranchAdmin = lazy(
  () => import("../../pages/WebAdmin/Branch/BranchAdmin.tsx")
);
const RolesAdmin = lazy(
  () => import("../../pages/WebAdmin/Roles/RolesAdmin.tsx")
);
const UserAdmin = lazy(
  () => import("../../pages/WebAdmin/User/UserAdmin.tsx")
);
const LabelAdmin = lazy(
  () => import("../../pages/WebAdmin/Label/LabelAdmin.tsx")
);
const ActivityAdmin = lazy(
  () => import("../../pages/WebAdmin/Activity/ActivityAdmin.tsx")
);
const AdminTask = lazy(
  () => import("../../pages/WebAdmin/Task/AdminTask.tsx")
);
const CheckpointAdmin = lazy(
  () =>
    import("../../pages/WebAdmin/Checkpoint/CheckpointAdmin.tsx")
);
const DeviceAdmin = lazy(
  () => import("../../pages/WebAdmin/Device/DeviceAdmin.tsx")
);
const SchedulerAdmin = lazy(
  () => import("../../pages/WebAdmin/Scheduler/SchedulerAdmin.tsx")
);
const AlertAdmin = lazy(
  () => import("../../pages/WebAdmin/Alert/AlertAdmin.tsx")
);
const GeofenceAdmin = lazy(
  () => import("../../pages/WebAdmin/Geofence/GeofenceAdmin.tsx")
);
const FormsAdmin = lazy(
  () => import("../../pages/WebAdmin/Forms/FormsAdmin.tsx")
);
const FormPicklistAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/FormPicklist/FormPicklistAdmin.tsx"
    )
);
const BeaconAdmin = lazy(
  () => import("../../pages/WebAdmin/Beacon/BeaconAdmin.tsx")
);
const SiteAdmin = lazy(
  () => import("../../pages/WebAdmin/Site/SiteAdmin.tsx")
);
const LicenseAdmin = lazy(
  () => import("../../pages/WebAdmin/License/LicenseAdmin.tsx")
);

const ActivityLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/ActivityLog/ActivityLogAdmin.tsx"
    )
);
const AlarmLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/Alarms/AlarmLogAdmin.tsx"
    )
);
const AverageRotationLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/AverageRotation/AverageRotationLogAdmin.tsx"
    )
);
const CheckpointActivityLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/CheckpointActivity/CheckpointActivityLogAdmin.tsx"
    )
);
const TaskLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/TaskLog/TaskLogAdmin.tsx"
    )
);
const FormLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/FormLog/FormLogAdmin.tsx"
    )
);
const GeofenceLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/GeofenceLog/GeofenceLogAdmin.tsx"
    )
);

const SignInOutLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/SignInOutLog/SignInOutLogAdmin.tsx"
    )
);

const BranchDetailsLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/BranchDetails/BranchDetailsLogAdmin.tsx"
    )
);

const CheckpointBatteryLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/CheckpointBattery/CheckpointBatteryLogAdmin.tsx"
    )
);

const ExceptionLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/Exception/ExceptionLogAdmin.tsx"
    )
);

const ExceptionDetailedLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/ExceptionDetailed/ExceptionDetailedLogAdmin.tsx"
    )
);

const MissedZoneLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/MissedZone/MissedZoneLogAdmin.tsx"
    )
);

const GpsHeatmapAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/GpsHeatmap/GpsHeatmapAdmin.tsx"
    )
);

const TimeRotationLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/TimeRotation/TimeRotationLogAdmin.tsx"
    )
);

const TimeOnZoneLogAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/TimeOnZone/TimeOnZoneLogAdmin.tsx"
    )
);

const UnknownDataAdmin = lazy(
  () =>
    import(
      "../../pages/WebAdmin/Analytics/UnknownData/UnknownDataAdmin.tsx"
    )
);

export const routeConfig = {
  auth: {
    login: {
      component: <Login />,
      path: "/login",
    },
  },
  sysAdmin: {
    dashboard: {
      component: <DashboardSysAdmin />,
      path: "/sysadmin/dashboard",
    },
    branch: {
      component: <BranchPage />,
      path: "/sysadmin/branch",
    },
    beacon: {
      component: <BeaconPage />,
      path: "/sysadmin/beacon",
    },
    license: {
      component: <LicensePage />,
      path: "/sysadmin/license",
    },
    settings: {
      component: <SettingPage />,
      path: "/sysadmin/settings"
    }
  },
  admin: {
    selectBranch: {
      component: <SelectBranch />,
      path: "/admin/branch",
    },
    dashboard: {
      component: <DashboardWebAdmin />,
      path: "/admin/branch/:branchCode/dashboard",
    },
    branch: {
      component: <BranchAdmin />,
      path: "/admin/branch/:branchCode/branch",
      module: "sub_branch",
      type: "MODULE"
    },
    roles: {
      component: <RolesAdmin />,
      path: "/admin/branch/:branchCode/roles",
      module: "permission",
      type: "MODULE"
    },
    user: {
      component: <UserAdmin />,
      path: "/admin/branch/:branchCode/users",
      module: "user",
      type: "MODULE"
    },
    label: {
      component: <LabelAdmin />,
      path: "/admin/branch/:branchCode/label",
      module: "label",
      type: "MODULE"
    },
    scheduler: {
      component: <SchedulerAdmin />,
      path: "/admin/branch/:branchCode/scheduler",
      module: "scheduler",
      type: "MODULE"
    },
    alert: {
      component: <AlertAdmin />,
      path: "/admin/branch/:branchCode/alert",
      module: "alert",
      type: "MODULE"
    },
    device: {
      component: <DeviceAdmin />,
      path: "/admin/branch/:branchCode/device",
      module: "device",
      type: "MODULE"
    },
    site: {
      component: <SiteAdmin />,
      path: "/admin/branch/:branchCode/site",
      module: "site",
      type: "MODULE"
    },
    checkpoint: {
      component: <CheckpointAdmin />,
      path: "/admin/branch/:branchCode/checkpoint",
      module: "checkpoint",
      type: "MODULE"
    },
    geofence: {
      component: <GeofenceAdmin />,
      path: "/admin/branch/:branchCode/geofence",
      module: "geofence",
      type: "MODULE"
    },
    forms: {
      component: <FormsAdmin />,
      path: "/admin/branch/:branchCode/forms",
      module: "form",
      type: "MODULE"
    },
    formPicklist: {
      component: <FormPicklistAdmin />,
      path: "/admin/branch/:branchCode/formPicklist",
      module: "form",
      type: "MODULE"
    },
    task: {
      component: <AdminTask />,
      path: "/admin/branch/:branchCode/task",
      module: "task",
      type: "MODULE"
    },
    activity: {
      component: <ActivityAdmin />,
      path: "/admin/branch/:branchCode/activity",
      module: "activity",
      type: "MODULE"
    },
    beacon: {
      component: <BeaconAdmin />,
      path: "/admin/branch/:branchCode/beacon",
      module: "beacon",
      type: "MODULE"
    },
    license: {
      component: <LicenseAdmin />,
      path: "/admin/branch/:branchCode/license",
      module: "license",
      type: "MODULE"
    },
    activityLog: {
      component: <ActivityLogAdmin />,
      path: "/admin/branch/:branchCode/activity-log",
      module: "activity_log",
      type: "REPORT"
    },
    alarmLog: {
      component: <AlarmLogAdmin />,
      path: "/admin/branch/:branchCode/alarms-log",
      module: "alarms",
      type: "REPORT"
    },
    averageRotationLog: {
      component: <AverageRotationLogAdmin />,
      path: "/admin/branch/:branchCode/average-rotation-log",
      module: "average_rotation",
      type: "REPORT"
    },
    checkpointActivity: {
      component: <CheckpointActivityLogAdmin />,
      path: "/admin/branch/:branchCode/checkpoint-activity-log",
      module: "checkpoint_activity",
      type: "REPORT"
    },
    checkpointBatteryLog: {
      component: <CheckpointBatteryLogAdmin />,
      path: "/admin/branch/:branchCode/checkpoint-battery-log",
      module: "checkpoint_battery",
      type: "REPORT"
    },
    exceptionLog: {
      component: <ExceptionLogAdmin />,
      path: "/admin/branch/:branchCode/exception-log",
      module: "exception",
      type: "REPORT"
    },
    exceptionDetailedLog: {
      component: <ExceptionDetailedLogAdmin />,
      path: "/admin/branch/:branchCode/exception-detailed-log",
      module: "exception_detailed",
      type: "REPORT"
    },
    tasksLog: {
      component: <TaskLogAdmin />,
      path: "/admin/branch/:branchCode/tasks-log",
      module: "tasks",
      type: "REPORT"
    },
    formsLog: {
      component: <FormLogAdmin />,
      path: "/admin/branch/:branchCode/forms-log",
      module: "forms",
      type: "REPORT"
    },
    analyticsGeofenceLog: {
      component: <GeofenceLogAdmin />,
      path: "/admin/branch/:branchCode/analytics-geofence-log",
      module: "geofence",
      type: "REPORT"
    },
    signInOutLog: {
      component: <SignInOutLogAdmin />,
      path: "/admin/branch/:branchCode/sign-on-off-log",
      module: "sign_on_off",
      type: "REPORT"
    },
    branchDetailsLog: {
      component: <BranchDetailsLogAdmin />,
      path: "/admin/branch/:branchCode/branch-details-log",
      module: "branch_detail",
      type: "REPORT"
    },
    missedZoneLog: {
      component: <MissedZoneLogAdmin />,
      path: "/admin/branch/:branchCode/missed-zone-log",
      module: "missed_zone",
      type: "REPORT"
    },
    gpsHeatmapLog: {
      component: <GpsHeatmapAdmin />,
      path: "/admin/branch/:branchCode/gps-heatmap-log",
      module: "gps_heatmap",
      type: "REPORT"
    },
    timeRotationLog: {
      component: <TimeRotationLogAdmin />,
      path: "/admin/branch/:branchCode/time-rotation-log",
      module: "time_rotation",
      type: "REPORT"
    },
    timeOnZoneLog: {
      component: <TimeOnZoneLogAdmin />,
      path: "/admin/branch/:branchCode/time-on-zone-log",
      module: "time_on_zone",
      type: "REPORT"
    },
    unknownData: {
      component: <UnknownDataAdmin />,
      path: "/admin/branch/:branchCode/unknown-data",
      module: "unknown_data",
      type: "REPORT"
    },
  },
  notFound: {
    component: <NotFound />,
    path: "/404",
  },
};
