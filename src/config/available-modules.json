[{"id": 1, "module_type": "MODULE", "module_code": "sub_branch", "module_name": "Sub Branch"}, {"id": 2, "module_type": "MODULE", "module_code": "permission", "module_name": "Permission"}, {"id": 3, "module_type": "MODULE", "module_code": "user", "module_name": "User"}, {"id": 4, "module_type": "MODULE", "module_code": "label", "module_name": "Label"}, {"id": 5, "module_type": "MODULE", "module_code": "scheduler", "module_name": "Scheduler"}, {"id": 6, "module_type": "MODULE", "module_code": "alert", "module_name": "<PERSON><PERSON>"}, {"id": 7, "module_type": "MODULE", "module_code": "device", "module_name": "<PERSON><PERSON>"}, {"id": 8, "module_type": "MODULE", "module_code": "site", "module_name": "Site"}, {"id": 9, "module_type": "MODULE", "module_code": "checkpoint", "module_name": "Checkpoint"}, {"id": 10, "module_type": "MODULE", "module_code": "geofence", "module_name": "Geofence"}, {"id": 11, "module_type": "MODULE", "module_code": "form", "module_name": "Form"}, {"id": 12, "module_type": "MODULE", "module_code": "task", "module_name": "Task"}, {"id": 13, "module_type": "MODULE", "module_code": "activity", "module_name": "Activity"}, {"id": 14, "module_type": "MODULE", "module_code": "beacon", "module_name": "Beacon"}, {"id": 15, "module_type": "MODULE", "module_code": "license", "module_name": "License"}, {"id": 16, "module_type": "REPORT", "module_code": "activity_log", "module_name": "Activity Log"}, {"id": 17, "module_type": "REPORT", "module_code": "alarms", "module_name": "Alarms"}, {"id": 18, "module_type": "REPORT", "module_code": "average_rotation", "module_name": "Average Rotation"}, {"id": 19, "module_type": "REPORT", "module_code": "branch_detail", "module_name": "Branch Details"}, {"id": 20, "module_type": "REPORT", "module_code": "checkpoint_activity", "module_name": "Checkpoint Activity"}, {"id": 21, "module_type": "REPORT", "module_code": "checkpoint_battery", "module_name": "Checkpoint Battery"}, {"id": 22, "module_type": "REPORT", "module_code": "exception", "module_name": "Exception"}, {"id": 23, "module_type": "REPORT", "module_code": "exception_detailed", "module_name": "Exception Detailed"}, {"id": 24, "module_type": "REPORT", "module_code": "forms", "module_name": "Forms"}, {"id": 25, "module_type": "REPORT", "module_code": "geofence", "module_name": "Geofence"}, {"id": 26, "module_type": "REPORT", "module_code": "gps_heatmap", "module_name": "GPS Heatmap"}, {"id": 27, "module_type": "REPORT", "module_code": "missed_zone", "module_name": "Missed Zone"}, {"id": 28, "module_type": "REPORT", "module_code": "sign_on_off", "module_name": "Sign on/off"}, {"id": 29, "module_type": "REPORT", "module_code": "tasks", "module_name": "Tasks"}, {"id": 30, "module_type": "REPORT", "module_code": "time_rotation", "module_name": "Time & Rotation"}, {"id": 31, "module_type": "REPORT", "module_code": "time_on_zone", "module_name": "Time On Zone"}]