import {Empty, Spin} from "antd";
import {License} from "../../../../services/mainApi/types/license.mainApi.types";
import {useTranslation} from "react-i18next";
import LicenseItem from "./LicenseItem";

interface LicenseListProps {
  licenses: License[];
  loading: boolean;
  onEdit: (license: License) => void;
}

export default function LicenseList({
                                      licenses,
                                      loading,
                                      onEdit,
                                    }: LicenseListProps) {
  const {t} = useTranslation();

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Spin size="large" tip="Loading licenses..."/>
      </div>
    );
  }

  if (licenses.length === 0) {
    return (
      <div className="p-8 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <span className="text-gray-500 dark:text-gray-400 text-base">
              {t("license.noLicensesFound", "No licenses found")}
            </span>
          }
          className="py-12"
        />
      </div>
    );
  }

  return (
    <div
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6"
    >
      {licenses.map((license) => (
        <LicenseItem license={license} onEdit={onEdit}/>
      ))}
    </div>
  );
}
