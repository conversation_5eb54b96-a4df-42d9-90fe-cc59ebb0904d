import { RootState } from "../..";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import MaintenanceMainApi from "../../../services/mainApi/sysadmin/maintenance.sysadmin.mainApi";
import { Setting } from "../../../services/mainApi/types/setting.mainApi.types";

export type SortField = "updated_at";
export type SortOrder = "asc" | "desc";

interface MaintenanceState {
    items: Setting[];
    loading: boolean;
    error: string | null;
    pagination: {
        current: number;
        pageSize: number;
        total: number;
    };
    filters: {
        searchQuery: string;
        sortField: SortField;
        sortOrder: SortOrder;
    };
}

const initialState: MaintenanceState = {
    items: [],
    loading: false,
    error: null,
    pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
    },
    filters: {
        searchQuery: "",
        sortField: "updated_at",
        sortOrder: "desc",
    },
};

export const fetchMaintenance = createAsyncThunk(
    "maintenance/fetchMaintenance",
    async (_, { getState, rejectWithValue }) => {
        try {
            const state = getState() as RootState;
            const { pagination, filters } = state.settingSysadmin;

            const response = await MaintenanceMainApi.getList({
                search: filters.searchQuery,
                page: pagination.current,
                limit: pagination.pageSize,
                order_by: filters.sortField,
                order_direction: filters.sortOrder.toUpperCase() as "ASC" | "DESC",
            });
            return {
                data: response.data || [],
                total: response.meta.total || 0,
            };
        } catch {
            return rejectWithValue("Failed to fetch maintenance data.");
        }
    }
);

// Slice
const settingListSysadminSlice = createSlice({
  name: "setting",
  initialState,
  reducers: {
    setSearchQuery: (
      state,
      action: PayloadAction<string>
    ) => {
      state.filters.searchQuery = action.payload;
      state.pagination.current = 1; // Reset to first page
    },
    setActiveFilter: (
      state
    ) => {
      state.pagination.current = 1; // Reset to first page
    },
    setSort: (
      state,
      action: PayloadAction<{
        field: SortField;
        order: SortOrder;
      }>
    ) => {
      state.filters.sortField = action.payload.field;
      state.filters.sortOrder = action.payload.order;
      state.pagination.current = 1; // Reset to first page
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.pagination.current = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchMaintenance.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMaintenance.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.data;
        state.pagination.total = action.payload.total;
      })
      .addCase(fetchMaintenance.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Actions
export const { setSearchQuery, setActiveFilter, setSort, setPage } =
  settingListSysadminSlice.actions;

// Selectors
export const selectSetting = (state: RootState) =>
  state.settingSysadmin.items;
export const selectSettingLoading = (state: RootState) =>
  state.settingSysadmin.loading;
export const selectSettingError = (state: RootState) =>
  state.settingSysadmin.error;
export const selectSettingPagination = (state: RootState) =>
  state.settingSysadmin.pagination;
export const selectSettingFilters = (state: RootState) =>
  state.settingSysadmin.filters;

export default settingListSysadminSlice;