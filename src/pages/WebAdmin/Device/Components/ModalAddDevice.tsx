import {
  App,
  Button,
  Form,
  Input,
  Modal,
  Select,
} from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../../store";
import deviceModalSlice, {
  createDevice,
  updateDevice,
} from "../../../../store/slices/admin/deviceModal.admin.slice";
import { useParams } from "react-router-dom";
import SelectLabel from "../../../../components/Label/SelectLabel";
import { EDeviceType } from "../../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { fetchDevicesAdmin } from "../../../../store/slices/admin/device.admin.slice.ts";
import { useAppSelector } from "../../../../store/hooks.ts";
import ErrorMessage from "../../../../components/Common/ErrorMessage/ErrorMessage.tsx";

const { Option } = Select;
const { TextArea } = Input;

const ModalAddDevice = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  const { message } = App.useApp();

  const {
    visible,
    mode,
    formData,
    currentDevice,
    isSubmitting,
    errorMessages,
  } = useAppSelector((state) => state.deviceModal);

  const { actions } = deviceModalSlice;

  const handleSubmit = async () => {
    if (
      !formData.device_name ||
      !formData.uniguard_device_type_id
    ) {
      message.error(t("device.form.requiredFields"));
      return;
    }
    try {
      if (!branchCode) {
        message.error(t("device.form.branchRequired"));
        return;
      }

      let result;
      if (mode === "create") {
        result = await dispatch(
          createDevice({
            branchCode,
            data: {
              device_name: formData.device_name,
              uniguard_device_type_id:
                formData.uniguard_device_type_id,
              imei: formData.imei,
              serial_number: formData.serial_number_hex,
              device_description:
                formData.device_description,
              label_ids: formData.label_ids || [],
            },
          })
        );
        if (result.meta.requestStatus === "fulfilled") {
          message.success(t("device.form.createSuccess"));
        }
      } else if (mode === "update" && currentDevice) {
        result = await dispatch(
          updateDevice({
            branchCode,
            deviceId: String(currentDevice.id),
            data: {
              device_name: formData.device_name,
              uniguard_device_type_id:
                formData.uniguard_device_type_id,
              imei: formData.imei,
              serial_number: formData.serial_number_hex,
              device_description:
                formData.device_description,
              label_ids: formData.label_ids,
            },
          })
        );
        if (result.meta.requestStatus === "fulfilled") {
          message.success(t("device.form.updateSuccess"));
        }
      }

      if (
        result &&
        result.meta.requestStatus === "fulfilled"
      ) {
        // Refresh device list
        await dispatch(fetchDevicesAdmin(branchCode));
        // Close modal
        dispatch(actions.closeModal());
      } else if (
        result &&
        result.meta.requestStatus === "rejected" &&
        result.payload
      ) {
        message.error(
          errorMessages?.[0] ||
            t("device.form.submitFailed")
        );
      }
    } catch {
      message.error(t("device.form.submitFailed"));
    }
  };

  const modalTitle =
    mode === "create"
      ? t("device.modal.createTitle")
      : t("device.modal.updateTitle");

  return (
    <Modal
      title={modalTitle}
      open={visible}
      onCancel={() => dispatch(actions.closeModal())}
      footer={[
        <Button
          key="cancel"
          onClick={() => dispatch(actions.closeModal())}
        >
          {t("common.cancel")}
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={isSubmitting}
          onClick={handleSubmit}
          className="bg-blue-500 hover:bg-blue-600"
        >
          {mode === "create"
            ? t("common.create")
            : t("common.update")}
        </Button>,
      ]}
      className="rounded-xl"
      maskClosable={!isSubmitting}
      closable={!isSubmitting}
      confirmLoading={isSubmitting}
      width={800}
      destroyOnClose
    >
      <Form layout="vertical">
        <Form.Item
          label={t("device.form.name")}
          required
        >
          <Input
            value={formData.device_name}
            onChange={(e) =>
              dispatch(
                actions.setDeviceName(e.target.value)
              )
            }
            placeholder={t(
              "device.form.namePlaceholder",
              "Masukkan nama perangkat"
            )}
          />
        </Form.Item>

        <Form.Item
          label={t("device.form.type")}
          required
        >
          <Select
            placeholder={t(
              "device.form.selectType",
              "Pilih tipe"
            )}
            value={formData.uniguard_device_type_id}
            onChange={(value) =>
              dispatch(actions.setDeviceTypeId(value))
            }
          >
            <Option value={EDeviceType.APP}>
              {t("device.form.app")}
            </Option>
            <Option value={EDeviceType.I_BUTTON}>
              {t("device.form.iButton")}
            </Option>
            <Option value={EDeviceType.RFID}>
              {t("device.form.rfid")}
            </Option>
            <Option value={EDeviceType.GPS}>
              {t("device.form.gps")}
            </Option>
          </Select>
        </Form.Item>

        <Form.Item label={t("device.form.imei")}>
          <Input
            value={formData.imei}
            onChange={(e) =>
              dispatch(actions.setImei(e.target.value))
            }
            placeholder={t(
              "device.form.imeiPlaceholder",
              "Masukkan IMEI"
            )}
          />
        </Form.Item>

        <Form.Item
          label={t(
            "device.form.serialNumber",
            "Nomor Seri"
          )}
        >
          <Input
            value={formData.serial_number_hex}
            onChange={(e) =>
              dispatch(
                actions.setSerialNumber(e.target.value)
              )
            }
            placeholder={t(
              "device.form.serialNumberPlaceholder",
              "Masukkan nomor seri"
            )}
          />
        </Form.Item>

        <Form.Item
          label={t("device.form.description", "Deskripsi")}
        >
          <TextArea
            rows={4}
            value={formData.device_description}
            onChange={(e) =>
              dispatch(
                actions.setDeviceDescription(e.target.value)
              )
            }
            placeholder={t(
              "device.form.descriptionPlaceholder",
              "Masukkan deskripsi"
            )}
          />
        </Form.Item>

        <Form.Item label={t("device.form.labels")}>
          <SelectLabel
            mode="multiple"
            onChange={(value) => {
              dispatch(
                actions.setLabelIds(value as string[])
              );
            }}
            value={formData.label_ids || []}
          />
        </Form.Item>
      </Form>

      {errorMessages && (
        <ErrorMessage message={errorMessages} />
      )}
    </Modal>
  );
};

export default ModalAddDevice;
