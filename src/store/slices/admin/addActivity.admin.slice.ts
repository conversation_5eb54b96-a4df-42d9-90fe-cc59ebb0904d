import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { AdminActivity } from "../../../services/mainApi/admin/types/activity.admin.mainApi.types";
import { RootState } from "../../index.ts";
import { activityAdminApi } from "../../../services/mainApi/admin/activity.admin.mainApi.ts";

interface State {
  open: boolean;
  mode: "create" | "update";
  activityEdit: AdminActivity | null;
  loading: boolean;
  errorMessage: string | null;

  activity_name: string;
  activity_description: string;
  gps_required: boolean;
  photo_required: boolean;
  comment_required: boolean;
  active: boolean;
}

const initialState: State = {
  open: false,
  mode: "create",
  activityEdit: null,
  loading: false,
  errorMessage: null,

  activity_name: "",
  activity_description: "",
  gps_required: false,
  photo_required: false,
  comment_required: false,
  active: true,
};

export const updateActivity = createAsyncThunk(
  "addActivityAdmin/submitActivity",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    const state = getState() as RootState;
    const addActivityState = state.addActivityAdmin;

    if (!addActivityState.activityEdit) {
      return rejectWithValue(
        "No activity selected for update"
      );
    }

    try {
      const response =
        await activityAdminApi.updateActivity(
          branchCode,
          addActivityState.activityEdit.id,
          {
            activity_name: addActivityState.activity_name,
            activity_description:
              addActivityState.activity_description,
            gps_required: addActivityState.gps_required,
            photo_required: addActivityState.photo_required,
            comment_required:
              addActivityState.comment_required,
            active: addActivityState.active,
          }
        );
      return response.data;
    } catch {
      return rejectWithValue("Failed to update activity");
    }
  }
);

export const createActivity = createAsyncThunk(
  "addActivityAdmin/submitActivity",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    const state = getState() as RootState;
    const addActivityState = state.addActivityAdmin;

    try {
      const response =
        await activityAdminApi.createActivity(branchCode, {
          activity_name: addActivityState.activity_name,
          activity_description:
            addActivityState.activity_description,
          gps_required: addActivityState.gps_required,
          photo_required: addActivityState.photo_required,
          comment_required:
            addActivityState.comment_required,
        });
      return response.data;
    } catch {
      return rejectWithValue("Failed to create activity");
    }
  }
);

const addActivityAdminSlice = createSlice({
  name: "addActivityAdmin",
  initialState: {
    ...initialState,
  },
  reducers: {
    open: (state) => {
      state.open = true;
      state.mode = "create";
    },
    openEdit: (
      state,
      action: PayloadAction<AdminActivity>
    ) => {
      state.open = true;
      state.mode = "update";
      state.activityEdit = action.payload;
      state.activity_name = action.payload.activity_name;
      state.activity_description =
        action.payload.activity_description;
      state.gps_required = action.payload.gps_required;
      state.photo_required = action.payload.photo_required;
      state.comment_required =
        action.payload.comment_required;
      state.active = action.payload.active;
    },
    close: () => {
      return {
        ...initialState,
      };
    },
    setError: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.errorMessage = action.payload;
    },
    setActivityName: (
      state,
      action: PayloadAction<string>
    ) => {
      state.activity_name = action.payload;
    },
    setActivityDescription: (
      state,
      action: PayloadAction<string>
    ) => {
      state.activity_description = action.payload;
    },
    setGpsRequired: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.gps_required = action.payload;
    },
    setPhotoRequired: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.photo_required = action.payload;
    },
    setCommentRequired: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.comment_required = action.payload;
    },
    setActive: (state, action: PayloadAction<boolean>) => {
      state.active = action.payload;
    },
  },
});

export default addActivityAdminSlice;
