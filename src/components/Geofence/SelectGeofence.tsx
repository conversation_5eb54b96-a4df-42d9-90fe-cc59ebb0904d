import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import geofenceAdminMainApi from "../../services/mainApi/admin/geofence.admin.mainApi";
import { AdminGeofence } from "../../services/mainApi/admin/types/geofence.admin.mainApi.types";
import { useParams } from "react-router-dom";

interface SelectGeofenceProps {
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  clearable?: boolean;
}

export default function SelectGeofence({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select geofence",
  clearable = true,
}: SelectGeofenceProps) {
  const [geofences, setGeofences] = useState<
    AdminGeofence[]
  >([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);
  const { branchCode } = useParams();

  useEffect(() => {
    const fetchGeofences = async () => {
      if (isFetching.current || !branchCode) return;

      try {
        isFetching.current = true;
        setLoading(true);
        const response =
          await geofenceAdminMainApi.getGeofences(
            branchCode
          );
        setGeofences(response.data || []);
      } catch (error) {
        console.error("Failed to fetch geofences:", error);
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchGeofences();
  }, [branchCode]);

  return (
    <Select
      className={className}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      disabled={disabled}
      loading={loading}
      showSearch
      optionFilterProp="label"
      options={geofences.map((geofence) => ({
        value: geofence.id,
        label: geofence.geofence_name,
      }))}
      allowClear={clearable}
    />
  );
}
