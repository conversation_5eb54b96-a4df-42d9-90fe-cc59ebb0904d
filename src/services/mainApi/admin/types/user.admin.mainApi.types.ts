import {
  BaseGetRequest,
  BaseResponse,
} from "../../types/base.mainApi.types";
import { AdminRole } from "./roles.admin.mainApi.types";
import { Branch } from "../../sysadmin/types/branch.mainApi.types";

interface Label {
  id: string;
  branch_id: string;
  label_name: string;
  label_description: string;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

export interface AdminUser {
  id: string;
  parent_branch_id: string;
  role_id: string | null;
  name: string;
  email: string;
  phone: string;
  system_access: boolean;
  web_access: boolean;
  mobile_access: boolean;
  active: boolean;
  created_at: string;
  updated_at: string;
  role: AdminRole | null;
  labels: Label[];
  branches: Branch[] | null;
}

// Response types
export type AdminUserListResponse = BaseResponse<
  AdminUser[]
>;
export type AdminUserDetailResponse =
  BaseResponse<AdminUser>;

// Request types for create/update
export interface CreateUpdateUserRequest {
  role_id: string | null;
  name: string;
  email: string;
  phone: string;
  password: string;
  confirm_password: string;
  web_access: boolean;
  mobile_access: boolean;
  label_ids: string[];
  branch_ids: string[];
}

export interface GetUserRequest extends BaseGetRequest {
  role_id?: string | number;
  branch_id?: string | number;
}

export interface UpdateUserRequest
  extends Omit<CreateUpdateUserRequest, "email"> {
  active: boolean;
}
