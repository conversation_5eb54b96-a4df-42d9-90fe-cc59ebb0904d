import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import beaconAdminMainApi from "../../../services/mainApi/admin/beacon.admin.mainApi";
import { AdminBeacon } from "../../../services/mainApi/admin/types/beacon.admin.mainApi.types";
import {
  BaseGetRequest,
  BasePaginationMeta,
} from "../../../services/mainApi/types/base.mainApi.types";

interface State {
  beacons: AdminBeacon[];
  loading: boolean;
  errorMessage: string | null;
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;
}

const initialState: State = {
  beacons: [],
  loading: false,
  errorMessage: null,
  filter: {
    search: null,
    page: 1,
    limit: 10,
    orderBy: "created_at",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    total_pages: 0,
  },
};

export const fetchBeaconsAdmin = createAsyncThunk<
  { data: AdminBeacon[]; meta: BasePaginationMeta },
  string,
  { state: RootState; rejectValue: string }
>(
  "beaconAdmin/fetchBeacons",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const { filter } = state.beaconAdmin;
      const params: BaseGetRequest = {
        search: filter.search || undefined,
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
      };

      const response = await beaconAdminMainApi.getBeacons(
        branchCode,
        params
      );
      return {
        data: response.data || [],
        meta: response.meta,
      };
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue("Failed to fetch beacons");
    }
  }
);

const beaconAdminSlice = createSlice({
  name: "beaconAdmin",
  initialState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchBeaconsAdmin.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchBeaconsAdmin.fulfilled,
        (state, action) => {
          state.loading = false;
          state.beacons = action.payload.data;
          state.pagination = action.payload.meta;
        }
      )
      .addCase(
        fetchBeaconsAdmin.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      );
  },
});

export const { setFilter } = beaconAdminSlice.actions;
export default beaconAdminSlice;
