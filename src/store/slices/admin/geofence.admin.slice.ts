import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import geofenceAdminMainApi from "../../../services/mainApi/admin/geofence.admin.mainApi";
import {
  AdminGeofence,
  AdminGeofenceGetRequest,
} from "../../../services/mainApi/admin/types/geofence.admin.mainApi.types";
import { BasePaginationMeta } from "../../../services/mainApi/types/base.mainApi.types";

interface State {
  geofences: AdminGeofence[];
  loading: boolean;
  errorMessage: string | null;
  filter: {
    search: string | null;
    status: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;
}

const initialState: State = {
  geofences: [],
  loading: false,
  errorMessage: null,
  filter: {
    search: null,
    status: null,
    page: 1,
    limit: 10,
    orderBy: "created_at",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
  },
};

export const fetchGeofencesAdmin = createAsyncThunk(
  "geofenceAdmin/fetchGeofences",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const { filter } = state.geofenceAdmin;
      const params: AdminGeofenceGetRequest = {
        search: filter.search || "",
        status: filter.status || undefined,
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
      };
      const response =
        await geofenceAdminMainApi.getGeofences(
          branchCode,
          params
        );
      return {
        data: response.data,
        meta: response.meta,
      };
    } catch (error) {
      console.error("Error fetching geofences:", error);
      return rejectWithValue("Failed to fetch geofences");
    }
  }
);

const geofenceAdminSlice = createSlice({
  name: "geofenceAdmin",
  initialState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch geofences
      .addCase(fetchGeofencesAdmin.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchGeofencesAdmin.fulfilled,
        (state, action) => {
          state.loading = false;
          state.geofences = action.payload.data || [];
          state.pagination = action.payload.meta;
        }
      )
      .addCase(
        fetchGeofencesAdmin.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      );
  },
});

export const { setFilter } = geofenceAdminSlice.actions;
export default geofenceAdminSlice;
