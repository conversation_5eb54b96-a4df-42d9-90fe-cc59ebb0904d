import { useCallback, useEffect, useRef } from "react";
import beaconAdminSlice, {
  fetchBeaconsAdmin,
} from "../../../../store/slices/admin/beacon.admin.slice";
import { useParams } from "react-router-dom";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks";
import { Empty, Pagination, Spin } from "antd";
import { useTranslation } from "react-i18next";
import BeaconListItem from "./BeaconListItem.tsx";

const BeaconList = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const isFetching = useRef(false);
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const { beacons, loading, pagination, filter } =
    useAppSelector((state) => state.beaconAdmin);

  const fetch = useCallback(async () => {
    if (isFetching.current) return;
    isFetching.current = true;
    await dispatch(fetchBeaconsAdmin(branchCode || ""));
    isFetching.current = false;
  }, [dispatch, branchCode]);

  const onPageChange = useCallback(
    (page: number, pageSize: number) => {
      dispatch(
        beaconAdminSlice.actions.setFilter({
          page,
          limit: pageSize,
        })
      );
      fetch();
    },
    [dispatch, fetch]
  );

  useEffect(() => {
    fetch();
  }, [fetch, filter.orderBy, filter.orderDirection]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px] bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <Spin size="large" />
      </div>
    );
  }

  if (!beacons || beacons.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8">
        <Empty
          description={
            <span className="text-gray-500 dark:text-gray-400">
              {t("beacon.noBeacons", "No beacons found")}
            </span>
          }
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Beacon List */}
      <div className="grid grid-cols-1 gap-4">
        {beacons.map((beacon) => (
          <BeaconListItem
            key={beacon.id}
            beacon={beacon}
          />
        ))}
      </div>

      {/* Pagination */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
        <Pagination
          current={filter.page || 1}
          pageSize={filter.limit || 10}
          total={pagination.total || 0}
          onChange={onPageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              {t(
                "common.totalItems",
                "Total {{total}} items",
                { total }
              )}
            </span>
          )}
          className="flex justify-end p-4"
        />
      </div>
    </div>
  );
};

export default BeaconList;
