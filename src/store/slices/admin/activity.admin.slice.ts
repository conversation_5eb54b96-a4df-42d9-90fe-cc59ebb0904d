import {
  createSlice,
  createAsyncThunk,
} from "@reduxjs/toolkit";
import { activityAdminApi } from "../../../services/mainApi/admin/activity.admin.mainApi";
import { AdminActivity } from "../../../services/mainApi/admin/types/activity.admin.mainApi.types";
import {
  BaseGetRequest,
  BasePaginationMeta,
} from "../../../services/mainApi/types/base.mainApi.types";
import { RootState } from "../../../store";

interface State {
  activities: AdminActivity[];
  loading: boolean;
  error: string | null;

  modalVisible: boolean;
  editData: AdminActivity | undefined;

  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;
}

const initialState: State = {
  activities: [],
  loading: false,
  error: null,
  filter: {
    search: null,
    page: 1,
    limit: 10,
    orderBy: "created_at",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
  },
  modalVisible: false,
  editData: undefined,
};

export const fetchActivities = createAsyncThunk(
  "activityAdmin/fetchActivities",
  async (
    {
      branchCode,
    }: {
      branchCode: string;
    },
    { getState }
  ) => {
    const state = getState() as RootState;
    const activityAdmin = state.activityAdmin;

    const params: BaseGetRequest = {
      page: activityAdmin.filter.page || undefined,
      limit: activityAdmin.filter.limit || undefined,
      search: activityAdmin.filter.search || undefined,
      order_by: activityAdmin.filter.orderBy,
      order_direction: activityAdmin.filter.orderDirection,
    };

    return await activityAdminApi.getActivities(
      branchCode,
      params
    );
  }
);

const activityAdminSlice = createSlice({
  name: "activityAdmin",
  initialState,
  reducers: {
    setSearch: (state, action) => {
      state.filter.search = action.payload;
    },
    setPage: (state, action) => {
      state.filter.page = action.payload;
    },
    setOrderBy: (state, action) => {
      state.filter.orderBy = action.payload;
    },
    setLimit: (state, action) => {
      state.filter.limit = action.payload;
    },
    setOrderDirection: (state, action) => {
      state.filter.orderDirection = action.payload;
    },
    setFilter: (state, action) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Activities
      .addCase(fetchActivities.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchActivities.fulfilled,
        (state, action) => {
          state.loading = false;
          state.activities = action.payload.data || [];
          state.pagination.total =
            action.payload.meta?.total || 0;
          state.pagination.page =
            action.payload.meta?.page || 1;
          state.pagination.limit =
            action.payload.meta?.limit || 10;
        }
      )
      .addCase(
        fetchActivities.rejected,
        (state, action) => {
          state.loading = false;
          state.error =
            action.error.message ||
            "Failed to fetch activities";
        }
      );
  },
});

export default activityAdminSlice;
