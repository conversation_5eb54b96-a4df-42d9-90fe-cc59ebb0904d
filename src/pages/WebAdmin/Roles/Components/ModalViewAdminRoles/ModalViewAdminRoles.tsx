import { memo, useCallback, useEffect } from "react";
import { <PERSON><PERSON>, Spin, Tabs } from "antd";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks.ts";
import {
  MdBusiness,
  MdError,
  MdSecurity,
} from "react-icons/md";
import dayjs from "dayjs";
import { AdminRoleDetail } from "../../../../../services/mainApi/admin/types/roles.admin.mainApi.types.ts";
import PermissionsTable from "./PermissionsTable.tsx";
import viewRoleAdminSlice, {
  fetchDetailAdminRole,
} from "../../../../../store/slices/admin/viewRole.admin.slice.ts";
import { useTranslation } from "react-i18next";

const ModalViewAdminRoles = memo(() => {
  const dispatch = useAppDispatch();
  const { open, roleView, loading, errorMessage, detail } =
    useAppSelector((state) => state.viewRoleAdmin);
  const { t } = useTranslation();

  const handleClose = useCallback(() => {
    dispatch(viewRoleAdminSlice.actions.close());
  }, [dispatch]);

  useEffect(() => {
    if (open && roleView) {
      dispatch(fetchDetailAdminRole());
    }
  }, [dispatch, open, roleView]);

  const renderPermissions = (detail: AdminRoleDetail) => {
    // Group permissions by module_type
    const groupedPermissions = detail.permissions.reduce(
      (acc, permission) => {
        const type = permission.module.module_type;
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(permission);
        return acc;
      },
      {} as Record<string, typeof detail.permissions>
    );

    const modulePermissions = Object.entries(
      groupedPermissions
    ).filter(([type]) => type !== "REPORT");

    const reportPermissions = Object.entries(
      groupedPermissions
    ).filter(([type]) => type === "REPORT");

    return (
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-4 dark:text-white">
          {t("roles.modal.sections.permissions")}
        </h3>
        <Tabs
          defaultActiveKey="module"
          items={[
            {
              key: "module",
              label: t(
                "roles.modal.permissions.module.title"
              ),
              children: (
                <div className="space-y-6">
                  {modulePermissions.map(
                    ([type, permissions]) => (
                      <PermissionsTable
                        key={type}
                        type={type}
                        permissions={permissions}
                      />
                    )
                  )}
                </div>
              ),
            },
            {
              key: "report",
              label: t(
                "roles.modal.permissions.report.title"
              ),
              children: (
                <div className="space-y-6">
                  {reportPermissions.map(
                    ([type, permissions]) => (
                      <PermissionsTable
                        key={type}
                        type={type}
                        permissions={permissions}
                      />
                    )
                  )}
                </div>
              ),
            },
          ]}
        />
      </div>
    );
  };

  if (!roleView && !loading && !errorMessage) return null;

  return (
    <Modal
      title={
        <div className="text-lg font-semibold dark:text-white">
          {t("roles.modal.view.title")}
        </div>
      }
      open={open}
      onCancel={handleClose}
      footer={null}
      className="rounded-xl"
      style={{
        maxWidth: "98vw",
        margin: "0 auto",
      }}
      width={800}
    >
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <Spin size="large" />
        </div>
      ) : errorMessage ? (
        <div className="flex items-center gap-2 text-red-500 py-4">
          <MdError className="text-xl" />
          <span>{errorMessage}</span>
        </div>
      ) : (
        detail && (
          <div className="mt-4">
            {/* Basic Role Information */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-4 dark:text-white">
                {t("roles.modal.sections.roleInfo")}
              </h3>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <MdSecurity className="text-blue-500 text-xl" />
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {t("roles.modal.form.name.label")}
                    </div>
                    <div className="font-medium dark:text-white">
                      {detail.role_name}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <MdBusiness className="text-green-500 text-xl" />
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {t("roles.modal.fields.parentBranch")}
                    </div>
                    <div className="font-medium dark:text-white">
                      {detail.parent_branch.branch_name}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Module Permissions */}
            {renderPermissions(detail)}

            {/* Additional Information */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-4 dark:text-white">
                {t("roles.modal.sections.additionalInfo")}
              </h3>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div
                    className={`w-2 h-2 rounded-full ${
                      detail.active
                        ? "bg-green-500"
                        : "bg-red-500"
                    }`}
                  />
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {t("roles.modal.status.label")}
                    </div>
                    <div className="font-medium dark:text-white">
                      {detail.active
                        ? t("roles.modal.status.active")
                        : t("roles.modal.status.inactive")}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-blue-500" />
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {t("roles.modal.fields.createdAt")}
                    </div>
                    <div className="font-medium dark:text-white">
                      {dayjs(detail.created_at).format(
                        "DD MMM YYYY"
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      )}
    </Modal>
  );
});

ModalViewAdminRoles.displayName = "ModalViewAdminRoles";

export default ModalViewAdminRoles;
