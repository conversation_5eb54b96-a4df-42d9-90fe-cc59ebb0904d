import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../..";
import maintenanceSysAdminMainApi from "../../../services/mainApi/sysadmin/maintenance.sysadmin.mainApi";
import { Maintenance } from "../../../services/mainApi/sysadmin/types/maintenance.mainApi.types";
import { UpdateMaintenanceRequest } from "../../../services/mainApi/sysadmin/types/maintenance.mainApi.types";
import { isAxiosError } from "axios";
import { handleApiError } from "../../../utils/error";

interface State {
    open: boolean;
    mode: "create" | "update";
    maintenanceEdit: Maintenance | null;
    loading: boolean;
    errorMessage: string[] | null;

    is_maintenance: boolean;
    description: string;
}

const initialState: State = {
    open: false,
    mode: "create",
    maintenanceEdit: null,
    loading: false,
    errorMessage: null,

    is_maintenance: false,
    description: "",
}

export const updateMaintenance = createAsyncThunk(
    "maintenance/updateMaintenance",
    async (
        settingId: string,
        {getState, rejectWithValue}
    ) => {
        try {
            const state = getState() as RootState;
            const settingData = state.editSettingSysadmin;

            const UpdateData: UpdateMaintenanceRequest = {
                is_maintenance: settingData.is_maintenance,
                description: settingData.description,
            };
            const response = await maintenanceSysAdminMainApi.update(settingId, UpdateData);
            return response;
        } catch (error) {
            if (isAxiosError(error)) {
                const e = handleApiError(error);
                return rejectWithValue(e.errors);
              }
              if (error instanceof Error) {
                return rejectWithValue(error.message);
              }
              return rejectWithValue("An unknown error occurred");
        }
    }
)

const EditSettingSysadminSlice = createSlice({
    name: "editSettingSysadmin",
    initialState,
    reducers: {
        open: (state) => {
            state.open = true;
            state.mode = "create";
        },
        openEdit: (state, action: PayloadAction<Maintenance | null>) => {
            state.open = true;
            state.mode = "update";
            state.maintenanceEdit = action.payload
            state.is_maintenance = action.payload?.is_maintenance || false;
            state.description = action.payload?.description || "";
        },
        close: () => {
            return {
                ...initialState,
            }
        },
        setError: (
            state,
            action: PayloadAction<string | null>
        ) => {
            state.errorMessage = action.payload ? [action.payload] : null;
        },
        setIsMaintenance: (
            state,
            action: PayloadAction<boolean>
        ) => {
            state.is_maintenance = action.payload;
        },
        setDescription: (
          state,
          action: PayloadAction<string>
        ) => {
          state.description = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            // Update License
            .addCase(updateMaintenance.pending, (state) => {
              state.loading = true;
              state.errorMessage = null;
            })
            .addCase(updateMaintenance.fulfilled, (state) => {
              state.loading = false;
              state.open = false;
            })
            .addCase(updateMaintenance.rejected, (state, action) => {
              state.loading = false;
              state.errorMessage = action.payload as string[];
            });
    },
});

export default EditSettingSysadminSlice;