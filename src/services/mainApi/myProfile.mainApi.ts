import baseMainApi from "./base.mainApi";
import { MyProfileResponse } from "./types/myProfile.mainApi.types";

const myProfileMainApi = {
  getProfile: async (queries?: {
    branch_code?: string;
  }) => {
    const response =
      await baseMainApi.get<MyProfileResponse>(
        "/web-api/account/profile",
        {
          params: queries,
        }
      );
    return response.data;
  },
};

export default myProfileMainApi;
