import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { debounce } from "lodash";
import { AppDispatch } from "../../index";
import formPicklistAdminMainApi from "../../../services/mainApi/admin/formPicklist.admin.mainApi";
import {
  AdminFormPicklist,
  GetFormPicklistRequest,
} from "../../../services/mainApi/admin/types/formPicklist.admin.mainApi.types";

// Define the FormPicklistOption type
interface FormPicklistOption {
  id: string;
  form_picklist_id: string;
  form_picklist_option_name: string;
  form_picklist_option_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
}

// Define the Branch type
interface Branch {
  id: string;
  parent_id: string | null;
  timezone_id: string;
  license_id: string;
  reseller_id: string | null;
  branch_code: string;
  branch_name: string;
  branch_description: string;
  branch_logo: string;
  branch_colour: string;
  gps_tracking_enabled: boolean;
  gps_interval: number;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

// Define the FormPicklist type
export interface FormPicklist {
  id: string;
  branch_id: string;
  form_picklist_name: string;
  form_picklist_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
  options: FormPicklistOption[];
  branch: Branch;
}

interface State {
  picklists: AdminFormPicklist[];
  loading: boolean;
  errorMessage: string | null;
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
    ignoreActiveStatus: boolean;
  };
  pagination: {
    total: number;
    totalPages: number;
  };
}

const initState: State = {
  picklists: [],
  loading: false,
  errorMessage: null,
  filter: {
    search: null,
    page: 1,
    limit: 10,
    orderBy: "created_at",
    orderDirection: "DESC",
    ignoreActiveStatus: false,
  },
  pagination: {
    total: 0,
    totalPages: 1,
  },
};

// Create the async thunk for fetching picklists
export const fetchFormPicklists = createAsyncThunk(
  "formPicklistAdmin/fetchFormPicklists",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as {
        formPicklistAdmin: State;
      };
      const { filter } = state.formPicklistAdmin;

      const params: GetFormPicklistRequest = {
        search: filter.search || undefined,
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
        ignore_active_status: filter.ignoreActiveStatus,
      };

      const response =
        await formPicklistAdminMainApi.getFormPicklists(
          branchCode,
          params
        );

      return response;
    } catch (error: unknown) {
      if (
        error &&
        typeof error === "object" &&
        "response" in error
      ) {
        const err = error as {
          response?: { data?: { message?: string } };
        };
        return rejectWithValue(
          err.response?.data?.message ||
            "Failed to fetch picklists"
        );
      }
      return rejectWithValue("Failed to fetch picklists");
    }
  }
);

// Create a debounced version of the fetch function
export const debouncedFetchFormPicklists = debounce(
  (dispatch: AppDispatch, branchCode: string) => {
    dispatch(fetchFormPicklists(branchCode));
  },
  500
);

const formPicklistAdminSlice = createSlice({
  name: "formPicklistAdmin",
  initialState: initState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchFormPicklists.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchFormPicklists.fulfilled,
        (state, action) => {
          state.loading = false;
          if (action.payload.data) {
            state.picklists = action.payload.data;
            state.pagination.total =
              action.payload.meta.total || 0;
            state.pagination.totalPages =
              action.payload.meta.total_pages || 1;
          }
        }
      )
      .addCase(
        fetchFormPicklists.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      );
  },
});

export const { actions } = formPicklistAdminSlice;
export default formPicklistAdminSlice;
