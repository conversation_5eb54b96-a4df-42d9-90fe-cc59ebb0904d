.leaflet-container {
  width: 100% !important;
  height: 100% !important;
  z-index: 1;
  border-radius: 4px;
}

/* Fix for Safari */
.leaflet-container, .leaflet-container * {
  box-sizing: border-box;
}

/* Make sure attribution is readable but not too prominent */
.leaflet-control-attribution {
  font-size: 9px;
  background-color: rgba(255, 255, 255, 0.7);
}

/* Better styling for popup */
.leaflet-popup-content-wrapper {
  border-radius: 4px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

/* Animation for the location finding indicator */
@keyframes pulse {
  0% {
    opacity: 0.4;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.4;
    transform: scale(0.8);
  }
}

.location-finding-indicator {
  animation: pulse 1.5s infinite ease-in-out;
}

/* The parent wrapper must be sized appropriately */
.map-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Override inline styles with !important to ensure proper sizing */
.map-wrapper > .leaflet-container {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* Polygon controls */
.polygon-controls {
  position: absolute;
  z-index: 1000;
  bottom: 20px;
  right: 20px;
}

/* New map custom controls */
.map-custom-controls {
  position: absolute;
  z-index: 1000;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.map-control-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: white;
  color: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
}

.map-control-btn:hover {
  background-color: #f0f8ff;
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

.map-control-btn.delete-polygon {
  color: #ff4d4f;
}

.map-control-btn.delete-polygon:hover {
  background-color: #fff1f0;
}

.polygon-create-btn {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.polygon-create-btn:hover {
  background-color: #40a9ff;
  box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

/* Leaflet Draw styles overrides */
.leaflet-draw-toolbar a {
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.2);
}

.leaflet-draw-actions a {
  background-color: white;
  color: #1890ff;
  border-radius: 0;
}

/* Coordinate markers and labels */
.coordinate-marker {
  background: transparent;
  border: none;
}

.coordinate-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 10px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
  opacity: 0.9;
}

/* Tooltip styling */
.leaflet-tooltip {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #1890ff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  padding: 4px 8px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
}

/* Vertex tooltip - smaller and less intrusive */
.vertex-tooltip {
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(24, 144, 255, 0.7);
  border-radius: 3px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  padding: 2px 6px;
  font-size: 10px;
  opacity: 0.8 !important;
  pointer-events: none;
  z-index: 600;
  max-width: none;
} 