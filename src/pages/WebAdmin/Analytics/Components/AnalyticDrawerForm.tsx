import React, { useState, useEffect } from "react";
import {
  But<PERSON>,
  Form,
  DatePicker,
  TimePicker,
  Select,
  Affix,
} from "antd";
import { useTranslation } from "react-i18next";
import SelectRole from "../../../../components/Roles/SelectRole";
import SelectUser from "../../../../components/SelectUser/SelectUser";
import SelectLabel from "../../../../components/Label/SelectLabel";
import SelectDevice from "../../../../components/SelectDevice/SelectDevice";
import GridSelectBranch from "../../../../components/Branch/GridSelectBranch";
import SelectActivity from "../../../../components/Activity/SelectActivity";
import SelectZone from "../../../../components/Zone/SelectZone";
import SelectForm from "../../../../components/Form/SelectForm";
import SelectTask from "../../../../components/Task/SelectTask";
import dayjs from "dayjs";
import { EReportType } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { AdminBranch } from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminUser } from "../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminLabel } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminRole } from "../../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminActivity } from "../../../../services/mainApi/admin/types/activity.admin.mainApi.types";
import { AdminZone } from "../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { AdminCheckpoint } from "../../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";
import { AdminForm } from "../../../../services/mainApi/admin/types/form.admin.mainApi.types";
import { AdminTask } from "../../../../services/mainApi/admin/types/task.admin.mainApi.types";
import SelectCheckpoint from "../../../../components/Checkpoint/SelectCheckpoint";
import { Branch } from "../../../../services/mainApi/sysadmin/types/branch.mainApi.types";

const { Option } = Select;

/**
 * Interface for analytics filters that defines the data structure
 * for filter state and what gets passed to onApply callback
 */
export interface AnalyticsFilterState {
  // Common filters that are always present
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;

  // Conditional filters based on report type
  selectedBranch: AdminBranch | null;
  role?: AdminRole | null;
  user?: AdminUser | null;
  userLabels?: AdminLabel[];
  device?: AdminDevice | null;
  deviceLabels?: AdminLabel[];
  activity?: AdminActivity | null;
  site?: AdminZone | null;
  siteLabels?: AdminLabel[];
  checkpoint?: AdminCheckpoint | null;
  checkpointLabels?: AdminLabel[];
  forms?: AdminForm | null;
  tasks?: AdminTask | null;
  rotationInterval?: number;
  rotationMethod?: string;
}

/**
 * Props for AnalyticDrawerForm component
 * The component now uses a simpler props interface with initialValues
 */
interface AnalyticDrawerFormProps {
  reportType: EReportType; // Type of report to be generated
  onApply?: (filters: AnalyticsFilterState) => void; // Optional callback when filters are applied
  onClose?: () => void; // Optional callback when drawer is closed
  initialValues?: Partial<AnalyticsFilterState>; // Optional initial values for form fields
}

/**
 * AnalyticDrawerForm Component
 *
 * A reusable component that displays a form within a drawer for filtering analytics data.
 * The form dynamically shows or hides fields based on the specified report type.
 *
 * @param props Component props of type AnalyticDrawerFormProps
 * @returns React component
 */
const AnalyticDrawerForm: React.FC<
  AnalyticDrawerFormProps
> = ({ reportType, onApply, onClose, initialValues }) => {
  const { t } = useTranslation();

  // Internal state to manage form values
  const [formState, setFormState] =
    useState<AnalyticsFilterState>({
      startDate: "",
      endDate: "",
      startTime: "",
      endTime: "",
      selectedBranch: null,
      role: null,
      user: null,
      userLabels: [],
      device: null,
      deviceLabels: [],
      activity: null,
      site: null,
      siteLabels: [],
      checkpoint: null,
      checkpointLabels: [],
      forms: null,
      tasks: null,
      rotationInterval: 0,
      rotationMethod: "COUNT",
    });

  // Initialize form with initialValues if provided
  useEffect(() => {
    if (initialValues) {
      setFormState((prevState) => ({
        ...prevState,
        ...initialValues,
      }));
    }
  }, [initialValues]);

  // Handler for user change and reset userLabels if specific user is selected
  const handleUserChange = (
    value: string | null,
    adminUser: AdminUser | null
  ) => {
    let user: AdminUser | null;

    if (value) {
      user = adminUser;
    } else {
      user = null;
    }

    setFormState((prevState) => ({
      ...prevState,
      user: user,
      // Reset user labels if a specific user is selected
      ...(value ? { userLabels: [] } : {}),
    }));
  };

  // Handler for device change and reset deviceLabels if specific device is selected
  const handleDeviceChange = (
    value: string | null,
    device: AdminDevice | null
  ) => {
    let _device: AdminDevice | null;

    if (value) {
      _device = device;
    } else {
      _device = null;
    }

    setFormState((prevState) => ({
      ...prevState,
      device: _device,
      // Reset device labels if a specific device is selected
      ...(value ? { deviceLabels: [] } : {}),
    }));
  };

  // Handler for site change and reset siteLabels if specific site is selected
  const handleSiteChange = (
    value: string,
    zone: AdminZone | null
  ) => {
    let site: AdminZone | null;

    if (value) {
      site = zone;
    } else {
      site = null;
    }

    setFormState((prevState) => ({
      ...prevState,
      site: site,
      // Reset site labels if a specific site is selected
      ...(value ? { siteLabels: [] } : {}),
    }));
  };

  // Handle apply filters and close drawer
  const handleApplyFilters = () => {
    if (onApply) {
      onApply(formState);
    }

    if (onClose) onClose();
  };

  /**
   * Function to get drawer title based on report type
   */
  const getDrawerTitle = (): string => {
    switch (reportType) {
      case EReportType.ACTIVITY_LOG:
        return t(
          "analytics.drawer.title.activityLog",
          "Filter Activity Log"
        );
      case EReportType.ALARMS:
        return t(
          "analytics.drawer.title.alarms",
          "Filter Alarms"
        );
      case EReportType.AVERAGE_ROTATION:
        return t(
          "analytics.drawer.title.averageRotation",
          "Filter Average Rotation"
        );
      case EReportType.BRANCH_DETAILS:
        return t(
          "analytics.drawer.title.branchDetails",
          "Filter Branch Details"
        );
      case EReportType.CHECKPOINT_ACTIVITY:
        return t(
          "analytics.drawer.title.checkpointActivity",
          "Filter Checkpoint Activity"
        );
      case EReportType.CHECKPOINT_BATTERY:
        return t(
          "analytics.drawer.title.checkpointBattery",
          "Filter Checkpoint Battery"
        );
      case EReportType.EXCEPTION:
        return t(
          "analytics.drawer.title.exception",
          "Filter Exception"
        );
      case EReportType.EXCEPTION_DETAILED:
        return t(
          "analytics.drawer.title.exceptionDetailed",
          "Filter Exception Detailed"
        );
      case EReportType.FORMS:
        return t(
          "analytics.drawer.title.forms",
          "Filter Forms"
        );
      case EReportType.GEOFENCE:
        return t(
          "analytics.drawer.title.geofence",
          "Filter Geofence"
        );
      case EReportType.GPS_HEATMAP:
        return t(
          "analytics.drawer.title.gpsHeatmap",
          "Filter GPS Heatmap"
        );
      case EReportType.MISSED_ZONE:
        return t(
          "analytics.drawer.title.missedZone",
          "Filter Missed Zone"
        );
      case EReportType.SIGN_ON_OFF:
        return t(
          "analytics.drawer.title.signOnOff",
          "Filter Sign On/Off"
        );
      case EReportType.TASKS:
        return t(
          "analytics.drawer.title.tasks",
          "Filter Tasks"
        );
      case EReportType.TIME_AND_ROTATION:
        return t(
          "analytics.drawer.title.timeAndRotation",
          "Filter Time and Rotation"
        );
      case EReportType.TIME_ON_ZONE:
        return t(
          "analytics.drawer.title.timeOnZone",
          "Filter Time on Zone"
        );
      default:
        return t(
          "analytics.drawer.title.default",
          "Filter Analytics"
        );
    }
  };

  /**
   * Configuration for fields that should be displayed for each report type
   */
  const reportConfig: Record<EReportType, string[]> = {
    // Format: [EReportType]: [array of field names]
    [EReportType.ACTIVITY_LOG]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "role",
      "user",
      "userLabels",
      "device",
      "deviceLabels",
      "activity",
    ],
    [EReportType.ALARMS]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "role",
      "user",
      "userLabels",
      "device",
      "deviceLabels",
    ],
    [EReportType.AVERAGE_ROTATION]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "site",
      "siteLabels",
    ],
    [EReportType.BRANCH_DETAILS]: [
      "branch"
    ], // Only branch selection
    [EReportType.CHECKPOINT_ACTIVITY]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "site",
      "siteLabels",
      "checkpoint",
      "checkpointLabels",
      "role",
      "user",
      "userLabels",
      "device",
      "deviceLabels",
    ],
    [EReportType.CHECKPOINT_BATTERY]: [
      "dateRange", 
      "branch",
      "site",
      "siteLabels",
      "checkpoint",
      "checkpointLabels",
    ],
    [EReportType.EXCEPTION]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "site",
      "siteLabels",
      "checkpoint",
      "checkpointLabels",
      "role",
      "user",
      "userLabels",
      "device",
      "deviceLabels",
    ],
    [EReportType.EXCEPTION_DETAILED]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "site",
      "siteLabels",
      "checkpoint",
      "checkpointLabels",
      "role",
      "user",
      "userLabels",
      "device",
      "deviceLabels",
    ],
    [EReportType.FORMS]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "role",
      "user",
      "userLabels",
      "device",
      "deviceLabels",
      "forms",
    ],
    [EReportType.GEOFENCE]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "site",
      "siteLabels",
      "role",
      "user",
      "userLabels",
      "device",
      "deviceLabels",
    ],
    [EReportType.GPS_HEATMAP]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "user", 
      "userLabels"
    ],
    [EReportType.MISSED_ZONE]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "site", 
      "siteLabels"
    ],
    [EReportType.SIGN_ON_OFF]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "user",
      "userLabels",
      "device",
      "deviceLabels",
    ],
    [EReportType.TASKS]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "role",
      "user",
      "userLabels",
      "device",
      "deviceLabels",
      "tasks",
    ],
    [EReportType.TIME_AND_ROTATION]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "site",
      "siteLabels",
      "checkpoint",
      "checkpointLabels",
      "role",
      "user",
      "userLabels",
      "device",
      "deviceLabels",
      "rotationInterval",
      "rotationMethod",
    ],
    [EReportType.TIME_ON_ZONE]: [
      "dateRange", 
      "timeRange", 
      "branch",
      "site",
      "siteLabels",
      "user",
      "userLabels",
      "device",
      "deviceLabels",
    ],
    [EReportType.UNKNOWN]: [
      "dateRange", 
      "timeRange"
    ], // Unknown report type
  };

  /**
   * Function to check if a specific field should be displayed based on the report type
   */
  const shouldShowField = (fieldName: string): boolean => {
    // Check if the field should be displayed for the current report type
    return (
      reportConfig[reportType]?.includes(fieldName) || false
    );
  };

  // Common fields that are conditionally displayed based on report type
  const renderCommonFields = () => (
    <>
      {/* Date Range - conditionally rendered */}
      {shouldShowField("dateRange") && (
        <div className="mb-6">
          <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">
            {t(
              "analytics.form.dateRange.title",
              "Date Range"
            )}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              label={t(
                "analytics.form.startDate.label",
                "Start Date"
              )}
              required
              tooltip={t(
                "analytics.form.startDate.tooltip",
                "Select start date for filtering"
              )}
            >
              <DatePicker
                value={
                  formState.startDate
                    ? dayjs(formState.startDate)
                    : undefined
                }
                className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                allowClear={false}
                disabledDate={(current) => {
                  // Disable dates after end date
                  if (formState.endDate) {
                    return current && current.isAfter(dayjs(formState.endDate));
                  }
                  return false;
                }}
                onChange={(date) => {
                  if (date) {
                    const dateStr = date.format("YYYY-MM-DD");
                    // Check if end date exists and start date is after end date
                    if (formState.endDate && dayjs(dateStr).isAfter(dayjs(formState.endDate))) {
                      // If invalid, don't update
                      return;
                    }
                    setFormState((prevState) => ({
                      ...prevState,
                      startDate: dateStr,
                    }));
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              label={t(
                "analytics.form.endDate.label",
                "End Date"
              )}
              required
              tooltip={t(
                "analytics.form.endDate.tooltip",
                "Select end date for filtering"
              )}
            >
              <DatePicker
                value={
                  formState.endDate
                    ? dayjs(formState.endDate)
                    : undefined
                }
                className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                allowClear={false}
                disabledDate={(current) => {
                  // Disable dates before start date
                  if (formState.startDate) {
                    return current && current.isBefore(dayjs(formState.startDate));
                  }
                  return false;
                }}
                onChange={(date) => {
                  if (date) {
                    const dateStr = date.format("YYYY-MM-DD");
                    // Check if start date exists and end date is before start date
                    if (formState.startDate && dayjs(dateStr).isBefore(dayjs(formState.startDate))) {
                      // If invalid, don't update
                      return;
                    }
                    setFormState((prevState) => ({
                      ...prevState,
                      endDate: dateStr,
                    }));
                  }
                }}
              />
            </Form.Item>
          </div>
        </div>
      )}

      {/* Time Range - conditionally rendered */}
      {shouldShowField("timeRange") && (
        <div className="mb-6">
          <h3 className="text-base font-medium mb-2 text-gray-700 dark:text-gray-300">
            {t(
              "analytics.form.timeRange.title",
              "Time Range"
            )}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              label={t(
                "analytics.form.startTime.label",
                "Start Time"
              )}
              required
              tooltip={t(
                "analytics.form.startTime.tooltip",
                "Select start time for filtering"
              )}
            >
              <TimePicker
                format="HH:mm"
                className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                value={
                  formState.startTime
                    ? dayjs(
                        `${dayjs().format("YYYY-MM-DD")} ${formState.startTime}`
                      )
                    : null
                }
                onChange={(time) => {
                  const timeStr = time
                    ? time.format("HH:mm")
                    : "";
                  setFormState((prevState) => ({
                    ...prevState,
                    startTime: timeStr,
                  }));
                }}
              />
            </Form.Item>
            <Form.Item
              label={t(
                "analytics.form.endTime.label",
                "End Time"
              )}
              required
              tooltip={t(
                "analytics.form.endTime.tooltip",
                "Select end time for filtering"
              )}
            >
              <TimePicker
                format="HH:mm"
                className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                value={
                  formState.endTime
                    ? dayjs(
                        `${dayjs().format("YYYY-MM-DD")} ${formState.endTime}`
                      )
                    : null
                }
                onChange={(time) => {
                  const timeStr = time
                    ? time.format("HH:mm")
                    : "";
                  setFormState((prevState) => ({
                    ...prevState,
                    endTime: timeStr,
                  }));
                }}
              />
            </Form.Item>
          </div>
        </div>
      )}

      {/* Branch Selection - conditionally rendered */}
      {shouldShowField("branch") && (
        <Form.Item
          label={t("analytics.form.branch.label", "Branch")}
          tooltip={t(
            "analytics.form.branch.tooltip",
            "Select branch for filtering"
          )}
        >
          <GridSelectBranch
            mode="select"
            clearable={true}
            value={formState.selectedBranch?.id || undefined}
            onChange={(value: Branch | Branch[]) => {
              if (Array.isArray(value)) {
                setFormState((prevState) => ({
                  ...prevState,
                  selectedBranch: value[0] || null,
                }));
              } else {
                setFormState((prevState) => ({
                  ...prevState,
                  selectedBranch: value || null,
                }));
              }
            }}
            className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
          />
        </Form.Item>
      )}
    </>
  );

  // Render conditional fields based on report type
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 pb-20 relative">
      <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-6">
        {getDrawerTitle()}
      </h2>

      <Form
        layout="vertical"
        className="space-y-6"
      >
        {/* Common fields (always shown) */}
        {renderCommonFields()}

        {/* Conditional fields based on report type */}
        {shouldShowField("role") && (
          <Form.Item
            label={t("analytics.form.role.label", "Role")}
            tooltip={t(
              "analytics.form.role.tooltip",
              "Select the role for filtering"
            )}
          >
            <SelectRole
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              value={formState.role?.id || undefined}
              onChange={(_, role) => {
                setFormState((prevState) => ({
                  ...prevState,
                  role: role || null,
                }));
              }}
              clearable={true}
              placeholder="All Roles"
            />
          </Form.Item>
        )}

        {shouldShowField("user") && (
          <Form.Item
            label={t("analytics.form.user.label", "User")}
            tooltip={t(
              "analytics.form.user.tooltip",
              "Select the user for filtering"
            )}
          >
            <SelectUser
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              value={formState.user?.id || undefined}
              onChange={handleUserChange}
              clearable={true}
              placeholder="All Users"
            />
          </Form.Item>
        )}

        {shouldShowField("userLabels") && (
          <Form.Item
            label={t(
              "analytics.form.userLabel.label",
              "User Labels"
            )}
            tooltip={t(
              "analytics.form.userLabel.tooltip",
              "Select user labels for filtering"
            )}
          >
            <SelectLabel
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              mode="multiple"
              value={
                formState.userLabels?.map(
                  (label) => label.id
                ) || undefined
              }
              onChange={(_, labels) => {
                setFormState((prevState) => ({
                  ...prevState,
                  userLabels: labels || [],
                }));
              }}
              placeholder="Select User Labels"
              disabled={!!formState.user} // Disable if specific user is selected
            />
          </Form.Item>
        )}

        {shouldShowField("device") && (
          <Form.Item
            label={t(
              "analytics.form.device.label",
              "Device"
            )}
            tooltip={t(
              "analytics.form.device.tooltip",
              "Select the device for filtering"
            )}
          >
            <SelectDevice
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              value={formState.device?.id || undefined}
              onChange={handleDeviceChange}
              clearable={true}
              placeholder="All Devices"
            />
          </Form.Item>
        )}

        {shouldShowField("deviceLabels") && (
          <Form.Item
            label={t(
              "analytics.form.deviceLabel.label",
              "Device Labels"
            )}
            tooltip={t(
              "analytics.form.deviceLabel.tooltip",
              "Select device labels for filtering"
            )}
          >
            <SelectLabel
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              mode="multiple"
              value={
                formState.deviceLabels?.map(
                  (label) => label.id
                ) || undefined
              }
              onChange={(_, labels) => {
                setFormState((prevState) => ({
                  ...prevState,
                  deviceLabels: labels || [],
                }));
              }}
              placeholder="Select Device Labels"
              disabled={!!formState.device} // Disable if specific device is selected
            />
          </Form.Item>
        )}

        {shouldShowField("activity") && (
          <Form.Item
            label={t(
              "analytics.form.activity.label",
              "Activity"
            )}
            tooltip={t(
              "analytics.form.activity.tooltip",
              "Select the activity for filtering"
            )}
          >
            <SelectActivity
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              value={formState.activity?.id || undefined}
              onChange={(_, activity) => {
                setFormState((prevState) => ({
                  ...prevState,
                  activity: activity || null,
                }));
              }}
              clearable={true}
              placeholder="All Activities"
            />
          </Form.Item>
        )}

        {shouldShowField("site") && (
          <Form.Item
            label={t("analytics.form.site.label", "Site")}
            tooltip={t(
              "analytics.form.site.tooltip",
              "Select the site for filtering"
            )}
          >
            <SelectZone
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              value={formState.site?.id || undefined}
              onChange={handleSiteChange}
              clearable={true}
              placeholder="All Sites"
            />
          </Form.Item>
        )}

        {shouldShowField("siteLabels") && (
          <Form.Item
            label={t(
              "analytics.form.siteLabel.label",
              "Site Labels"
            )}
            tooltip={t(
              "analytics.form.siteLabel.tooltip",
              "Select site labels for filtering"
            )}
          >
            <SelectLabel
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              mode="multiple"
              value={
                formState.siteLabels?.map(
                  (label) => label.id
                ) || undefined
              }
              onChange={(_, labels) => {
                setFormState((prevState) => ({
                  ...prevState,
                  siteLabels: labels || [],
                }));
              }}
              placeholder="Select Site Labels"
              disabled={!!formState.site} // Disable if specific site is selected
            />
          </Form.Item>
        )}

        {shouldShowField("checkpoint") && (
          <Form.Item
            label={t(
              "analytics.form.checkpoint.label",
              "Checkpoint"
            )}
            tooltip={t(
              "analytics.form.checkpoint.tooltip",
              "Select the checkpoint for filtering"
            )}
          >
            <SelectCheckpoint
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              value={formState.checkpoint?.id || undefined}
              onChange={(_, checkpoint) => {
                setFormState((prevState) => ({
                  ...prevState,
                  checkpoint: checkpoint || null,
                }));
              }}
              clearable={true}
              placeholder="All Checkpoints"
            />
          </Form.Item>
        )}

        {shouldShowField("checkpointLabels") && (
          <Form.Item
            label={t(
              "analytics.form.checkpointLabel.label",
              "Checkpoint Labels"
            )}
            tooltip={t(
              "analytics.form.checkpointLabel.tooltip",
              "Select checkpoint labels for filtering"
            )}
          >
            <SelectLabel
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              mode="multiple"
              value={
                formState.checkpointLabels?.map(
                  (label) => label.id
                ) || undefined
              }
              onChange={(_, labels) => {
                setFormState((prevState) => ({
                  ...prevState,
                  checkpointLabels: labels || [],
                }));
              }}
              placeholder="Select Checkpoint Labels"
              disabled={!!formState.checkpoint} // Disable if specific checkpoint is selected
            />
          </Form.Item>
        )}

        {shouldShowField("forms") && (
          <Form.Item
            label={t("analytics.form.forms.label", "Forms")}
            tooltip={t(
              "analytics.form.forms.tooltip",
              "Select the forms for filtering"
            )}
          >
            <SelectForm
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              value={formState.forms?.id || undefined}
              onChange={(_, form) => {
                setFormState((prevState) => ({
                  ...prevState,
                  forms: form || null,
                }));
              }}
              clearable={true}
              placeholder="All Forms"
            />
          </Form.Item>
        )}

        {shouldShowField("tasks") && (
          <Form.Item
            label={t("analytics.form.tasks.label", "Tasks")}
            tooltip={t(
              "analytics.form.tasks.tooltip",
              "Select the tasks for filtering"
            )}
          >
            <SelectTask
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              value={formState.tasks?.id || undefined}
              onChange={(_, task) => {
                setFormState((prevState) => ({
                  ...prevState,
                  tasks: task || null,
                }));
              }}
              clearable={true}
              placeholder="All Tasks"
            />
          </Form.Item>
        )}

        {shouldShowField("rotationInterval") && (
          <Form.Item
            label={t(
              "analytics.form.rotationInterval.label",
              "Rotation Interval (Minutes)"
            )}
            tooltip={t(
              "analytics.form.rotationInterval.tooltip",
              "Enter rotation interval in minutes"
            )}
            required
          >
            <Select
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              value={formState.rotationInterval}
              onChange={(value) => {
                setFormState((prevState) => ({
                  ...prevState,
                  rotationInterval: value || 0,
                }));
              }}
            >
              <Option value={10}>10</Option>
              <Option value={15}>15</Option>
              <Option value={20}>20</Option>
              <Option value={30}>30</Option>
              <Option value={40}>40</Option>
              {Array.from({ length: 141 }, (_, i) => i * 10 + 50).filter(val => val <= 1440).map(value => (
                <Option key={value} value={value}>{value}</Option>
              ))}
            </Select>
          </Form.Item>
        )}

        {shouldShowField("rotationMethod") && (
          <Form.Item
            label={t(
              "analytics.form.rotationMethod.label",
              "Rotation Method"
            )}
            tooltip={t(
              "analytics.form.rotationMethod.tooltip",
              "Select rotation method"
            )}
          >
            <Select
              className="w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
              value={formState.rotationMethod}
              onChange={(value) => {
                if (value) {
                  setFormState((prevState) => ({
                    ...prevState,
                    rotationMethod: value,
                  }));
                }
              }}
            >
              <Option value="COUNT">Count</Option>
              <Option value="PERCENTAGE">Percentage</Option>
            </Select>
          </Form.Item>
        )}

        {/* Apply Button */}
        <div className="pt-4">
          {/* No border-t here as we're using the fixed button below */}
        </div>
      </Form>

      {/* Fixed Apply Button with Affix */}
      <Affix
        offsetBottom={0}
        className="absolute bottom-0 left-0 right-0"
      >
        <div className="bg-white dark:bg-gray-800 p-4 border-t w-full">
          <Button
            type="primary"
            size="large"
            onClick={handleApplyFilters}
            className="w-full bg-blue-500 hover:bg-blue-600"
          >
            {t("analytics.form.apply", "Apply Filters")}
          </Button>
        </div>
      </Affix>
    </div>
  );
};

export default AnalyticDrawerForm;
