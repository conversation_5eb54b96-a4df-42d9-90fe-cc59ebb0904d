import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Tooltip } from "antd";
import { AdminGeofence } from "../../../../services/mainApi/admin/types/geofence.admin.mainApi.types";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { MdAccessTime } from "react-icons/md";
import {
  FiEdit,
  FiTrash2,
  FiMapPin,
  FiClock,
  FiChevronDown,
  FiChevronUp,
  FiTag
} from "react-icons/fi";
import { useAppDispatch } from "../../../../store/hooks";
import addEditGeofenceAdminSlice from "../../../../store/slices/admin/addEditGeofence.admin.slice";

interface GeofenceListItemProps {
  geofence: AdminGeofence;
}

const GeofenceListItem: React.FC<GeofenceListItemProps> = ({
  geofence,
}) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);

  const handleEdit = () => {
    dispatch(
      addEditGeofenceAdminSlice.actions.openEdit(geofence)
    );
  };

  const handleDelete = () => {
    console.log("Delete geofence:", geofence.id);
  };

  // Function to truncate text and add ellipsis
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return "-";
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  const formatDuration = (duration: string) => {
    const minutes = parseInt(duration);
    if (isNaN(minutes)) return duration;

    if (minutes < 60) {
      return `${minutes} ${t("common.minutes", "minutes")}`;
    }

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (remainingMinutes === 0) {
      return `${hours} ${t("common.hours", "hours")}`;
    }

    return `${hours} ${t("common.hours", "hours")} ${remainingMinutes} ${t("common.minutes", "minutes")}`;
  };

  const getActiveTimeDisplay = () => {
    if (
      !geofence.active_time_start ||
      !geofence.active_time_end
    ) {
      return t(
        "geofence.list.noActiveTime",
        "No active time"
      );
    }
    return `${geofence.active_time_start} - ${geofence.active_time_end}`;
  };

  const getStayDurationDisplay = () => {
    if (
      !geofence.minimum_stay_duration &&
      !geofence.maximum_stay_duration
    ) {
      return t(
        "geofence.list.noStayDuration",
        "No stay duration"
      );
    }
    return (
      <div className="flex items-center gap-2">
        <span>
          Min:{" "}
          {formatDuration(geofence.minimum_stay_duration)}
        </span>
        <span>•</span>
        <span>
          Max:{" "}
          {formatDuration(geofence.maximum_stay_duration)}
        </span>
      </div>
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Header section */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <FiMapPin className="text-blue-500" />
            {geofence.geofence_name}
            {geofence.active === false && (
              <Tag
                color="red"
                className="ml-2"
              >
                {t("geofence.list.status.inactive", "Inactive")}
              </Tag>
            )}
          </h3>
          {geofence.zone?.zone_name && (
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              {geofence.zone.zone_name}
            </p>
          )}
          <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            <p>
              {isDescriptionExpanded
                ? geofence.geofence_description || "-"
                : truncateText(geofence.geofence_description || "-", 100)}
            </p>
            {geofence.geofence_description && geofence.geofence_description.length > 100 && (
              <button
                onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                className="text-blue-500 hover:text-blue-600 flex items-center mt-1 text-xs font-medium"
              >
                {isDescriptionExpanded
                  ? <><FiChevronUp className="mr-1" /> {t("common.showLess")}</>
                  : <><FiChevronDown className="mr-1" /> {t("common.showMore")}</>
                }
              </button>
            )}
          </div>
        </div>
        <div>
          <Tooltip title={t("common.edit")}>
            <Button
              type="text"
              icon={<FiEdit className="text-lg" />}
              onClick={handleEdit}
              className="text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400"
            />
          </Tooltip>
          <Tooltip title={t("common.remove")}>
            <Button
              type="text"
              icon={<FiTrash2 className="text-lg" />}
              onClick={handleDelete}
              className="text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400"
            />
          </Tooltip>
        </div>
      </div>

      {/* Content section */}
      <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Left column */}
        <div className="space-y-4">
          {/* Details section */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
              <FiClock className="text-blue-500" />
              {t("geofence.list.activeTime", "Active Time")}
            </h4>
            <div className="grid grid-cols-1 gap-x-2 gap-y-1 text-xs p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="flex items-center gap-1">
                <span className="text-gray-600 dark:text-gray-300">
                  {t("geofence.list.activeTimeRange", "Time Range")}:
                </span>
                <span className="font-medium">
                  {getActiveTimeDisplay()}
                </span>
              </div>
            </div>
          </div>

          {/* Geofence Data section */}
          {geofence.geofence_data && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                <FiMapPin className="text-red-500" />
                {t("geofence.list.geofenceData", "Geofence Data")}
              </h4>
              <div className="grid grid-cols-1 gap-x-2 gap-y-1 text-xs p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                <div className="flex items-center gap-1">
                  <span className="text-gray-600 dark:text-gray-300">
                    {t("geofence.list.type", "Type")}:
                  </span>
                  <span className="font-medium">
                    {geofence.geofence_data.type}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-gray-600 dark:text-gray-300">
                    {t("geofence.list.points", "Points")}:
                  </span>
                  <span className="font-medium">
                    {geofence.geofence_data.coordinates[0]?.length || 0}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Right column */}
        <div className="space-y-4">
          {/* Stay Duration section */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
              <MdAccessTime className="text-yellow-500" />
              {t("geofence.list.stayDuration", "Stay Duration")}
            </h4>
            <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              {getStayDurationDisplay()}
            </div>
          </div>

          {/* Labels section */}
          {geofence.labels && geofence.labels.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                <FiTag className="text-green-500" />
                {t("common.labels", "Labels")}
              </h4>
              <div className="flex flex-wrap gap-2 p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                {geofence.labels.map((label) => (
                  <Tag
                    key={label.id}
                    className="rounded-full m-0"
                    color={label.active ? "processing" : "default"}
                  >
                    {label.label_name}
                  </Tag>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer section */}
      <div className="px-4 py-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400">
        {t("common.created")}{" "}
        {dayjs(geofence.created_at).format("DD MMM YYYY HH:mm")}
      </div>
    </div>
  );
};

export default GeofenceListItem;
