import { <PERSON>, <PERSON>, <PERSON><PERSON>, Tooltip } from "antd";
import {
  EditOutlined,
  InfoCircleOutlined,
  GlobalOutlined,
} from "@ant-design/icons";
import { Branch } from "../../../../services/mainApi/sysadmin/types/branch.mainApi.types.ts";
import { BranchSkeleton } from "./BranchSkeleton.tsx";
import dayjs from "dayjs";

interface BranchListProps {
  branches: Branch[];
  loading: boolean;
  onEdit: (branch: Branch) => void;
  onDelete: (branch: Branch) => void;
}

export default function BranchList({
  branches,
  loading,
  onEdit,
}: BranchListProps) {
  if (loading) {
    return <BranchSkeleton />;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6 p-6">
      {branches.map((branch) => (
        <div
          key={branch.id}
          className="h-full"
        >
          <Card
            className={`
              rounded-lg shadow hover:shadow-lg transition-all duration-300 h-full
              bg-white dark:bg-gray-800/95 border border-gray-100 dark:border-gray-700
              ${!branch.active ? "opacity-75" : ""}
            `}
            bodyStyle={{ 
              padding: '1.25rem',
              height: '100%',
              display: 'flex',
              flexDirection: 'column'
            }}
            bordered={false}
          >
            <div className="flex justify-between items-start mb-4">
              <Tag
                className={`
                  rounded-full px-3 py-1 text-xs font-medium
                  ${
                    branch.active
                      ? "bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-300 border-green-200 dark:border-green-800/60"
                      : "bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-300 border-red-200 dark:border-red-800/60"
                  }
                `}
              >
                {branch.active ? "Active" : "Inactive"}
              </Tag>
              
              <Tooltip title="Edit Branch">
                <Button
                  type="text"
                  shape="circle"
                  icon={<EditOutlined />}
                  onClick={() => onEdit(branch)}
                  className="text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:text-gray-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20"
                />
              </Tooltip>
            </div>
            
            <div className="space-y-4 flex-grow">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                  {branch.branch_name}
                </h3>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                  License: {branch.license.license_name}
                </p>
              </div>

              <div className="space-y-3 mt-4">
                <div className="flex items-center text-gray-600 dark:text-gray-300">
                  <GlobalOutlined className="mr-2 text-gray-500 dark:text-gray-400 text-base" />
                  <span className="text-sm">
                    {branch.timezone.timezone_name} ({branch.timezone.gmt_offset})
                  </span>
                </div>

                {branch.branch_description && (
                  <div className="flex items-start text-gray-600 dark:text-gray-300">
                    <InfoCircleOutlined className="mr-2 mt-1 text-gray-500 dark:text-gray-400 text-base" />
                    <span className="text-sm line-clamp-2">
                      {branch.branch_description}
                    </span>
                  </div>
                )}

                {branch.branch_owner_user && (
                  <div className="flex flex-col text-gray-700 dark:text-gray-200 mt-2">
                    <span className="text-xs font-semibold uppercase tracking-wide text-gray-400 dark:text-gray-500 mb-1">Branch Owner</span>
                    <span className="text-sm font-medium">{branch.branch_owner_user.name}</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">{branch.branch_owner_user.email}</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700/80">
              <p className="text-xs text-gray-400 dark:text-gray-500">
                Created: {dayjs(branch.created_at).format("DD MMM YYYY HH:mm")}
              </p>
            </div>
          </Card>
        </div>
      ))}

      {branches.length === 0 && (
        <div className="col-span-full flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
          <p className="text-lg font-medium">No branches found</p>
          <p className="text-sm mt-2">
            Try adjusting your search or filters
          </p>
        </div>
      )}
    </div>
  );
}
