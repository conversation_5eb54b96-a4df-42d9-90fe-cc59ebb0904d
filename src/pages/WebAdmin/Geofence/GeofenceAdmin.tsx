import { Button, Input, Select } from "antd";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {
  PlusOutlined,
  SearchOutlined,
  SortAscendingOutlined,
} from "@ant-design/icons";
import { MdMap } from "react-icons/md";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import { useTranslation } from "react-i18next";
import "leaflet/dist/leaflet.css";
import GeofenceList from "./Components/GeofenceList";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks";
import { useCallback, useEffect, useRef } from "react";
import { useParams } from "react-router-dom";
import geofenceAdminSlice, {
  fetchGeofencesAdmin,
} from "../../../store/slices/admin/geofence.admin.slice.ts";
import _ from "lodash";
import ModalAddEditGeofence from "./Components/ModalAddEditGeofence";
import addEditGeofenceAdminSlice from "../../../store/slices/admin/addEditGeofence.admin.slice";

const { Option } = Select;

export default function GeofenceAdmin() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { branchCode } = useParams();
  const { actions } = geofenceAdminSlice;
  const geofenceAdmin = useAppSelector(
    (state) => state.geofenceAdmin
  );
  const isFetching = useRef<boolean>(false);
  const debounce = useRef(
    _.debounce(() => {
      dispatch(fetchGeofencesAdmin(branchCode || ""));
    }, 500)
  );

  const handleSearch = useCallback(
    (value: string) => {
      dispatch(
        actions.setFilter({ search: value || null })
      );
      debounce.current();
    },
    [actions, dispatch]
  );

  const handleSortChange = useCallback(
    (value: string) => {
      dispatch(actions.setFilter({ orderBy: value }));
    },
    [actions, dispatch]
  );

  const handleSortDirectionToggle = useCallback(() => {
    dispatch(
      actions.setFilter({
        orderDirection:
          geofenceAdmin.filter.orderDirection === "ASC"
            ? "DESC"
            : "ASC",
      })
    );
  }, [
    actions,
    dispatch,
    geofenceAdmin.filter.orderDirection,
  ]);

  const handleAddGeofence = () => {
    dispatch(addEditGeofenceAdminSlice.actions.open());
  };

  const fetch = useCallback(async () => {
    if (isFetching.current) return;
    isFetching.current = true;
    await dispatch(fetchGeofencesAdmin(branchCode || ""));
    isFetching.current = false;
  }, [branchCode, dispatch]);

  // Fetch geofences when component mounts
  useEffect(() => {
    fetch();
  }, [
    fetch,
    geofenceAdmin.filter.orderBy,
    geofenceAdmin.filter.orderDirection,
    geofenceAdmin.filter.page,
  ]);

  return (
    <WebAdminLayout activePage="geofence">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdMap />}
            title={t("geofence.title")}
            description={t("geofence.description")}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={handleAddGeofence}
              >
                {t("geofence.addButton")}
              </Button>,
            ]}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder={t(
                  "geofence.search.placeholder"
                )}
                prefix={<SearchOutlined />}
                size="large"
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                onChange={(e) =>
                  handleSearch(e.target.value)
                }
              />
              <div className="flex gap-2">
                <Select
                  defaultValue="created_at"
                  className="min-w-[160px] dark:bg-gray-700"
                  size="large"
                  onChange={handleSortChange}
                  value={
                    geofenceAdmin.filter.orderBy ||
                    "created_at"
                  }
                >
                  <Option value="created_at">
                    {t("common.reportType.options.1")}
                  </Option>
                  <Option value="geofence_name">
                    {t(
                      "geofence.search.sortOptions.name",
                      "Name"
                    )}
                  </Option>
                  <Option value="geofence_type">
                    {t("geofence.typeLabel")}
                  </Option>
                </Select>
                <Button
                  icon={
                    <SortAscendingOutlined
                      rotate={
                        geofenceAdmin.filter
                          .orderDirection === "DESC"
                          ? 180
                          : 0
                      }
                    />
                  }
                  size="large"
                  onClick={handleSortDirectionToggle}
                  className="dark:border-gray-600 dark:text-gray-300"
                />
              </div>
            </div>
          </div>

          {/* List Section */}
          <GeofenceList />

          {/* Modal Add/Edit Geofence */}
          <ModalAddEditGeofence />
        </div>
      </div>
    </WebAdminLayout>
  );
}
