import Cookies, {
  CookieSetOptions,
} from "universal-cookie";

const cookies = new Cookies(null, { path: "/" });

interface Tokens {
  accessToken?: string;
  refreshToken?: string;
}

export const removeUserData = (): void => {
  localStorage.removeItem("user");
};

export const setUserData = (userData: any): void => {
  localStorage.setItem("user", JSON.stringify(userData));
};

export const getUserData = (): any => {
  const userData = localStorage.getItem("user");
  return userData ? JSON.parse(userData) : null;
};

export const setTokens = (
  accessToken: string,
  refreshToken: string
): void => {
  const commonOptions: CookieSetOptions = {
    secure: true,
    sameSite: "strict" as const,
    path: "/",
  };

  cookies.set("accessToken", accessToken, {
    ...commonOptions,
    maxAge: 3 * 24 * 3600, // 3 days in seconds
  });

  // Belum digunakan tapi siapkan dulu saja kode nya
  cookies.set("refreshToken", refreshToken, {
    ...commonOptions,
    maxAge: 7 * 24 * 3600, // 7 days in seconds
  });
};

export const getTokens = (): Tokens => {
  return {
    accessToken: cookies.get("accessToken"),
    refreshToken: cookies.get("refreshToken"),
  };
};

export const removeTokens = (): void => {
  const domain = new URL(import.meta.env.VITE_API_URL)
    .hostname;

  cookies.remove("accessToken", { path: "/", domain });
  cookies.remove("refreshToken", { path: "/", domain });
};
