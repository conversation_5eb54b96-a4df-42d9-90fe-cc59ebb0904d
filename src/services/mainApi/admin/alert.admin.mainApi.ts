import baseMain<PERSON>pi from "../base.mainApi";
import {
  GetAlertListResponse,
  GetAlertDetailResponse,
  CreateAlertRequest,
  UpdateAlertRequest,
  GetAlertRequest,
} from "./types/alert.admin.mainApi.types";

const alertAdminMainApi = {
  /**
   * Get all alerts for a branch
   * @param params Query parameters like page, limit, etc.
   * @param branchCode Branch code
   * @returns Alert list response
   */
  getAlerts: async (
    params: GetAlertRequest,
    branchCode: string
  ) => {
    const response =
      await baseMainApi.get<GetAlertListResponse>(
        `/web-api/admin/${branchCode}/alert`,
        { params }
      );
    return response.data;
  },

  /**
   * Get alert by ID
   * @param branchCode Branch code
   * @param id Alert ID
   * @returns Alert detail response
   */
  getAlertById: async (branchCode: string, id: string) => {
    const response =
      await baseMainApi.get<GetAlertDetailResponse>(
        `/web-api/admin/${branchCode}/alert/${id}`
      );
    return response.data;
  },

  /**
   * Create a new alert
   * @param data Alert data
   * @param branchCode Branch code
   * @returns Created alert data
   */
  createAlert: async (
    data: CreateAlertRequest,
    branchCode: string
  ) => {
    const response =
      await baseMainApi.post<GetAlertDetailResponse>(
        `/web-api/admin/${branchCode}/alert`,
        data
      );
    return response.data;
  },

  /**
   * Update an existing alert
   * @param data Alert data
   * @param branchCode Branch code
   * @param id Alert ID
   * @returns Updated alert data
   */
  updateAlert: async (
    data: UpdateAlertRequest,
    branchCode: string,
    id: string
  ) => {
    const response =
      await baseMainApi.put<GetAlertDetailResponse>(
        `/web-api/admin/${branchCode}/alert/${id}`,
        data
      );
    return response.data;
  },

  /**
   * Delete an alert
   * @param branchCode Branch code
   * @param id Alert ID
   * @returns Response data
   */
  deleteAlert: async (branchCode: string, id: string) => {
    const response =
      await baseMainApi.delete<GetAlertDetailResponse>(
        `/web-api/admin/${branchCode}/alert/${id}`
      );
    return response.data;
  },
};

export default alertAdminMainApi;
