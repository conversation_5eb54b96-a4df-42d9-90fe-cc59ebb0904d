import {
  createSlice,
  createAsyncThunk,
  PayloadAction,
} from "@reduxjs/toolkit";
import { batteryLevelLogAdminApi } from "../../../services/mainApi/admin/batteryLevelLog.admin.mainApi";
import {
  AdminBatteryLevelLogEntry,
  AdminBatteryLevelLogListParams,
} from "../../../services/mainApi/admin/types/batteryLevelLog.admin.mainApi.types";
import { BasePaginationMeta } from "../../../services/mainApi/types/base.mainApi.types";
import { RootState } from "../../../store";
import { AdminLabel } from "../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminBranch } from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminCheckpoint } from "../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";
import { AdminZone } from "../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import dayjs from "dayjs";

interface State {
  batteryLevelLogs: AdminBatteryLevelLogEntry[];
  loading: boolean;
  error: string | null;

  filter: {
    startDate: string | null;
    endDate: string | null;
    branchId: string | number | null;
    siteId: string | number | null;
    siteLabels: (string | number)[];
    checkpointId: string | number | null;
    checkpointLabels: (string | number)[];
    page: number;
    limit: number;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;

  // Filter selection objects
  selectedBranch: AdminBranch | null;
  selectedSite: AdminZone | null;
  selectedCheckpoint: AdminCheckpoint | null;
  selectedSiteLabels: AdminLabel[];
  selectedCheckpointLabels: AdminLabel[];
}

const initialState: State = {
  batteryLevelLogs: [],
  loading: false,
  error: null,

  filter: {
    startDate: dayjs()
      .subtract(1, "day")
      .startOf("day")
      .format("YYYY-MM-DD"),
    endDate: dayjs().endOf("day").format("YYYY-MM-DD"),
    branchId: null,
    siteId: null,
    siteLabels: [],
    checkpointId: null,
    checkpointLabels: [],
    page: 1,
    limit: 10,
    orderBy: "original_submitted_time",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
  },

  selectedBranch: null,
  selectedSite: null,
  selectedCheckpoint: null,
  selectedSiteLabels: [],
  selectedCheckpointLabels: [],
};

export const fetchBatteryLevelLogs = createAsyncThunk(
  "batteryLevelLogAdmin/fetchBatteryLevelLogs",
  async (
    {
      branchCode,
    }: {
      branchCode: string;
    },
    { getState }
  ) => {
    const state = getState() as RootState;
    const batteryLevelLogAdmin = state.batteryLevelLogAdmin;

    // Combine date and time if both are present
    const startDate = batteryLevelLogAdmin.filter.startDate;
    const endDate = batteryLevelLogAdmin.filter.endDate;

    // Note: In a real implementation, you would need to combine the date and time properly
    // For simplicity in this example, we're just using the date

    const params: AdminBatteryLevelLogListParams = {
      page: batteryLevelLogAdmin.filter.page,
      limit: batteryLevelLogAdmin.filter.limit,
      start_date: startDate || undefined,
      end_date: endDate || undefined,
      branch_id:
        batteryLevelLogAdmin.filter.branchId || undefined,
      site_id:
        batteryLevelLogAdmin.filter.siteId || undefined,
      site_labels:
        batteryLevelLogAdmin.filter.siteLabels.length > 0
          ? batteryLevelLogAdmin.filter.siteLabels.join(",")
          : undefined,
      checkpoint_id:
        batteryLevelLogAdmin.filter.checkpointId ||
        undefined,
      checkpoint_labels:
        batteryLevelLogAdmin.filter.checkpointLabels
          .length > 0
          ? batteryLevelLogAdmin.filter.checkpointLabels.join(
              ","
            )
          : undefined,
      order_by: batteryLevelLogAdmin.filter.orderBy,
      order_direction:
        batteryLevelLogAdmin.filter.orderDirection,
    };

    return await batteryLevelLogAdminApi.getBatteryLevelLogs(
      branchCode,
      params
    );
  }
);

export const exportBatteryLevelLogs = createAsyncThunk(
  "batteryLevelLogAdmin/exportBatteryLevelLogs",
  async (
    {
      branchCode,
      format,
      saveType,
    }: {
      branchCode: string;
      format: "pdf" | "spreadsheet";
      saveType: "link" | "buffer";
    },
    { getState }
  ) => {
    const state = getState() as RootState;
    const batteryLevelLogAdmin = state.batteryLevelLogAdmin;

    // Combine date and time if both are present
    const startDate = batteryLevelLogAdmin.filter.startDate;
    const endDate = batteryLevelLogAdmin.filter.endDate;

    // Note: In a real implementation, you would need to combine the date and time properly
    // For simplicity in this example, we're just using the date

    const params: AdminBatteryLevelLogListParams = {
      start_date: startDate || undefined,
      end_date: endDate || undefined,
      branch_id:
        batteryLevelLogAdmin.filter.branchId || undefined,
      site_id:
        batteryLevelLogAdmin.filter.siteId || undefined,
      site_labels:
        batteryLevelLogAdmin.filter.siteLabels.length > 0
          ? batteryLevelLogAdmin.filter.siteLabels.join(",")
          : undefined,
      checkpoint_id:
        batteryLevelLogAdmin.filter.checkpointId ||
        undefined,
      checkpoint_labels:
        batteryLevelLogAdmin.filter.checkpointLabels
          .length > 0
          ? batteryLevelLogAdmin.filter.checkpointLabels.join(
              ","
            )
          : undefined,
      order_by: batteryLevelLogAdmin.filter.orderBy,
      order_direction:
        batteryLevelLogAdmin.filter.orderDirection,
    };

    return await batteryLevelLogAdminApi.exportBatteryLevelLogs(
      branchCode,
      params,
      format,
      saveType
    );
  }
);

const batteryLevelLogAdminSlice = createSlice({
  name: "batteryLevelLogAdmin",
  initialState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.filter.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.filter.limit = action.payload;
    },
    setOrderBy: (state, action: PayloadAction<string>) => {
      state.filter.orderBy = action.payload;
    },
    setOrderDirection: (
      state,
      action: PayloadAction<"ASC" | "DESC">
    ) => {
      state.filter.orderDirection = action.payload;
    },
    clearFilters: (state) => {
      state.filter = {
        ...initialState.filter,
        page: state.filter.page,
        limit: state.filter.limit,
      };
      state.selectedBranch = null;
      state.selectedSite = null;
      state.selectedCheckpoint = null;
      state.selectedSiteLabels = [];
      state.selectedCheckpointLabels = [];
    },

    // Setting filter selection objects
    setSelectedBranch: (
      state,
      action: PayloadAction<AdminBranch | null>
    ) => {
      state.selectedBranch = action.payload;
      state.filter.branchId = action.payload?.id || null;
    },
    setSelectedSite: (
      state,
      action: PayloadAction<AdminZone | null>
    ) => {
      state.selectedSite = action.payload;
      state.filter.siteId = action.payload?.id || null;
    },
    setSelectedCheckpoint: (
      state,
      action: PayloadAction<AdminCheckpoint | null>
    ) => {
      state.selectedCheckpoint = action.payload;
      state.filter.checkpointId =
        action.payload?.id || null;
    },
    setSelectedSiteLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedSiteLabels = action.payload;
      state.filter.siteLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setSelectedCheckpointLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedCheckpointLabels = action.payload;
      state.filter.checkpointLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setStartDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startDate = action.payload;
    },
    setEndDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endDate = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Battery Level Logs
      .addCase(fetchBatteryLevelLogs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchBatteryLevelLogs.fulfilled,
        (state, action) => {
          state.loading = false;
          state.batteryLevelLogs =
            action.payload.data || [];
          state.pagination.total =
            action.payload.meta?.total || 0;
          state.pagination.page =
            action.payload.meta?.page || 1;
          state.pagination.limit =
            action.payload.meta?.limit || 10;
        }
      )
      .addCase(
        fetchBatteryLevelLogs.rejected,
        (state, action) => {
          state.loading = false;
          state.error =
            action.error.message ||
            "Failed to fetch battery level logs";
        }
      );
  },
});

export default batteryLevelLogAdminSlice;
