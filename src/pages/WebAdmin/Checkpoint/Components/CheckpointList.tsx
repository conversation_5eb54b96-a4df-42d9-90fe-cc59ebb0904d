import { useCallback, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks.ts";
import {
  fetchCheckpointAdmin,
  setFilter,
} from "../../../../store/slices/admin/checkpoint.admin.slice.ts";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { Empty, Pagination, Spin } from "antd";
import { useParams } from "react-router-dom";
import CheckpointItem from "./CheckpointItem.tsx";

// Extend dayjs with relativeTime plugin
dayjs.extend(relativeTime);

const CheckpointList = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { checkpoints, loading, pagination, filter } =
    useAppSelector((state) => state.checkpointAdmin);
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const isFetching = useRef(false);

  const fetch = useCallback(async () => {
    if (!branchCode) return;
    if (isFetching.current) return;

    isFetching.current = true;
    await dispatch(fetchCheckpointAdmin(branchCode));
    isFetching.current = false;
  }, [branchCode, dispatch]);

  useEffect(() => {
    fetch();
  }, [fetch, filter.orderBy, filter.orderDirection]);

  const handlePageChange = useCallback(
    (page: number, pageSize: number) => {
      dispatch(setFilter({ page, limit: pageSize }));
      fetch();
    },
    [dispatch, fetch]
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!checkpoints.length) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8">
        <Empty
          description={t("checkpoint.list.empty")}
          className="dark:text-gray-400"
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4">
        {checkpoints.map((checkpoint) => (
          <CheckpointItem
            key={checkpoint.id}
            checkpoint={checkpoint}
          />
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <Pagination
          current={filter.page || 1}
          pageSize={filter.limit || 10}
          total={pagination.total}
          onChange={handlePageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              {t("checkpoint.list.total", { total })}
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

export default CheckpointList;
