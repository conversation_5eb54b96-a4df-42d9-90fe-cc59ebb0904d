import {
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { AdminBranch } from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { Branch } from "../../../services/mainApi/sysadmin/types/branch.mainApi.types.ts";

interface State {
  open: boolean;
  branchView: Branch | null;
  loading: boolean;
  errorMessage: string | null;
}

const initialState: State = {
  open: false,
  branchView: null,
  loading: false,
  errorMessage: null,
};

const viewBranchAdminSlice = createSlice({
  name: "viewBranchAdmin",
  initialState,
  reducers: {
    open: (state, action: PayloadAction<AdminBranch>) => {
      state.open = true;
      state.branchView = action.payload;
      state.errorMessage = null;
    },
    close: (state) => {
      state.open = false;
      state.branchView = null;
      state.errorMessage = null;
      state.loading = false;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.errorMessage = action.payload;
    },
  },
});

export default viewBranchAdminSlice;
