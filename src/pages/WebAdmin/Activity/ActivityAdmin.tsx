import { Button, Input, Select, Spin } from "antd";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {
  PlusOutlined,
  SearchOutlined,
  SortAscendingOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { MdHistory } from "react-icons/md";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import { useTranslation } from "react-i18next";
import { useCallback, useEffect, useRef } from "react";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks";
import { useParams } from "react-router-dom";
import activityAdminSlice, {
  fetchActivities,
} from "../../../store/slices/admin/activity.admin.slice";
import addActivityAdminSlice from "../../../store/slices/admin/addActivity.admin.slice";
import ActivityList from "./Components/ActivityList.tsx";
import ModalAddActivity from "./Components/ModalAddActivity/ModalAddActivity";
import _ from "lodash";

const { Option } = Select;

const ActivityAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  const actions = activityAdminSlice.actions;
  const addActivityActions = addActivityAdminSlice.actions;

  const activityAdmin = useAppSelector(
    (state) => state.activityAdmin
  );

  const { loading } = activityAdmin;

  const debouncedFetchRef = useRef(
    _.debounce(() => {
      if (!branchCode) return;
      dispatch(fetchActivities({ branchCode }));
    }, 500)
  );

  const handleSearch = useCallback(
    (value: string) => {
      dispatch(actions.setSearch(value || null));
      debouncedFetchRef.current();
    },
    [dispatch, actions]
  );

  const handleSortChange = useCallback(
    (value: string) => {
      dispatch(actions.setOrderBy(value));
    },
    [dispatch, actions]
  );

  const handleSortDirectionToggle = useCallback(() => {
    dispatch(
      actions.setOrderDirection(
        activityAdmin.filter.orderDirection === "ASC"
          ? "DESC"
          : "ASC"
      )
    );
  }, [
    dispatch,
    actions,
    activityAdmin.filter.orderDirection,
  ]);

  const handleAdd = useCallback(() => {
    dispatch(addActivityActions.open());
  }, [dispatch, addActivityActions]);

  useEffect(() => {
    if (branchCode) {
      debouncedFetchRef.current();
    }
  }, [
    branchCode,
    activityAdmin.filter.orderBy,
    activityAdmin.filter.orderDirection,
    activityAdmin.filter.page,
  ]);

  if (!branchCode) {
    return null;
  }

  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

  return (
    <WebAdminLayout activePage="activity">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6 relative">
          {loading && (
            <div className="absolute inset-0 bg-white/50 dark:bg-gray-900/50 z-10 flex items-center justify-center">
              <Spin indicator={antIcon} />
            </div>
          )}

          <PageHeader
            icon={<MdHistory />}
            title={t("activity.title")}
            description={t("activity.description")}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={handleAdd}
              >
                {t("activity.addNew")}
              </Button>,
            ]}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder={t(
                  "activity.search.placeholder"
                )}
                prefix={<SearchOutlined />}
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                size="large"
                onChange={(e) =>
                  handleSearch(e.target.value)
                }
                value={activityAdmin.filter.search || ""}
                allowClear
              />
              <div className="flex gap-2">
                <Select
                  value={activityAdmin.filter.orderBy}
                  className="min-w-[160px] dark:bg-gray-700"
                  size="large"
                  onChange={handleSortChange}
                >
                  <Option value="created_at">
                    {t(
                      "activity.search.sortOptions.createdAt"
                    )}
                  </Option>
                  <Option value="activity_name">
                    {t("activity.search.sortOptions.name")}
                  </Option>
                </Select>
                <Button
                  icon={
                    <SortAscendingOutlined
                      rotate={
                        activityAdmin.filter
                          .orderDirection === "DESC"
                          ? 180
                          : 0
                      }
                    />
                  }
                  size="large"
                  onClick={handleSortDirectionToggle}
                  className="dark:border-gray-600 dark:text-gray-300"
                />
              </div>
            </div>
          </div>

          {/* Activity List */}
          <ActivityList />
        </div>
      </div>
      <ModalAddActivity />
    </WebAdminLayout>
  );
};

export default ActivityAdmin;
