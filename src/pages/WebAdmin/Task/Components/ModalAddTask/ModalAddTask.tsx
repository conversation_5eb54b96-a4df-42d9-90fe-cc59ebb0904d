import {
  Input,
  Modal,
  Select,
  DatePicker,
  InputNumber,
  Divider,
  Spin,
  Form,
  App,
} from "antd";
import { useTranslation } from "react-i18next";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks.ts";
import SelectRole from "../../../../../components/Roles/SelectRole.tsx";
import SelectUser from "../../../../../components/SelectUser/SelectUser.tsx";
import SelectCheckpoint from "../../../../../components/Checkpoint/SelectCheckpoint.tsx";
import addTaskAdminSlice, {
  updateTaskAdmin,
  createTaskAdmin,
} from "../../../../../store/slices/admin/addTask.admin.slice.ts";
import FieldsAddTask from "./FieldsAddTask.tsx";
import { useParams } from "react-router-dom";
import ErrorMessage from "../../../../../components/Common/ErrorMessage/ErrorMessage.tsx";
import { fetchTaskAdmin } from "../../../../../store/slices/admin/task.admin.slice.ts";
import { convertToLocalTimezone, convertToUTC } from "../../../../../utils/dateUtils.ts";

const { TextArea } = Input;
const ModalAddTask = () => {
  // Hook untuk translation dan state management
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const modalAdd = useAppSelector(
    (state) => state.addTaskAdmin
  );
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const { message } = App.useApp();

  // Actions dari slice untuk manajemen state
  const actions = addTaskAdminSlice.actions;

  // Handler untuk submit form
  const handleSubmit = async () => {
    if (!branchCode) {
      message.error(
        t(
          "task.modal.error.branchCode",
          "Branch code is required"
        )
      );
      return;
    }

    if (modalAdd.mode === "update") {
      const result = await dispatch(
        updateTaskAdmin(branchCode)
      );
      if (result.meta.requestStatus === "rejected") {
        message.error({
          content: t(
            "task.modal.edit.error",
            "Failed to update task"
          ),
        });
        return;
      }
      if (result.meta.requestStatus === "fulfilled") {
        message.success({
          content: t(
            "task.modal.edit.success",
            "Task updated successfully"
          ),
        });
        dispatch(actions.close());
        dispatch(fetchTaskAdmin(branchCode));
      }
    } else if (modalAdd.mode === "create") {
      const result = await dispatch(
        createTaskAdmin(branchCode)
      );
      if (result.meta.requestStatus === "rejected") {
        message.error({
          content: t(
            "task.modal.edit.error",
            "Failed to update task"
          ),
        });
        return;
      }
      if (result.meta.requestStatus === "fulfilled") {
        message.success({
          content: t(
            "task.modal.create.success",
            "Task created successfully"
          ),
        });
        dispatch(actions.close());
        dispatch(fetchTaskAdmin(branchCode));
      }
    }
  };

  // Handler untuk menutup modal
  const handleClose = () => {
    dispatch(actions.close());
  };

  return (
    <Modal
      title={
        <div className="text-lg font-semibold">
          {modalAdd.mode === "update"
            ? t("task.modal.edit.title")
            : t("task.modal.add.title")}
        </div>
      }
      open={modalAdd.open}
      onCancel={handleClose}
      onOk={handleSubmit}
      confirmLoading={modalAdd.submitting}
      className="rounded-xl"
      okButtonProps={{
        className: "bg-blue-500 hover:bg-blue-600 border-0",
      }}
      width={800}
      maskClosable={!modalAdd.submitting}
      closable={!modalAdd.submitting}
    >
      {modalAdd.loading ? (
        // Tampilkan spinner saat loading
        <div className="flex justify-center items-center py-12">
          <Spin size="large" />
        </div>
      ) : (
        // Form utama untuk menambahkan/mengedit task
        <Form
          layout="vertical"
          className="mt-4 space-y-6"
        >
          {/* Section: Informasi dasar task */}
          <div className="space-y-4">
            <Divider
              orientation="left"
              className="text-gray-500 dark:text-gray-400"
            >
              {t("task.modal.sections.basic")}
            </Divider>

            <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg space-y-4">
              {/* Field: Nama task */}
              <Form.Item
                label={t(
                  "task.modal.form.name.label",
                  "Task Name"
                )}
                required
              >
                <Input
                  placeholder={t(
                    "task.modal.form.name.placeholder",
                    "Enter task name"
                  )}
                  className="rounded-lg"
                  onChange={(e) =>
                    dispatch(
                      actions.setTaskName(e.target.value)
                    )
                  }
                  value={modalAdd.task_name}
                />
              </Form.Item>

              <Form.Item
                label={t(
                  "task.modal.form.description.label",
                  "Task Description"
                )}
              >
                <TextArea
                  rows={4}
                  placeholder={t(
                    "task.modal.form.description.placeholder",
                    "Enter task description"
                  )}
                  className="rounded-lg"
                  onChange={(e) =>
                    dispatch(
                      actions.setTaskDescription(
                        e.target.value
                      )
                    )
                  }
                  value={modalAdd.task_description}
                />
              </Form.Item>

              <Form.Item
                label={t(
                  "task.modal.form.type.label",
                  "Task Type"
                )}
                required
              >
                <Select
                  className="rounded-lg w-full"
                  options={[
                    {
                      value: "SCHEDULED",
                      label: t(
                        "task.type.scheduled",
                        "Scheduled"
                      ),
                    },
                    {
                      value: "REPEATING",
                      label: t(
                        "task.type.repeating",
                        "Repeating"
                      ),
                    },
                  ]}
                  onChange={(value) =>
                    dispatch(actions.setTaskType(value))
                  }
                  value={modalAdd.task_type}
                  placeholder={t(
                    "task.modal.form.type.placeholder",
                    "Select task type"
                  )}
                />
              </Form.Item>
            </div>
          </div>

          {/* Section: Jadwal task */}
          <div className="space-y-4">
            <Divider
              orientation="left"
              className="text-gray-500 dark:text-gray-400"
            >
              {t(
                "task.modal.sections.schedule",
                "Schedule"
              )}
            </Divider>

            <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg space-y-4">
              <Form.Item
                label={t(
                  "task.modal.form.dateTimeRange.label",
                  "Date Time Range"
                )}
                required
              >
                <div className="grid grid-cols-2 gap-4">
                  <DatePicker
                    showTime={{ format: 'HH:mm' }}
                    format="YYYY-MM-DD HH:mm"
                    className="w-full rounded-lg"
                    popupClassName="rounded-lg"
                    placeholder={t(
                      "task.modal.form.dateTimeRange.startPlaceholder",
                      "Start Date Time"
                    )}
                    disabledDate={(current) => {
                      // Disable dates after end_time if end_time is set
                      if (modalAdd.end_time && current) {
                        const endTime = convertToLocalTimezone(modalAdd.end_time);
                        if (endTime) {
                          return current.isAfter(endTime, 'day');
                        }
                      }
                      return false;
                    }}
                    onChange={(dateTime) => {
                      if (dateTime) {
                        dispatch(
                          actions.setStartTime(
                            convertToUTC(dateTime) || ''
                          )
                        );
                      }
                    }}
                    value={convertToLocalTimezone(modalAdd.start_time)}
                  />
                  <DatePicker
                    showTime={{ format: 'HH:mm' }}
                    format="YYYY-MM-DD HH:mm"
                    className="w-full rounded-lg"
                    popupClassName="rounded-lg"
                    placeholder={t(
                      "task.modal.form.dateTimeRange.endPlaceholder",
                      "End Date Time"
                    )}
                    disabledDate={(current) => {
                      // Disable dates before start_time if start_time is set
                      if (modalAdd.start_time && current) {
                        const startTime = convertToLocalTimezone(modalAdd.start_time);
                        if (startTime) {
                          return current.isBefore(startTime, 'day');
                        }
                      }
                      return false;
                    }}
                    onChange={(dateTime) => {
                      if (dateTime) {
                        dispatch(
                          actions.setEndTime(
                            convertToUTC(dateTime) || ''
                          )
                        );
                      }
                    }}
                    value={convertToLocalTimezone(modalAdd.end_time)}
                  />
                </div>
              </Form.Item>

              <Form.Item
                label={t(
                  "task.modal.form.allowedTime.label",
                  "Allowed Time"
                )}
                required
              >
                <InputNumber
                  min={1}
                  className="w-full rounded-lg"
                  onChange={(value) =>
                    dispatch(
                      actions.setAllowedTime(value || 0)
                    )
                  }
                  value={modalAdd.allowed_time}
                  placeholder="Enter allowed time"
                />
              </Form.Item>
            </div>
          </div>

          {/* Section: Penugasan task */}
          <div className="space-y-4">
            <Divider
              orientation="left"
              className="text-gray-500 dark:text-gray-400"
            >
              {t(
                "task.modal.sections.assignment",
                "Assignment"
              )}
            </Divider>

            <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg space-y-4">
              <Form.Item
                label={t(
                  "task.modal.form.role.label",
                  "Role"
                )}
              >
                <SelectRole
                  onChange={(value) =>
                    dispatch(
                      actions.setRoleId(value || null)
                    )
                  }
                  value={modalAdd.role_id || undefined}
                />
              </Form.Item>

              {modalAdd.role_id && (
                <Form.Item
                  label={t(
                    "task.modal.form.user.label",
                    "User"
                  )}
                >
                  <SelectUser
                    onChange={(value) => {
                      dispatch(
                        actions.setUserId(value || null)
                      );
                    }}
                    value={modalAdd.user_id || undefined}
                    roleId={modalAdd.role_id || undefined}
                  />
                </Form.Item>
              )}

              <Form.Item
                label={t(
                  "task.modal.form.checkpoint.label",
                  "Checkpoint"
                )}
                rules={[
                  {
                    required: true,
                    message: t(
                      "task.modal.form.checkpoint.required"
                    ),
                  },
                ]}
              >
                <SelectCheckpoint
                  onChange={(value) => {
                    if (!Array.isArray(value)) {
                      dispatch(
                        actions.setCheckpointId(
                          value || null
                        )
                      );
                    }
                  }}
                  value={
                    modalAdd.checkpoint_id || undefined
                  }
                />
              </Form.Item>
            </div>

            {/* Section: Field task */}
            <FieldsAddTask />
          </div>
        </Form>
      )}

      {/* Tampilkan pesan error jika ada */}
      {modalAdd.errorMessage && (
        <div className="mt-4">
          <ErrorMessage message={modalAdd.errorMessage} />
        </div>
      )}
    </Modal>
  );
};

export default ModalAddTask;
