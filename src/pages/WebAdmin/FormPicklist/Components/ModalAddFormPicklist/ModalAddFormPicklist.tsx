import {
  Modal,
  Form,
  Input,
  Button,
  Switch,
  Space,
  Tooltip,
  App,
} from "antd";
import {
  DeleteOutlined,
  PlusOutlined,
  InfoCircleOutlined,
  DragOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks";
import { useParams } from "react-router-dom";
import addFormPicklistAdminSlice, {
  createFormPicklist,
  updateFormPicklist,
} from "../../../../../store/slices/admin/addFormPicklist.admin.slice";
import ErrorMessage from "../../../../../components/Common/ErrorMessage/ErrorMessage";
import { fetchFormPicklists } from "../../../../../store/slices/admin/formPicklist.admin.slice";

const { TextArea } = Input;

const ModalAddFormPicklist = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { message } = App.useApp();
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  const {
    open,
    mode,
    submitting,
    errorMessage,
    form_picklist_name,
    form_picklist_description,
    active,
    options,
  } = useAppSelector((state) => state.addFormPicklistAdmin);

  const { actions } = addFormPicklistAdminSlice;

  const handleClose = () => {
    dispatch(actions.close());
  };

  const handleSubmit = async () => {
    // Validasi dasar
    if (!form_picklist_name.trim()) {
      message.error(
        t("formPicklist.validation.nameRequired")
      );
      return;
    }

    if (options.length === 0) {
      message.error(
        t("formPicklist.validation.optionsRequired")
      );
      return;
    }

    if (
      options.some(
        (opt) => !opt.form_picklist_option_name.trim()
      )
    ) {
      message.error(
        t("formPicklist.validation.optionNameRequired")
      );
      return;
    }

    try {
      if (!branchCode) {
        throw new Error("Branch code is required");
      }

      const action =
        mode === "create"
          ? createFormPicklist(branchCode)
          : updateFormPicklist(branchCode);

      const result = await dispatch(action);

      if (result.meta.requestStatus === "fulfilled") {
        message.success(
          mode === "create"
            ? t("formPicklist.message.createSuccess")
            : t("formPicklist.message.updateSuccess")
        );
        await dispatch(fetchFormPicklists(branchCode));
        handleClose();
      }
    } catch (error) {
      console.error(
        "Failed to submit form picklist:",
        error
      );
    }
  };

  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <span className="text-lg font-medium">
            {mode === "create"
              ? t("formPicklist.modal.createTitle")
              : t("formPicklist.modal.updateTitle")}
          </span>
        </div>
      }
      open={open}
      onCancel={handleClose}
      footer={[
        <Button
          key="cancel"
          onClick={handleClose}
          disabled={submitting}
        >
          {t("common.cancel")}
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleSubmit}
          loading={submitting}
          className="bg-blue-500 hover:bg-blue-600"
        >
          {mode === "create"
            ? t("common.create")
            : t("common.update")}
        </Button>,
      ]}
      className="rounded-xl"
    >
      <div className="relative">
        {errorMessage && (
          <ErrorMessage message={errorMessage} />
        )}

        <Form
          layout="vertical"
          className="mt-4"
        >
          {/* Basic Information Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
              <h3 className="text-lg font-medium">
                {t("formPicklist.sections.basic")}
              </h3>
              <Tooltip
                title={t(
                  "formPicklist.sections.basic.tooltip"
                )}
              >
                <InfoCircleOutlined className="text-gray-400" />
              </Tooltip>
            </div>

            <div className="p-6 rounded-lg bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600">
              <Form.Item
                label={t("formPicklist.form.name.label")}
                required
                tooltip={t(
                  "formPicklist.form.name.tooltip"
                )}
              >
                <Input
                  value={form_picklist_name}
                  onChange={(e) =>
                    dispatch(
                      actions.setName(e.target.value)
                    )
                  }
                  placeholder={t(
                    "formPicklist.form.name.placeholder"
                  )}
                />
              </Form.Item>

              <Form.Item
                label={t(
                  "formPicklist.form.description.label"
                )}
                tooltip={t(
                  "formPicklist.form.description.tooltip"
                )}
              >
                <TextArea
                  value={form_picklist_description || ""}
                  onChange={(e) =>
                    dispatch(
                      actions.setDescription(e.target.value)
                    )
                  }
                  placeholder={t(
                    "formPicklist.form.description.placeholder"
                  )}
                  rows={3}
                />
              </Form.Item>

              <Form.Item
                label={t("formPicklist.form.active.label")}
                tooltip={t(
                  "formPicklist.form.active.tooltip"
                )}
              >
                <Switch
                  checked={active}
                  onChange={(checked) =>
                    dispatch(actions.setActive(checked))
                  }
                />
              </Form.Item>
            </div>
          </div>

          {/* Options Section */}
          <div className="mt-8 space-y-4 pb-16">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <h3 className="text-lg font-medium">
                  {t("formPicklist.sections.options")}
                </h3>
                <Tooltip
                  title={t(
                    "formPicklist.sections.options.tooltip"
                  )}
                >
                  <InfoCircleOutlined className="text-gray-400" />
                </Tooltip>
              </div>
              <Button
                type="dashed"
                onClick={() =>
                  dispatch(actions.addOption())
                }
                icon={<PlusOutlined />}
                className="border-blue-500 text-blue-500"
              >
                {t("formPicklist.options.add")}
              </Button>
            </div>

            <div className="space-y-4">
              {options.map((option, index) => (
                <div
                  key={option.rowId}
                  className="p-6 rounded-lg bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <DragOutlined className="text-gray-400 cursor-move" />
                      <span className="text-base font-medium">
                        {t("formPicklist.options.option")} #
                        {index + 1}
                      </span>
                    </div>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() =>
                        dispatch(
                          actions.removeOption(option.rowId)
                        )
                      }
                    />
                  </div>

                  <Space
                    direction="vertical"
                    className="w-full"
                  >
                    <Form.Item
                      label={t(
                        "formPicklist.options.name.label"
                      )}
                      required
                      className="mb-2"
                    >
                      <Input
                        value={
                          option.form_picklist_option_name
                        }
                        onChange={(e) =>
                          dispatch(
                            actions.setOptionName({
                              rowId: option.rowId,
                              form_picklist_option_name:
                                e.target.value,
                            })
                          )
                        }
                        placeholder={t(
                          "formPicklist.options.name.placeholder"
                        )}
                      />
                    </Form.Item>

                    <Form.Item
                      label={t(
                        "formPicklist.options.description.label"
                      )}
                      className="mb-2"
                    >
                      <Input.TextArea
                        rows={3}
                        value={
                          option.form_picklist_option_description ||
                          ""
                        }
                        onChange={(e) =>
                          dispatch(
                            actions.setOptionDescription({
                              rowId: option.rowId,
                              form_picklist_option_description:
                                e.target.value,
                            })
                          )
                        }
                        placeholder={t(
                          "formPicklist.options.description.placeholder"
                        )}
                      />
                    </Form.Item>

                    <Form.Item
                      label={t(
                        "formPicklist.options.active.label"
                      )}
                      className="mb-0"
                    >
                      <Switch
                        checked={option.active}
                        onChange={(checked) =>
                          dispatch(
                            actions.setOptionActive({
                              rowId: option.rowId,
                              active: checked,
                            })
                          )
                        }
                      />
                    </Form.Item>
                  </Space>
                </div>
              ))}
            </div>
          </div>
        </Form>

        {/* Floating Add Option Button */}
        {options.length > 2 && (
          <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50 bg-white dark:bg-gray-800 p-2 rounded-full shadow-lg">
            <Button
              type="primary"
              onClick={() => dispatch(actions.addOption())}
              icon={<PlusOutlined />}
              className="bg-blue-500 hover:bg-blue-600 flex items-center"
            >
              {t("formPicklist.options.add")}
            </Button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ModalAddFormPicklist;
