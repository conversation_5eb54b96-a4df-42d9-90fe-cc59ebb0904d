import React, {useState} from "react";
import {Button, Dropdown, Spin, Tag, Tooltip} from "antd";
import {FiCalendar, FiClock, FiDownload, FiFileText, FiGlobe, FiGrid, FiInfo, FiMapPin, FiUser,} from "react-icons/fi";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import {useTranslation} from "react-i18next";
import {AdminAlarmLog} from "../../../../../services/mainApi/admin/types/alarmLog.admin.mainApi.types";
import AlarmLocationModal from "./AlarmLocationModal";
import {useParams} from "react-router-dom";
import {alarmLogAdminApi} from "../../../../../services/mainApi/admin/alarmLog.admin.mainApi";
import {formatDateTime, formatTimeAgo} from "../../../../../utils/dateUtils.ts";

dayjs.extend(relativeTime);

interface AlarmLogItemProps {
  alarmLog: AdminAlarmLog;
}

const AlarmLogItem: React.FC<AlarmLogItemProps> = ({
                                                     alarmLog,
                                                   }) => {
  const {t} = useTranslation();
  const [
    isStartLocationModalOpen,
    setIsStartLocationModalOpen,
  ] = useState(false);
  const [
    isEndLocationModalOpen,
    setIsEndLocationModalOpen,
  ] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const {branchCode} = useParams<{
    branchCode: string;
  }>();

  const handleDownloadSpreadsheet = async () => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const blob =
        await alarmLogAdminApi.exportAlarmLogById(
          branchCode,
          alarmLog.id,
          "spreadsheet",
          "buffer"
        );

      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `alarm_log_${alarmLog.id}.xlsx`;
      link.click();

      URL.revokeObjectURL(url);
    } catch (error) {
      console.error(
        "Error downloading spreadsheet:",
        error
      );
    } finally {
      setIsDownloading(false);
    }
  };

  const downloadPdf = async () => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const blob =
        await alarmLogAdminApi.exportAlarmLogById(
          branchCode,
          alarmLog.id,
          "pdf",
          "buffer"
        );

      // Create URL and open in new tab instead of downloading
      const url = URL.createObjectURL(blob);
      window.open(url, "_blank");

      // Still need to revoke the URL when done, but we need to delay it
      // to ensure the new tab has time to load the resource
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 5000); // 5 seconds delay before cleanup
    } catch (error) {
      console.error("Error opening PDF:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  const exportMenu = {
    items: [
      {
        key: "spreadsheet",
        icon: <FiGrid className="text-lg"/>,
        label: t(
          "alarms.downloadSpreadsheet",
          "Export as Spreadsheet"
        ),
        onClick: handleDownloadSpreadsheet,
      },
      {
        key: "pdf",
        icon: <FiFileText className="text-lg"/>,
        label: t("alarms.downloadPdf", "Export as PDF"),
        onClick: downloadPdf,
      },
    ],
  };

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-0 p-4 mb-4">
      {/* Header - with gradient background */}
      <div
        className="mb-3 -mx-4 -mt-4 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 rounded-t-xl border-b border-gray-100 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center gap-2">
              {t("alarms.logTitle", "Alarm Log")} #{alarmLog.id}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {alarmLog.branch_name}
            </p>
          </div>
          <Tooltip
            title={formatDateTime(alarmLog.original_submitted_time, "DD MMM YYYY HH:mm", alarmLog.timezone_name)}
          >
            <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
              <FiClock className="inline"/>
              {formatTimeAgo(alarmLog.original_submitted_time, alarmLog.timezone_name)}
            </span>
          </Tooltip>
        </div>
      </div>

      {/* Two Column Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Left Column */}
        <div className="space-y-3">
          {/* User Info - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
              <FiUser className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("alarms.user", "User")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {alarmLog.user_name}
                </span>
                <Tag color="blue">{alarmLog.role_name}</Tag>
              </div>
            </div>
          </div>

          {/* Device - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
              <FiInfo className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("alarms.device", "Device")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {alarmLog.device_name ||
                    t("alarms.noDevice", "No device")}
                </span>
              </div>
            </div>
          </div>

          {/* Start Location - compact */}
          {alarmLog.start_latitude &&
            alarmLog.start_longitude && (
              <div
                className="group flex items-center gap-2 text-sm hover:bg-red-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
                <div
                  className="flex items-center justify-center w-7 h-7 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300 flex-shrink-0">
                  <FiMapPin className="w-4 h-4"/>
                </div>
                <div className="flex flex-col min-w-0">
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {t("alarms.startLocation", "Start Location")}:
                    </span>
                    <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                      {alarmLog.start_latitude.toFixed(6)}, {alarmLog.start_longitude.toFixed(6)}
                    </span>
                    <Button
                      type="link"
                      size="small"
                      onClick={() =>
                        setIsStartLocationModalOpen(true)
                      }
                    >
                      {t("alarms.viewMap", "View Map")}
                    </Button>
                  </div>
                </div>
              </div>
            )}

          {/* End Location - compact */}
          {alarmLog.end_latitude &&
            alarmLog.end_longitude && (
              <div
                className="group flex items-center gap-2 text-sm hover:bg-orange-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
                <div
                  className="flex items-center justify-center w-7 h-7 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 flex-shrink-0">
                  <FiMapPin className="w-4 h-4"/>
                </div>
                <div className="flex flex-col min-w-0">
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {t("alarms.endLocation", "End Location")}:
                    </span>
                    <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                      {alarmLog.end_latitude.toFixed(6)}, {alarmLog.end_longitude.toFixed(6)}
                    </span>
                    <Button
                      type="link"
                      size="small"
                      onClick={() =>
                        setIsEndLocationModalOpen(true)
                      }
                    >
                      {t("alarms.viewMap", "View Map")}
                    </Button>
                  </div>
                </div>
              </div>
            )}
        </div>

        {/* Right Column */}
        <div className="space-y-3">
          {/* Timestamp Info - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-purple-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 flex-shrink-0">
              <FiCalendar className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("alarms.startDateTime", "Start Date/Time")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {dayjs(alarmLog.start_date_time).format("DD MMM YYYY HH:mm:ss")}
                </span>
              </div>
            </div>
          </div>

          {alarmLog.end_date_time && (
            <div
              className="group flex items-center gap-2 text-sm hover:bg-orange-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
              <div
                className="flex items-center justify-center w-7 h-7 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 flex-shrink-0">
                <FiCalendar className="w-4 h-4"/>
              </div>
              <div className="flex flex-col min-w-0">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t("alarms.endDateTime", "End Date/Time")}:
                  </span>
                  <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                    {dayjs(alarmLog.end_date_time).format("DD MMM YYYY HH:mm:ss")}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Timezone - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
              <FiGlobe className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("alarms.timezone", "Timezone")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {alarmLog.timezone_name} ({alarmLog.timezone.gmt_offset})
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer Section with modern styling */}
      <div
        className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center bg-gray-50 dark:bg-gray-800 -mx-4 -mb-4 px-4 py-3 rounded-b-xl">
        <div className="text-xs text-gray-500 dark:text-gray-400 flex flex-wrap gap-2">
          <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md">
            {t("alarms.uuid", "UUID")}: {alarmLog.uuid}
          </span>
          <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md">
            {t("alarms.submittedTime", "Submitted")}:{" "}
            {formatDateTime(
              alarmLog.original_submitted_time,
              "DD MMM YYYY HH:mm",
              alarmLog.timezone_name
            )}
          </span>
        </div>
        <div className="flex gap-2">
          <Dropdown
            menu={exportMenu}
            placement="bottomRight"
          >
            <Button
              type="default"
              icon={
                isDownloading ? (
                  <Spin size="small"/>
                ) : (
                  <FiDownload/>
                )
              }
              disabled={isDownloading}
              className="flex items-center gap-2 transition-all duration-300"
            >
              {t("common.export", "Export")}
            </Button>
          </Dropdown>
        </div>
      </div>

      {/* Location Modals */}
      {alarmLog.start_latitude &&
        alarmLog.start_longitude && (
          <AlarmLocationModal
            isOpen={isStartLocationModalOpen}
            onClose={() =>
              setIsStartLocationModalOpen(false)
            }
            latitude={alarmLog.start_latitude}
            longitude={alarmLog.start_longitude}
            locationTitle={t(
              "alarms.startLocationTitle",
              "Start Location"
            )}
          />
        )}

      {alarmLog.end_latitude && alarmLog.end_longitude && (
        <AlarmLocationModal
          isOpen={isEndLocationModalOpen}
          onClose={() => setIsEndLocationModalOpen(false)}
          latitude={alarmLog.end_latitude}
          longitude={alarmLog.end_longitude}
          locationTitle={t(
            "alarms.endLocationTitle",
            "End Location"
          )}
        />
      )}
    </div>
  );
};

export default AlarmLogItem;
