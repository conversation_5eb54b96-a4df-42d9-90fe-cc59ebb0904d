import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, Collapse } from "antd";
import {
  FiClock,
  FiUser,
  FiSmartphone,
  FiAlertTriangle,
  FiCalendar,
  FiMessageSquare,
  FiDatabase,
  FiServer,
  FiCode,
  FiCheckCircle
} from "react-icons/fi";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useTranslation } from "react-i18next";

dayjs.extend(relativeTime);

// Interface for the unknown data item props
interface UnknownDataItemProps {
  unknownDataLog: {
    id: number;
    uuid?: string;
    data_type: string;
    source?: string;
    timestamp: string;
    user_name?: string;
    user_id?: number;
    role_name?: string;
    role_id?: number;
    device_name?: string;
    device_id?: number;
    branch_name?: string;
    branch_id?: number;
    status: string;
    severity?: string;
    raw_data?: string;
    analysis_result?: string;
    resolution?: string;
    notes?: string;
  };
}

const UnknownDataItem: React.FC<UnknownDataItemProps> = ({ unknownDataLog }) => {
  const { t } = useTranslation();
  const { Panel } = Collapse;

  // Determine status color
  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'pending review':
      case 'unresolved':
        return "orange";
      case 'analyzed':
        return "blue";
      case 'resolved':
        return "green";
      default:
        return "gray";
    }
  };

  // Determine severity color
  const getSeverityColor = (severity: string): string => {
    switch (severity?.toLowerCase()) {
      case 'high':
        return "red";
      case 'medium':
        return "orange";
      case 'low':
        return "green";
      default:
        return "blue";
    }
  };

  // Determine data type color
  const getDataTypeColor = (dataType: string): string => {
    switch (dataType.toLowerCase()) {
      case 'unrecognized format':
        return "purple";
      case 'corrupted data':
        return "red";
      case 'legacy format':
        return "orange";
      default:
        return "blue";
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-4">
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-base font-medium text-gray-900 dark:text-white">
            {unknownDataLog.data_type}
          </h3>
          <div className="flex gap-2 items-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {unknownDataLog.branch_name && `${unknownDataLog.branch_name} - `}
              ID: {unknownDataLog.id}
            </p>
            <Tag color={getDataTypeColor(unknownDataLog.data_type)}>
              {unknownDataLog.data_type}
            </Tag>
            <Tag color={getStatusColor(unknownDataLog.status)}>
              {unknownDataLog.status}
            </Tag>
            {unknownDataLog.severity && (
              <Tag color={getSeverityColor(unknownDataLog.severity)}>
                {unknownDataLog.severity} Severity
              </Tag>
            )}
          </div>
        </div>
        <Tooltip
          title={dayjs(unknownDataLog.timestamp).format(
            "DD MMM YYYY HH:mm:ss"
          )}
        >
          <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
            <FiClock className="inline" />
            {dayjs(unknownDataLog.timestamp).fromNow()}
          </span>
        </Tooltip>
      </div>

      {/* Source Information */}
      <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg mb-4">
        <div className="flex items-center gap-2 mb-2">
          <FiServer className="text-blue-500" />
          <span className="font-medium">
            {t("unknownData.sourceInfo", "Source Information")}
          </span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {unknownDataLog.source && (
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                <span className="font-medium">{t("unknownData.source", "Source")}:</span> {unknownDataLog.source}
              </p>
            </div>
          )}
          <div>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              <span className="font-medium">{t("unknownData.status", "Status")}:</span>
              <span className={`text-${getStatusColor(unknownDataLog.status)}-500 ml-1 font-medium`}>
                {unknownDataLog.status}
              </span>
            </p>
          </div>
        </div>
      </div>

      {/* Raw Data (Collapsible) */}
      {unknownDataLog.raw_data && (
        <div className="mb-4">
          <Collapse ghost>
            <Panel
              header={
                <div className="flex items-center gap-2">
                  <FiDatabase className="text-purple-500" />
                  <span className="font-medium text-sm">
                    {t("unknownData.rawData", "Raw Data")}
                  </span>
                </div>
              }
              key="1"
            >
              <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-auto max-h-60">
                {unknownDataLog.raw_data}
              </pre>
            </Panel>
          </Collapse>
        </div>
      )}

      {/* Additional Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Left Column */}
        <div className="space-y-3">
          {/* User Info */}
          {unknownDataLog.user_name && (
            <div className="flex items-center gap-2 text-sm">
              <FiUser className="text-green-500" />
              <span className="text-gray-600 dark:text-gray-300">
                {t("unknownData.user", "User")}:
                {" "}{unknownDataLog.user_name}
                {unknownDataLog.role_name && ` (${unknownDataLog.role_name})`}
              </span>
            </div>
          )}

          {/* Device Info */}
          {unknownDataLog.device_name && (
            <div className="flex items-center gap-2 text-sm">
              <FiSmartphone className="text-blue-500" />
              <span className="text-gray-600 dark:text-gray-300">
                {t("unknownData.device", "Device")}:
                {" "}{unknownDataLog.device_name}
              </span>
            </div>
          )}
        </div>

        {/* Right Column */}
        <div className="space-y-3">
          {/* Timestamp */}
          <div className="flex items-center gap-2 text-sm">
            <FiCalendar className="text-purple-500" />
            <span className="text-gray-600 dark:text-gray-300">
              {t("unknownData.timestamp", "Recorded")}:
              {" "}{dayjs(unknownDataLog.timestamp).format("DD MMM YYYY HH:mm:ss")}
            </span>
          </div>

          {/* Status with colored indicator */}
          <div className="flex items-center gap-2 text-sm">
            <FiAlertTriangle className={`text-${getStatusColor(unknownDataLog.status)}-500`} />
            <span className="text-gray-600 dark:text-gray-300">
              {t("unknownData.status", "Status")}:
              {" "}<span className={`text-${getStatusColor(unknownDataLog.status)}-500 font-medium`}>{unknownDataLog.status}</span>
            </span>
          </div>
        </div>
      </div>

      {/* Analysis Result (if available) */}
      {unknownDataLog.analysis_result && (
        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-1">
            <FiCode className="text-blue-500" />
            <span className="font-medium text-sm">
              {t("unknownData.analysisResult", "Analysis Result")}
            </span>
          </div>
          <p className="text-sm text-gray-700 dark:text-gray-300 pl-6">
            {unknownDataLog.analysis_result}
          </p>
        </div>
      )}

      {/* Resolution (if available) */}
      {unknownDataLog.resolution && (
        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-1">
            <FiCheckCircle className="text-green-500" />
            <span className="font-medium text-sm">
              {t("unknownData.resolution", "Resolution")}
            </span>
          </div>
          <p className="text-sm text-gray-700 dark:text-gray-300 pl-6">
            {unknownDataLog.resolution}
          </p>
        </div>
      )}

      {/* Notes (if available) */}
      {unknownDataLog.notes && (
        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-1">
            <FiMessageSquare className="text-gray-500" />
            <span className="font-medium text-sm">
              {t("unknownData.notes", "Notes")}
            </span>
          </div>
          <p className="text-sm text-gray-700 dark:text-gray-300 pl-6">
            {unknownDataLog.notes}
          </p>
        </div>
      )}

      {/* Footer Section */}
      {unknownDataLog.uuid && (
        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            <span>
              {t("unknownData.id", "ID")}: {unknownDataLog.uuid}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default UnknownDataItem;
