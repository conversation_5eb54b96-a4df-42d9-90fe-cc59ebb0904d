import { useTranslation } from "react-i18next";
import { Md<PERSON><PERSON><PERSON><PERSON>roblem, MdFilterList } from "react-icons/md";
import { useCallback, useEffect, useRef, useState } from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import { <PERSON><PERSON>, Drawer } from "antd";
import ExceptionLogList from "./Components/ExceptionLogList";
import { useParams } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../../../store/hooks";
import exceptionLogAdminSlice, {
  fetchExceptionLogs,
} from "../../../../store/slices/admin/exceptionLog.admin.slice";
import ActiveFilters, {
  createEndDateFilter,
  createEndTimeFilter,
  createLabelFilter,
  createSimpleFilter,
  createStartDateFilter,
  createStartTimeFilter,
  FilterGroup,
} from "../../../../components/Admin/Common/ActiveFilters";
import AnalyticDrawerForm, {
  AnalyticsFilterState,
} from "../../Analytics/Components/AnalyticDrawerForm";
import { EReportType } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { AdminRole } from "../../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminBranch } from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminZone } from "../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { AdminLabel } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminUser } from "../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminCheckpoint } from "../../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";
import ExceptionLogExportButton from "./Components/ExceptionLogExportButton";

interface FilterState {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: null | AdminBranch;
  site: null | AdminZone;
  siteLabels: AdminLabel[];
  checkpoint: null | AdminCheckpoint;
  checkpointLabels: AdminLabel[];
  role: null | AdminRole;
  user: null | AdminUser;
  userLabels: AdminLabel[];
  device: null | AdminDevice;
  deviceLabels: AdminLabel[];
}

const ExceptionLogAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const isFetching = useRef(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  // Get branch code from URL params
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  // Redux state
  const exceptionLogState = useAppSelector(
    (state) => state.exceptionLogAdmin
  );
  const loading = exceptionLogState.loading;
  const pagination = exceptionLogState.pagination;
  const { actions } = exceptionLogAdminSlice;

  const fetch = useCallback(async () => {
    if (isFetching.current) return;
    isFetching.current = true;
    await dispatch(fetchExceptionLogs({ branchCode: branchCode || "" }));
    isFetching.current = false;
  }, [branchCode, dispatch]);

  // Load data on mount and when filters change
  useEffect(() => {
    if (branchCode) {
      fetch().then();
    }
  }, [
    fetch,
    branchCode,
    exceptionLogState.filter.page,
    exceptionLogState.filter.limit,
    exceptionLogState.filter.startDate,
    exceptionLogState.filter.endDate,
    exceptionLogState.filter.startTime,
    exceptionLogState.filter.endTime,
    exceptionLogState.filter.branchId,
    exceptionLogState.filter.siteId,
    exceptionLogState.filter.siteLabels,
    exceptionLogState.filter.checkpointId,
    exceptionLogState.filter.checkpointLabels,
    exceptionLogState.filter.roleId,
    exceptionLogState.filter.userId,
    exceptionLogState.filter.userLabels,
    exceptionLogState.filter.deviceId,
    exceptionLogState.filter.deviceLabels,
  ]);

  // Convert applied filter state to format expected by drawer
  const getAppliedFilters = (): FilterState => {
    return {
      startDate: exceptionLogState.filter.startDate,
      endDate: exceptionLogState.filter.endDate,
      startTime: exceptionLogState.filter.startTime,
      endTime: exceptionLogState.filter.endTime,
      branch: exceptionLogState.selectedBranch,
      site: exceptionLogState.selectedSite,
      siteLabels: exceptionLogState.selectedSiteLabels || [],
      checkpoint: exceptionLogState.selectedCheckpoint,
      checkpointLabels: exceptionLogState.selectedCheckpointLabels || [],
      role: exceptionLogState.selectedRole,
      user: exceptionLogState.selectedUser,
      userLabels: exceptionLogState.selectedUserLabels || [],
      device: exceptionLogState.selectedDevice,
      deviceLabels: exceptionLogState.selectedDeviceLabels || [],
    };
  };

  // Apply filters from the drawer
  const handleApplyFilters = (
    newFilters: AnalyticsFilterState
  ) => {
    // Update redux state
    dispatch(actions.setSelectedRole(newFilters.role || null));
    dispatch(actions.setSelectedUser(newFilters.user || null));
    dispatch(actions.setSelectedUserLabels(newFilters.userLabels || []));
    dispatch(actions.setSelectedDevice(newFilters.device || null));
    dispatch(actions.setSelectedDeviceLabels(newFilters.deviceLabels || []));
    dispatch(actions.setSelectedSite(newFilters.site || null));
    dispatch(actions.setSelectedSiteLabels(newFilters.siteLabels || []));
    dispatch(actions.setSelectedCheckpoint(newFilters.checkpoint || null));
    dispatch(actions.setSelectedCheckpointLabels(newFilters.checkpointLabels || []));
    dispatch(actions.setSelectedBranch(newFilters.selectedBranch || null));
    dispatch(actions.setStartDate(newFilters.startDate || null));
    dispatch(actions.setEndDate(newFilters.endDate || null));
    dispatch(actions.setStartTime(newFilters.startTime || null));
    dispatch(actions.setEndTime(newFilters.endTime || null));

    // Reset page to 1
    dispatch(actions.setPage(1));
    setDrawerVisible(false);
  };

  // Handle page change
  const handlePageChange = (page: number, pageSize?: number) => {
    dispatch(actions.setPage(page));
    if (pageSize) {
      dispatch(actions.setLimit(pageSize));
    }
  };

  // Check if there are any active filters
  const hasActiveFilters = (): boolean => {
    const filter = exceptionLogState.filter;
    return !!(
      filter.startDate ||
      filter.endDate ||
      filter.startTime ||
      filter.endTime ||
      filter.branchId ||
      filter.siteId ||
      filter.siteLabels?.length > 0 ||
      filter.checkpointId ||
      filter.checkpointLabels?.length > 0 ||
      filter.roleId ||
      filter.userId ||
      filter.userLabels?.length > 0 ||
      filter.deviceId ||
      filter.deviceLabels?.length > 0
    );
  };

  // Get filter groups configuration for ActiveFilters component
  const getFilterGroups = (): FilterGroup[] => {
    const appliedFilters = getAppliedFilters();

    return [
      {
        key: "datetime",
        filters: [
          createStartDateFilter(appliedFilters.startDate),
          createEndDateFilter(appliedFilters.endDate),
          createStartTimeFilter(appliedFilters.startTime),
          createEndTimeFilter(appliedFilters.endTime),
        ],
      },
      {
        key: "user",
        filters: [
          createSimpleFilter(
            "role",
            "role",
            appliedFilters.role,
            "role_name"
          ),
          createSimpleFilter(
            "user",
            "user",
            appliedFilters.user,
            "name"
          ),
          createLabelFilter(
            "userLabels",
            "userLabels",
            appliedFilters.userLabels
          ),
        ],
      },
      {
        key: "device",
        filters: [
          createSimpleFilter(
            "device",
            "device",
            appliedFilters.device,
            "device_name"
          ),
          createLabelFilter(
            "deviceLabels",
            "deviceLabels",
            appliedFilters.deviceLabels
          ),
        ],
      },
      {
        key: "location",
        filters: [
          createSimpleFilter(
            "site",
            "site",
            appliedFilters.site,
            "zone_name"
          ),
          createLabelFilter(
            "siteLabels",
            "siteLabels",
            appliedFilters.siteLabels
          ),
          createSimpleFilter(
            "checkpoint",
            "checkpoint",
            appliedFilters.checkpoint,
            "checkpoint_name"
          ),
          createLabelFilter(
            "checkpointLabels",
            "checkpointLabels",
            appliedFilters.checkpointLabels
          ),
          createSimpleFilter(
            "branch",
            "branch",
            appliedFilters.branch,
            "branch_name"
          ),
        ],
      },
    ];
  };

  const handleClearAll = () => {
    dispatch(actions.clearFilters());
  };

  const handleRemoveFilter = (
    filterKey: keyof FilterState
  ) => {
    switch (filterKey) {
      case "role":
        dispatch(actions.setSelectedRole(null));
        break;
      case "user":
        dispatch(actions.setSelectedUser(null));
        break;
      case "userLabels":
        dispatch(actions.setSelectedUserLabels([]));
        break;
      case "device":
        dispatch(actions.setSelectedDevice(null));
        break;
      case "deviceLabels":
        dispatch(actions.setSelectedDeviceLabels([]));
        break;
      case "site":
        dispatch(actions.setSelectedSite(null));
        break;
      case "siteLabels":
        dispatch(actions.setSelectedSiteLabels([]));
        break;
      case "checkpoint":
        dispatch(actions.setSelectedCheckpoint(null));
        break;
      case "checkpointLabels":
        dispatch(actions.setSelectedCheckpointLabels([]));
        break;
      case "branch":
        dispatch(actions.setSelectedBranch(null));
        break;
      case "startDate":
        dispatch(actions.setStartDate(null));
        break;
      case "endDate":
        dispatch(actions.setEndDate(null));
        break;
      case "startTime":
        dispatch(actions.setStartTime(null));
        break;
      case "endTime":
        dispatch(actions.setEndTime(null));
        break;
    }
  };

  // Handle clicking on an active filter to edit it
  const handleEditFilter = () => {
    // We only need to open the drawer
    setDrawerVisible(true);
  };

  // Convert FilterState to AnalyticsFilterState for the drawer form
  const getInitialDrawerValues = (): Partial<AnalyticsFilterState> => {
    const appliedFilters = getAppliedFilters();

    return {
      startDate: appliedFilters.startDate || "",
      endDate: appliedFilters.endDate || "",
      startTime: appliedFilters.startTime || "",
      endTime: appliedFilters.endTime || "",
      selectedBranch: appliedFilters.branch || null,
      role: appliedFilters.role,
      site: appliedFilters.site,
      siteLabels: appliedFilters.siteLabels,
      checkpoint: appliedFilters.checkpoint,
      checkpointLabels: appliedFilters.checkpointLabels,
      user: appliedFilters.user,
      userLabels: appliedFilters.userLabels,
      device: appliedFilters.device,
      deviceLabels: appliedFilters.deviceLabels,
    };
  };

  // Create a reusable function to get filter info
  const getFilterInfo = (): {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    branch: AdminBranch | null;
    site: AdminZone | null;
    siteLabels: AdminLabel[];
    checkpoint: AdminCheckpoint | null;
    checkpointLabels: AdminLabel[];
    role: AdminRole | null;
    user: AdminUser | null;
    userLabels: AdminLabel[];
    device: AdminDevice | null;
    deviceLabels: AdminLabel[];
  } => ({
    startDate: exceptionLogState.filter.startDate || null,
    endDate: exceptionLogState.filter.endDate || null,
    startTime: exceptionLogState.filter.startTime || null,
    endTime: exceptionLogState.filter.endTime || null,
    branch: exceptionLogState.selectedBranch || null,
    site: exceptionLogState.selectedSite || null,
    siteLabels: exceptionLogState.selectedSiteLabels || [],
    checkpoint: exceptionLogState.selectedCheckpoint || null,
    checkpointLabels: exceptionLogState.selectedCheckpointLabels || [],
    role: exceptionLogState.selectedRole || null,
    user: exceptionLogState.selectedUser || null,
    userLabels: exceptionLogState.selectedUserLabels || [],
    device: exceptionLogState.selectedDevice || null,
    deviceLabels: exceptionLogState.selectedDeviceLabels || [],
  });

  return (
    <WebAdminLayout activePage="exception-log">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdReportProblem />}
            title={t(
              "exceptionLog.title",
              "Exception Log"
            )}
            description={t(
              "exceptionLog.description",
              "View and manage system exception logs"
            )}
            actions={[
              // Desktop view - show all buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <ExceptionLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                />
                <Button
                  key="filter"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <ExceptionLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                  buttonText={t(
                    "common.download",
                    "Download"
                  )}
                />
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList />}
                  size="large"
                  type="primary"
                  className="bg-indigo-600 hover:bg-indigo-700 border-0 shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters using the reusable component */}
          <ActiveFilters<FilterState>
            filterGroups={getFilterGroups()}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAll}
            onShowFilters={() => {
              setDrawerVisible(true);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Exception Log List */}
          <ExceptionLogList
            exceptionLogs={exceptionLogState.exceptionLogs}
            loading={loading}
            total={pagination.total || 0}
            page={pagination.page || 1}
            limit={pagination.limit || 10}
            onPageChange={handlePageChange}
          />

          {/* Filter Drawer with modern styling */}
          <Drawer
            title={
              <div className="text-lg font-semibold text-gray-800 dark:text-white">
                {t(
                  "exceptionLog.filters.title",
                  "Filter Exception Log"
                )}
              </div>
            }
            placement="right"
            onClose={() => setDrawerVisible(false)}
            open={drawerVisible}
            width={400}
            className="dark:bg-gray-800"
          >
            <AnalyticDrawerForm
              reportType={EReportType.EXCEPTION}
              onApply={handleApplyFilters}
              onClose={() => setDrawerVisible(false)}
              initialValues={getInitialDrawerValues()}
            />
          </Drawer>
        </div>
      </div>
    </WebAdminLayout>
  );
};

export default ExceptionLogAdmin;
