import {
  ModuleType,
  PermissionType,
} from "../pages/WebAdmin/Roles/Components/ModalAddAdminRoles/types";
import { Permission } from "../services/mainApi/types/myProfile.mainApi.types";

// Helper function to check module permissions
const checkModule = (
  authorizedModule: Permission[] | undefined,
  moduleCode: string,
  type: ModuleType = "MODULE",
  moduleType: PermissionType = "allow_view"
) => {
  if (!authorizedModule) return false;

  const findModule = authorizedModule.find(
    (item) =>
      item.module.module_code === moduleCode &&
      item.module.module_type === type
  );

  return findModule
    ? findModule[moduleType] || false
    : false;
};

export { checkModule };
