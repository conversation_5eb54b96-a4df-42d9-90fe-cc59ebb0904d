import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import {
  AdminCheckpoint,
  CreateUpdateCheckpointRequest,
  ECheckPointType,
} from "../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types.ts";
import { RootState } from "../..";
import checkpointAdminMain<PERSON>pi from "../../../services/mainApi/admin/checkpoint.admin.mainApi";
import { isAxiosError } from "axios";
import { handleApiError } from "../../../utils/error";
import {decimalToHex, hexToDecimal} from "../../../utils/conversion.ts";

interface CheckpointFormState {
  open: boolean;
  mode: "create" | "update";
  loading: boolean;
  errorMessage: string[] | string | null;
  selectedCheckpoint: AdminCheckpoint | null;

  // Basic Info
  checkpoint_name: string;
  checkpoint_description: string;
  zone_id: string | null;
  checkpoint_type_id: ECheckPointType | null;

  // Identification
  beacon_id: string | null;
  major_value: string | null;
  minor_value: string | null;
  serial_number_hex: string;
  serial_number_dec: string;
  serial_number_second_hex: string;
  serial_number_second_dec: string;
  geofence_id: string | null;

  // Location
  latitude: number | null;
  longitude: number | null;

  // Schedule
  monday: boolean;
  monday_count: number;
  tuesday: boolean;
  tuesday_count: number;
  wednesday: boolean;
  wednesday_count: number;
  thursday: boolean;
  thursday_count: number;
  friday: boolean;
  friday_count: number;
  saturday: boolean;
  saturday_count: number;
  sunday: boolean;
  sunday_count: number;

  // Settings
  visit_interval: boolean;
  rotation_interval: number | null;
  warning_notification: number | null;
  label_ids: string[];
  active: boolean;
}

const initialState: CheckpointFormState = {
  open: false,
  mode: "create",
  loading: false,
  errorMessage: null,
  selectedCheckpoint: null,

  checkpoint_name: "",
  checkpoint_description: "",
  zone_id: null,
  checkpoint_type_id: null,

  beacon_id: null,
  major_value: null,
  minor_value: null,
  serial_number_hex: "",
  serial_number_dec: "",
  serial_number_second_hex: "",
  serial_number_second_dec: "",
  geofence_id: null,

  latitude: null,
  longitude: null,

  monday: false,
  monday_count: 0,
  tuesday: false,
  tuesday_count: 0,
  wednesday: false,
  wednesday_count: 0,
  thursday: false,
  thursday_count: 0,
  friday: false,
  friday_count: 0,
  saturday: false,
  saturday_count: 0,
  sunday: false,
  sunday_count: 0,

  visit_interval: false,
  rotation_interval: null,
  warning_notification: null,
  label_ids: [],
  active: true,
};

// Create checkpoint thunk
export const createCheckpoint = createAsyncThunk(
  "addCheckpointAdmin/createCheckpoint",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const addCheckpointState = state.addCheckpointAdmin;

      const payload: CreateUpdateCheckpointRequest = {
        zone_id: addCheckpointState.zone_id || "",
        checkpoint_type_id:
          addCheckpointState.checkpoint_type_id || "",
        beacon_id:
          addCheckpointState.beacon_id || undefined,
        geofence_id:
          addCheckpointState.geofence_id || undefined,
        checkpoint_name: addCheckpointState.checkpoint_name,
        checkpoint_description:
          addCheckpointState.checkpoint_description || "",
        major_value:
          addCheckpointState.major_value || undefined,
        minor_value:
          addCheckpointState.minor_value || undefined,
        serial_number_hex:
          addCheckpointState.serial_number_hex,
        serial_number_dec:
          addCheckpointState.serial_number_dec,
        serial_number_second_hex:
          addCheckpointState.serial_number_second_hex,
        serial_number_second_dec:
          addCheckpointState.serial_number_second_dec,
        latitude: addCheckpointState.latitude || 0,
        longitude: addCheckpointState.longitude || 0,
        monday: addCheckpointState.monday,
        monday_count: addCheckpointState.monday_count,
        tuesday: addCheckpointState.tuesday,
        tuesday_count: addCheckpointState.tuesday_count,
        wednesday: addCheckpointState.wednesday,
        wednesday_count: addCheckpointState.wednesday_count,
        thursday: addCheckpointState.thursday,
        thursday_count: addCheckpointState.thursday_count,
        friday: addCheckpointState.friday,
        friday_count: addCheckpointState.friday_count,
        saturday: addCheckpointState.saturday,
        saturday_count: addCheckpointState.saturday_count,
        sunday: addCheckpointState.sunday,
        sunday_count: addCheckpointState.sunday_count,
        visit_interval: addCheckpointState.visit_interval,
        rotation_interval:
          addCheckpointState.rotation_interval || 0,
        warning_notification:
          addCheckpointState.warning_notification || 0,
        label_ids: addCheckpointState.label_ids,
        active: addCheckpointState.active,
      };

      const response =
        await checkpointAdminMainApi.createCheckpoint(
          branchCode,
          payload
        );
      return response.data;
    } catch (e) {
      if (isAxiosError(e)) {
        const { errors } = handleApiError(e);
        return rejectWithValue(errors);
      }
      return rejectWithValue([
        "Failed to create checkpoint",
      ]);
    }
  }
);

// Update checkpoint thunk
export const updateCheckpoint = createAsyncThunk(
  "addCheckpointAdmin/updateCheckpoint",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const addCheckpointState = state.addCheckpointAdmin;

      if (!addCheckpointState.selectedCheckpoint?.id) {
        return rejectWithValue([
          "No checkpoint selected for update",
        ]);
      }

      const payload: CreateUpdateCheckpointRequest = {
        zone_id: addCheckpointState.zone_id || "",
        checkpoint_type_id:
          addCheckpointState.checkpoint_type_id || "",
        beacon_id:
          addCheckpointState.beacon_id || undefined,
        geofence_id:
          addCheckpointState.geofence_id || undefined,
        checkpoint_name: addCheckpointState.checkpoint_name,
        checkpoint_description:
          addCheckpointState.checkpoint_description || "",
        major_value:
          addCheckpointState.major_value || undefined,
        minor_value:
          addCheckpointState.minor_value || undefined,
        serial_number_hex:
          addCheckpointState.serial_number_hex,
        serial_number_dec:
          addCheckpointState.serial_number_dec,
        serial_number_second_hex:
          addCheckpointState.serial_number_second_hex,
        serial_number_second_dec:
          addCheckpointState.serial_number_second_dec,
        latitude: addCheckpointState.latitude || 0,
        longitude: addCheckpointState.longitude || 0,
        monday: addCheckpointState.monday,
        monday_count: addCheckpointState.monday_count,
        tuesday: addCheckpointState.tuesday,
        tuesday_count: addCheckpointState.tuesday_count,
        wednesday: addCheckpointState.wednesday,
        wednesday_count: addCheckpointState.wednesday_count,
        thursday: addCheckpointState.thursday,
        thursday_count: addCheckpointState.thursday_count,
        friday: addCheckpointState.friday,
        friday_count: addCheckpointState.friday_count,
        saturday: addCheckpointState.saturday,
        saturday_count: addCheckpointState.saturday_count,
        sunday: addCheckpointState.sunday,
        sunday_count: addCheckpointState.sunday_count,
        visit_interval: addCheckpointState.visit_interval,
        rotation_interval:
          addCheckpointState.rotation_interval || 0,
        warning_notification:
          addCheckpointState.warning_notification || 0,
        label_ids: addCheckpointState.label_ids,
        active: addCheckpointState.active,
      };

      const response =
        await checkpointAdminMainApi.updateCheckpoint(
          branchCode,
          addCheckpointState.selectedCheckpoint.id,
          payload
        );
      return response.data;
    } catch (e) {
      if (isAxiosError(e)) {
        const { errors } = handleApiError(e);
        return rejectWithValue(errors);
      }
      return rejectWithValue([
        "Failed to update checkpoint",
      ]);
    }
  }
);

const addCheckpointSlice = createSlice({
  name: "addCheckpoint",
  initialState: {
    ...initialState,
  },
  reducers: {
    open: (state) => {
      state.open = true;
    },
    openUpdate: (
      state,
      action: PayloadAction<AdminCheckpoint>
    ) => {
      state.open = true;
      state.mode = "update";
      state.selectedCheckpoint = action.payload;

      state.checkpoint_type_id =
        action.payload.checkpoint_type.id;
      state.zone_id = action.payload.zone_id;
      state.beacon_id = action.payload.beacon_id;
      state.geofence_id = action.payload.geofence_id;
      state.checkpoint_name =
        action.payload.checkpoint_name;
      state.checkpoint_description =
        action.payload.checkpoint_description || "";
      state.major_value = action.payload.major_value;
      state.minor_value = action.payload.minor_value;
      state.serial_number_hex =
        action.payload.serial_number_hex;
      state.serial_number_dec =
        action.payload.serial_number_dec;
      state.serial_number_second_hex =
        action.payload.serial_number_second_hex || "";
      state.serial_number_second_dec =
        action.payload.serial_number_second_dec || "";
      state.latitude = action.payload.latitude;
      state.longitude = action.payload.longitude;
      state.monday = action.payload.monday || false;
      state.monday_count = action.payload.monday_count || 0;
      state.tuesday = action.payload.tuesday || false;
      state.tuesday_count =
        action.payload.tuesday_count || 0;
      state.wednesday = action.payload.wednesday || false;
      state.wednesday_count =
        action.payload.wednesday_count || 0;
      state.thursday = action.payload.thursday || false;
      state.thursday_count =
        action.payload.thursday_count || 0;
      state.friday = action.payload.friday || false;
      state.friday_count = action.payload.friday_count || 0;
      state.saturday = action.payload.saturday || false;
      state.saturday_count =
        action.payload.saturday_count || 0;
      state.sunday = action.payload.sunday || false;
      state.sunday_count = action.payload.sunday_count || 0;
      state.visit_interval =
        action.payload.visit_interval || false;
      state.rotation_interval =
        action.payload.rotation_interval || null;
      state.warning_notification =
        action.payload.warning_notification || null;
      state.label_ids = action.payload.labels.map(
        (label) => label.id
      );
      state.active = action.payload.active;
    },
    close: () => {
      return {
        ...initialState,
      };
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setCheckpointName: (
      state,
      action: PayloadAction<string>
    ) => {
      state.checkpoint_name = action.payload;
    },
    setCheckpointDescription: (
      state,
      action: PayloadAction<string>
    ) => {
      state.checkpoint_description = action.payload;
    },
    setCheckpointActive: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.active = action.payload;
    },
    setCheckpointType: (
      state,
      action: PayloadAction<ECheckPointType>
    ) => {
      state.checkpoint_type_id = action.payload;
    },
    setSerialNumberHex: (
      state,
      action: PayloadAction<string>
    ) => {
      state.serial_number_hex = action.payload;
      if(action.payload === ""){
        state.serial_number_dec = "";
        return;
      }
      state.serial_number_dec = hexToDecimal(action.payload).toString();
    },
    setSerialNumberDec: (
      state,
      action: PayloadAction<string>
    ) => {
      state.serial_number_dec = action.payload;
      if(action.payload === ""){
        state.serial_number_hex = "";
        return;
      }
      state.serial_number_hex = decimalToHex(parseFloat(action.payload));
    },
    setSerialNumberSecondHex: (
      state,
      action: PayloadAction<string>
    ) => {
      state.serial_number_second_hex = action.payload;
      if(action.payload === ""){
        state.serial_number_second_dec = "";
        return;
      }
      state.serial_number_second_dec = hexToDecimal(action.payload).toString();
    },
    setSerialNumberSecondDec: (
      state,
      action: PayloadAction<string>
    ) => {
      state.serial_number_second_dec = action.payload;
      if(action.payload === ""){
        state.serial_number_second_hex = "";
        return;
      }
      state.serial_number_second_hex = decimalToHex(parseFloat(action.payload));
    },
    setMajorValue: (
      state,
      action: PayloadAction<string>
    ) => {
      state.major_value = action.payload;
    },
    setMinorValue: (
      state,
      action: PayloadAction<string>
    ) => {
      state.minor_value = action.payload;
    },
    setGeofence: (state, action: PayloadAction<string>) => {
      state.zone_id = action.payload;
    },
    setGeofenceId: (
      state,
      action: PayloadAction<string>
    ) => {
      state.geofence_id = action.payload;
    },
    setDayActive: (
      state,
      action: PayloadAction<{
        data:
          | "monday"
          | "tuesday"
          | "wednesday"
          | "thursday"
          | "friday"
          | "saturday"
          | "sunday";
        active: boolean;
      }>
    ) => {
      state[action.payload.data] = action.payload.active;
    },
    setDayCount: (
      state,
      action: PayloadAction<{
        data:
          | "monday"
          | "tuesday"
          | "wednesday"
          | "thursday"
          | "friday"
          | "saturday"
          | "sunday";
        count: number;
      }>
    ) => {
      state[`${action.payload.data}_count`] =
        action.payload.count;
    },
    setVisitInterval: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.visit_interval = action.payload;
    },
    setRotationInterval: (
      state,
      action: PayloadAction<number>
    ) => {
      state.rotation_interval = action.payload;
    },
    setWarningNotification: (
      state,
      action: PayloadAction<number>
    ) => {
      state.warning_notification = action.payload;
    },
    setLatitude: (
      state,
      action: PayloadAction<number | null>
    ) => {
      state.latitude = action.payload;
    },
    setLongitude: (
      state,
      action: PayloadAction<number | null>
    ) => {
      state.longitude = action.payload;
    },
    setLabels: (state, action: PayloadAction<string[]>) => {
      state.label_ids = action.payload;
    },
    setBeaconId: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.beacon_id = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create checkpoint cases
      .addCase(createCheckpoint.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(createCheckpoint.fulfilled, (state) => {
        state.loading = false;
        state.open = false;
      })
      .addCase(
        createCheckpoint.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as
            | string
            | string[];
        }
      )
      // Update checkpoint cases
      .addCase(updateCheckpoint.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(updateCheckpoint.fulfilled, (state) => {
        state.loading = false;
        state.open = false;
      })
      .addCase(
        updateCheckpoint.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as
            | string
            | string[];
        }
      );
  },
});

export default addCheckpointSlice;
