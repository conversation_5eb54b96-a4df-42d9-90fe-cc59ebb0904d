import baseMainApi from "../base.mainApi";
import {
  AdminBranchCreateRequestBody,
  AdminBranchGetRequestQuery,
  AdminBranchListResponse,
} from "./types/branch.admin.mainApi.types";

const branchAdminMainApi = {
  getBranches: async (
    params: AdminBranchGetRequestQuery
  ) => {
    const response =
      await baseMainApi.get<AdminBranchListResponse>(
        `/web-api/admin/branch`,
        {
          params,
        }
      );
    return response.data;
  },

  selectBranch: async (params?: {
    show_all_branches?: boolean;
  }) => {
    const response =
      await baseMainApi.get<AdminBranchListResponse>(
        `/web-api/admin/branch/select`,
        { params }
      );
    return response.data;
  },

  createBranch: async (
    data: AdminBranchCreateRequestBody
  ) => {
    const response =
      await baseMainApi.post<AdminBranchListResponse>(
        `/web-api/admin/branch`,
        data
      );
    return response.data;
  },

  updateBranch: async (
    id: string,
    data: AdminBranchCreateRequestBody
  ) => {
    const response =
      await baseMainApi.put<AdminBranchListResponse>(
        `/web-api/admin/branch/${id}`,
        data
      );
    return response.data;
  },
};

export default branchAdminMainApi;
