import { Tag } from "antd";
import { AdminBeacon } from "../../../../services/mainApi/admin/types/beacon.admin.mainApi.types";
import dayjs from "dayjs";
import {
  MdAccessTime,
  MdBluetooth,
  MdKey,
  MdStore,
} from "react-icons/md";

interface Props {
  beacon: AdminBeacon;
}

const BeaconListItem = ({ beacon }: Props) => {
  return (
    <div
      className={`
        rounded-xl border transition-all duration-200 hover:shadow-lg
        ${
          beacon.active
            ? "bg-white dark:bg-gray-800 border-gray-100 dark:border-gray-700"
            : "bg-gray-50/50 dark:bg-gray-800/50 border-gray-200/80 dark:border-gray-700/50"
        }
      `}
    >
      <div className="p-5">
        {/* Header Section */}
        <div className="flex items-start justify-between mb-3">
          <div>
            <div className="flex items-center gap-2">
              <h3
                className={`text-lg font-medium ${
                  beacon.active
                    ? "text-gray-900 dark:text-gray-100"
                    : "text-gray-500 dark:text-gray-400"
                }`}
              >
                {beacon.beacon_name}
                {!beacon.active && (
                  <Tag className="ml-2 text-xs bg-red-50 text-red-600 border-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-800/50">
                    INACTIVE
                  </Tag>
                )}
              </h3>
            </div>
            {beacon.beacon_description && (
              <p className="mt-1.5 text-sm text-gray-500 dark:text-gray-400">
                {beacon.beacon_description}
              </p>
            )}
          </div>
        </div>

        {/* Info Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Left Column - Technical Info */}
          <div className="space-y-2.5">
            <InfoItem
              icon={<MdBluetooth />}
              label="UUID"
              value={beacon.beacon_uuid}
              valueClassName="font-mono text-xs"
            />
            <InfoItem
              icon={<MdKey />}
              label="Counter Customer Code"
              value={beacon.counter_customer_code}
            />
            {beacon.pip_api_key && (
              <InfoItem
                icon={<MdKey />}
                label="PIP API Key"
                value={beacon.pip_api_key}
                valueClassName="font-mono text-xs"
              />
            )}
          </div>

          {/* Right Column - Branch Info */}
          <div className="space-y-2.5">
            <InfoItem
              icon={<MdStore />}
              label="Branch"
              value={beacon.parent_branch.branch_name}
            />
            <InfoItem
              icon={<MdAccessTime />}
              label="Created At"
              value={dayjs(beacon.created_at).format(
                "DD MMM YYYY HH:mm"
              )}
            />
            <InfoItem
              icon={<MdAccessTime />}
              label="Last Updated"
              value={dayjs(beacon.updated_at).format(
                "DD MMM YYYY HH:mm"
              )}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const InfoItem = ({
  icon,
  label,
  value,
  valueClassName = "",
}: {
  icon: React.ReactNode;
  label: string;
  value: React.ReactNode;
  valueClassName?: string;
}) => (
  <div className="flex items-start gap-2">
    <div className="text-lg text-gray-400 dark:text-gray-500">
      {icon}
    </div>
    <div>
      <div className="text-xs text-gray-500 dark:text-gray-400">
        {label}
      </div>
      <div
        className={`text-sm font-medium text-gray-900 dark:text-gray-100 ${valueClassName}`}
      >
        {value}
      </div>
    </div>
  </div>
);

export default BeaconListItem;
