import { Checkbox } from "antd";
import { Module } from "../../../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { Permission, PermissionType } from "./types";

interface PermissionItemProps {
  module: Module;
  permission: Permission;
  onChange: (
    moduleCode: string,
    moduleType: "MODULE" | "REPORT",
    permission: PermissionType,
    value: boolean
  ) => void;
}

const PermissionItem = ({
  module,
  permission,
  onChange,
}: PermissionItemProps) => {
  // Handler for view checkbox
  const handleViewChange = (checked: boolean) => {
    onChange(module.module_code, module.module_type, "allow_view", checked);
    if (!checked) {
      // Uncheck create, update, delete if view is unchecked
      ["allow_create", "allow_update", "allow_delete"].forEach((perm) => {
        if (permission[perm as PermissionType]) {
          onChange(module.module_code, module.module_type, perm as PermissionType, false);
        }
      });
    }
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 py-3 border-b last:border-b-0 dark:border-gray-700">
      <div className="flex-1 min-w-0">
        <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
          {module.module_name}
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
          {module.module_description}
        </div>
      </div>
      <div className="flex gap-4 flex-wrap sm:flex-nowrap">
        <Checkbox
          checked={permission.allow_view}
          onChange={e => handleViewChange(e.target.checked)}
        >
          View
        </Checkbox>
        <Checkbox
          checked={permission.allow_create}
          disabled={!permission.allow_view}
          onChange={e => onChange(
            module.module_code,
            module.module_type,
            "allow_create",
            e.target.checked
          )}
        >
          Create
        </Checkbox>
        <Checkbox
          checked={permission.allow_update}
          disabled={!permission.allow_view}
          onChange={e => onChange(
            module.module_code,
            module.module_type,
            "allow_update",
            e.target.checked
          )}
        >
          Update
        </Checkbox>
        <Checkbox
          checked={permission.allow_delete}
          disabled={!permission.allow_view}
          onChange={e => onChange(
            module.module_code,
            module.module_type,
            "allow_delete",
            e.target.checked
          )}
        >
          Delete
        </Checkbox>
      </div>
    </div>
  );
};

export default PermissionItem;
