import React from "react";
import { Empty, Pagination, Spin } from "antd";
import { useTranslation } from "react-i18next";
import { AdminTaskLog } from "../../../../../services/mainApi/admin/types/taskLog.admin.mainApi.types";
import TaskLogListItem from "./TaskLogListItem";

interface TaskLogListProps {
  taskLogs: AdminTaskLog[];
  loading: boolean;
  total: number;
  page: number;
  limit: number;
  onPageChange: (page: number, pageSize?: number) => void;
}

const TaskLogList: React.FC<TaskLogListProps> = ({
  taskLogs,
  loading,
  total,
  page,
  limit,
  onPageChange,
}) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="flex justify-center items-center py-16 bg-white dark:bg-gray-800 rounded-xl shadow-lg border-0 p-8 animate-pulse">
        <div className="flex flex-col items-center">
          <Spin size="large" />
          <p className="mt-4 text-gray-500 dark:text-gray-400">
            {t("common.loading", "Loading data...")}
          </p>
        </div>
      </div>
    );
  }

  if (taskLogs.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border-0 p-12 transition-all duration-300">
        <Empty
          description={
            <span className="text-gray-500 dark:text-gray-400 text-base">
              {t(
                "taskLog.noData",
                "No task logs found"
              )}
            </span>
          }
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          className="opacity-80"
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* List with items */}
      <div className="space-y-5">
        {taskLogs.map((taskLog) => (
          <TaskLogListItem
            key={taskLog.id}
            taskLog={taskLog}
          />
        ))}
      </div>

      {/* Pagination with modern styling */}
      {total > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border-0 p-4 my-6 transition-all duration-300">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="text-sm text-gray-500 dark:text-gray-400 font-medium">
              {t("pagination.total", "Total {{total}} items", {
                total,
              })}
            </div>
            <Pagination
              current={page}
              pageSize={limit}
              total={total}
              onChange={onPageChange}
              showSizeChanger
              pageSizeOptions={["10", "20", "50", "100"]}
              className="dark:text-gray-300"
              showTotal={() => null} // We're showing the total separately above
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskLogList;
