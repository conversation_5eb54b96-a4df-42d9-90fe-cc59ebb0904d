import React, { useState } from "react";
import type { MenuProps } from "antd";
import { <PERSON><PERSON>, Dropdown, Tooltip } from "antd";
import { FiDownload } from "react-icons/fi";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { timeOnZoneAdminMainApi } from "../../../../../services/mainApi/admin/timeOnZone.admin.mainApi";
import { AdminBranch } from "../../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminZone } from "../../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { AdminTimeOnZoneListParams } from "../../../../../services/mainApi/admin/types/timeOnZone.admin.mainApi.types";
import { AdminLabel } from "../../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminUser } from "../../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../../../services/mainApi/admin/types/device.admin.mainApi.types.ts";

interface TimeOnZoneLogExportButtonProps {
  filters: {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    selectedBranch: AdminBranch | null;
    site: AdminZone | null;
    siteLabels: AdminLabel[];
    user: AdminUser | null;
    userLabels: AdminLabel[];
    device: AdminDevice | null;
    deviceLabels: AdminLabel[];
  };
  className?: string;
  buttonText?: string;
  showPdf?: boolean;
  showSpreadsheet?: boolean;
}

type ExportFormat = "pdf" | "spreadsheet";

const TimeOnZoneLogExportButton: React.FC<
  TimeOnZoneLogExportButtonProps
> = ({
       filters,
       className,
       buttonText,
       showPdf = true,
       showSpreadsheet = true,
     }) => {
  const {t} = useTranslation();
  const [isDownloading, setIsDownloading] = useState(false);
  const {branchCode} = useParams<{
    branchCode: string;
  }>();

  const handleDownload = async (format: ExportFormat) => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const filterParams: AdminTimeOnZoneListParams = {
        start_date: filters.startDate || undefined,
        end_date: filters.endDate || undefined,
        start_time: filters.startTime || undefined,
        end_time: filters.endTime || undefined,
        branch_id: filters.selectedBranch?.id || undefined,
        zone_id: filters.site?.id || undefined,
        zone_labels:
          filters.siteLabels?.map((label) => label.id) ||
          undefined,
        user_id: filters.user?.id || undefined,
        user_labels:
          filters.userLabels?.map((label) => label.id) ||
          undefined,
        device_id: filters.device?.id || undefined,
        device_labels:
          filters.deviceLabels?.map((label) => label.id) ||
          undefined,
      };

      const blob =
        await timeOnZoneAdminMainApi.exportTimeOnZone(
          branchCode,
          filterParams,
          format,
          "buffer"
        );

      if (format === "pdf") {
        // Create URL and open in new tab
        const url = URL.createObjectURL(blob);
        window.open(url, "_blank");

        // Delay URL revocation to ensure the new tab has time to load
        setTimeout(() => {
          URL.revokeObjectURL(url);
        }, 5000);
      } else {
        // Create URL and trigger download
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `time_on_zone_logs_${new Date().toISOString().slice(0, 10)}.xlsx`;
        link.click();

        // Clean up
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error(`Error exporting ${format}:`, error);
    } finally {
      setIsDownloading(false);
    }
  };

  // If only one format is shown, render a simple button
  if (
    (showPdf && !showSpreadsheet) ||
    (!showPdf && showSpreadsheet)
  ) {
    const format = showPdf ? "pdf" : "spreadsheet";
    const tooltipText = showPdf
      ? t(
          "timeOnZone.downloadPdf",
          "Download as PDF"
        )
      : t(
          "timeOnZone.downloadSpreadsheet",
          "Download as Spreadsheet"
        );

    return (
      <Tooltip title={tooltipText}>
        <Button
          icon={<FiDownload />}
          loading={isDownloading}
          onClick={() => handleDownload(format)}
          size="large"
          className={className}
        >
          {buttonText ||
            (showPdf
              ? t("timeOnZone.pdf", "PDF")
              : t(
                  "timeOnZone.spreadsheet",
                  "Spreadsheet"
                ))}
        </Button>
      </Tooltip>
    );
  }

  // Otherwise, render a dropdown with both options
  const items: MenuProps["items"] = [
    {
      key: "pdf",
      label: t(
        "timeOnZone.exportPdf",
        "Export as PDF"
      ),
      onClick: () => handleDownload("pdf"),
      disabled: !showPdf,
    },
    {
      key: "spreadsheet",
      label: t(
        "timeOnZone.exportSpreadsheet",
        "Export as Spreadsheet"
      ),
      onClick: () => handleDownload("spreadsheet"),
      disabled: !showSpreadsheet,
    },
  ];

  return (
    <Dropdown
      menu={{ items }}
      disabled={isDownloading}
    >
      <Button
        icon={<FiDownload />}
        loading={isDownloading}
        size="large"
        className={className}
      >
        {buttonText ||
          t("timeOnZone.export", "Export")}
      </Button>
    </Dropdown>
  );
};

export default TimeOnZoneLogExportButton;
