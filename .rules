# Project Development Rules

# File Naming Conventions
naming:
  components:
    pattern: "PascalCase"
    examples: ["Button.tsx", "UserCard.tsx", "DataTable.tsx"]
  pages:
    pattern: "PascalCase"
    examples: ["Login.tsx", "Dashboard.tsx"]
  hooks:
    pattern: "camelCase"
    prefix: "use"
    examples: ["useAuth.ts", "useTheme.ts"]
  utils:
    pattern: "camelCase"
    examples: ["formatDate.ts", "validateInput.ts"]
  types:
    pattern: "PascalCase"
    suffix: ".types.ts"
    examples: ["User.types.ts", "Auth.types.ts"]
  constants:
    pattern: "SCREAMING_SNAKE_CASE"
    examples: ["API_ENDPOINTS.ts", "ROUTE_PATHS.ts"]
  services:
    pattern: "camelCase"
    suffix: ".mainApi.ts"
    examples: ["auth.mainApi.ts", "user.mainApi.ts", "branch.admin.mainApi.ts", "license.sysadmin.mainApi.ts"]
  slices:
    pattern: "camelCase"
    suffix: ".slice.ts"
    examples: ["auth.slice.ts", "label.admin.slice.ts", "branchList.sysadmin.slice.ts"]

# Code Organization
structure:
  components:
    - name: "index.tsx"      # Main component file
    - name: "styles.ts"      # Styled components/styles
    - name: "types.ts"       # Component types
    - name: "utils.ts"       # Component-specific utilities
    - name: "constants.ts"   # Component-specific constants
    - name: "hooks.ts"       # Component-specific hooks
  pages:
    webAdmin:
      path: "src/pages/WebAdmin/<ModuleName>/<PageName>.tsx"
      examples: ["src/pages/WebAdmin/Label/LabelAdmin.tsx", "src/pages/WebAdmin/Beacon/BeaconAdmin.tsx"]
    sysAdmin:
      path: "src/pages/SysAdmin/<ModuleName>/<PageName>.tsx"
      examples: ["src/pages/SysAdmin/Branch/BranchPage.tsx", "src/pages/SysAdmin/License/LicensePage.tsx"]
    auth:
      path: "src/pages/Auth/<PageName>.tsx"
      examples: ["src/pages/Auth/Login.tsx", "src/pages/Auth/ForgotPassword.tsx"]
    analytics:
      path: "src/pages/WebAdmin/Analytics/<ModuleName>/<PageName>.tsx"
      examples: ["src/pages/WebAdmin/Analytics/ActivityLog/ActivityLogAdmin.tsx", "src/pages/WebAdmin/Analytics/FormLog/FormLogAdmin.tsx"]

# Documentation Requirements
documentation:
  components:
    required:
      - description
      - props
      - examples
    template: |
      /**
       * @description Component description
       * @param {PropType} props - Props description
       * @example
       *   <Component prop="value" />
       */

# State Management
state:
  redux:
    slicePattern: |
      interface State {
        loading: boolean;
        error: string | null;
        data: any[];
        pagination: BasePaginationMeta;
      }

      const initialState: State = {
        loading: false,
        error: null,
        data: [],
        pagination: {
          total: 0,
          page: 1,
          limit: 10,
          total_pages: 0,
        },
      };

      export const slice = createSlice({
        name: 'sliceName',
        initialState,
        reducers: {
          setLoading: (state, action: PayloadAction<boolean>) => {
            state.loading = action.payload;
          },
        },
      });
    sliceNaming:
      admin: "<feature>.admin.slice.ts"
      sysadmin: "<feature>.sysadmin.slice.ts"
      common: "<feature>.slice.ts"
    sliceLocation:
      admin: "src/store/slices/admin/"
      sysadmin: "src/store/slices/sysadmin/"
      common: "src/store/slices/"

# API Integration
api:
  servicePattern: |
    import baseMainApi from "../base.mainApi";
    import { FeatureResponse } from "./types/feature.admin.mainApi.types";

    const featureAdminApi = {
      getAll: (params?: any) =>
        baseMainApi.get<FeatureResponse>('/web-api/admin/feature', { params }),
      getById: (id: string) =>
        baseMainApi.get<FeatureResponse>(`/web-api/admin/feature/${id}`),
      create: (data: any) =>
        baseMainApi.post('/web-api/admin/feature', data),
      update: (id: string, data: any) =>
        baseMainApi.put(`/web-api/admin/feature/${id}`, data),
      delete: (id: string) =>
        baseMainApi.delete(`/web-api/admin/feature/${id}`),
    };

    export default featureAdminApi;
  serviceLocation:
    admin: "src/services/mainApi/admin/"
    sysadmin: "src/services/mainApi/sysadmin/"
    common: "src/services/mainApi/"
  serviceNaming:
    admin: "<feature>.admin.mainApi.ts"
    sysadmin: "<feature>.sysadmin.mainApi.ts"
    common: "<feature>.mainApi.ts"
  typeLocation:
    admin: "src/services/mainApi/admin/types/"
    sysadmin: "src/services/mainApi/sysadmin/types/"
    common: "src/services/mainApi/types/"
  typeNaming:
    admin: "<feature>.admin.mainApi.types.ts"
    sysadmin: "<feature>.sysadmin.mainApi.types.ts"
    common: "<feature>.mainApi.types.ts"
  baseTypes: |
    export interface BasePaginationMeta {
      total?: number;
      page?: number;
      limit?: number;
      total_pages?: number;
    }

    export interface BaseResponse<T, M extends BasePaginationMeta = BasePaginationMeta> {
      success: boolean;
      message: string;
      data: T | null;
      meta: {
        timestamp: string;
        path: string;
      } & M;
      error: BaseError | null;
    }

# Testing
testing:
  components:
    required:
      - render test
      - user interaction
      - props validation
  hooks:
    required:
      - initialization
      - state changes
      - cleanup

# Commit Messages
git:
  commitPattern: "<type>(<scope>): <description>"
  types:
    - feat     # New feature
    - fix      # Bug fix
    - docs     # Documentation
    - style    # Formatting
    - refactor # Code restructure
    - test     # Tests
    - chore    # Maintenance

# Code Generation Templates
templates:
  component: |
    import { FC } from 'react';

    interface Props {}

    export const Component: FC<Props> = () => {
      return <div>Component</div>;
    };

  page: |
    import { FC } from 'react';
    import { useTranslation } from 'react-i18next';

    export const Page: FC = () => {
      const { t } = useTranslation();
      return <div>{t('page.title')}</div>;
    };

  hook: |
    import { useState, useEffect } from 'react';

    export const useHook = () => {
      const [state, setState] = useState();

      useEffect(() => {
        // Effect logic
      }, []);

      return { state };
    };

# Style Guide
styling:
  tailwind:
    customClasses:
      layout: ["container", "wrapper", "section"]
      spacing: ["space-y-4", "space-x-4"]
      responsive: ["sm:", "md:", "lg:", "xl:"]
  antd:
    themeCustomization:
      primary: "#1890ff"
      success: "#52c41a"
      warning: "#faad14"
      error: "#f5222d"

# Panduan Pembuatan Halaman Baru
pageCreationGuide:
  # Langkah-langkah membuat halaman baru beserta routing dan menu
  newPage:
    steps:
      - "1. Buat komponen halaman baru di lokasi yang sesuai (WebAdmin atau SysAdmin)"
      - "2. Tambahkan route di src/config/routes.tsx"
      - "3. Tambahkan item menu di src/config/menuConfig.tsx"
      - "4. Buat slice, API service, dan types jika diperlukan"
      - "5. Tambahkan terjemahan di file locales"
    webAdminExample: |
      // 1. Buat file halaman di src/pages/WebAdmin/NewModule/NewModulePage.tsx
      import { FC } from 'react';
      import { useTranslation } from 'react-i18next';
      import WebAdminLayout from '../../../components/Layout/WebAdminLayout';
      import PageHeader from '../../../components/Admin/Common/PageHeader';
      import { Button } from 'antd';
      import { MdNewModule } from 'react-icons/md';

      const NewModulePage: FC = () => {
        const { t } = useTranslation();

        return (
          <WebAdminLayout activePage="newModule">
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
              <div className="max-w-7xl mx-auto space-y-6">
                <PageHeader
                  title={t('newModule.title')}
                  description={t('newModule.description')}
                  icon={<MdNewModule className="text-primary text-2xl" />}
                  actions={[
                    <Button key="action" type="primary">
                      {t('newModule.action')}
                    </Button>
                  ]}
                />
                {/* Page content */}
              </div>
            </div>
          </WebAdminLayout>
        );
      };

      export default NewModulePage;

      // 2. Tambahkan route di src/config/routes.tsx
      const NewModulePage = lazy(() => import("../pages/WebAdmin/NewModule/NewModulePage"));

      export const routeConfig = {
        // ... routes lainnya
        admin: {
          // ... routes admin lainnya
          newModule: {
            component: <NewModulePage />,
            path: "/admin/branch/:branchCode/new-module",
          },
        },
      };

      // 3. Tambahkan item menu di src/config/menuConfig.tsx
      export const useMenuConfig = () => {
        const { t } = useTranslation();

        const webAdminConfig = {
          menuItems: [
            // ... menu items lainnya
            {
              key: "newModule",
              label: t("menu.newModule"),
              icon: <MdNewModule />,
            },
          ],
          menuPaths: {
            // ... menu paths lainnya
            newModule: "/admin/branch/:branchCode/new-module",
          },
        };

        return { webAdminConfig };
      };

      // 4. Tambahkan terjemahan di src/locales/id/newModule.json
      // {
      //   "newModule": {
      //     "title": "Judul Modul Baru",
      //     "description": "Deskripsi modul baru",
      //     "action": "Tambah Baru"
      //   },
      //   "menu": {
      //     "newModule": "Modul Baru"
      //   }
      // }
    sysAdminExample: |
      // 1. Buat file halaman di src/pages/SysAdmin/NewModule/NewModulePage.tsx
      import { FC } from 'react';
      import { useTranslation } from 'react-i18next';
      import SysAdminLayout from '../../../components/Layout/SysAdminLayout';
      import { Button } from 'antd';

      const NewModulePage: FC = () => {
        const { t } = useTranslation();

        return (
          <SysAdminLayout activePage="newModule">
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
              <div className="max-w-7xl mx-auto space-y-6">
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
                  <h1 className="text-2xl font-bold">{t('newModule.title')}</h1>
                  <p className="text-gray-600 dark:text-gray-300">{t('newModule.description')}</p>
                  <div className="mt-4">
                    <Button type="primary">{t('newModule.action')}</Button>
                  </div>
                </div>
                {/* Page content */}
              </div>
            </div>
          </SysAdminLayout>
        );
      };

      export default NewModulePage;

      // 2. Tambahkan route di src/config/routes.tsx
      const NewModulePage = lazy(() => import("../pages/SysAdmin/NewModule/NewModulePage"));

      export const routeConfig = {
        // ... routes lainnya
        sysAdmin: {
          // ... routes sysAdmin lainnya
          newModule: {
            component: <NewModulePage />,
            path: "/sysadmin/new-module",
          },
        },
      };

      // 3. Tambahkan item menu di src/config/menuConfig.tsx
      export const useMenuConfig = () => {
        const { t } = useTranslation();

        const sysAdminConfig = {
          menuItems: [
            // ... menu items lainnya
            {
              key: "newModule",
              label: t("menu.newModule"),
              icon: <MdNewModule />,
            },
          ],
          menuPaths: {
            // ... menu paths lainnya
            newModule: "/sysadmin/new-module",
          },
        };

        return { sysAdminConfig };
      };

  # Panduan membuat menu Analytics
  analyticsMenu:
    steps:
      - "1. Buat folder untuk modul analitik baru di src/pages/WebAdmin/Analytics/<NamaModul>/"
      - "2. Buat komponen halaman utama di folder tersebut"
      - "3. Tambahkan route di src/config/routes.tsx"
      - "4. Tambahkan item menu di src/config/menuConfig.tsx sebagai submenu Analytics"
      - "5. Buat slice, API service, dan types untuk modul analitik"
    example: |
      // 1. Buat file halaman di src/pages/WebAdmin/Analytics/NewAnalytics/NewAnalyticsAdmin.tsx
      import { FC } from 'react';
      import { useTranslation } from 'react-i18next';
      import WebAdminLayout from '../../../../components/Layout/WebAdminLayout';
      import PageHeader from '../../../../components/Admin/Common/PageHeader';
      import { DatePicker, Select, Button } from 'antd';
      import { MdAnalytics } from 'react-icons/md';

      const NewAnalyticsAdmin: FC = () => {
        const { t } = useTranslation();
        const { RangePicker } = DatePicker;

        return (
          <WebAdminLayout activePage="analytics.newAnalytics">
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
              <div className="max-w-7xl mx-auto space-y-6">
                <PageHeader
                  title={t('analytics.newAnalytics.title')}
                  description={t('analytics.newAnalytics.description')}
                  icon={<MdAnalytics className="text-primary text-2xl" />}
                />

                {/* Filter Section */}
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
                  <div className="flex flex-wrap gap-4 items-end">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {t('common.dateRange')}
                      </label>
                      <RangePicker className="w-full" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {t('common.filterBy')}
                      </label>
                      <Select className="w-48" placeholder={t('common.selectOption')} />
                    </div>
                    <Button type="primary">{t('common.filter')}</Button>
                  </div>
                </div>

                {/* Analytics Content */}
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
                  {/* Analytics data visualization */}
                </div>
              </div>
            </div>
          </WebAdminLayout>
        );
      };

      export default NewAnalyticsAdmin;

      // 2. Tambahkan route di src/config/routes.tsx
      const NewAnalyticsAdmin = lazy(() => import("../pages/WebAdmin/Analytics/NewAnalytics/NewAnalyticsAdmin"));

      export const routeConfig = {
        // ... routes lainnya
        admin: {
          // ... routes admin lainnya
          newAnalytics: {
            component: <NewAnalyticsAdmin />,
            path: "/admin/branch/:branchCode/analytics/new-analytics",
          },
        },
      };

      // 3. Tambahkan item menu di src/config/menuConfig.tsx
      export const useMenuConfig = () => {
        const { t } = useTranslation();

        const webAdminConfig = {
          menuItems: [
            // ... menu items lainnya
            {
              key: "analytics",
              label: t("menu.analytics"),
              icon: <MdAnalytics />,
              children: [
                // ... submenu analytics lainnya
                {
                  key: "analytics.newAnalytics",
                  label: t("menu.analytics.newAnalytics"),
                },
              ],
            },
          ],
          menuPaths: {
            // ... menu paths lainnya
            "analytics.newAnalytics": "/admin/branch/:branchCode/analytics/new-analytics",
          },
        };

        return { webAdminConfig };
      };

  # Panduan membuat service API
  apiService:
    steps:
      - "1. Buat file API service di lokasi yang sesuai (admin/sysadmin/common)"
      - "2. Buat file types untuk API response dan request"
      - "3. Implementasikan fungsi-fungsi API yang diperlukan"
      - "4. Gunakan service di komponen atau slice"
    adminExample: |
      // 1. Buat file types di src/services/mainApi/admin/types/newModule.admin.mainApi.types.ts
      import { BaseResponse, BasePaginationMeta } from "../../../types/base.mainApi.types";

      export interface NewModuleItem {
        id: string;
        name: string;
        description: string;
        active: boolean;
        created_at: string;
        updated_at: string;
      }

      export interface NewModuleListParams {
        page?: number;
        limit?: number;
        search?: string;
        order_by?: string;
        order_direction?: "ASC" | "DESC";
      }

      export interface NewModuleCreateParams {
        name: string;
        description: string;
        active: boolean;
      }

      export interface NewModuleUpdateParams {
        name?: string;
        description?: string;
        active?: boolean;
      }

      export type NewModuleListResponse = BaseResponse<NewModuleItem[], BasePaginationMeta>;
      export type NewModuleDetailResponse = BaseResponse<NewModuleItem>;

      // 2. Buat file service di src/services/mainApi/admin/newModule.admin.mainApi.ts
      import baseMainApi from "../base.mainApi";
      import {
        NewModuleListResponse,
        NewModuleDetailResponse,
        NewModuleListParams,
        NewModuleCreateParams,
        NewModuleUpdateParams,
      } from "./types/newModule.admin.mainApi.types";

      const newModuleAdminApi = {
        getAll: (params?: NewModuleListParams) =>
          baseMainApi.get<NewModuleListResponse>("/web-api/admin/new-module", { params }),

        getById: (id: string) =>
          baseMainApi.get<NewModuleDetailResponse>(`/web-api/admin/new-module/${id}`),

        create: (data: NewModuleCreateParams) =>
          baseMainApi.post<NewModuleDetailResponse>("/web-api/admin/new-module", data),

        update: (id: string, data: NewModuleUpdateParams) =>
          baseMainApi.put<NewModuleDetailResponse>(`/web-api/admin/new-module/${id}`, data),

        delete: (id: string) =>
          baseMainApi.delete<BaseResponse<null>>(`/web-api/admin/new-module/${id}`),

        toggleActive: (id: string, active: boolean) =>
          baseMainApi.patch<NewModuleDetailResponse>(`/web-api/admin/new-module/${id}/toggle-active`, { active }),
      };

      export default newModuleAdminApi;

      // 3. Contoh penggunaan di slice
      import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
      import { RootState } from "../..";
      import newModuleAdminApi from "../../../services/mainApi/admin/newModule.admin.mainApi";
      import { NewModuleItem, NewModuleListParams } from "../../../services/mainApi/admin/types/newModule.admin.mainApi.types";
      import { BasePaginationMeta } from "../../../services/mainApi/types/base.mainApi.types";

      interface State {
        items: NewModuleItem[];
        loading: boolean;
        error: string | null;
        pagination: BasePaginationMeta;
        filter: NewModuleListParams;
      }

      const initialState: State = {
        items: [],
        loading: false,
        error: null,
        pagination: {
          total: 0,
          page: 1,
          limit: 10,
          total_pages: 0,
        },
        filter: {
          page: 1,
          limit: 10,
          search: "",
          order_by: "created_at",
          order_direction: "DESC",
        },
      };

      export const fetchNewModules = createAsyncThunk(
        "newModule/fetchAll",
        async (_, { getState, rejectWithValue }) => {
          try {
            const state = getState() as RootState;
            const filter = state.newModule.filter;

            const response = await newModuleAdminApi.getAll(filter);
            return response.data;
          } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || "Failed to fetch data");
          }
        }
      );

      const newModuleSlice = createSlice({
        name: "newModule",
        initialState,
        reducers: {
          setFilter: (state, action: PayloadAction<Partial<NewModuleListParams>>) => {
            state.filter = { ...state.filter, ...action.payload };
          },
        },
        extraReducers: (builder) => {
          builder
            .addCase(fetchNewModules.pending, (state) => {
              state.loading = true;
              state.error = null;
            })
            .addCase(fetchNewModules.fulfilled, (state, action) => {
              state.loading = false;
              state.items = action.payload?.data || [];
              state.pagination = {
                total: action.payload?.meta?.total || 0,
                page: action.payload?.meta?.page || 1,
                limit: action.payload?.meta?.limit || 10,
                total_pages: action.payload?.meta?.total_pages || 0,
              };
            })
            .addCase(fetchNewModules.rejected, (state, action) => {
              state.loading = false;
              state.error = action.payload as string;
            });
        },
      });

      export const { setFilter } = newModuleSlice.actions;
      export default newModuleSlice;
    sysadminExample: |
      // 1. Buat file types di src/services/mainApi/sysadmin/types/newModule.sysadmin.mainApi.types.ts
      import { BaseResponse, BasePaginationMeta } from "../../../types/base.mainApi.types";

      export interface SysNewModuleItem {
        id: string;
        name: string;
        description: string;
        active: boolean;
        created_at: string;
        updated_at: string;
      }

      export interface SysNewModuleListParams {
        page?: number;
        limit?: number;
        search?: string;
        order_by?: string;
        order_direction?: "ASC" | "DESC";
      }

      export type SysNewModuleListResponse = BaseResponse<SysNewModuleItem[], BasePaginationMeta>;
      export type SysNewModuleDetailResponse = BaseResponse<SysNewModuleItem>;

      // 2. Buat file service di src/services/mainApi/sysadmin/newModule.sysadmin.mainApi.ts
      import baseMainApi from "../base.mainApi";
      import {
        SysNewModuleListResponse,
        SysNewModuleDetailResponse,
        SysNewModuleListParams,
      } from "./types/newModule.sysadmin.mainApi.types";

      const newModuleSysadminApi = {
        getAll: (params?: SysNewModuleListParams) =>
          baseMainApi.get<SysNewModuleListResponse>("/web-api/sysadmin/new-module", { params }),

        getById: (id: string) =>
          baseMainApi.get<SysNewModuleDetailResponse>(`/web-api/sysadmin/new-module/${id}`),
      };

      export default newModuleSysadminApi;

# Panduan Export Data
exportFeatures:
  # Panduan membuat fitur export PDF
  pdfExport:
    steps:
      - "1. Install library jsPDF untuk export PDF: npm install jspdf jspdf-autotable"
      - "2. Buat utility function untuk export PDF"
      - "3. Implementasikan tombol export di halaman analytics"
      - "4. Tambahkan fungsi handler untuk memproses data dan menghasilkan PDF"
    example: |
      // 1. Buat utility function di src/utils/exportPdf.ts
      import jsPDF from 'jspdf';
      import autoTable from 'jspdf-autotable';
      import dayjs from 'dayjs';

      export interface PdfColumn {
        header: string;
        dataKey: string;
        width?: number;
      }

      export interface PdfExportOptions {
        title: string;
        fileName: string;
        columns: PdfColumn[];
        data: Record<string, any>[];
        orientation?: 'portrait' | 'landscape';
        pageSize?: string;
        includeTimestamp?: boolean;
        additionalInfo?: Record<string, string>;
        logo?: string;
      }

      export const exportToPdf = (options: PdfExportOptions) => {
        const {
          title,
          fileName,
          columns,
          data,
          orientation = 'portrait',
          pageSize = 'a4',
          includeTimestamp = true,
          additionalInfo = {},
          logo,
        } = options;

        // Inisialisasi dokumen PDF
        const doc = new jsPDF({
          orientation,
          unit: 'mm',
          format: pageSize,
        });

        // Tambahkan logo jika ada
        if (logo) {
          doc.addImage(logo, 'PNG', 14, 10, 30, 10);
        }

        // Tambahkan judul
        doc.setFontSize(16);
        doc.text(title, 14, logo ? 30 : 15);

        // Tambahkan timestamp jika diperlukan
        if (includeTimestamp) {
          doc.setFontSize(10);
          doc.text(
            `Generated on: ${dayjs().format('DD MMM YYYY HH:mm:ss')}`,
            14,
            logo ? 35 : 20
          );
        }

        // Tambahkan informasi tambahan
        let yPos = logo ? 40 : 25;
        if (Object.keys(additionalInfo).length > 0) {
          doc.setFontSize(10);
          Object.entries(additionalInfo).forEach(([key, value]) => {
            doc.text(`${key}: ${value}`, 14, yPos);
            yPos += 5;
          });
        }

        // Siapkan data untuk tabel
        const tableColumns = columns.map((col) => ({
          header: col.header,
          dataKey: col.dataKey,
          width: col.width,
        }));

        // Tambahkan tabel
        autoTable(doc, {
          startY: yPos + 5,
          head: [tableColumns.map((col) => col.header)],
          body: data.map((row) =>
            tableColumns.map((col) => row[col.dataKey] || '')
          ),
          theme: 'grid',
          headStyles: {
            fillColor: [24, 144, 255],
            textColor: 255,
            fontStyle: 'bold',
          },
          styles: {
            overflow: 'linebreak',
            cellWidth: 'auto',
            fontSize: 9,
          },
          columnStyles: tableColumns.reduce((acc, col, index) => {
            if (col.width) {
              acc[index] = { cellWidth: col.width };
            }
            return acc;
          }, {}),
        });

        // Tambahkan nomor halaman
        const pageCount = doc.internal.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
          doc.setPage(i);
          doc.setFontSize(10);
          doc.text(
            `Page ${i} of ${pageCount}`,
            doc.internal.pageSize.width - 30,
            doc.internal.pageSize.height - 10
          );
        }

        // Simpan PDF
        doc.save(`${fileName}.pdf`);
      };

      // 2. Implementasikan tombol export di halaman analytics
      // src/pages/WebAdmin/Analytics/SomeAnalytics/SomeAnalyticsAdmin.tsx
      import { FC, useState } from 'react';
      import { useTranslation } from 'react-i18next';
      import { Button, DatePicker, Select, Table } from 'antd';
      import { DownloadOutlined } from '@ant-design/icons';
      import WebAdminLayout from '../../../../components/Layout/WebAdminLayout';
      import PageHeader from '../../../../components/Admin/Common/PageHeader';
      import { exportToPdf, PdfColumn } from '../../../../utils/exportPdf';

      const SomeAnalyticsAdmin: FC = () => {
        const { t } = useTranslation();
        const [data, setData] = useState<any[]>([]);
        // ... kode lainnya

        const handleExportPdf = () => {
          // Definisikan kolom untuk PDF
          const pdfColumns: PdfColumn[] = [
            { header: t('analytics.someAnalytics.column1'), dataKey: 'field1' },
            { header: t('analytics.someAnalytics.column2'), dataKey: 'field2' },
            { header: t('analytics.someAnalytics.column3'), dataKey: 'field3' },
            // ... kolom lainnya
          ];

          // Export ke PDF
          exportToPdf({
            title: t('analytics.someAnalytics.title'),
            fileName: `analytics-report-${new Date().getTime()}`,
            columns: pdfColumns,
            data: data,
            orientation: 'landscape',
            additionalInfo: {
              'Date Range': `${startDate} - ${endDate}`,
              'Filter': selectedFilter,
            },
          });
        };

        return (
          <WebAdminLayout activePage="analytics.someAnalytics">
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
              <div className="max-w-7xl mx-auto space-y-6">
                <PageHeader
                  title={t('analytics.someAnalytics.title')}
                  description={t('analytics.someAnalytics.description')}
                  actions={[
                    <Button
                      key="export-pdf"
                      type="primary"
                      icon={<DownloadOutlined />}
                      onClick={handleExportPdf}
                    >
                      {t('common.exportPdf')}
                    </Button>
                  ]}
                />

                {/* ... konten lainnya */}
              </div>
            </div>
          </WebAdminLayout>
        );
      };

      export default SomeAnalyticsAdmin;

  # Panduan membuat fitur export spreadsheet (Excel)
  spreadsheetExport:
    steps:
      - "1. Install library exceljs untuk export Excel: npm install exceljs file-saver"
      - "2. Buat utility function untuk export Excel"
      - "3. Implementasikan tombol export di halaman analytics"
      - "4. Tambahkan fungsi handler untuk memproses data dan menghasilkan file Excel"
    example: |
      // 1. Buat utility function di src/utils/exportExcel.ts
      import ExcelJS from 'exceljs';
      import { saveAs } from 'file-saver';
      import dayjs from 'dayjs';

      export interface ExcelColumn {
        header: string;
        key: string;
        width?: number;
        style?: Partial<ExcelJS.Style>;
      }

      export interface ExcelSheetData {
        name: string;
        columns: ExcelColumn[];
        data: Record<string, any>[];
      }

      export interface ExcelExportOptions {
        fileName: string;
        sheets: ExcelSheetData[];
        title?: string;
        author?: string;
        company?: string;
        includeTimestamp?: boolean;
        additionalInfo?: Record<string, string>;
      }

      export const exportToExcel = async (options: ExcelExportOptions) => {
        const {
          fileName,
          sheets,
          title,
          author = 'Uniguard System',
          company = 'Uniguard',
          includeTimestamp = true,
          additionalInfo = {},
        } = options;

        // Buat workbook baru
        const workbook = new ExcelJS.Workbook();
        workbook.creator = author;
        workbook.lastModifiedBy = author;
        workbook.created = new Date();
        workbook.modified = new Date();
        workbook.properties.set('company', company);
        if (title) {
          workbook.properties.set('title', title);
        }

        // Proses setiap sheet
        sheets.forEach((sheetData) => {
          const sheet = workbook.addWorksheet(sheetData.name);

          // Tambahkan judul jika ada
          if (title) {
            const titleRow = sheet.addRow([title]);
            titleRow.font = { bold: true, size: 16 };
            sheet.addRow([]);
          }

          // Tambahkan timestamp jika diperlukan
          if (includeTimestamp) {
            const timestampRow = sheet.addRow([`Generated on: ${dayjs().format('DD MMM YYYY HH:mm:ss')}`);
            timestampRow.font = { italic: true, size: 10 };
            sheet.addRow([]);
          }

          // Tambahkan informasi tambahan
          if (Object.keys(additionalInfo).length > 0) {
            Object.entries(additionalInfo).forEach(([key, value]) => {
              const infoRow = sheet.addRow([`${key}: ${value}`]);
              infoRow.font = { size: 10 };
            });
            sheet.addRow([]);
          }

          // Definisikan kolom
          sheet.columns = sheetData.columns.map((col) => ({
            header: col.header,
            key: col.key,
            width: col.width || 15,
            style: col.style || {},
          }));

          // Tambahkan data
          sheet.addRows(sheetData.data);

          // Style header
          const headerRow = sheet.getRow(sheet.rowCount - sheetData.data.length);
          headerRow.eachCell((cell) => {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: '1890FF' },
            };
            cell.font = {
              bold: true,
              color: { argb: 'FFFFFF' },
            };
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            };
          });

          // Style data cells
          for (let i = 1; i <= sheetData.data.length; i++) {
            const row = sheet.getRow(sheet.rowCount - sheetData.data.length + i);
            row.eachCell((cell) => {
              cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
              };
            });
          }

          // Auto filter
          sheet.autoFilter = {
            from: {
              row: sheet.rowCount - sheetData.data.length,
              column: 1,
            },
            to: {
              row: sheet.rowCount,
              column: sheetData.columns.length,
            },
          };
        });

        // Generate dan simpan file
        const buffer = await workbook.xlsx.writeBuffer();
        const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        saveAs(blob, `${fileName}.xlsx`);
      };

      // 2. Implementasikan tombol export di halaman analytics
      // src/pages/WebAdmin/Analytics/SomeAnalytics/SomeAnalyticsAdmin.tsx
      import { FC, useState } from 'react';
      import { useTranslation } from 'react-i18next';
      import { Button, DatePicker, Select, Table } from 'antd';
      import { DownloadOutlined, FileExcelOutlined } from '@ant-design/icons';
      import WebAdminLayout from '../../../../components/Layout/WebAdminLayout';
      import PageHeader from '../../../../components/Admin/Common/PageHeader';
      import { exportToExcel, ExcelColumn } from '../../../../utils/exportExcel';

      const SomeAnalyticsAdmin: FC = () => {
        const { t } = useTranslation();
        const [data, setData] = useState<any[]>([]);
        // ... kode lainnya

        const handleExportExcel = () => {
          // Definisikan kolom untuk Excel
          const excelColumns: ExcelColumn[] = [
            { header: t('analytics.someAnalytics.column1'), key: 'field1', width: 20 },
            { header: t('analytics.someAnalytics.column2'), key: 'field2', width: 15 },
            { header: t('analytics.someAnalytics.column3'), key: 'field3', width: 25 },
            // ... kolom lainnya
          ];

          // Export ke Excel
          exportToExcel({
            fileName: `analytics-report-${new Date().getTime()}`,
            title: t('analytics.someAnalytics.title'),
            sheets: [
              {
                name: 'Analytics Data',
                columns: excelColumns,
                data: data,
              },
            ],
            additionalInfo: {
              'Date Range': `${startDate} - ${endDate}`,
              'Filter': selectedFilter,
            },
          });
        };

        return (
          <WebAdminLayout activePage="analytics.someAnalytics">
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
              <div className="max-w-7xl mx-auto space-y-6">
                <PageHeader
                  title={t('analytics.someAnalytics.title')}
                  description={t('analytics.someAnalytics.description')}
                  actions={[
                    <Button
                      key="export-excel"
                      type="primary"
                      icon={<FileExcelOutlined />}
                      onClick={handleExportExcel}
                      className="mr-2"
                    >
                      {t('common.exportExcel')}
                    </Button>,
                    <Button
                      key="export-pdf"
                      type="primary"
                      icon={<DownloadOutlined />}
                      onClick={handleExportPdf}
                    >
                      {t('common.exportPdf')}
                    </Button>
                  ]}
                />

                {/* ... konten lainnya */}
              </div>
            </div>
          </WebAdminLayout>
        );
      };

      export default SomeAnalyticsAdmin;

  # Panduan membuat fitur export untuk item detail
  itemDetailExport:
    steps:
      - "1. Gunakan utility function yang sudah dibuat untuk export PDF dan Excel"
      - "2. Implementasikan tombol export di halaman detail item"
      - "3. Format data item detail untuk export"
    example: |
      // src/pages/WebAdmin/Analytics/SomeAnalytics/ItemDetail.tsx
      import { FC, useEffect, useState } from 'react';
      import { useTranslation } from 'react-i18next';
      import { useParams } from 'react-router-dom';
      import { Button, Card, Descriptions, Spin } from 'antd';
      import { DownloadOutlined, FileExcelOutlined } from '@ant-design/icons';
      import WebAdminLayout from '../../../../components/Layout/WebAdminLayout';
      import PageHeader from '../../../../components/Admin/Common/PageHeader';
      import { exportToPdf } from '../../../../utils/exportPdf';
      import { exportToExcel } from '../../../../utils/exportExcel';
      import someAnalyticsApi from '../../../../services/mainApi/admin/someAnalytics.admin.mainApi';

      const ItemDetail: FC = () => {
        const { t } = useTranslation();
        const { id } = useParams<{ id: string }>();
        const [loading, setLoading] = useState(true);
        const [itemData, setItemData] = useState<any>(null);

        useEffect(() => {
          const fetchItemDetail = async () => {
            try {
              setLoading(true);
              const response = await someAnalyticsApi.getItemById(id!);
              setItemData(response.data.data);
            } catch (error) {
              console.error('Failed to fetch item detail:', error);
            } finally {
              setLoading(false);
            }
          };

          fetchItemDetail();
        }, [id]);

        const handleExportPdf = () => {
          if (!itemData) return;

          // Format data untuk PDF
          const pdfData = [
            {
              id: itemData.id,
              name: itemData.name,
              date: itemData.date,
              status: itemData.status,
              // ... properti lainnya
            }
          ];

          // Export ke PDF
          exportToPdf({
            title: `${t('analytics.someAnalytics.itemDetail')} - ${itemData.name}`,
            fileName: `item-detail-${itemData.id}`,
            columns: [
              { header: t('common.id'), dataKey: 'id' },
              { header: t('common.name'), dataKey: 'name' },
              { header: t('common.date'), dataKey: 'date' },
              { header: t('common.status'), dataKey: 'status' },
              // ... kolom lainnya
            ],
            data: pdfData,
          });
        };

        const handleExportExcel = () => {
          if (!itemData) return;

          // Format data untuk Excel
          const excelData = [
            {
              id: itemData.id,
              name: itemData.name,
              date: itemData.date,
              status: itemData.status,
              // ... properti lainnya
            }
          ];

          // Export ke Excel
          exportToExcel({
            fileName: `item-detail-${itemData.id}`,
            title: `${t('analytics.someAnalytics.itemDetail')} - ${itemData.name}`,
            sheets: [
              {
                name: 'Item Detail',
                columns: [
                  { header: t('common.id'), key: 'id' },
                  { header: t('common.name'), key: 'name' },
                  { header: t('common.date'), key: 'date' },
                  { header: t('common.status'), key: 'status' },
                  // ... kolom lainnya
                ],
                data: excelData,
              },
            ],
          });
        };

        if (loading) {
          return (
            <WebAdminLayout activePage="analytics.someAnalytics">
              <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6 flex justify-center items-center">
                <Spin size="large" />
              </div>
            </WebAdminLayout>
          );
        }

        if (!itemData) {
          return (
            <WebAdminLayout activePage="analytics.someAnalytics">
              <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
                <div className="max-w-7xl mx-auto">
                  <Card>
                    <p>{t('common.itemNotFound')}</p>
                  </Card>
                </div>
              </div>
            </WebAdminLayout>
          );
        }

        return (
          <WebAdminLayout activePage="analytics.someAnalytics">
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
              <div className="max-w-7xl mx-auto space-y-6">
                <PageHeader
                  title={`${t('analytics.someAnalytics.itemDetail')} - ${itemData.name}`}
                  description={t('analytics.someAnalytics.itemDetailDescription')}
                  actions={[
                    <Button
                      key="export-excel"
                      type="primary"
                      icon={<FileExcelOutlined />}
                      onClick={handleExportExcel}
                      className="mr-2"
                    >
                      {t('common.exportExcel')}
                    </Button>,
                    <Button
                      key="export-pdf"
                      type="primary"
                      icon={<DownloadOutlined />}
                      onClick={handleExportPdf}
                    >
                      {t('common.exportPdf')}
                    </Button>
                  ]}
                />

                <Card>
                  <Descriptions title={t('common.details')} bordered column={{ xs: 1, sm: 2, md: 3 }}>
                    <Descriptions.Item label={t('common.id')}>{itemData.id}</Descriptions.Item>
                    <Descriptions.Item label={t('common.name')}>{itemData.name}</Descriptions.Item>
                    <Descriptions.Item label={t('common.date')}>{itemData.date}</Descriptions.Item>
                    <Descriptions.Item label={t('common.status')}>{itemData.status}</Descriptions.Item>
                    {/* ... item lainnya */}
                  </Descriptions>
                </Card>

                {/* ... konten detail lainnya */}
              </div>
            </div>
          </WebAdminLayout>
        );
      };

      export default ItemDetail;

# Performance Guidelines
performance:
  rules:
    - "Use React.memo for expensive components"
    - "Implement virtualization for long lists"
    - "Lazy load routes and heavy components"
    - "Optimize images and assets"
    - "Use proper key props in lists"
    - "Minimize component re-renders"
    - "Use useMemo and useCallback for optimizing performance"
    - "Prefer server-side pagination for large datasets"

# Page Creation Guidelines
pageCreation:
  webAdmin:
    steps:
      - "1. Create page component in src/pages/WebAdmin/<ModuleName>/<PageName>.tsx"
      - "2. Add route in src/config/routes.tsx under admin section"
      - "3. Create slice in src/store/slices/admin/<moduleName>.admin.slice.ts"
      - "4. Create API service in src/services/mainApi/admin/<moduleName>.admin.mainApi.ts"
      - "5. Add types in src/services/mainApi/admin/types/<moduleName>.admin.mainApi.types.ts"
      - "6. Add translations in src/locales/<lang>/<moduleName>.json"
    template:
      component: |
        import { FC } from 'react';
        import { useTranslation } from 'react-i18next';
        import { WebAdminLayout } from '../../../components/Layout';
        import { PageHeader } from '../../../components/Common';
        import { Button } from 'antd';

        const NewAdminPage: FC = () => {
          const { t } = useTranslation();

          return (
            <WebAdminLayout activePage="newModule">
              <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
                <div className="max-w-7xl mx-auto space-y-6">
                  <PageHeader
                    title={t('newModule.title')}
                    description={t('newModule.description')}
                    actions={[
                      <Button key="action" type="primary">
                        {t('newModule.action')}
                      </Button>
                    ]}
                  />
                  {/* Page content */}
                </div>
              </div>
            </WebAdminLayout>
          );
        };

        export default NewAdminPage;

      slice: |
        import { createSlice, PayloadAction } from '@reduxjs/toolkit';

        interface State {
          loading: boolean;
          error: string | null;
          data: any[];
        }

        const initialState: State = {
          loading: false,
          error: null,
          data: [],
        };

        export const newModuleSlice = createSlice({
          name: 'newModule',
          initialState,
          reducers: {
            setLoading: (state, action: PayloadAction<boolean>) => {
              state.loading = action.payload;
            },
          },
        });

      api: |
        import { mainApiInstance } from '../mainApiInstance';
        import { NewModuleResponse } from './types/newModule.webAdmin.mainApi.types';

        const newModuleApi = {
          getAll: () =>
            mainApiInstance.get<NewModuleResponse>('/admin/new-module'),
          create: (data: any) =>
            mainApiInstance.post('/admin/new-module', data),
        };

        export default newModuleApi;

      types: |
        export interface NewModuleResponse {
          data: NewModule[];
          meta: {
            total: number;
            page: number;
            limit: number;
          };
        }

        export interface NewModule {
          id: string;
          name: string;
          created_at: string;
          updated_at: string;
        }

  sysAdmin:
    steps:
      - "1. Create page component in src/pages/SysAdmin/<ModuleName>/<PageName>.tsx"
      - "2. Add route in src/config/routes.tsx under sysAdmin section"
      - "3. Create slice in src/store/slices/sysadmin/<moduleName>.sysadmin.slice.ts"
      - "4. Create API service in src/services/mainApi/sysadmin/<moduleName>.sysadmin.mainApi.ts"
      - "5. Add types in src/services/mainApi/sysadmin/types/<moduleName>.sysadmin.mainApi.types.ts"
      - "6. Add translations in src/locales/<lang>/<moduleName>.json"
    template:
      component: |
        import { FC } from 'react';
        import { useTranslation } from 'react-i18next';
        import { SysAdminLayout } from '../../../components/Layout';
        import { PageHeader } from '../../../components/Common';
        import { Button } from 'antd';

        const NewSysAdminPage: FC = () => {
          const { t } = useTranslation();

          return (
            <SysAdminLayout activePage="newModule">
              <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
                <div className="max-w-7xl mx-auto space-y-6">
                  <PageHeader
                    title={t('newModule.title')}
                    description={t('newModule.description')}
                    actions={[
                      <Button key="action" type="primary">
                        {t('newModule.action')}
                      </Button>
                    ]}
                  />
                  {/* Page content */}
                </div>
              </div>
            </SysAdminLayout>
          );
        };

        export default NewSysAdminPage;

# Module-Specific Implementation Guidelines

# Beacon Management Implementation
beaconManagement:
  components:
    path: "src/pages/WebAdmin/Beacon/Components/"
    required:
      - "BeaconList"
      - "BeaconForm"
      - "BeaconDetails"
  api:
    path: "src/services/mainApi/admin/beacon.admin.mainApi.ts"
  slice:
    path: "src/store/slices/admin/beacon.admin.slice.ts"

# Geofence Management Implementation
geofenceManagement:
  components:
    path: "src/pages/WebAdmin/Geofence/Components/"
    required:
      - "GeofenceList"
      - "GeofenceForm"
      - "GeofenceMap"
  api:
    path: "src/services/mainApi/admin/geofence.admin.mainApi.ts"
  slice:
    path: "src/store/slices/admin/geofence.admin.slice.ts"

# Zone Management Implementation
zoneManagement:
  components:
    path: "src/pages/WebAdmin/Zone/Components/"
    required:
      - "ZoneList"
      - "ZoneForm"
      - "ZoneDetails"
  api:
    path: "src/services/mainApi/admin/zone.admin.mainApi.ts"
  slice:
    path: "src/store/slices/admin/zone.admin.slice.ts"

# Checkpoint Management Implementation
checkpointManagement:
  components:
    path: "src/pages/WebAdmin/Checkpoint/Components/"
    required:
      - "CheckpointList"
      - "ModalAddCheckpoint"
      - "CheckpointMap"
  api:
    path: "src/services/mainApi/admin/checkpoint.admin.mainApi.ts"
  slice:
    path: "src/store/slices/admin/checkpoint.admin.slice.ts"

# Branch Management Implementation
branchManagement:
  components:
    path: "src/pages/SysAdmin/Branch/Components/"
    required:
      - "BranchList"
      - "AddEditBranchModal"
      - "DeleteBranchModal"
  api:
    path: "src/services/mainApi/sysadmin/branch.sysadmin.mainApi.ts"
  slice:
    path: "src/store/slices/sysadmin/branchList.sysadmin.slice.ts"

# License Management Implementation
licenseManagement:
  components:
    path: "src/pages/SysAdmin/License/Components/"
    required:
      - "LicenseList"
      - "AddEditLicenseModal"
      - "DeleteLicenseModal"
  api:
    path: "src/services/mainApi/license.mainApi.ts"
  slice:
    path: "src/store/slices/admin/license.admin.slice.ts"

# Label Management Implementation
labelManagement:
  components:
    path: "src/pages/WebAdmin/Label/Components/"
    required:
      - "LabelGrid"
      - "ModalAddLabel"
  api:
    path: "src/services/mainApi/admin/label.admin.mainApi.ts"
  slice:
    path: "src/store/slices/admin/label.admin.slice.ts"

# Analytics Implementation
analytics:
  components:
    path: "src/pages/WebAdmin/Analytics/"
    modules:
      - "ActivityLog"
      - "Alarms"
      - "AverageRotation"
      - "CheckpointActivity"
      - "FormLog"
      - "GeofenceLog"
      - "SignInOutLog"
      - "TaskLog"
  api:
    path: "src/services/mainApi/admin/"
    examples:
      - "activityLog.admin.mainApi.ts"
      - "alarmLog.admin.mainApi.ts"
      - "formLog.admin.mainApi.ts"
  slice:
    path: "src/store/slices/admin/"
    examples:
      - "activityLog.admin.slice.ts"
      - "alarmLog.admin.slice.ts"
      - "formLog.admin.slice.ts"

# Form Management Implementation
formManagement:
  components:
    path: "src/pages/WebAdmin/Forms/Components/"
    required:
      - "FormList"
      - "ModalAddForm"
  api:
    path: "src/services/mainApi/admin/form.admin.mainApi.ts"
  slice:
    path: "src/store/slices/admin/form.admin.slice.ts"

# Form Picklist Management Implementation
formPicklistManagement:
  components:
    path: "src/pages/WebAdmin/FormPicklist/Components/"
    required:
      - "PicklistList"
      - "ModalAddPicklist"
  api:
    path: "src/services/mainApi/admin/formPicklist.admin.mainApi.ts"
  slice:
    path: "src/store/slices/admin/formPicklist.admin.slice.ts"

# Site Management Implementation
siteManagement:
  components:
    path: "src/pages/WebAdmin/Site/Components/"
    required:
      - "SiteList"
      - "AddSiteModal"
  api:
    path: "src/services/mainApi/admin/site.admin.mainApi.ts"
  slice:
    path: "src/store/slices/admin/site.admin.slice.ts"

# Task Management Implementation
taskManagement:
  components:
    path: "src/pages/WebAdmin/Task/Components/"
    required:
      - "AdminTaskList"
      - "ModalAddTask"
  api:
    path: "src/services/mainApi/admin/task.admin.mainApi.ts"
  slice:
    path: "src/store/slices/admin/task.admin.slice.ts"

# User Management Implementation
userManagement:
  components:
    path: "src/pages/WebAdmin/User/Components/"
    required:
      - "UsersList"
      - "ModalAddUser"
  api:
    path: "src/services/mainApi/admin/user.admin.mainApi.ts"
  slice:
    path: "src/store/slices/admin/user.admin.slice.ts"

# Role Management Implementation
roleManagement:
  components:
    path: "src/pages/WebAdmin/Roles/Components/"
    required:
      - "RolesList"
      - "ModalAddRole"
  api:
    path: "src/services/mainApi/admin/roles.admin.mainApi.ts"
  slice:
    path: "src/store/slices/admin/roles.admin.slice.ts"
