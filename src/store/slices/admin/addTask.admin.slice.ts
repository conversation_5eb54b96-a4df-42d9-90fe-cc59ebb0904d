import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { AdminTask } from "../../../services/mainApi/admin/types/task.admin.mainApi.types";
import { FieldType } from "../../../types/FieldType.enum";
import { v4 as uuidv4 } from "uuid";
import taskAdminMainApi from "../../../services/mainApi/admin/task.admin.mainApi";
import { RootState } from "../..";
import { handleApiError } from "../../../utils/error";
import { isAxiosError } from "axios";

interface State {
  open: boolean;
  mode: "create" | "update";
  loading: boolean;
  submitting: boolean;
  errorMessage: string[] | null;
  selectedTask: AdminTask | null;

  task_name: string;
  task_description: string;
  task_type: "SCHEDULED" | "REPEATING";
  start_time: string;
  end_time: string;
  allowed_time: number;
  fields: {
    rowId: string;
    field_type_id: FieldType;
    task_field_name: string;
    task_field_description: string | null;
    active: boolean;
  }[];
  role_id: string | null;
  user_id: string | null;
  checkpoint_id: string | null;
}

const initialState: State = {
  open: false,
  mode: "create",
  loading: false,
  submitting: false,
  errorMessage: null,
  selectedTask: null,

  task_name: "",
  task_description: "",
  task_type: "REPEATING",
  start_time: "",
  end_time: "",
  allowed_time: 0,
  fields: [
    {
      rowId: uuidv4(),
      field_type_id: FieldType.INPUT,
      task_field_name: "",
      task_field_description: "",
      active: true,
    },
  ],
  role_id: null,
  user_id: "",
  checkpoint_id: null,
};

export const updateTaskAdmin = createAsyncThunk(
  "addTaskAdmin/updateTask",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    const state = getState() as RootState;
    const addTaskState = state.addTaskAdmin;

    if (!addTaskState.selectedTask) {
      return rejectWithValue([
        "No task selected for update",
      ]);
    }

    try {
      const response = await taskAdminMainApi.updateTask(
        branchCode,
        addTaskState.selectedTask.id,
        {
          task_name: addTaskState.task_name,
          task_description: addTaskState.task_description,
          task_type: addTaskState.task_type,
          start_date_time: addTaskState.start_time,
          end_date_time: addTaskState.end_time,
          allowed_time: addTaskState.allowed_time,
          fields: addTaskState.fields.map((field) => ({
            field_type_id: field.field_type_id,
            task_field_name: field.task_field_name,
            task_field_description:
              field.task_field_description,
            active: field.active,
          })),
          role_id: addTaskState.role_id,
          user_id: addTaskState.user_id,
          checkpoint_id: addTaskState.checkpoint_id,
          active: true,
        }
      );
      return response.data;
    } catch (e) {
      if (isAxiosError(e)) {
        const { errors } = handleApiError(e);
        return rejectWithValue(errors);
      }
      return rejectWithValue(["Failed to update task"]);
    }
  }
);

export const createTaskAdmin = createAsyncThunk(
  "addTaskAdmin/createTask",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    const state = getState() as RootState;
    const addTaskState = state.addTaskAdmin;

    try {
      const response = await taskAdminMainApi.createTask(
        branchCode,
        {
          task_name: addTaskState.task_name,
          task_description: addTaskState.task_description,
          task_type: addTaskState.task_type,
          start_date_time: addTaskState.start_time,
          end_date_time: addTaskState.end_time,
          allowed_time: addTaskState.allowed_time,
          fields: addTaskState.fields.map((field) => ({
            field_type_id: field.field_type_id,
            task_field_name: field.task_field_name,
            task_field_description:
              field.task_field_description,
            active: field.active,
          })),
          role_id: addTaskState.role_id,
          user_id: addTaskState.user_id,
          checkpoint_id: addTaskState.checkpoint_id,
        }
      );
      return response.data;
    } catch (e) {
      if (isAxiosError(e)) {
        const { errors } = handleApiError(e);
        return rejectWithValue(errors);
      }
      return rejectWithValue(["Failed to create task"]);
    }
  }
);

const addTaskAdminSlice = createSlice({
  name: "addTaskAdmin",
  initialState: {
    ...initialState,
  },
  reducers: {
    open: (state) => {
      state.open = true;
      state.mode = "create";
      state.role_id = null;
      state.user_id = "";
      state.checkpoint_id = null;
    },
    openUpdate: (
      state,
      action: PayloadAction<AdminTask>
    ) => {
      state.open = true;
      state.mode = "update";
      state.selectedTask = action.payload;

      state.task_name = action.payload.task_name;
      state.task_description =
        action.payload.task_description || "";
      state.task_type = action.payload.task_type;
      state.start_time = action.payload.start_time;
      state.end_time = action.payload.end_time;
      state.allowed_time = action.payload.allowed_time;
      state.role_id = action.payload.role_id;
      state.user_id = action.payload.user_id;
      state.checkpoint_id = action.payload.check_point_id;

      state.fields = action.payload.fields.map((field) => ({
        rowId: uuidv4(),
        field_type_id: field.field_type_id,
        task_field_name: field.task_field_name,
        task_field_description:
          field.task_field_description || "",
        active: field.active,
      }));
    },
    close: () => {
      return {
        ...initialState,
      };
    },
    setError: (
      state,
      action: PayloadAction<string[] | null>
    ) => {
      state.errorMessage = action.payload;
    },
    setSubmitting: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.submitting = action.payload;
    },

    setTaskName: (state, action: PayloadAction<string>) => {
      state.task_name = action.payload;
    },
    setTaskDescription: (
      state,
      action: PayloadAction<string>
    ) => {
      state.task_description = action.payload;
    },
    setTaskType: (
      state,
      action: PayloadAction<"SCHEDULED" | "REPEATING">
    ) => {
      state.task_type = action.payload;
    },
    setStartTime: (
      state,
      action: PayloadAction<string>
    ) => {
      state.start_time = action.payload;
    },
    setEndTime: (state, action: PayloadAction<string>) => {
      state.end_time = action.payload;
    },

    setAllowedTime: (
      state,
      action: PayloadAction<number>
    ) => {
      state.allowed_time = action.payload;
    },
    setRoleId: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.role_id = action.payload;
      state.user_id = null;
    },
    setUserId: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.user_id = action.payload;
    },
    setCheckpointId: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.checkpoint_id = action.payload;
    },

    addField: (state) => {
      const id = uuidv4();
      state.fields.push({
        rowId: id,
        field_type_id: FieldType.INPUT,
        task_field_name: "",
        task_field_description: "",
        active: true,
      });
    },
    removeField: (state, action: PayloadAction<string>) => {
      state.fields = state.fields.filter(
        (field) => field.rowId !== action.payload
      );
    },

    setFieldType: (
      state,
      action: PayloadAction<{
        rowId: string;
        field_type_id: FieldType;
      }>
    ) => {
      const { rowId, field_type_id } = action.payload;
      const field = state.fields.find(
        (field) => field.rowId === rowId
      );
      if (field) {
        field.field_type_id = field_type_id;
      }
    },
    setFieldName: (
      state,
      action: PayloadAction<{
        rowId: string;
        task_field_name: string;
      }>
    ) => {
      const { rowId, task_field_name } = action.payload;
      const field = state.fields.find(
        (field) => field.rowId === rowId
      );
      if (field) {
        field.task_field_name = task_field_name;
      }
    },
    setFieldDescription: (
      state,
      action: PayloadAction<{
        rowId: string;
        task_field_description: string;
      }>
    ) => {
      const { rowId, task_field_description } =
        action.payload;
      const field = state.fields.find(
        (field) => field.rowId === rowId
      );
      if (field) {
        field.task_field_description =
          task_field_description;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(updateTaskAdmin.pending, (state) => {
        state.submitting = true;
        state.errorMessage = null;
      })
      .addCase(updateTaskAdmin.fulfilled, (state) => {
        state.submitting = false;
      })
      .addCase(
        updateTaskAdmin.rejected,
        (state, action) => {
          state.submitting = false;
          state.errorMessage = action.payload as string[];
        }
      );

    builder
      .addCase(createTaskAdmin.pending, (state) => {
        state.submitting = true;
        state.errorMessage = null;
      })
      .addCase(createTaskAdmin.fulfilled, (state) => {
        state.submitting = false;
      })
      .addCase(
        createTaskAdmin.rejected,
        (state, action) => {
          state.submitting = false;
          state.errorMessage = action.payload as string[];
        }
      );
  },
});

export default addTaskAdminSlice;
