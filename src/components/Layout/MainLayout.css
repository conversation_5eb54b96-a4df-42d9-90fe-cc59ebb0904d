/* Base Layout Components */
.sidebar,
.main-header {
  position: fixed;
  top: 0;
  height: 100vh;
  transition: all 0.3s;
  background-color: #fff;
  border-right: 1px solid #f0f0f0;
}

.main-header {
  right: 0;
  height: 64px;
  z-index: 999;
  border-right: none;
  border-bottom: 1px solid #f0f0f0;
}

/* Sidebar Components */
.sidebar {
  left: 0;
  z-index: 1000;
}

.sidebar-expanded {
  width: 260px;
}
.sidebar-collapsed {
  width: 80px;
}

.sidebar-menu-container {
  height: calc(100vh - 64px - 57px);
  overflow-y: auto;
  padding: 8px;
}

/* Scrollbar Styling */
.sidebar-menu-container::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu-container::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-menu-container::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
}

/* Header Components */
.main-header-expanded {
  left: 260px;
}
.main-header-collapsed {
  left: 80px;
}

.logo-section {
  height: 64px;
  padding: 0 24px;
  border-bottom: 1px solid #f0f0f0;
}

/* Main Content */
.main-content {
  position: relative;
  transition: all 0.3s;
  min-height: 100vh;
  padding-top: 64px;
  background-color: #f0f2f5;
  z-index: 1;
}

.main-content-expanded {
  margin-left: 260px;
}
.main-content-collapsed {
  margin-left: 80px;
}

.main-content-inner {
  padding: 24px;
  min-height: calc(100vh - 64px);
}

/* Menu Styling */
.ant-menu-inline .ant-menu-item,
.ant-menu-inline .ant-menu-submenu-title {
  margin: 4px 0 !important;
  height: 40px !important;
  line-height: 40px !important;
  border-radius: 8px;
  color: #374151 !important;
}

.ant-menu-item .ant-menu-item-icon,
.ant-menu-submenu-title .ant-menu-item-icon {
  min-width: 20px;
  color: currentColor !important;
}

/* Menu States */
.ant-menu-item:not(.ant-menu-item-selected):hover,
.ant-menu-submenu-title:hover {
  background-color: rgb(243 244 246) !important;
  color: #1f2937 !important;
}

.ant-menu-item.ant-menu-item-selected {
  background-color: #428cf8 !important;
  color: #ffffff !important;
}

/* Submenu Styling */
.ant-menu-submenu.ant-menu-submenu-open
  > .ant-menu-submenu-title {
  background-color: rgb(243 244 246) !important;
  color: #428cf8 !important;
}

.ant-menu-submenu.ant-menu-submenu-open
  > .ant-menu-submenu-title
  .ant-menu-item-icon {
  color: #428cf8 !important;
}

.ant-menu-submenu-arrow {
  color: currentColor !important;
}

.ant-menu-submenu-open
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow {
  transform: rotate(180deg) !important;
}

.ant-menu-sub.ant-menu-inline {
  background: transparent !important;
}

.ant-menu-sub.ant-menu-inline .ant-menu-item {
  padding-left: 48px !important;
}

/* Selected Item Hover Prevention */
[id^="rc-menu-uuid-"]
  > li.ant-menu-item.ant-menu-item-selected:hover,
.ant-menu-submenu-popup
  .ant-menu-item.ant-menu-item-selected:hover,
.ant-menu-item.ant-menu-item-selected:hover {
  background-color: #428cf8 !important;
  color: #ffffff !important;
}

[id^="rc-menu-uuid-"]
  > li.ant-menu-item.ant-menu-item-selected:hover
  .ant-menu-item-icon,
.ant-menu-submenu-popup
  .ant-menu-item.ant-menu-item-selected:hover
  .ant-menu-item-icon {
  color: #ffffff !important;
}

/* Dark Mode */
.dark {
  .sidebar,
  .main-header {
    background-color: #1f2937;
    border-color: #374151;
  }

  .logo-section,
  .dark-mode-switch {
    border-color: #374151;
  }

  .sidebar-menu-container::-webkit-scrollbar-thumb {
    background: #475569;
  }

  .ant-menu-item,
  .ant-menu-submenu-title {
    color: #e5e7eb !important;
  }

  .ant-menu-item:not(.ant-menu-item-selected):hover,
  .ant-menu-submenu-title:hover,
  .ant-menu-sub.ant-menu-inline .ant-menu-item:hover {
    background-color: rgb(55 65 81) !important;
    color: #428cf8 !important;
  }

  .ant-menu-submenu.ant-menu-submenu-open
    > .ant-menu-submenu-title {
    background-color: rgb(55 65 81) !important;
    color: #428cf8 !important;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .sidebar-expanded {
    transform: translateX(0);
  }

  .main-header,
  .main-content {
    left: 0 !important;
    margin-left: 0 !important;
    width: 100% !important;
  }

  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: block;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }

  .sidebar-expanded + .sidebar-overlay {
    opacity: 1;
  }
}

@media (min-width: 769px) {
  .sidebar-overlay {
    display: none !important;
  }
}
