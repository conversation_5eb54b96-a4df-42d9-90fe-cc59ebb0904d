import { <PERSON><PERSON>, <PERSON>, Tooltip } from "antd";
import { FiTrash2, Fi<PERSON>lock } from "react-icons/fi";
import { EditOutlined } from "@ant-design/icons";
import { AdminScheduler } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import dayjs from "dayjs";
import { useAppDispatch } from "../../../../store/hooks";
import {
  MdSchedule,
  MdWarning,
  MdAccessTime,
  MdLabel,
} from "react-icons/md";
import React from "react";
import { deleteScheduler } from "../../../../store/slices/admin/scheduler.admin.slice";
import { useParams } from "react-router-dom";
import relativeTime from "dayjs/plugin/relativeTime";
import addSchedulerAdminSlice from "../../../../store/slices/admin/addScheduler.admin.slice";
import { useTranslation } from "react-i18next";
import { AdminLabelDetail } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types.ts";

dayjs.extend(relativeTime);
interface SchedulerListItemProps {
  scheduler: AdminScheduler;
}

const SchedulerListItem: React.FC<
  SchedulerListItemProps
> = ({ scheduler }) => {
  const dispatch = useAppDispatch();
  const { branchCode } = useParams();
  const { openEdit } = addSchedulerAdminSlice.actions;
  const { t } = useTranslation();

  const handleEdit = () => {
    dispatch(openEdit(scheduler));
  };

  const handleDelete = async () => {
    if (branchCode) {
      await dispatch(
        deleteScheduler({
          branchCode,
          schedulerId: scheduler.id,
        })
      );
    }
  };

  // Helper function to render labels
  const renderLabels = (labels: Omit<AdminLabelDetail, "branch">[], labelType: string) => {
    if (!labels || labels.length === 0) return null;
    
    return (
      <div className="mt-2">
        <div className="text-xs text-gray-600 dark:text-gray-300 mb-1 flex items-center gap-1">
          <MdLabel className="text-gray-400" />
          <span>{t(`scheduler.item.${labelType}Labels`)}:</span>
        </div>
        <div className="flex flex-wrap gap-1">
          {labels.map((label) => (
            <Tag 
              key={label.id} 
              className="text-xs bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
            >
              {label.label_name}
            </Tag>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border ${
        scheduler.active
          ? "border-gray-100 dark:border-gray-700"
          : "border-red-100 dark:border-red-900 bg-red-50/10"
      } overflow-hidden`}
    >
      <div className="p-4">
        {/* Header: Name, Description, Status */}
        <div className="flex items-start justify-between mb-3 pb-3 border-b dark:border-gray-700">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">
                {scheduler.scheduler_name}
              </h3>
              {!scheduler.active && (
                <Tag
                  icon={<MdWarning className="mr-1" />}
                  className="bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 border-red-200 dark:border-red-800"
                >
                  {t("scheduler.item.inactive")}
                </Tag>
              )}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {scheduler.scheduler_description ||
                t("scheduler.item.noDescription")}
            </div>
          </div>
          <div className="flex gap-1">
            <Tooltip title={t("scheduler.item.edit")}>
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={handleEdit}
                className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
              />
            </Tooltip>
            <Tooltip title={t("scheduler.item.delete")}>
              <Button
                type="text"
                icon={<FiTrash2 className="text-lg" />}
                onClick={handleDelete}
                className="text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400"
              />
            </Tooltip>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            {/* Report Type Information */}
            <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800/80 rounded">
              <h4 className="text-xs uppercase text-gray-500 dark:text-gray-400 mb-2 font-medium">
                {t("scheduler.item.reportDetails")}
              </h4>
              <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs">
                <div className="flex items-center gap-1">
                  <span className="text-gray-600 dark:text-gray-300">
                    {t("scheduler.item.type")}:
                  </span>
                  <span className="font-medium">
                    {scheduler.report_type.report_type_name}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-gray-600 dark:text-gray-300">
                    {t("scheduler.item.format")}:
                  </span>
                  <Tag className="m-0 text-xs bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400">
                    {scheduler.report_format_type}
                  </Tag>
                </div>
              </div>
            </div>

            {/* Frequency Information */}
            <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800/80 rounded">
              <h4 className="text-xs uppercase text-gray-500 dark:text-gray-400 mb-2 font-medium">
                {t("scheduler.item.scheduleSettings")}
              </h4>
              <div className="grid gap-y-1 text-xs">
                <div className="flex items-center gap-1">
                  <MdSchedule className="text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-300">
                    {t("scheduler.item.frequency")}:
                  </span>
                  <span className="font-medium">
                    {scheduler.frequency.frequency_name}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <MdAccessTime className="text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-300">
                    {t("scheduler.item.time")}:
                  </span>
                  <span>
                    {scheduler.start_time} -{" "}
                    {scheduler.end_time}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div>
            {/* User Information */}
            <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800/80 rounded">
              <h4 className="text-xs uppercase text-gray-500 dark:text-gray-400 mb-2 font-medium">
                {t("scheduler.item.assignment")}
              </h4>
              <div className="grid gap-y-1 text-xs">
                <div className="flex items-center gap-1">
                  <span className="text-gray-600 dark:text-gray-300">
                    {t("scheduler.item.user")}:
                  </span>
                  <span className="font-medium">
                    {scheduler.user?.name || t("scheduler.item.noUser")}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-gray-600 dark:text-gray-300">
                    {t("scheduler.item.branch")}:
                  </span>
                  <span>
                    {scheduler.branch?.branch_name || t("scheduler.item.noBranch")}
                  </span>
                </div>
                {renderLabels(scheduler.user_labels, "user")}
              </div>
            </div>

            {/* Next Run Information */}
            <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800/80 rounded">
              <h4 className="text-xs uppercase text-gray-500 dark:text-gray-400 mb-2 font-medium">
                {t("scheduler.item.executionInfo")}
              </h4>
              <div className="grid gap-y-1 text-xs">
                {scheduler.next_run_time ? (
                  <div className="flex items-center gap-1">
                    <FiClock className="text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-300">
                      {t("scheduler.item.nextRun")}:
                    </span>
                    <Tooltip
                      title={dayjs(
                        scheduler.next_run_time
                      ).format("DD MMM YYYY HH:mm")}
                    >
                      <span>
                        {dayjs(
                          scheduler.next_run_time
                        ).fromNow()}
                      </span>
                    </Tooltip>
                  </div>
                ) : (
                  <div className="flex items-center gap-1 text-amber-500 dark:text-amber-400">
                    <MdWarning />
                    <span>
                      {t("scheduler.item.noScheduledRun")}
                    </span>
                  </div>
                )}
                <div className="flex items-center gap-1">
                  <span className="text-gray-600 dark:text-gray-300">
                    {t("scheduler.item.generatedOn")}:
                  </span>
                  <span>
                    {scheduler.generate_date} at{" "}
                    {scheduler.generate_time}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Labels Section */}
        {(scheduler.device_labels.length > 0 || 
          scheduler.zone_labels.length > 0 || 
          scheduler.checkpoint_labels.length > 0) && (
          <div className="mt-3 p-2 bg-gray-50 dark:bg-gray-800/80 rounded">
            <h4 className="text-xs uppercase text-gray-500 dark:text-gray-400 mb-2 font-medium">
              {t("scheduler.item.additionalFilters")}
            </h4>
            <div className="grid gap-y-2 text-xs">
              {renderLabels(scheduler.device_labels, "device")}
              {renderLabels(scheduler.zone_labels, "zone")}
              {renderLabels(scheduler.checkpoint_labels, "checkpoint")}
            </div>
          </div>
        )}

        {/* Footer with timestamps */}
        <div className="flex flex-wrap items-center gap-3 text-xs text-gray-500 dark:text-gray-400 pt-2 mt-2 border-t dark:border-gray-700">
          {scheduler.created_at && (
            <Tooltip
              title={dayjs(scheduler.created_at).format(
                "DD MMM YYYY HH:mm"
              )}
            >
              <div className="flex items-center gap-1">
                <FiClock className="text-xs" />
                <span>
                  {t("scheduler.item.created")}:{" "}
                  {dayjs(scheduler.created_at).fromNow()}
                </span>
              </div>
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
};

export default SchedulerListItem;
