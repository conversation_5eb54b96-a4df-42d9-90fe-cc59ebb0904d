import React, { useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  useMap,
} from "react-leaflet";
import "leaflet/dist/leaflet.css";
import L from "leaflet";
import markerIcon2x from "leaflet/dist/images/marker-icon-2x.png";
import markerIcon from "leaflet/dist/images/marker-icon.png";
import markerShadow from "leaflet/dist/images/marker-shadow.png";

// Fix for default marker icons in react-leaflet
delete (
  L.Icon.Default.prototype as { _getIconUrl?: () => string }
)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: markerIcon2x,
  iconUrl: markerIcon,
  shadowUrl: markerShadow,
});

// Component to handle map invalidation after modal animation
const MapResizer = () => {
  const map = useMap();

  useEffect(() => {
    // Delay map invalidation to ensure modal is fully rendered
    const timer = setTimeout(() => {
      map.invalidateSize();
    }, 300);

    return () => clearTimeout(timer);
  }, [map]);

  return null;
};

interface AlarmLocationMapProps {
  latitude: number;
  longitude: number;
  popupText?: string;
}

const AlarmLocationMap: React.FC<AlarmLocationMapProps> = ({
  latitude,
  longitude,
  popupText = "Location",
}) => {
  return (
    <MapContainer
      center={[latitude, longitude]}
      zoom={13}
      style={{
        height: "400px",
        width: "100%",
        borderRadius: "0.5rem",
      }}
    >
      <MapResizer />
      <TileLayer
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />
      <Marker position={[latitude, longitude]}>
        <Popup>{popupText}</Popup>
      </Marker>
    </MapContainer>
  );
};

export default AlarmLocationMap;
