import React from "react";
import {Empty, Pagination, Spin} from "antd";
import {useTranslation} from "react-i18next";
import {AdminBranchDetailsLogBranchDetail} from "../../../../../services/mainApi/admin/types/branchDetailsLog.admin.mainApi.types";
import BranchDetailsLogItem from "./BranchDetailsLogItem";

interface BranchDetailsLogListProps {
  branchDetailsLogs: AdminBranchDetailsLogBranchDetail[];
  loading: boolean;
  total: number;
  page: number;
  limit?: number;
  onPageChange?: (page: number, pageSize?: number) => void;
  /** Number of items per row in the grid layout (default is 3) */
  itemsPerRow?: number;
}

const BranchDetailsLogList: React.FC<BranchDetailsLogListProps> = ({
  branchDetailsLogs,
  loading,
  total,
  page,
  limit,
  onPageChange,
  itemsPerRow = 3, // Default to 3 items per row
}) => {
  const {t} = useTranslation();
  
  // Determine grid class based on itemsPerRow
  const getGridClass = () => {
    // Base grid class with responsive breakpoints
    const baseClass = "grid grid-cols-1 sm:grid-cols-2 gap-4 ";
    
    // Add specific classes based on itemsPerRow for large screens
    if (itemsPerRow === 2) {
      return baseClass + "lg:grid-cols-2";
    } else if (itemsPerRow === 4) {
      return baseClass + "lg:grid-cols-4";
    } else if (itemsPerRow === 5) {
      return baseClass + "lg:grid-cols-5";
    } else if (itemsPerRow === 6) {
      return baseClass + "lg:grid-cols-6";
    } else {
      // Default to 3 columns
      return baseClass + "lg:grid-cols-3";
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-16 bg-white dark:bg-gray-800 rounded-xl shadow-lg border-0 p-8 animate-pulse">
        <div className="flex flex-col items-center">
          <Spin size="large" />
          <p className="mt-4 text-gray-500 dark:text-gray-400">
            {t("common.loading", "Loading data...")}
          </p>
        </div>
      </div>
    );
  }

  if (branchDetailsLogs.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border-0 p-12 transition-all duration-300">
        <Empty
          description={
            <span className="text-gray-500 dark:text-gray-400 text-base">
              {t(
                "branchDetailsLog.noData",
                "No branch details logs found"
              )}
            </span>
          }
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          className="opacity-80"
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Grid layout for branch details log items */}
      <div className={getGridClass()}>
        {branchDetailsLogs.map((log) => (
          <BranchDetailsLogItem
            key={log.branch_id}
            branchDetailsLog={log}
          />
        ))}
      </div>

      {/* Pagination with modern styling */}
      {total > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border-0 p-4 my-6 transition-all duration-300">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="text-sm text-gray-500 dark:text-gray-400 font-medium">
              {t("pagination.total", "Total {{total}} items", {
                total,
              })}
            </div>
            <Pagination
              current={page}
              pageSize={limit}
              total={total}
              onChange={onPageChange}
              showSizeChanger
              pageSizeOptions={["10", "20", "50", "100"]}
              className="dark:text-gray-300"
              showTotal={() => null} // We're showing the total separately above
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default BranchDetailsLogList;
