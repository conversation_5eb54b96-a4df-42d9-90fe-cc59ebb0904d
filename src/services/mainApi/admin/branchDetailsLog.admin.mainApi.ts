import baseMainApi from "../base.mainApi";
import {
  AdminBranchDetailsLogListParams,
  AdminBranchDetailsLogListResponse,
} from "./types/branchDetailsLog.admin.mainApi.types";

export const branchDetailsLogAdminMainApi = {
  getBranchDetailsLogs: async (
    branchCode: string,
    params?: AdminBranchDetailsLogListParams
  ) => {
    const response = await baseMainApi.get<AdminBranchDetailsLogListResponse>(
      `/web-api/admin/${branchCode}/branch-detail-log`,
      { params }
    );
    
    return response.data;
  },

  exportBranchDetailsLogs: async (
    branchCode: string,
    params: AdminBranchDetailsLogListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/branch-detail-log/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
};
