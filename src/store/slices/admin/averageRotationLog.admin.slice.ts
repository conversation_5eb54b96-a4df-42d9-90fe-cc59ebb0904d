import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { AdminRole } from "../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminBranch } from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminZone } from "../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { averageRotationLogAdminMainApi } from "../../../services/mainApi/admin/averageRotationLog.admin.mainApi";
import { AdminAverageRotationLogEntry } from "../../../services/mainApi/admin/types/averageRotationLog.admin.mainApi.types";
import dayjs from "dayjs";
import { AdminLabel } from "../../../services/mainApi/admin/types/label.admin.mainApi.types";
export interface AverageRotationLogFilter {
  page: number;
  limit: number;
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branchId: string | null;
  roleId: string | null;
  siteId: string | null;
  siteLabels: (string | number)[];
  orderBy: string;
  orderDirection: "asc" | "desc";
}

interface AverageRotationLogState {
  averageRotationLogs: AdminAverageRotationLogEntry[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
  filter: AverageRotationLogFilter;
  selectedRole: AdminRole | null;
  selectedSite: AdminZone | null;
  selectedSiteLabels: AdminLabel[];
  selectedBranch: AdminBranch | null;
}

// Initial filter state
const initialFilter: AverageRotationLogFilter = {
  page: 1,
  limit: 10,
  startDate: dayjs()
    .subtract(1, "day")
    .startOf("day")
    .format("YYYY-MM-DD"),
  endDate: dayjs().endOf("day").format("YYYY-MM-DD"),
  startTime: "00:00:00",
  endTime: "23:59:59",
  branchId: null,
  roleId: null,
  siteId: null,
  siteLabels: [],
  orderBy: "checkpoint_name",
  orderDirection: "asc",
};

// Initial state
const initialState: AverageRotationLogState = {
  averageRotationLogs: [],
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
  },
  filter: initialFilter,
  selectedRole: null,
  selectedSite: null,
  selectedSiteLabels: [],
  selectedBranch: null,
};

// Async thunk for fetching average rotation logs
export const fetchAverageRotationLogs = createAsyncThunk(
  "averageRotationLog/fetchAverageRotationLogs",
  async (
    params: {
      branchCode: string;
      filter?: AverageRotationLogFilter;
    },
    { rejectWithValue }
  ) => {
    try {
      const { branchCode, filter } = params;

      // Convert filter to API parameters format (camelCase to snake_case)
      const apiParams = filter
        ? {
            page: filter.page,
            limit: filter.limit,
            start_date: filter.startDate || undefined,
            end_date: filter.endDate || undefined,
            start_time: filter.startTime || undefined,
            end_time: filter.endTime || undefined,
            branch_id: filter.branchId || undefined,
            zone_id: filter.siteId || undefined,
            zone_labels:
              filter.siteLabels.length > 0
                ? filter.siteLabels.join(",")
                : undefined,
            order_by: filter.orderBy,
            order_direction:
              filter.orderDirection.toUpperCase() as
                | "ASC"
                | "DESC",
          }
        : undefined;

      // Call the API
      const response =
        await averageRotationLogAdminMainApi.getAverageRotationLogs(
          branchCode,
          apiParams
        );

      const data = response.data || [];

      return {
        data: {
          averageRotationLogs: data,
          pagination: {
            page: response.meta.page || 1,
            limit: response.meta.limit || 10,
            total: response.meta.total || 0,
          },
        },
      };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error
          ? error.message
          : "An error occurred"
      );
    }
  }
);

// Slice
const averageRotationLogAdminSlice = createSlice({
  name: "averageRotationLogAdmin",
  initialState,
  reducers: {
    // Pagination actions
    setPage: (state, action: PayloadAction<number>) => {
      state.filter.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.filter.limit = action.payload;
      state.filter.page = 1; // Reset to first page when changing limit
    },
    // Filter actions
    setStartDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startDate = action.payload;
    },
    setEndDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endDate = action.payload;
    },
    setStartTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startTime = action.payload;
    },
    setEndTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endTime = action.payload;
    },
    setSelectedRole: (
      state,
      action: PayloadAction<AdminRole | null>
    ) => {
      state.selectedRole = action.payload;
      state.filter.roleId = action.payload?.id || null;
    },
    setSelectedSite: (
      state,
      action: PayloadAction<AdminZone | null>
    ) => {
      state.selectedSite = action.payload;
      state.filter.siteId = action.payload?.id || null;
    },
    setSelectedSiteLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedSiteLabels = action.payload;
      state.filter.siteLabels = action.payload.map(
        (label) => label.id
      );
    },
    setSelectedBranch: (
      state,
      action: PayloadAction<AdminBranch | null>
    ) => {
      state.selectedBranch = action.payload;
      state.filter.branchId = action.payload?.id || null;
    },
    setOrderBy: (state, action: PayloadAction<string>) => {
      state.filter.orderBy = action.payload;
    },
    setOrderDirection: (
      state,
      action: PayloadAction<"asc" | "desc">
    ) => {
      state.filter.orderDirection = action.payload;
    },
    clearFilters: (state) => {
      state.filter = {
        ...initialFilter,
        page: state.filter.page,
        limit: state.filter.limit,
      };
      state.selectedRole = null;
      state.selectedSite = null;
      state.selectedBranch = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(
        fetchAverageRotationLogs.pending,
        (state) => {
          state.loading = true;
          state.error = null;
        }
      )
      .addCase(
        fetchAverageRotationLogs.fulfilled,
        (state, action) => {
          state.loading = false;
          state.averageRotationLogs =
            action.payload.data.averageRotationLogs;
          state.pagination = action.payload.data.pagination;
        }
      )
      .addCase(
        fetchAverageRotationLogs.rejected,
        (state, action) => {
          state.loading = false;
          state.error = action.payload as string;
        }
      );
  },
});

export default averageRotationLogAdminSlice;
