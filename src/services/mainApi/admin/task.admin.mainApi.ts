import baseMainApi from "../base.mainApi";
import { BaseGetRequest } from "../types/base.mainApi.types";
import {
  AdminTaskListResponse,
  CreateTaskRequest,
  UpdateTaskRequest,
} from "./types/task.admin.mainApi.types";

const taskAdminMainApi = {
  getTasks: async (
    branchCode: string,
    params?: BaseGetRequest
  ) => {
    const response =
      await baseMainApi.get<AdminTaskListResponse>(
        `/web-api/admin/${branchCode}/task`,
        { params }
      );
    return response.data;
  },

  getTaskById: async (branchCode: string, id: string) => {
    const response =
      await baseMainApi.get<AdminTaskListResponse>(
        `/web-api/admin/${branchCode}/task/${id}`
      );
    return response.data;
  },

  createTask: async (
    branchCode: string,
    data: CreateTaskRequest
  ) => {
    const response =
      await baseMainApi.post<AdminTaskListResponse>(
        `/web-api/admin/${branchCode}/task`,
        data
      );
    return response.data;
  },

  updateTask: async (
    branchCode: string,
    id: string,
    data: UpdateTaskRequest
  ) => {
    const response =
      await baseMainApi.put<AdminTaskListResponse>(
        `/web-api/admin/${branchCode}/task/${id}`,
        data
      );
    return response.data;
  },
};

export default taskAdminMainApi;
