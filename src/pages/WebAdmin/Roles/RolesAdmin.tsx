import { Button, Input, Select } from "antd";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {
  PlusOutlined,
  SearchOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
} from "@ant-design/icons";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks";
import { useCallback, useEffect, useRef } from "react";
import rolesAdminSlice, {
  fetchRoles,
} from "../../../store/slices/admin/roles.admin.slice";
import { MdSecurity } from "react-icons/md";
import ModalViewAdminRoles from "./Components/ModalViewAdminRoles/ModalViewAdminRoles";
import viewRoleAdminSlice from "../../../store/slices/admin/viewRole.admin.slice";
import { AdminRole } from "../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import ModalAddAdminRoles from "./Components/ModalAddAdminRoles/ModalAddAdminRoles";
import addRoleAdminSlice from "../../../store/slices/admin/addRole.admin.slice";
import RolesGrid from "./Components/RolesGrid/RolesGrid";
import { debounce } from "lodash";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import { useTranslation } from "react-i18next";

const { Option } = Select;

const RolesAdmin = () => {
  const dispatch = useAppDispatch();
  const { roles, loading, pagination, filter } =
    useAppSelector((state) => state.rolesAdmin);
  const isLoading = useRef(false);
  const { t } = useTranslation();

  const debouncedSearch = useRef(
    debounce((value: string) => {
      dispatch(
        rolesAdminSlice.actions.setFilter({
          search: value || null,
        })
      );
    }, 500)
  );

  const fetch = useCallback(async () => {
    if (isLoading.current) return;
    isLoading.current = true;
    await dispatch(fetchRoles());
    isLoading.current = false;
  }, [dispatch]);

  useEffect(() => {
    fetch();
  }, [fetch, filter]);

  const handleSearch = (value: string) => {
    debouncedSearch.current(value);
  };

  const handleSortDirectionToggle = () => {
    dispatch(
      rolesAdminSlice.actions.setFilter({
        orderDirection:
          filter.orderDirection === "ASC" ? "DESC" : "ASC",
      })
    );
  };

  const handleSortFieldChange = (value: string) => {
    dispatch(
      rolesAdminSlice.actions.setFilter({ orderBy: value })
    );
  };

  const handleViewRole = useCallback(
    (role: AdminRole) => {
      dispatch(viewRoleAdminSlice.actions.open(role));
    },
    [dispatch]
  );

  const handlePageChange = useCallback(
    (page: number) => {
      dispatch(
        rolesAdminSlice.actions.setFilter({
          page,
        })
      );
    },
    [dispatch]
  );

  const onAdd = useCallback(() => {
    dispatch(addRoleAdminSlice.actions.open());
  }, [dispatch]);

  const handleEditRole = useCallback(
    (role: AdminRole) => {
      dispatch(addRoleAdminSlice.actions.openEdit(role));
    },
    [dispatch]
  );

  return (
    <WebAdminLayout activePage="roles">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdSecurity />}
            title={t("roles.title")}
            description={t("roles.description")}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={onAdd}
              >
                {t("roles.addButton")}
              </Button>,
            ]}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder={t("roles.search.placeholder")}
                prefix={<SearchOutlined />}
                onChange={(e) =>
                  handleSearch(e.target.value)
                }
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                size="large"
              />
              <div className="flex gap-2">
                <Select
                  defaultValue={filter.orderBy}
                  onChange={handleSortFieldChange}
                  size="large"
                  style={{ minWidth: 120 }}
                  className="dark:bg-gray-700"
                >
                  <Option value="created_at">
                    {t(
                      "roles.search.sortOptions.createdAt"
                    )}
                  </Option>
                  <Option value="role_name">
                    {t("roles.search.sortOptions.roleName")}
                  </Option>
                  <Option value="updated_at">
                    {t(
                      "roles.search.sortOptions.updatedAt"
                    )}
                  </Option>
                </Select>
                <Button
                  icon={
                    filter.orderDirection === "ASC" ? (
                      <SortAscendingOutlined />
                    ) : (
                      <SortDescendingOutlined />
                    )
                  }
                  size="large"
                  onClick={handleSortDirectionToggle}
                  className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100 dark:hover:bg-gray-600"
                >
                  {filter.orderDirection === "ASC"
                    ? t("roles.search.sortDirection.asc")
                    : t("roles.search.sortDirection.desc")}
                </Button>
              </div>
            </div>
          </div>

          {/* Roles Grid */}
          <RolesGrid
            roles={roles}
            loading={loading}
            pagination={{
              current: (filter.page || 1) as number,
              pageSize: (filter.limit || 10) as number,
              total: pagination.total || 1,
            }}
            onViewRole={handleViewRole}
            onPageChange={handlePageChange}
            onEditRole={handleEditRole}
          />
        </div>
      </div>
      <ModalViewAdminRoles />
      <ModalAddAdminRoles />
    </WebAdminLayout>
  );
};

export default RolesAdmin;
