import { useCallback, useRef } from "react";
import { Button, Input, Select } from "antd";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {
  PlusOutlined,
  SearchOutlined,
  SortAscendingOutlined,
} from "@ant-design/icons";
import { MdNotifications } from "react-icons/md";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import { useTranslation } from "react-i18next";
import AlertList from "./Components/AlertList";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks";
import {
  fetchAlertAdmin,
  setFilter,
} from "../../../store/slices/admin/alert.admin.slice";
import _ from "lodash";
import { useParams } from "react-router-dom";
import ModalAddAlert from "./Components/ModalAddAlert/ModalAddAlert";
import addAlertAdminSlice from "../../../store/slices/admin/addAlert.admin.slice";

const { Option } = Select;

export default function AlertAdmin() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { filter } = useAppSelector(
    (state) => state.alertAdmin
  );
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const isFetching = useRef(false);
  const { actions } = addAlertAdminSlice;

  const fetch = useCallback(() => {
    if (isFetching.current) return;
    if (!branchCode) return;

    isFetching.current = true;
    dispatch(fetchAlertAdmin(branchCode));
    isFetching.current = false;
  }, [dispatch, branchCode]);

  const debouncedSearchRef = useRef(
    _.debounce(() => {
      fetch();
    }, 500)
  );

  const handleSearch = useCallback(
    (value: string) => {
      dispatch(
        setFilter({ search: value || null, page: 1 })
      );
      debouncedSearchRef.current();
    },
    [dispatch]
  );

  const handleSortChange = useCallback(
    (value: string) => {
      dispatch(setFilter({ orderBy: value }));
    },
    [dispatch]
  );

  const handleSortDirectionToggle = useCallback(() => {
    dispatch(
      setFilter({
        orderDirection:
          filter.orderDirection === "ASC" ? "DESC" : "ASC",
      })
    );
  }, [dispatch, filter.orderDirection]);

  const handleAlertTypeChange = useCallback(
    (value: string | null) => {
      dispatch(setFilter({ alertType: value }));
      fetch();
    },
    [dispatch, fetch]
  );

  const handleAddClick = useCallback(() => {
    dispatch(actions.setOpen(true));
  }, [dispatch, actions]);

  return (
    <WebAdminLayout activePage="alert">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdNotifications />}
            title={t("alert.title")}
            description={t("alert.description")}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={handleAddClick}
              >
                {t("alert.addButton")}
              </Button>,
            ]}
          />

          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder={t("alert.search.placeholder")}
                prefix={<SearchOutlined />}
                size="large"
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                onChange={(e) =>
                  handleSearch(e.target.value)
                }
                allowClear
              />
              <div className="flex gap-2">
                <Select
                  defaultValue="all"
                  className="min-w-[160px] dark:bg-gray-700"
                  size="large"
                  onChange={(value) =>
                    handleAlertTypeChange(
                      value === "all" ? null : value
                    )
                  }
                >
                  <Option value="all">
                    {t("alert.filter.all")}
                  </Option>
                  <Option value="critical">
                    {t("alert.status.critical")}
                  </Option>
                  <Option value="warning">
                    {t("alert.status.warning")}
                  </Option>
                  <Option value="normal">
                    {t("alert.status.normal")}
                  </Option>
                </Select>
                <Select
                  defaultValue="created_at"
                  className="min-w-[160px] dark:bg-gray-700"
                  size="large"
                  onChange={handleSortChange}
                  value={filter.orderBy}
                >
                  <Option value="created_at">
                    {t(
                      "alert.search.sortOptions.createdAt"
                    )}
                  </Option>
                  <Option value="alert_name">
                    {t("alert.search.sortOptions.name")}
                  </Option>
                </Select>
                <Button
                  icon={
                    <SortAscendingOutlined
                      rotate={
                        filter.orderDirection === "DESC"
                          ? 180
                          : 0
                      }
                    />
                  }
                  size="large"
                  onClick={handleSortDirectionToggle}
                  className="dark:border-gray-600 dark:text-gray-300"
                />
              </div>
            </div>
          </div>

          {/* Content Section */}
          <AlertList />

          {/* Modal Add/Edit Alert */}
          <ModalAddAlert />
        </div>
      </div>
    </WebAdminLayout>
  );
}
