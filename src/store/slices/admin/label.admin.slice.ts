import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import labelAdminMainApi from "../../../services/mainApi/admin/label.admin.mainApi";
import { AdminLabel } from "../../../services/mainApi/admin/types/label.admin.mainApi.types";
import {
  BaseGetRequest,
  BasePaginationMeta,
} from "../../../services/mainApi/types/base.mainApi.types";

interface State {
  labels: AdminLabel[];
  loading: boolean;
  errorMessage: string | null;
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;
}

const initialState: State = {
  labels: [],
  loading: false,
  errorMessage: null,
  filter: {
    search: null,
    page: null,
    limit: null,
    orderBy: "created_at",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
  },
};

export const fetchLabelsAdmin = createAsyncThunk(
  "labelAdmin/fetchLabels",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const { filter } = state.labelAdmin;
      const params: BaseGetRequest = {
        search: filter.search || "",
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
      };
      const response = await labelAdminMainApi.getLabels(
        branchCode,
        params
      );
      return {
        data: response.data,
        meta: response.meta,
      };
    } catch {
      return rejectWithValue("Failed to fetch labels");
    }
  }
);

export const deleteLabel = createAsyncThunk(
  "labelAdmin/deleteLabel",
  async (
    {
      branchCode,
      labelId,
    }: { branchCode: string; labelId: string },
    { rejectWithValue }
  ) => {
    try {
      await labelAdminMainApi.deleteLabel(
        branchCode,
        labelId
      );
      return labelId;
    } catch {
      return rejectWithValue("Failed to delete label");
    }
  }
);

const labelAdminSlice = createSlice({
  name: "labelAdmin",
  initialState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch labels
      .addCase(fetchLabelsAdmin.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchLabelsAdmin.fulfilled,
        (state, action) => {
          state.loading = false;
          state.labels = action.payload.data || [];
          state.pagination = action.payload.meta;
        }
      )
      .addCase(
        fetchLabelsAdmin.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      )
      // Delete label
      .addCase(deleteLabel.fulfilled, (state, action) => {
        state.labels = state.labels.filter(
          (label) => label.id !== action.payload
        );
        state.pagination.total =
          (state.pagination.total || 1) - 1;
      });
  },
});

export default labelAdminSlice;
