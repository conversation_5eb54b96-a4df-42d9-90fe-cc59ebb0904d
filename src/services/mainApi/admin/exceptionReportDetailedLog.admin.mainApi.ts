import baseMainApi from "../base.mainApi";
import {
  AdminExceptionReportDetailedLogListParams,
  AdminExceptionReportDetailedLogListResponse,
} from "./types/exceptionReportDetailedLog.admin.mainApi.types";

export const exceptionReportDetailedLogAdminApi = {
  getExceptionReportDetailedLogs: async (
    branchCode: string,
    params?: AdminExceptionReportDetailedLogListParams
  ) => {
    const response =
      await baseMainApi.get<AdminExceptionReportDetailedLogListResponse>(
        `/web-api/admin/${branchCode}/exception-report-detailed-log`,
        { params }
      );
    return response.data;
  },

  exportExceptionReportDetailedLogs: async (
    branchCode: string,
    params: AdminExceptionReportDetailedLogListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/exception-report-detailed-log/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
}; 