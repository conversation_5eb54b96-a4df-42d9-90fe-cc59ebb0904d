import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {<PERSON>ton, Tabs} from "antd";
import "./Dashboard.css";
import {useTranslation} from "react-i18next";
import "../../../i18n";
import DashboardActivity from "./components/DashboardActivity";
import DashboardMap from "./components/DashboardMap";
import {FilterOutlined} from "@ant-design/icons";
import dashboardMapAdminSlice from "../../../store/slices/admin/dashboardMap.admin.slice.ts";
import {useAppDispatch} from "../../../store/hooks.ts";
import {useState} from "react";

function Dashboard() {
  const {t} = useTranslation();
  const [activeTab, setActiveTab] = useState("activity");

  const dispatch = useAppDispatch()

  const items = [
    {
      key: "activity",
      label: t("dashboard.tabs.activity"),
      children: <DashboardActivity isActive={activeTab === "activity"}/>,
    },
    {
      key: "maps",
      label: t("dashboard.tabs.maps"),
      children: <DashboardMap/>,
    },
  ];

  const handleToggleFilterModal = () => {
    dispatch(dashboardMapAdminSlice.actions.toggleFilterModal());
  };

  return (
    <WebAdminLayout activePage="dashboard">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        defaultActiveKey="activity"
        items={items}
        className="dashboard-tabs"
        tabBarExtraContent={
          <div className="flex items-center space-x-2">
            {
              activeTab === "maps" && (
                <Button
                  icon={<FilterOutlined/>}
                  size="large"
                  onClick={handleToggleFilterModal}
                >
                  {t("dashboard.filter.title", "Filter")}
                </Button>
              )
            }
          </div>
        }
      />
    </WebAdminLayout>
  );
}

export default Dashboard;
