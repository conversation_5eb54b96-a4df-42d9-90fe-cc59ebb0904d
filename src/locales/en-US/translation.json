{"dashboard": {"title": "Activity Dashboard", "activity": {"title": "Latest User Activity", "totalUsers": "Total Users: {{count}}", "lastLogin": "Last Login"}, "tabs": {"activity": "ACTIVITY", "maps": "MAPS"}}, "branch": {"title": "Client Management", "description": "Manage your organization branches", "addButton": "Add New Client", "search": {"placeholder": "Search branches...", "sortOptions": {"createdAt": "Created Date", "branchName": "Branch Name"}, "sortDirection": {"asc": "Ascending", "desc": "Descending"}}, "grid": {"empty": "No branches found", "total": "Total {{total}} items", "status": {"inactive": "Inactive", "mainBranch": "Main Branch"}, "license": "License", "columns": {"name": "Branch Name", "code": "Branch Code", "address": "Address", "phone": "Phone", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete"}}, "modal": {"add": {"title": "Add New Branch", "success": "Branch added successfully", "error": "Failed to add branch"}, "edit": {"title": "Edit Branch", "success": "Branch updated successfully", "error": "Failed to update branch"}, "view": {"title": "Branch Details", "edit": "Edit Branch", "delete": "Delete Branch", "confirmDelete": "Are you sure you want to delete this branch?"}, "form": {"name": {"label": "Branch Name", "placeholder": "Enter branch name", "required": "Please enter branch name"}, "code": {"label": "Branch Code", "placeholder": "Enter branch code"}, "description": {"label": "Description", "placeholder": "Enter branch description"}, "license": {"label": "License", "placeholder": "Select license", "required": "Please select license"}, "timezone": {"label": "Timezone", "placeholder": "Select timezone", "required": "Please select timezone"}, "status": {"label": "Status", "active": "Active", "inactive": "Inactive"}, "submit": "Submit", "cancel": "Cancel"}, "sections": {"branchInfo": "Branch Information", "licenseDetails": "License Details", "timezoneConfig": "Timezone Configuration", "additionalInfo": "Additional Details", "adminInfo": "Branch Admin Information"}, "admin": {"name": {"label": "Admin Name", "placeholder": "Enter admin name", "required": "Please enter admin name"}, "email": {"label": "Email", "placeholder": "Enter email address", "required": "Please enter email", "invalid": "Please enter a valid email"}, "phone": {"label": "Phone Number", "placeholder": "Enter phone number", "required": "Please enter phone number", "invalid": "Please enter a valid phone number"}, "password": {"label": "Password", "placeholder": "Enter password", "placeholderUpdate": "Enter new password (leave blank to keep current)", "required": "Please enter password", "minLength": "Password must be at least 6 characters", "optionalHint": "Leave blank if you do not want to change the password"}, "confirmPassword": {"label": "Confirm Password", "placeholder": "Confirm password", "placeholderUpdate": "Confirm new password (leave blank to keep current)", "required": "Please confirm password", "mismatch": "Passwords do not match"}}}}, "roles": {"title": "Role Management", "description": "Manage your organization roles", "addButton": "Add New Role", "search": {"placeholder": "Search roles...", "sortOptions": {"createdAt": "Created Date", "roleName": "Role Name", "updatedAt": "Updated Date"}, "sortDirection": {"asc": "Ascending", "desc": "Descending"}}, "modal": {"add": {"title": "Add New Role", "success": "Role created successfully", "error": "Failed to create role"}, "edit": {"title": "Edit Role", "success": "Role updated successfully", "error": "Failed to update role"}, "view": {"title": "View Role Details"}, "form": {"name": {"label": "Role Name", "placeholder": "Enter role name", "required": "Role name is required"}}, "sections": {"roleInfo": "Role Information", "permissions": "Permissions", "additionalInfo": "Additional Details"}, "permissions": {"module": {"title": "Module Permissions", "label": "⚡ Module Permissions"}, "report": {"title": "Report Permissions", "label": "📊 Report Permissions"}, "checkAll": "Check All Permissions"}, "status": {"label": "Status", "active": "Active", "inactive": "Inactive"}, "fields": {"parentBranch": "Parent Branch", "createdAt": "Created At"}}, "table": {"roleName": "Role Name", "status": "Status", "createdAt": "Created At", "action": "Action", "edit": "Edit", "view": "View", "editTooltip": "Edit Role", "viewTooltip": "View Role"}, "permissions": {"name": "Module Name", "view": "View", "create": "Create", "update": "Update", "delete": "Delete", "modulePermissions": "Module Permissions", "reportPermissions": "Report Permissions"}}, "menu": {"dashboard": "Dashboard", "branch": "Branch Management", "license": "License Management", "settings": "Settings", "activity": "Activity", "task": "Task", "alert": "<PERSON><PERSON>", "scheduler": "Scheduler", "forms": "Forms", "forms-picklist": "Forms Picklist", "forms-group": "Forms", "field-monitoring": "Field Monitoring", "monitoring-group": "Field Monitoring", "monitoring-site": "Site", "site": "Site", "geofence": "Geofence", "checkpoint": "Checkpoint", "analytics": "Analytics", "analytics-group": "Analytics", "activity-log": "Activity Log", "alarms-log": "Alarms", "average-rotation-log": "Average Rotation", "branch-details-log": "Branch Details", "checkpoint-activity-log": "Checkpoint Activity", "checkpoint-battery-log": "Checkpoint Battery", "exception-log": "Exception", "exception-detailed-log": "Exception Detailed", "forms-log": "Forms", "analytics-forms-log": "Forms", "geofence-log": "Geofence", "analytics-geofence-log": "Geofence", "missed-zone-log": "Missed Zone", "sign-on-off-log": "Sign On/Off", "gps-heatmap-log": "GPS Heatmap", "tasks-log": "Tasks", "analytics-tasks-log": "Tasks", "time-rotation-log": "Time & Rotation", "time-on-zone-log": "Time On Zone", "unknown-log": "Unknown Data", "settings-group": "Settings", "client": "Client Management", "roles": "Role Management", "users": "User Management", "label": "Label Management", "device": "Device Management", "beacon": "Beacon Management"}, "users": {"title": "User Management", "description": "Manage your organization users", "addButton": "Add New User", "search": {"placeholder": "Search users...", "sortOptions": {"createdAt": "Created Date", "name": "Name", "email": "Email"}, "sortDirection": {"asc": "Ascending", "desc": "Descending"}}, "userItem": {"inactive": "Inactive", "roleAccess": "Role & Access", "role": "Role", "access": "Access", "noRole": "No Role", "branchAssignment": "Branch Assignment", "branch": "Branch", "branches": "Branches", "noBranchesAssigned": "No branches assigned", "contactInfo": "Contact Info", "noPhoneNumber": "No phone number", "created": "Created", "web": "Web", "mobile": "Mobile"}, "modal": {"add": {"title": "Add New User"}, "edit": {"title": "Edit User"}, "saveSuccess": "User saved successfully", "form": {"email": {"label": "Email", "placeholder": "Enter email"}, "name": {"label": "Name", "placeholder": "Enter name"}, "phone": {"label": "Phone", "placeholder": "Enter phone number"}, "password": {"label": "Password", "placeholder": "Enter password", "placeholderUpdate": "Enter new password"}, "confirmPassword": {"label": "Confirm Password", "placeholder": "Confirm password", "placeholderUpdate": "Confirm new password"}, "allowMobileAccess": "Allow Mobile Access", "allowWebAccess": "Allow Web Access", "role": {"label": "Select Role"}, "label": {"label": "Select Label"}, "client": {"label": "Select Client"}}, "table": {"name": "Name", "email": "Email", "phone": "Phone", "role": "Role", "status": "Status", "actions": "Actions"}, "status": {"active": "Active", "inactive": "Inactive"}, "actions": {"edit": "Edit", "view": "View", "delete": "Delete"}}, "list": {"noUsersFound": "No users found", "total": "Total {{total}} items"}}, "label": {"title": "Label Management", "description": "Manage your organization labels", "addButton": "Add New Label", "search": {"placeholder": "Search labels...", "sortOptions": {"createdAt": "Created Date", "labelName": "Name", "labelDescription": "Description"}, "sortDirection": {"asc": "Ascending", "desc": "Descending"}}, "modal": {"add": {"title": "Add New Label", "success": "Label created successfully", "error": "Failed to create label"}, "edit": {"title": "Edit Label", "success": "Label updated successfully", "error": "Failed to update label"}, "form": {"name": {"label": "Label Name", "placeholder": "Enter label name", "required": "Please input the label name"}, "description": {"label": "Description", "placeholder": "Enter label description", "required": "Please input the label description"}}}, "noLabelsFound": "No labels found", "noDescription": "No description"}, "mainLayout": {"darkMode": "Dark Mode", "changeClient": "Change Client", "selectClient": "Select Client", "appName": "<PERSON><PERSON><PERSON>", "defaultBranch": "Admin"}, "layout": {"webAdmin": "Web Admin"}, "selectBranch": {"title": "Select Client", "description": "Choose a Organization / Client to manage", "searchPlaceholder": "Search branches...", "errors": {"noPermission": "You don't have permission to access this client", "verifyFailed": "Failed to verify branch access"}, "branchItem": {"mainBranch": "Main Branch", "select": "Select"}}, "activity": {"title": "Activity Management", "description": "Manage and track activities in your organization", "addNew": "Add New Activity", "search": {"placeholder": "Search activities...", "sortOptions": {"createdAt": "Created Date", "name": "Activity Name"}}, "table": {"name": "Activity Name", "description": "Description", "requirements": "Requirements", "status": {"active": "Active", "inactive": "Inactive"}, "gpsRequired": "GPS Required", "photoRequired": "Photo Required", "commentRequired": "Comment Required", "active": "Active", "inactive": "Inactive"}, "form": {"name": "Activity Name", "description": "Description", "requirements": "Requirements", "status": "Status", "active": "Active", "gpsRequired": "GPS Required", "photoRequired": "Photo Required", "commentRequired": "Comment Required", "nameRequired": "Please enter activity name", "descriptionRequired": "Please enter activity description"}, "modal": {"add": {"title": "Create New Activity", "success": "Activity created successfully", "error": "Failed to create activity"}, "edit": {"title": "Edit Activity", "success": "Activity updated successfully", "error": "Failed to update activity"}}, "list": {"empty": "No activities found", "createdAt": "Created"}, "noDescription": "No description", "logTitle": "Activity Log", "type": "Activity", "location": "Location", "dateTime": "Date/Time", "comment": "Comment", "id": "ID", "eventTime": "Event Time"}, "scheduler": {"title": "Scheduler Management", "description": "Manage and monitor all scheduling activities", "addButton": "Add Schedule", "search": {"placeholder": "Search schedules..."}, "sort": {"createdAt": "Created Date", "name": "Name"}, "list": {"noSchedulersFound": "No schedulers found", "total": "Total {{total}} items"}, "item": {"inactive": "Inactive", "noDescription": "No description", "reportDetails": "Report Details", "type": "Type", "format": "Format", "scheduleSettings": "Schedule Settings", "frequency": "Frequency", "period": "Period", "time": "Time", "assignment": "Assignment", "user": "User", "noUser": "No user assigned", "branch": "Branch", "noBranch": "No branch selected", "executionInfo": "Execution Info", "nextRun": "Next run", "noScheduledRun": "No scheduled run", "generatedOn": "Generated on", "created": "Created", "edit": "Edit", "delete": "Delete", "additionalFilters": "Additional Filters", "userLabels": "User Labels", "deviceLabels": "Device Labels", "zoneLabels": "Zone Labels", "checkpointLabels": "Checkpoint Labels"}, "modal": {"title": {"add": "Add New Scheduler", "edit": "Edit Scheduler"}, "addTitle": "Add New Scheduler", "editTitle": "Edit Scheduler", "sections": {"basic": {"title": "Basic Information", "tooltip": "Fill in the basic details of your scheduler"}, "basicTitle": "Basic Information", "basicTooltip": "Fill in the basic details of your scheduler", "schedule": {"title": "Schedule Configuration", "tooltip": "Configure when reports should be generated"}, "filter": "Filter Configuration", "email": {"title": "Email Configuration", "tooltip": "Configure email settings for report delivery"}}, "form": {"name": {"label": "Scheduler Name", "tooltip": "Enter a descriptive name for your scheduler", "placeholder": "e.g., Daily Safety Report"}, "nameLabel": "Scheduler Name", "nameTooltip": "Enter a descriptive name for your scheduler", "namePlaceholder": "e.g., Daily Safety Report", "description": {"label": "Description", "tooltip": "Provide details about the scheduler's purpose", "placeholder": "Describe the purpose and usage of this scheduler..."}, "descriptionLabel": "Description", "descriptionTooltip": "Provide details about the scheduler's purpose", "descriptionPlaceholder": "Describe the purpose and usage of this scheduler...", "reportType": {"label": "Report Type", "tooltip": "Select the type of report to generate"}, "frequency": {"label": "Frequency", "tooltip": "Select how often this report should be generated"}, "reportFormat": {"label": "Report Format", "tooltip": "Select the format for generated reports", "options": {"pdf": "PDF", "csv": "CSV", "excel": "Excel"}}, "period": {"start": {"label": "Start Period", "tooltip": "Select the start date for this scheduler"}, "end": {"label": "End Period", "tooltip": "Select the end date for this scheduler"}}, "generate": {"date": {"label": "Generate Date", "tooltip": "Select the date when reports should be generated"}, "time": {"label": "Generate Time", "tooltip": "Select the time when reports should be generated"}}, "generateDate": {"label": "Generate Date", "tooltip": "Select the date when reports should be generated"}, "generateTime": {"label": "Generate Time", "tooltip": "Select the time when reports should be generated"}, "stopIfBlank": {"label": "Stop If Blank", "tooltip": "Stop generation if no data is available"}, "detailedReport": {"label": "Detailed Report", "tooltip": "Include detailed information in reports"}, "active": {"label": "Active", "tooltip": "Enable or disable this scheduler"}, "startTime": {"label": "Data Capture Start Time", "tooltip": "Set the time from which data should be captured"}, "endTime": {"label": "Data Capture End Time", "tooltip": "Set the time until which data should be captured"}, "selectedBranch": {"label": "Select Branch", "tooltip": "Select a branch for this scheduler"}, "selectAll": {"label": "Select All Branches", "tooltip": "Select all available branches"}, "subject": {"label": "Email Subject", "tooltip": "Enter the subject line for the email", "placeholder": "e.g., Daily Safety Report for {{date}}"}, "message": {"label": "Email Message", "tooltip": "Enter the message body for the email", "placeholder": "Enter the email message content..."}, "email": {"label": "Email Recipients", "tooltip": "Add email addresses to receive reports", "placeholder": "Enter email address", "addButton": "Add"}}}}, "alert": {"title": "Alert Management", "description": "Monitor and manage system alerts", "addButton": "<PERSON><PERSON>", "search": {"placeholder": "Search alerts...", "sortOptions": {"createdAt": "Created Date", "name": "Alert <PERSON>"}}, "filter": {"all": "All Alerts"}, "status": {"critical": "Critical", "warning": "Warning", "normal": "Normal", "inactive": "Inactive"}, "labels": {"event": "Event", "action": "Action", "createdAt": "Created", "updatedAt": "Last Updated"}, "list": {"empty": "No alerts found"}, "form": {"name": {"label": "Alert <PERSON>", "placeholder": "Enter alert name"}, "description": {"label": "Description", "placeholder": "Enter alert description"}, "event": {"label": "Event", "placeholder": "Select event"}, "logicalCondition": {"label": "Logical Condition Type"}, "subject": {"label": "Subject"}, "message": {"label": "Message"}, "action": {"label": "Action"}, "sections": {"basic": "Basic Information", "conditions": "<PERSON><PERSON>", "recipients": "<PERSON><PERSON>nts", "branches": "Branches"}, "branches": {"label": "Branches"}, "active": {"label": "Active"}}, "modal": {"create": {"title": "Create <PERSON>"}, "edit": {"title": "<PERSON>"}, "add": {"success": "<PERSON><PERSON> created successfully", "error": "An error occurred"}, "update": {"success": "<PERSON><PERSON> updated successfully"}}, "error": {"noBranchCode": "Branch code is required", "emptyRecipient": "All recipients must have contact information"}}, "geofence": {"title": "Geofence Management", "description": "Manage your geofence areas and boundaries", "addButton": "Add Geofence", "typeLabel": "Type", "search": {"placeholder": "Search geofences...", "sortOptions": {"createdAt": "Created Date", "name": "Name", "type": "Type"}}, "type": {"placeholder": "Select type", "polygon": "Polygon", "circle": "Circle", "rectangle": "Rectangle"}, "list": {"empty": "No geofences found", "noZone": "No Zone", "noActiveTime": "No active time", "noStayDuration": "No stay duration", "activeTime": "Active Time", "stayDuration": "Stay Duration"}, "modal": {"add": {"title": "Add Geofence", "success": "Geofence created successfully"}, "edit": {"title": "Edit Geofence", "success": "Geofence updated successfully"}, "error": "Failed to save geofence", "sections": {"basic": "Basic Information"}, "tabs": {"basic": "Basic Information", "map": "Map"}, "form": {"name": {"label": "Name", "placeholder": "Enter geofence name"}, "description": {"label": "Description", "placeholder": "Enter description"}, "zone": {"label": "Zone"}, "activeTime": {"start": "Active From", "end": "Active To"}, "minStayDuration": "Minimum Stay Duration", "maxStayDuration": "Maximum Stay Duration"}, "map": {"clearButton": "Clear Points"}}}, "beacon": {"title": "Beacon Management", "description": "Manage your beacon devices and configurations", "addButton": "Add Beacon", "search": {"placeholder": "Search beacons..."}, "table": {"name": "Name", "uuid": "UUID", "major": "Major", "minor": "Minor", "status": "Status", "lastSeen": "Last Seen"}}, "site": {"title": "Site Management", "description": "Manage your site locations and configurations", "addButton": "Add Site", "search": {"placeholder": "Search sites...", "sortOptions": {"createdAt": "Created Date", "name": "Name"}}, "filter": {"status": "Filter by status"}, "status": {"active": "Active", "inactive": "Inactive"}, "sort": {"createdAt": "Created Date", "name": "Name"}, "table": {"name": "Name", "address": "Address", "city": "City", "type": "Type", "checkpoints": "Checkpoints", "status": "Status", "lastUpdated": "Last Updated", "actions": "Actions"}, "list": {"empty": "No sites found", "total": "Total {{total}} items"}, "modal": {"createTitle": "Add New Site", "editTitle": "Edit Site", "createSuccess": "Site created successfully", "editSuccess": "Site updated successfully", "add": {"title": "Add New Site", "success": "Site created successfully", "error": "Failed to create site"}, "edit": {"title": "Edit Site", "success": "Site updated successfully", "error": "Failed to update site"}}, "form": {"basicInfo": "Basic Information", "locationInfo": "Location Information", "notificationInfo": "Notification Settings", "labels": "Labels", "name": "Site Name", "nameTooltip": "Enter a unique name for this site", "namePlaceholder": "Enter site name", "description": "Description", "descriptionTooltip": "Provide additional details about this site", "descriptionPlaceholder": "Enter site description", "address": "Address", "addressTooltip": "Enter the physical address of the site", "addressPlaceholder": "Enter site address", "timezone": "Timezone", "timezoneTooltip": "Select the timezone for this site", "coordinates": "Coordinates", "latitude": "Latitude", "latitudePlaceholder": "Enter latitude", "longitude": "Longitude", "longitudePlaceholder": "Enter longitude", "scheduleSettings": "Schedule Settings", "intervalActive": "Schedule active time range", "startTime": "Start Time", "endTime": "End Time", "emailSettings": "Email Notification Settings", "emailSubject": "Email Subject", "emailSubjectPlaceholder": "Enter email subject", "recipients": "Recipients", "addRecipient": "Add Recipient", "emailAddress": "Email Address", "emailPlaceholder": "Enter email address", "nameField": "Site Name", "timezoneField": "Timezone", "latitudeField": "Latitude", "longitudeField": "Longitude", "addressField": "Address", "labelsField": "Labels", "endTimeField": "End Time", "emailSubjectField": "Email Subject", "recipientsField": "Recipients", "emailField": "Email", "phoneField": "Phone", "selectLabels": "Select Labels"}, "sections": {"basic": "Basic Information", "location": "Location Details", "additional": "Additional Information"}}, "checkpoint": {"title": "Checkpoint Management", "description": "Manage your checkpoints and their configurations", "add": "Add Checkpoint", "search": {"placeholder": "Search checkpoints..."}, "sort": {"createdAt": "Created Date", "name": "Name"}, "list": {"empty": "No checkpoints found", "total": "Total {{total}} items", "status": {"active": "Active", "inactive": "Inactive"}, "details": "Details", "schedule": "Schedule"}, "modal": {"add": {"title": "Add New Checkpoint", "success": "Checkpoint created successfully", "error": "Failed to create checkpoint"}, "edit": {"title": "Edit Checkpoint", "success": "Checkpoint updated successfully", "error": "Failed to update checkpoint"}, "createTitle": "Add New Checkpoint", "editTitle": "Edit Checkpoint", "createSuccess": "Checkpoint created successfully", "editSuccess": "Checkpoint updated successfully", "form": {"name": {"label": "Checkpoint Name", "placeholder": "Enter checkpoint name", "required": "Checkpoint name is required"}, "description": {"label": "Description", "placeholder": "Enter checkpoint description"}, "type": {"label": "Checkpoint Type", "placeholder": "Select checkpoint type", "required": "Checkpoint type is required"}, "zone": {"label": "Zone", "placeholder": "Select zone"}, "geofence": {"label": "Geofence", "placeholder": "Select geofence"}, "beacon": {"label": "Beacon", "placeholder": "Select beacon"}, "label": {"label": "Label", "placeholder": "Select label"}, "coordinates": {"label": "Coordinates", "latitude": "Latitude", "longitude": "Longitude"}, "address": {"label": "Address", "placeholder": "Enter address"}, "schedule": {"title": "Schedule Settings", "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "visits": "visits required", "enabled": "Enabled", "count": "Visit Count"}, "alert": {"title": "<PERSON><PERSON>", "enabled": "Enable <PERSON>"}, "active": "Active", "basicInfo": "Basic Information", "locationInfo": "Location Information", "scheduleInfo": "Schedule Information", "alertSettings": "<PERSON><PERSON>", "nameTooltip": "Enter a unique name for this checkpoint", "descriptionTooltip": "Provide additional details about this checkpoint", "typeTooltip": "Select the type of checkpoint to create", "zoneTooltip": "Select a zone for this checkpoint", "geofenceTooltip": "Select a geofence to associate with this checkpoint", "beaconTooltip": "Select a beacon to associate with this checkpoint", "labelTooltip": "Select labels for this checkpoint", "coordinatesTooltip": "Enter GPS coordinates for this checkpoint", "addressTooltip": "Enter the physical address of the checkpoint"}}, "types": {"qr": "QR Code", "nfc": "NFC Tag", "beacon": "Beacon", "geofence": "Geofence"}, "error": {"noBranchCode": "Branch code is required", "invalidCoordinates": "Invalid coordinates", "createFailed": "Failed to create checkpoint", "updateFailed": "Failed to update checkpoint", "deleteFailed": "Failed to delete checkpoint"}, "technical": {"details": "Technical Details", "type": "Type", "uuid": "UUID", "major": "Major", "minor": "Minor", "name": "Name"}, "map": {"title": "Location Map", "tooltip": "Click on the map to set checkpoint location", "searchPlaceholder": "Search location...", "markerTooltip": "Checkpoint"}, "location": {"title": "Location Information", "description": "Set the checkpoint location"}}, "license": {"title": "License Management", "description": "Manage your licenses and their limitations", "add": "Add License", "list": {"empty": "No licenses found", "error": "Failed to fetch licenses", "total": "Total {{total}} items"}, "table": {"name": "License Name", "maxSubbranch": "<PERSON>", "maxUser": "Max User", "maxLabel": "Max Label", "status": "Status"}, "status": {"active": "Active", "inactive": "Inactive"}, "limitations": "Limitations", "additionalLimits": "Additional Limits", "maxSubbranch": "<PERSON>", "maxUser": "Max Users", "maxLabel": "Max Labels", "maxBeacon": "Max Beacons", "maxScheduler": "Max Scheduler", "maxAlert": "<PERSON>", "maxDevice": "<PERSON> Devices", "maxSite": "Max <PERSON>"}, "formPicklist": {"title": "Form Picklist Management", "description": "Manage your form picklist options", "addButton": "Add New Picklist", "search": {"placeholder": "Search picklists...", "sortOptions": {"createdAt": "Created Date", "name": "Name", "description": "Description"}, "sortDirection": {"asc": "Ascending", "desc": "Descending"}}, "noPicklistsFound": "No picklists found", "list": {"status": {"active": "Active", "inactive": "Inactive"}, "showMore": "Show more", "showLess": "Show less", "optionsCount": "{{count}} items", "creationInfo": "Creation Information", "created": "Created", "updated": "Updated", "timeFormat": "DD MMM YYYY HH:mm", "tooltips": {"edit": "Edit picklist", "createdAt": "Created at {{time}}", "updatedAt": "Updated at {{time}}"}}, "modal": {"createTitle": "Create New Picklist", "updateTitle": "Edit Picklist", "createSuccess": "Picklist created successfully", "updateSuccess": "Picklist updated successfully", "createError": "Failed to create picklist", "updateError": "Failed to update picklist"}, "sections": {"basic": "Basic Information", "basic.tooltip": "Basic information about the picklist", "options": "Picklist Options", "options.tooltip": "Add and manage options for this picklist"}, "form": {"name": {"label": "Picklist Name", "placeholder": "e.g., Status Options", "tooltip": "Enter a descriptive name for your picklist"}, "description": {"label": "Description", "placeholder": "Enter a description...", "tooltip": "Optional description for this picklist"}, "active": {"label": "Active", "tooltip": "Toggle to enable/disable this picklist"}}, "options": {"add": "Add Option", "option": "Option", "name": {"label": "Option Name", "placeholder": "Enter option name"}, "description": {"label": "Description", "placeholder": "Enter option description"}, "active": {"label": "Active"}, "dragTooltip": "Drag to reorder"}, "validation": {"nameRequired": "Picklist name is required", "optionsRequired": "At least one option is required", "optionNameRequired": "All options must have a name"}, "message": {"createSuccess": "Picklist created successfully", "updateSuccess": "Picklist updated successfully", "deleteSuccess": "Picklist deleted successfully", "createError": "Failed to create picklist", "updateError": "Failed to update picklist", "deleteError": "Failed to delete picklist"}}, "forms": {"title": "Forms Management", "description": "Manage your forms and templates", "addButton": "Add Form", "search": {"placeholder": "Search forms...", "sortOptions": {"createdAt": "Created Date", "name": "Name", "updatedAt": "Updated Date"}}, "noFormsFound": "No forms found", "checkpoint": "Checkpoint", "noCheckpoint": "No checkpoint", "role": "Role Access", "noRole": "No role assigned", "fields": "Fields", "fieldCount": "fields", "modal": {"add": {"title": "Add New Form"}, "edit": {"title": "Edit Form"}, "field": {"add": "Add Field", "title": "Field"}, "sections": {"basic": "Basic Information", "basic.tooltip": "Fill in the basic details of your form"}, "form": {"name": {"label": "Form Name", "tooltip": "Enter a descriptive name for your form", "placeholder": "e.g., Daily Inspection Form"}, "description": {"label": "Description", "tooltip": "Provide details about the form's purpose", "placeholder": "Describe the purpose and usage of this form..."}, "role": {"label": "Role Access", "tooltip": "Select which roles can access this form"}, "checkpoint": {"label": "Associated Checkpoint", "tooltip": "Link this form to a specific checkpoint"}, "branches": {"label": "Branch Availability", "tooltip": "Select which branches can use this form"}}}}, "device": {"title": "Device Management", "description": "Manage your devices and configurations", "addButton": "Add <PERSON>", "search": {"placeholder": "Search devices...", "sortOptions": {"createdAt": "Created Date", "name": "Name", "deviceName": "Device Name"}}, "sort": {"createdAt": "Created Date", "name": "Name"}, "imei": "IMEI", "serialNumber": "Serial Number", "information": "Device Information", "noDevicesFound": "No devices found", "form": {"name": "Device Name", "namePlaceholder": "Enter device name", "type": "Device Type", "selectType": "Select a type", "imei": "IMEI", "imeiPlaceholder": "Enter IMEI", "serialNumber": "Serial Number", "serialNumberPlaceholder": "Enter serial number", "description": "Description", "descriptionPlaceholder": "Enter description", "labels": "Labels", "app": "App", "iButton": "I-<PERSON><PERSON>", "rfid": "RFID", "gps": "GPS", "requiredFields": "Please fill all the required fields", "branchRequired": "Branch code is required", "createSuccess": "<PERSON>ce created successfully", "updateSuccess": "Device updated successfully", "submitFailed": "Failed to submit device"}, "modal": {"createTitle": "Add New Device", "updateTitle": "<PERSON>"}, "status": {"active": "Active", "inactive": "Inactive"}, "list": {"empty": "No devices found", "total": "Total {{total}} items"}}, "task": {"title": "Task Management", "description": "Manage your tasks and assignments", "addButton": "Add Task", "search": {"placeholder": "Search tasks...", "sortOptions": {"createdAt": "Created Date", "startTime": "Start Time", "dueDate": "Due Date", "priority": "Priority"}, "sortDirection": {"asc": "Ascending", "desc": "Descending"}}, "status": {"pending": "Pending", "inProgress": "In Progress", "completed": "Completed", "overdue": "Overdue", "cancelled": "Cancelled"}, "priority": {"low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}, "modal": {"add": {"title": "Add New Task", "success": "Task created successfully", "error": "Failed to create task"}, "edit": {"title": "Edit Task", "success": "Task updated successfully", "error": "Failed to update task"}, "form": {"title": {"label": "Task Title", "placeholder": "Enter task title", "required": "Task title is required"}, "description": {"label": "Description", "placeholder": "Enter task description"}, "assignee": {"label": "Assignee", "placeholder": "Select assignee"}, "startDate": {"label": "Start Date"}, "dueDate": {"label": "Due Date"}, "priority": {"label": "Priority", "placeholder": "Select priority"}, "status": {"label": "Status", "placeholder": "Select status"}, "site": {"label": "Site", "placeholder": "Select site"}, "checkpoint": {"label": "Checkpoint", "placeholder": "Select checkpoint"}, "dateTimeRange": {"label": "Date Time Range", "startPlaceholder": "Start Date Time", "endPlaceholder": "End Date Time"}, "fields": {"field": "Field", "add": "Add Field"}}, "sections": {"basic": "Basic Information", "fields": "Fields"}}, "list": {"empty": "No tasks found", "assignedTo": "Assigned to", "unassigned": "Unassigned", "dueDate": "Due date", "noDueDate": "No due date", "minutes": "min", "timeRange": "Time Range", "role": "Role", "fields": "Fields", "actions": {"edit": "Edit", "delete": "Delete", "view": "View Details"}}, "type": {"scheduled": "Scheduled", "repeating": "Repeating"}}, "common": {"actions": "Actions", "edit": "Edit", "total": "Total", "items": "items", "cancel": "Cancel", "create": "Create", "update": "Update", "save": "Save", "remove": "Remove", "created": "Created", "labels": "Labels", "createdAt": "Created", "updatedAt": "Updated", "error": "Error", "showMore": "Show more", "showLess": "Show less", "pagination": {"total": "Total {{total}} items"}, "minutes": "minutes", "hours": "hours", "days": {"in_number": {"0": "Sunday", "1": "Monday", "2": "Tuesday", "3": "Wednesday", "4": "Thursday", "5": "Friday", "6": "Saturday"}}, "reportType": {"options": {"1": "Activity Log", "2": "Alarms", "3": "Average Rotation", "4": "Branch Details", "5": "Checkpoint Activity", "6": "Checkpoint Battery", "7": "Exception", "8": "Exception Detailed", "9": "Forms", "10": "Geofence", "11": "GPS Heatmap", "12": "Missed Zone", "13": "Sign On Off", "14": "Tasks", "15": "Time and Rotation", "16": "Time on Zone", "17": "Unknown"}}, "reportFrequency": {"options": {"1": "Daily", "2": "Weekly", "3": "Monthly", "4": "Yearly", "5": "Two Days"}}, "name": "Name", "email": "Email", "phone": "Phone", "role": "Role", "timezone": "Timezone", "latitude": "Latitude", "longitude": "Longitude", "notAvailable": "Not Available", "viewImage": "View Image", "filters": "Filters"}, "analytics": {"title": "Analytics", "description": "View and analyze your organization's data", "comingSoon": "Analytics dashboard coming soon", "drawer": {"title": {"activityLog": "Filter Activity Log", "alarms": "Filter Alarms", "averageRotation": "Filter Average Rotation", "branchDetails": "Filter Branch Details", "checkpointActivity": "Filter Checkpoint Activity", "checkpointBattery": "Filter Checkpoint Battery", "exception": "Filter Exception", "exceptionDetailed": "Filter Exception Detailed", "forms": "Filter Forms", "geofence": "<PERSON>lter G<PERSON>ce", "gpsHeatmap": "Filter GPS Heatmap", "missedZone": "Filter Missed Zone", "signOnOff": "Filter Sign On/Off", "tasks": "Filter Tasks", "timeAndRotation": "Filter Time and Rotation", "timeOnZone": "Filter Time on Zone", "default": "Filter Analytics"}}, "form": {"dateRange": {"title": "Date Range"}, "startDate": {"label": "Start Date", "tooltip": "Select start date for filtering"}, "endDate": {"label": "End Date", "tooltip": "Select end date for filtering"}, "timeRange": {"title": "Time Range"}, "startTime": {"label": "Start Time", "tooltip": "Select start time for filtering"}, "endTime": {"label": "End Time", "tooltip": "Select end time for filtering"}, "branch": {"label": "Branch", "tooltip": "Select branch for filtering"}, "role": {"label": "Role", "tooltip": "Select the role for filtering"}, "user": {"label": "User", "tooltip": "Select the user for filtering"}, "userLabel": {"label": "User Labels", "tooltip": "Select user labels for filtering"}, "device": {"label": "<PERSON><PERSON>", "tooltip": "Select the device for filtering"}, "deviceLabel": {"label": "Device Labels", "tooltip": "Select device labels for filtering"}, "activity": {"label": "Activity", "tooltip": "Select the activity for filtering"}, "site": {"label": "Site", "tooltip": "Select the site for filtering"}, "siteLabel": {"label": "Site Labels", "tooltip": "Select site labels for filtering"}, "checkpoint": {"label": "Checkpoint", "tooltip": "Select the checkpoint for filtering"}, "checkpointLabel": {"label": "Checkpoint Labels", "tooltip": "Select checkpoint labels for filtering"}, "forms": {"label": "Forms", "tooltip": "Select the forms for filtering"}, "tasks": {"label": "Tasks", "tooltip": "Select the tasks for filtering"}, "rotationInterval": {"label": "Rotation Interval (Minutes)", "tooltip": "Enter rotation interval in minutes"}, "rotationMethod": {"label": "Rotation Method", "tooltip": "Select rotation method"}, "apply": "Apply Filters"}, "missedZoneLog": {"expectedVisits": "Expected", "actualVisits": "Actual", "missedVisits": "Missed", "completionRate": "Completion"}}, "activityLog": {"title": "Activity Log", "description": "View and manage activity logs", "noLogs": "No activity logs found", "photoAlt": "Activity photo", "viewPhoto": "View Photo", "filters": {"title": "Filter Activity Log", "button": "Filters"}}, "geofenceLog": {"title": "Geofence Log", "description": "View and manage geofence logs", "noLogs": "No geofence logs found", "logTitle": "Geofence Log", "device": "<PERSON><PERSON>", "location": "Location", "zone": "Zone", "geofence": "Geofence", "dateTime": "Date/Time", "activeTime": "Active Time", "id": "ID", "timezone": "Timezone", "gpsTracking": "GPS Tracking", "gpsInterval": "GPS Interval", "filters": {"title": "Filter Geofence Log", "button": "Filters"}}, "alarms": {"title": "Alarms", "description": "View and manage system alarms", "logTitle": "Alarm Log", "noLogs": "No alarm logs found", "filters": {"title": "Filter Alarms"}}}