import { Navigate, Route, RouteProps } from "react-router-dom";
import { ProtectedRoute } from "../../components/Auth/ProtectedRoute";
import { BranchRedirect } from "../../components/Auth/BranchRedirect.tsx";
import { PermissionGuard } from "../../components/Auth/PermissionGuard";
import { routeConfig } from "./routes.tsx";

// Return array of routes definition (no hooks here)
export const getAdminRoutes = (): React.ReactElement<RouteProps>[] => {
  return [
    <Route
      key="admin"
      path="/admin"
      element={<ProtectedRoute guard="admin" />}
    >
      <Route
        index
        element={<Navigate to="/admin/branch" replace />}
      />

      {/* Branch selection */}
      <Route
        path={routeConfig.admin.selectBranch.path}
        element={routeConfig.admin.selectBranch.component}
      />

      {/* Branch specific pages */}
      <Route path="/admin/branch/:branchCode">
        <Route
          index
          element={<BranchRedirect />}
        />
        
        <Route
          path={routeConfig.admin.dashboard.path}
          element={
            <PermissionGuard routeKey="dashboard">
              {routeConfig.admin.dashboard.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.branch.path}
          element={
            <PermissionGuard routeKey="branch">
              {routeConfig.admin.branch.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.roles.path}
          element={
            <PermissionGuard routeKey="roles">
              {routeConfig.admin.roles.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.user.path}
          element={
            <PermissionGuard routeKey="user">
              {routeConfig.admin.user.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.label.path}
          element={
            <PermissionGuard routeKey="label">
              {routeConfig.admin.label.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.scheduler.path}
          element={
            <PermissionGuard routeKey="scheduler">
              {routeConfig.admin.scheduler.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.alert.path}
          element={
            <PermissionGuard routeKey="alert">
              {routeConfig.admin.alert.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.device.path}
          element={
            <PermissionGuard routeKey="device">
              {routeConfig.admin.device.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.site.path}
          element={
            <PermissionGuard routeKey="site">
              {routeConfig.admin.site.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.checkpoint.path}
          element={
            <PermissionGuard routeKey="checkpoint">
              {routeConfig.admin.checkpoint.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.geofence.path}
          element={
            <PermissionGuard routeKey="geofence">
              {routeConfig.admin.geofence.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.forms.path}
          element={
            <PermissionGuard routeKey="forms">
              {routeConfig.admin.forms.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.formPicklist.path}
          element={
            <PermissionGuard routeKey="formPicklist">
              {routeConfig.admin.formPicklist.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.task.path}
          element={
            <PermissionGuard routeKey="task">
              {routeConfig.admin.task.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.activity.path}
          element={
            <PermissionGuard routeKey="activity">
              {routeConfig.admin.activity.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.beacon.path}
          element={
            <PermissionGuard routeKey="beacon">
              {routeConfig.admin.beacon.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.license.path}
          element={
            <PermissionGuard routeKey="license">
              {routeConfig.admin.license.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.activityLog.path}
          element={
            <PermissionGuard routeKey="activityLog">
              {routeConfig.admin.activityLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.alarmLog.path}
          element={
            <PermissionGuard routeKey="alarmLog">
              {routeConfig.admin.alarmLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.averageRotationLog.path}
          element={
            <PermissionGuard routeKey="averageRotationLog">
              {routeConfig.admin.averageRotationLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.checkpointActivity.path}
          element={
            <PermissionGuard routeKey="checkpointActivity">
              {routeConfig.admin.checkpointActivity.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.checkpointBatteryLog.path}
          element={
            <PermissionGuard routeKey="checkpointBatteryLog">
              {routeConfig.admin.checkpointBatteryLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.exceptionLog.path}
          element={
            <PermissionGuard routeKey="exceptionLog">
              {routeConfig.admin.exceptionLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.exceptionDetailedLog.path}
          element={
            <PermissionGuard routeKey="exceptionDetailedLog">
              {routeConfig.admin.exceptionDetailedLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.tasksLog.path}
          element={
            <PermissionGuard routeKey="tasksLog">
              {routeConfig.admin.tasksLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.formsLog.path}
          element={
            <PermissionGuard routeKey="formsLog">
              {routeConfig.admin.formsLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.analyticsGeofenceLog.path}
          element={
            <PermissionGuard routeKey="analyticsGeofenceLog">
              {routeConfig.admin.analyticsGeofenceLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.signInOutLog.path}
          element={
            <PermissionGuard routeKey="signInOutLog">
              {routeConfig.admin.signInOutLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.branchDetailsLog.path}
          element={
            <PermissionGuard routeKey="branchDetailsLog">
              {routeConfig.admin.branchDetailsLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.missedZoneLog.path}
          element={
            <PermissionGuard routeKey="missedZoneLog">
              {routeConfig.admin.missedZoneLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.gpsHeatmapLog.path}
          element={
            <PermissionGuard routeKey="gpsHeatmapLog">
              {routeConfig.admin.gpsHeatmapLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.timeRotationLog.path}
          element={
            <PermissionGuard routeKey="timeRotationLog">
              {routeConfig.admin.timeRotationLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.timeOnZoneLog.path}
          element={
            <PermissionGuard routeKey="timeOnZoneLog">
              {routeConfig.admin.timeOnZoneLog.component}
            </PermissionGuard>
          }
        />
        
        <Route
          path={routeConfig.admin.unknownData.path}
          element={
            <PermissionGuard routeKey="unknownData">
              {routeConfig.admin.unknownData.component}
            </PermissionGuard>
          }
        />
      </Route>
    </Route>
  ];
}; 