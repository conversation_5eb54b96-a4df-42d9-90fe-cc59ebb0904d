import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Modal, Select, Switch } from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../../store";
import { useAppSelector } from "../../../../store/hooks";
import updateGpsTrackingIntervalAdminSlice, { 
  fetchGpsTrackingIntervalAdmin,
  updateGpsTrackingIntervalAdmin
} from "../../../../store/slices/admin/updateGpsTrackingInterval.admin.slice";
import { useParams } from "react-router-dom";
import ErrorMessage from "../../../../components/Common/ErrorMessage/ErrorMessage";
import { useEffect } from "react";

const { Option } = Select;

// Interval options in seconds
const INTERVAL_OPTIONS = [
  { label: "30 seconds", value: 30 },
  { label: "1 minute", value: 60 },
  { label: "5 minutes", value: 300 },
  { label: "10 minutes", value: 600 },
  { label: "30 minutes", value: 1800 },
  { label: "60 minutes", value: 3600 }
];

const GpsTrackingIntervalModal = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  const { message } = App.useApp();

  const {
    open,
    loading,
    error,
    interval,
    active
  } = useAppSelector((state) => state.updateGpsTrackingIntervalAdmin);

  const { actions } = updateGpsTrackingIntervalAdminSlice;
  
  // Fetch GPS tracking interval settings when modal opens
  useEffect(() => {
    if (open && branchCode) {
      dispatch(fetchGpsTrackingIntervalAdmin(branchCode));
    }
  }, [open, branchCode, dispatch]);

  const handleSubmit = async () => {
    if (!branchCode) {
      message.error(t("device.form.branchRequired", "Branch is required"));
      return;
    }

    try {
      const result = await dispatch(updateGpsTrackingIntervalAdmin(branchCode));

      if (result.meta.requestStatus === "fulfilled") {
        message.success(t("device.gpsTracking.updateSuccess", "GPS tracking interval updated successfully"));
        dispatch(actions.close());
      } else if (result.meta.requestStatus === "rejected" && result.payload) {
        message.error(
          Array.isArray(result.payload) && result.payload.length > 0
            ? result.payload[0]
            : t("device.gpsTracking.updateFailed", "Failed to update GPS tracking interval")
        );
      }
    } catch {
      message.error(t("device.gpsTracking.updateFailed", "Failed to update GPS tracking interval"));
    }
  };

  return (
    <Modal
      title={t("device.gpsTracking.modalTitle", "GPS Tracking Interval Settings")}
      open={open}
      onCancel={() => dispatch(actions.close())}
      footer={[
        <Button
          key="cancel"
          onClick={() => dispatch(actions.close())}
          disabled={loading}
        >
          {t("common.cancel", "Cancel")}
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
          className="bg-blue-500 hover:bg-blue-600"
        >
          {t("common.save", "Save")}
        </Button>,
      ]}
      className="rounded-xl"
      maskClosable={!loading}
      closable={!loading}
      confirmLoading={loading}
      width={500}
      destroyOnClose
    >
      {error && <ErrorMessage message={error} />}
      
      <Form layout="vertical">
        <Form.Item
          label={t("device.gpsTracking.enableTracking", "Enable GPS Tracking")}
          className="mb-6"
        >
          <Switch
            checked={active}
            onChange={(checked) => dispatch(actions.setActive(checked))}
            disabled={loading}
          />
        </Form.Item>

        <Form.Item
          label={t("device.gpsTracking.interval", "Tracking Interval")}
          help={t("device.gpsTracking.intervalHelp", "Set how often devices should report their GPS position")}
        >
          <Select
            value={interval}
            onChange={(value) => dispatch(actions.setInterval(Number(value)))}
            disabled={loading || !active}
            placeholder={t("device.gpsTracking.intervalPlaceholder", "Select tracking interval")}
            style={{ width: '100%' }}
          >
            {INTERVAL_OPTIONS.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default GpsTrackingIntervalModal; 