import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import taskAdminMainApi from "../../services/mainApi/admin/task.admin.mainApi";
import { AdminTask } from "../../services/mainApi/admin/types/task.admin.mainApi.types";

interface SelectTaskProps {
  value?: string;
  onChange?: (
    value: string | null,
    task: AdminTask | null
  ) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  clearable?: boolean;
}

export default function SelectTask({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select task",
  clearable = true,
}: SelectTaskProps) {
  const [tasks, setTasks] = useState<AdminTask[]>([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);
  const { branchCode } = useParams();

  useEffect(() => {
    const fetchTasks = async () => {
      if (isFetching.current || !branchCode) return;

      try {
        isFetching.current = true;
        setLoading(true);
        const response =
          await taskAdminMainApi.getTasks(branchCode);
        setTasks(response.data || []);
      } catch (error) {
        console.error("Failed to fetch tasks:", error);
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchTasks();
  }, [branchCode]);

  const handleChange = (selectedValue: string | null) => {
    if (!onChange) return;

    if (!selectedValue) {
      onChange(null, null);
      return;
    }

    const selectedTask =
      tasks.find((task) => task.id === selectedValue) ||
      null;
    onChange(selectedValue, selectedTask);
  };

  return (
    <Select
      className={className}
      placeholder={placeholder}
      value={value}
      onChange={handleChange}
      disabled={disabled}
      loading={loading}
      allowClear={clearable}
      showSearch
      optionFilterProp="label"
      options={tasks.map((task) => ({
        value: task.id,
        label: task.task_name,
      }))}
    />
  );
}
