import {
  BaseResponse,
  BaseGetRequest,
} from "../../types/base.mainApi.types";
import { User } from "../../types/auth.mainApi.types.ts";
import { AdminRole } from "./roles.admin.mainApi.types.ts";
import { AdminCheckpoint } from "./checkpoint.admin.mainApi.types.ts";
import { AdminGeofence } from "./geofence.admin.mainApi.types.ts";
import { AdminDevice } from "./device.admin.mainApi.types.ts";
import { AdminBranch } from "./branch.admin.mainApi.types.ts";

export enum EAlertEvent {
  ACTIVITY_SUBMITTED = "1",
  TASK_SUBMITTED = "2",
  FORM_SUBMITTED = "3",
  ALARM = "4",
  CHECKPOINT_SCAN = "5",
  SIGN_ON = "6",
  SIGN_OUT = "7",
  GEOFENCES = "8",
}

export enum EAlertConditionType {
  USER = "1",
  ROLE = "2",
  DAY_OF_MONTH = "3",
  DAY_OF_WEEK = "4",
  HOURS = "5",
  MINUTES = "6",
  CHECKPOINT = "7",
  GEOFENCE_NAME = "8",
  GEOFENCE_ASSIGNED_ZONE = "9",
  DEVICE_NAME = "10",
}

export enum EAlertOperatorConditionType {
  EQUAL = "=",
  NOT_EQUAL = "!=",
}

export enum EAlertLogicalConditionType {
  AND = "AND",
  OR = "OR",
}

export enum EAlertAction {
  EMAIL = "1",
  SMS = "2",
  DASHBOARD = "3",
}

export interface AlertCondition {
  id: string;
  alert_id: string;
  alert_condition_type_id: EAlertConditionType;
  alert_operator_condition_type: EAlertOperatorConditionType;
  alert_condition_value_id: string;
  user_id: string | null;
  role_id: string | null;
  day_of_month: string | null;
  day_of_week: string | null;
  hours: string | null;
  minutes: string | null;
  checkpoint_id: string | null;
  geofence_id: string | null;
  zone_id: string | null;
  device_id: string | null;
  alert_condition_description: string;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
  alert_condition_type: {
    id: string;
    condition_name: string;
    condition_description: string | null;
    active: boolean;
    created_at: string;
    created_by: string | null;
    updated_at: string;
    updated_by: string | null;
  };
  user: User | null;
  role: AdminRole | null;
  checkpoint: AdminCheckpoint | null;
  geofence: AdminGeofence | null;
  device: AdminDevice | null;
}

export interface AlertRecipient {
  id: string;
  alert_id: string;
  recipient_type: string;
  recipient_contact: string;
  created_at: string;
  created_by: string;
}

export interface AlertAdmin {
  id: string;
  parent_branch_id: string;
  alert_action_id: string;
  alert_event_id: string;
  alert_logical_condition_type: EAlertLogicalConditionType;
  alert_name: string;
  alert_description: string;
  subject: string;
  message: string;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
  parent_branch: {
    id: string;
    parent_id: string;
    timezone_id: string;
    license_id: string;
    reseller_id: string | null;
    branch_code: string;
    branch_name: string;
    branch_description: string;
    branch_logo: string;
    branch_colour: string;
    gps_tracking_enabled: boolean;
    gps_interval: number;
    active: boolean;
    created_at: string;
    created_by: string | null;
    updated_at: string;
    updated_by: string | null;
  };
  alert_action: {
    id: string;
    action_name: string;
    action_description: string | null;
    active: boolean;
    created_at: string;
    created_by: string;
    updated_at: string;
    updated_by: string | null;
  };
  alert_event: {
    id: string;
    event_name: string;
    event_description: string | null;
    active: boolean;
    created_at: string;
    created_by: string;
    updated_at: string;
    updated_by: string | null;
  };
  conditions: AlertCondition[];
  recipients: AlertRecipient[];
  branches: AdminBranch[];
}

export type GetAlertListResponse = BaseResponse<
  AlertAdmin[]
>;

export type GetAlertDetailResponse =
  BaseResponse<AlertAdmin>;

export interface AlertConditionRequest {
  alert_condition_type_id: number;
  alert_operator_condition_type: string;
  alert_condition_value_id: number;
  alert_condition_description: string;
  active: boolean;
}

export interface AlertRecipientRequest {
  recipient_type: string;
  recipient_contact: string;
}

export interface CreateAlertRequest {
  alert_name: string;
  alert_description: string;
  alert_action_id: number;
  alert_event_id: number;
  alert_logical_condition_type: string;
  subject: string;
  message: string;
  conditions: AlertConditionRequest[];
  recipients: AlertRecipientRequest[];
  branch_ids: number[];
  active: boolean;
}

export type UpdateAlertRequest = CreateAlertRequest;

export type GetAlertRequest = BaseGetRequest;
// Additional query parameters specific to alerts, if any
