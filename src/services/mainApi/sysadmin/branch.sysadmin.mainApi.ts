import baseMain<PERSON>pi from "../base.mainApi";
import {
  BranchListParams,
  BranchListResponse,
  CreateBranchWithUserRequest,
  CreateBranchWithUserResponse,
  UpdateBranchRequest,
  UpdateBranchResponse,
} from "./types/branch.mainApi.types";

const branchSysAdminMainApi = {
  getList: async (params: BranchListParams = {}) => {
    const response =
      await baseMainApi.get<BranchListResponse>(
        "/web-api/sysadmin/branch",
        {
          params: {
            search: params.search,
            active: params.active,
            page: params.page || 1,
            limit: params.limit || 10,
            start_date: params.startDate,
            end_date: params.endDate,
            order_by: params.orderBy,
            order_direction: params.orderDirection,
          },
        }
      );
    return response.data;
  },

  createWithUser: async (
    data: CreateBranchWithUserRequest
  ) => {
    const response =
      await baseMainApi.post<CreateBranchWithUserResponse>(
        "/web-api/sysadmin/branch",
        {
          name: data.name,
          description: data.description,
          timezone: data.timezone,
          license: data.license,
          user: {
            name: data.user.name,
            email: data.user.email,
            password: data.user.password,
            confirm_password: data.user.confirm_password,
            phone: data.user.phone,
          },
        }
      );
    return response.data;
  },

  update: async (
    id: string | number,
    data: UpdateBranchRequest
  ) => {
    const response =
      await baseMainApi.put<UpdateBranchResponse>(
        `/web-api/sysadmin/branch/${id}`,
        {
          name: data.name,
          description: data.description,
          timezone: data.timezone,
          license: data.license,
          active: data.active,
          admin_name: data.admin_name,
          admin_email: data.admin_email,
          admin_phone: data.admin_phone,
          admin_password: data.admin_password,
          admin_confirm_password: data.admin_confirm_password,
        }
      );
    return response.data;
  },
};

export default branchSysAdminMainApi;
