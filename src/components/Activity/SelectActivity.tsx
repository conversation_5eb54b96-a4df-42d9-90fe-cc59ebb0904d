import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import { AdminActivity } from "../../services/mainApi/admin/types/activity.admin.mainApi.types";
import { useParams } from "react-router-dom";
import { activityAdminApi } from "../../services/mainApi/admin/activity.admin.mainApi.ts";

interface SelectActivityProps {
  value?: string;
  onChange?: (
    value: string,
    activity: AdminActivity | null
  ) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  clearable?: boolean;
}

export default function SelectActivity({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select activity",
  clearable = true,
}: SelectActivityProps) {
  const [activities, setActivities] = useState<
    AdminActivity[]
  >([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);
  const { branchCode } = useParams();

  useEffect(() => {
    const fetchActivities = async () => {
      if (isFetching.current || !branchCode) return;

      try {
        isFetching.current = true;
        setLoading(true);
        const response =
          await activityAdminApi.getActivities(branchCode);
        setActivities(response.data || []);
      } catch (error) {
        console.error("Failed to fetch activities:", error);
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchActivities();
  }, [branchCode]);

  const handleChange = (
    selectedValue: string | undefined
  ) => {
    if (!onChange) return;

    if (!selectedValue) {
      onChange(selectedValue || "", null);
      return;
    }

    const selectedActivity =
      activities.find(
        (activity) => activity.id === selectedValue
      ) || null;
    onChange(selectedValue, selectedActivity);
  };

  return (
    <Select
      className={className}
      placeholder={placeholder}
      value={value}
      onChange={handleChange}
      disabled={disabled}
      loading={loading}
      showSearch
      optionFilterProp="label"
      options={activities.map((activity) => ({
        value: activity.id,
        label: activity.activity_name,
      }))}
      allowClear={clearable}
    />
  );
}
