import baseMainApi from "../base.mainApi";
import { BaseGetRequest } from "../types/base.mainApi.types";
import {
  AdminCheckpointDetailResponse,
  AdminCheckpointListResponse,
  CreateUpdateCheckpointRequest,
} from "./types/checkpoint.admin.mainApi.types";

const checkpointAdminMainApi = {
  getCheckpoints: async (
    branchCode: string,
    params?: BaseGetRequest
  ) => {
    const response =
      await baseMainApi.get<AdminCheckpointListResponse>(
        `/web-api/admin/${branchCode}/checkpoint`,
        { params }
      );
    return response.data;
  },

  getCheckpointById: async (
    branchCode: string,
    id: string
  ) => {
    const response =
      await baseMainApi.get<AdminCheckpointDetailResponse>(
        `/web-api/admin/${branchCode}/checkpoint/${id}`
      );
    return response.data;
  },

  createCheckpoint: async (
    branchCode: string,
    data: CreateUpdateCheckpointRequest
  ) => {
    const response =
      await baseMainApi.post<AdminCheckpointDetailResponse>(
        `/web-api/admin/${branchCode}/checkpoint`,
        data
      );
    return response.data;
  },

  updateCheckpoint: async (
    branchCode: string,
    id: string,
    data: CreateUpdateCheckpointRequest
  ) => {
    const response =
      await baseMainApi.put<AdminCheckpointDetailResponse>(
        `/web-api/admin/${branchCode}/checkpoint/${id}`,
        data
      );
    return response.data;
  },
};

export default checkpointAdminMainApi;
