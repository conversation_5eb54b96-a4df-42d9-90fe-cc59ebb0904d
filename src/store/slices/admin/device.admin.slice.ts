import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import deviceAdminMainApi from "../../../services/mainApi/admin/device.admin.mainApi";
import { AdminDevice } from "../../../services/mainApi/admin/types/device.admin.mainApi.types";
import {
  BaseGetRequest,
  BasePaginationMeta,
} from "../../../services/mainApi/types/base.mainApi.types";

interface State {
  devices: AdminDevice[];
  loading: boolean;
  errorMessage: string | null;
  filter: {
    search: string | null;
    page: number | null;
    limit: number | null;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;
}

const initialState: State = {
  devices: [],
  loading: false,
  errorMessage: null,
  filter: {
    search: null,
    page: null,
    limit: null,
    orderBy: "created_at",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
  },
};

export const fetchDevicesAdmin = createAsyncThunk(
  "deviceAdmin/fetchDevices",
  async (
    branchCode: string,
    { getState, rejectWithValue }
  ) => {
    try {
      const state = getState() as RootState;
      const { filter } = state.deviceAdmin;
      const params: BaseGetRequest = {
        search: filter.search || undefined,
        page: filter.page || undefined,
        limit: filter.limit || undefined,
        order_by: filter.orderBy,
        order_direction: filter.orderDirection,
      };
      const response = await deviceAdminMainApi.getDevices(
        branchCode,
        params
      );
      return {
        data: response.data,
        meta: response.meta,
      };
    } catch {
      return rejectWithValue("Failed to fetch devices");
    }
  }
);

export const deleteDevice = createAsyncThunk(
  "deviceAdmin/deleteDevice",
  async (
    {
      branchCode,
      deviceId,
    }: { branchCode: string; deviceId: string },
    { rejectWithValue }
  ) => {
    try {
      await deviceAdminMainApi.deleteDevice(
        branchCode,
        deviceId
      );
      return deviceId;
    } catch {
      return rejectWithValue("Failed to delete device");
    }
  }
);

const deviceAdminSlice = createSlice({
  name: "deviceAdmin",
  initialState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch devices
      .addCase(fetchDevicesAdmin.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchDevicesAdmin.fulfilled,
        (state, action) => {
          state.loading = false;
          state.devices = action.payload.data || [];
          state.pagination = action.payload.meta;
        }
      )
      .addCase(
        fetchDevicesAdmin.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      )
      // Delete device
      .addCase(deleteDevice.fulfilled, (state, action) => {
        state.devices = state.devices.filter(
          (device) =>
            device.id.toString() !== action.payload
        );
        state.pagination.total =
          (state.pagination.total || 1) - 1;
      });
  },
});

export default deviceAdminSlice;
