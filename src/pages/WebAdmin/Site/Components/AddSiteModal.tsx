import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Divider,
  Form,
  Input,
  InputNumber,
  Modal,
  Select,
  Space,
  Switch,
  TimePicker,
  Tooltip,
} from "antd";
import {useTranslation} from "react-i18next";
import {useAppDispatch, useAppSelector,} from "../../../../store/hooks";
import {
  ClockCircleOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  MailOutlined,
  MinusCircleOutlined,
  PlusOutlined,
  TagsOutlined,
} from "@ant-design/icons";
import SelectTimezone from "../../../../components/Timezone/SelectTimezone";
import addSiteAdminSlice, {createSite, updateSite,} from "../../../../store/slices/admin/addSite.admin.slice";
import SelectLabel from "../../../../components/Label/SelectLabel";
import dayjs from "dayjs";
import {useParams} from "react-router-dom";
import ErrorMessage from "../../../../components/Common/ErrorMessage/ErrorMessage";

const {TextArea} = Input;

const AddSiteModal = () => {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const {branchCode} = useParams<{
    branchCode: string;
  }>();
  const {message} = App.useApp();

  const {
    isOpen,
    mode,
    selectedSite,
    loading,
    error,
    siteName,
    siteDescription,
    siteAddress,
    siteLatitude,
    siteLongitude,
    siteTimezone,
    siteIntervalActive,
    siteIntervalStartTime,
    siteIntervalEndTime,
    siteSubject,
    siteRecipients,
    siteLabels,
  } = useAppSelector((state) => state.addSiteAdmin);

  const handleCancel = () => {
    dispatch(addSiteAdminSlice.actions.close());
  };

  const handleSubmit = async () => {
    if (!branchCode) {
      console.error("Branch code is missing");
      return;
    }

    if (mode === "create") {
      const resultAction = await dispatch(createSite({branchCode}));
      if (createSite.fulfilled.match(resultAction) && !error) {
        message.success(t("site.modal.createSuccess", "Site created successfully"));
      } else if (createSite.rejected.match(resultAction)) {
        message.error(t("site.modal.createError", "Error creating site"));
      }
    } else if (mode === "edit" && selectedSite) {
      const resultAction = await dispatch(
        updateSite({
          branchCode,
          zoneId: selectedSite.id,
        })
      );
      if (updateSite.fulfilled.match(resultAction) && !error) {
        message.success(t("site.modal.editSuccess", "Site updated successfully"));
      } else if (updateSite.rejected.match(resultAction)) {
        message.error(t("site.modal.editError", "Error updating site"));
      }
    }
  };

  const modalTitle =
    mode === "create"
      ? t("site.modal.createTitle")
      : t("site.modal.editTitle");

  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <span className="text-lg font-medium">
            {modalTitle}
          </span>
        </div>
      }
      open={isOpen}
      onCancel={handleCancel}
      width={800}
      footer={[
        <Button
          key="cancel"
          onClick={handleCancel}
          disabled={loading}
          loading={loading}
        >
          {t("common.cancel")}
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleSubmit}
          loading={loading}
          className="bg-blue-500 hover:bg-blue-600"
        >
          {t("common.save")}
        </Button>,
      ]}
      className="site-modal"
      maskClosable={!loading}
      closable={!loading}
    >
      <Form
        layout="vertical"
        className="mt-4 space-y-6"
      >
        {/* Basic Information Section */}
        <Card
          className="shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg"
          title={
            <div className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
              <span className="text-base font-medium">
                {t(
                  "site.form.basicInfo",
                  "Basic Information"
                )}
              </span>
            </div>
          }
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              label={t("site.form.name")}
              required
              tooltip={t(
                "site.form.nameTooltip",
                "Enter a unique name for this site"
              )}
            >
              <Input
                value={siteName}
                onChange={(e) =>
                  dispatch(
                    addSiteAdminSlice.actions.setSiteName(
                      e.target.value
                    )
                  )
                }
                placeholder={t(
                  "site.form.namePlaceholder",
                  "Enter site name"
                )}
                disabled={loading}
              />
            </Form.Item>

            <Form.Item
              label={t("site.form.timezone")}
              required
              tooltip={t(
                "site.form.timezoneTooltip",
                "Select the timezone for this site"
              )}
            >
              <SelectTimezone
                value={siteTimezone || undefined}
                onChange={(value) =>
                  dispatch(
                    addSiteAdminSlice.actions.setSiteTimezone(
                      value
                    )
                  )
                }
                disabled={loading}
              />
            </Form.Item>
          </div>

          <Form.Item
            label={t(
              "site.form.description",
              "Description"
            )}
            tooltip={t(
              "site.form.descriptionTooltip",
              "Provide additional details about this site"
            )}
          >
            <TextArea
              rows={3}
              value={siteDescription}
              onChange={(e) =>
                dispatch(
                  addSiteAdminSlice.actions.setSiteDescription(
                    e.target.value
                  )
                )
              }
              placeholder={t(
                "site.form.descriptionPlaceholder",
                "Enter site description"
              )}
              disabled={loading}
            />
          </Form.Item>
        </Card>

        {/* Location Information Section */}
        <Card
          className="shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg"
          title={
            <div className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
              <EnvironmentOutlined/>
              <span className="text-base font-medium">
                {t(
                  "site.form.locationInfo",
                  "Location Information"
                )}
              </span>
            </div>
          }
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              label={t("site.form.latitude")}
              tooltip={t(
                "site.form.latitudeTooltip",
                "Enter the latitude coordinate"
              )}
            >
              <InputNumber
                className="w-full"
                value={siteLatitude}
                onChange={(value) =>
                  dispatch(
                    addSiteAdminSlice.actions.setSiteLatitude(
                      value || 0
                    )
                  )
                }
                placeholder="0.000000"
                disabled={loading}
                min={-90}
                max={90}
              />
            </Form.Item>

            <Form.Item
              label={t("site.form.longitude")}
              tooltip={t(
                "site.form.longitudeTooltip",
                "Enter the longitude coordinate"
              )}
            >
              <InputNumber
                className="w-full"
                value={siteLongitude}
                onChange={(value) =>
                  dispatch(
                    addSiteAdminSlice.actions.setSiteLongitude(
                      value || 0
                    )
                  )
                }
                placeholder="0.000000"
                disabled={loading}
                min={-180}
                max={180}
              />
            </Form.Item>
          </div>

          <Form.Item
            label={t("site.form.address")}
            required
            tooltip={t(
              "site.form.addressTooltip",
              "Enter the physical address of this site"
            )}
          >
            <TextArea
              rows={3}
              value={siteAddress}
              onChange={(e) =>
                dispatch(
                  addSiteAdminSlice.actions.setSiteAddress(
                    e.target.value
                  )
                )
              }
              placeholder={t(
                "site.form.addressPlaceholder",
                "Enter full address"
              )}
              disabled={loading}
            />
          </Form.Item>
        </Card>

        {/* Labels Section */}
        <Card
          className="shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg"
          title={
            <div className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
              <TagsOutlined/>
              <span className="text-base font-medium">
                {t("site.form.labelsSection", "Labels")}
              </span>
            </div>
          }
          bordered={false}
        >
          <Form.Item
            tooltip={t(
              "site.form.labelsTooltip",
              "Assign labels to categorize this site"
            )}
          >
            <SelectLabel
              mode="multiple"
              value={siteLabels}
              onChange={(labels) =>
                dispatch(
                  addSiteAdminSlice.actions.setSiteLabels(
                    labels as string[]
                  )
                )
              }
              placeholder={t(
                "site.form.labelsPlaceholder",
                "Select labels"
              )}
              disabled={loading}
            />
          </Form.Item>
        </Card>

        {/* Time Interval Settings */}
        <Card
          className="shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg"
          title={
            <div className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
              <ClockCircleOutlined/>
              <span className="text-base font-medium">
                {t(
                  "site.form.timeSettings",
                  "Time Interval Settings"
                )}
              </span>
            </div>
          }
        >
          <Form.Item
            label={
              <Space>
                {t(
                  "site.form.intervalActive",
                  "Enable Time Interval"
                )}
                <Tooltip
                  title={t(
                    "site.form.intervalTooltip",
                    "Enable to set specific operating hours for this site"
                  )}
                >
                  <InfoCircleOutlined className="text-gray-400"/>
                </Tooltip>
              </Space>
            }
          >
            <Switch
              checked={siteIntervalActive}
              onChange={(checked) =>
                dispatch(
                  addSiteAdminSlice.actions.setSiteIntervalActive(
                    checked
                  )
                )
              }
              disabled={loading}
            />
          </Form.Item>

          {siteIntervalActive && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                label={t(
                  "site.form.startTime",
                  "Start Time"
                )}
                required={siteIntervalActive}
              >
                <TimePicker
                  className="w-full"
                  format="HH:mm"
                  value={
                    siteIntervalStartTime
                      ? dayjs(
                        siteIntervalStartTime,
                        "HH:mm"
                      )
                      : null
                  }
                  onChange={(time) =>
                    dispatch(
                      addSiteAdminSlice.actions.setSiteIntervalStartTime(
                        time ? time.format("HH:mm") : ""
                      )
                    )
                  }
                  placeholder={t(
                    "site.form.startTimePlaceholder",
                    "Select start time"
                  )}
                  disabled={loading}
                />
              </Form.Item>

              <Form.Item
                label={t("site.form.endTime", "End Time")}
                required={siteIntervalActive}
              >
                <TimePicker
                  className="w-full"
                  format="HH:mm"
                  value={
                    siteIntervalEndTime
                      ? dayjs(siteIntervalEndTime, "HH:mm")
                      : null
                  }
                  onChange={(time) =>
                    dispatch(
                      addSiteAdminSlice.actions.setSiteIntervalEndTime(
                        time ? time.format("HH:mm") : ""
                      )
                    )
                  }
                  placeholder={t(
                    "site.form.endTimePlaceholder",
                    "Select end time"
                  )}
                  disabled={loading}
                />
              </Form.Item>
            </div>
          )}
        </Card>

        {/* Notification Settings */}
        <Card
          className="shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg"
          title={
            <div className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
              <MailOutlined/>
              <span className="text-base font-medium">
                {t(
                  "site.form.notificationSettings",
                  "Notification Settings"
                )}
              </span>
            </div>
          }
        >
          <Form.Item
            label={t("site.form.emailSubject")}
            tooltip={t(
              "site.form.subjectTooltip",
              "Subject line for notification emails"
            )}
            required={siteIntervalActive}
          >
            <Input
              value={siteSubject || ""}
              onChange={(e) =>
                dispatch(
                  addSiteAdminSlice.actions.setSiteSubject(
                    e.target.value
                  )
                )
              }
              placeholder={t(
                "site.form.subjectPlaceholder",
                "Enter email subject"
              )}
              disabled={loading}
            />
          </Form.Item>

          <Divider orientation="left">
            {t("site.form.recipients", "Recipients")}
          </Divider>

          {siteRecipients.map((recipient) => (
            <div
              key={recipient.rowId}
              className="flex items-start gap-2 mb-2"
            >
              <Form.Item
                className="w-1/3 mb-0"
                required={siteIntervalActive}
              >
                <Select
                  value={recipient.recipient_type}
                  onChange={(value) =>
                    dispatch(
                      addSiteAdminSlice.actions.setRecipientType(
                        {
                          rowId: recipient.rowId,
                          recipient_type: value,
                        }
                      )
                    )
                  }
                  disabled={loading}
                >
                  <Select.Option value="EMAIL">
                    {t("site.form.email")}
                  </Select.Option>
                  <Select.Option value="PHONE">
                    {t("site.form.phone")}
                  </Select.Option>
                </Select>
              </Form.Item>
              <Form.Item
                className="flex-1 mb-0"
                required={siteIntervalActive}
              >
                <Input
                  value={recipient.recipient_contact}
                  onChange={(e) =>
                    dispatch(
                      addSiteAdminSlice.actions.setRecipientContact(
                        {
                          rowId: recipient.rowId,
                          recipient_contact: e.target.value,
                        }
                      )
                    )
                  }
                  placeholder={
                    recipient.recipient_type === "EMAIL"
                      ? t(
                        "site.form.emailPlaceholder",
                        "Enter email address"
                      )
                      : t(
                        "site.form.phonePlaceholder",
                        "Enter phone number"
                      )
                  }
                  disabled={loading}
                />
              </Form.Item>
              <Button
                type="text"
                icon={<MinusCircleOutlined/>}
                onClick={() =>
                  dispatch(
                    addSiteAdminSlice.actions.removeRecipient(
                      recipient.rowId
                    )
                  )
                }
                className="text-red-500"
                disabled={loading}
              />
            </div>
          ))}

          <Button
            type="dashed"
            onClick={() =>
              dispatch(
                addSiteAdminSlice.actions.addRecipient()
              )
            }
            block
            icon={<PlusOutlined/>}
            className="mt-2"
            disabled={loading}
          >
            {t("site.form.addRecipient")}
          </Button>
        </Card>
      </Form>

      {error && (
        <div className="mt-4">
          <ErrorMessage message={error}/>
        </div>
      )}
    </Modal>
  );
};

export default AddSiteModal;
