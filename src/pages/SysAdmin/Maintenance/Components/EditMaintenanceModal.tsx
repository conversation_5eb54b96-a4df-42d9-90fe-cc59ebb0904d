import {
  App,
  Form,
  Input,
  Modal,
  Switch,
} from "antd";
import { AxiosError } from "axios";
import { useEffect } from "react";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks.ts";
import EditSettingListSysadminSlice, {
  updateMaintenance,
} from "../../../../store/slices/sysadmin/EditSetting.sysadmin.slice.ts";
import { fetchMaintenance } from "../../../../store/slices/sysadmin/settingList.sysadmin.slice.ts";


interface ApiErrorResponse {
  message: string;
}

export default function EditMaintenanceModal() {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const dispatch = useAppDispatch();

  // Get state from Redux
  const {
    open,
    mode,
    maintenanceEdit,
    loading,
    errorMessage,
    is_maintenance,
    description
  } = useAppSelector((state) => state.editSettingSysadmin);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // Update form values to Redux state

      if (mode === "update" && maintenanceEdit) {
        // Update existing maintenance
        dispatch(
          EditSettingListSysadminSlice.actions.setIsMaintenance(
            values.is_maintenance
          )
        )
        dispatch(
          EditSettingListSysadminSlice.actions.setDescription(
            values.description
          )
        )
        await dispatch(
          updateMaintenance(maintenanceEdit.id)
        ).unwrap();
        message.success(
          `Setting has been updated successfully`
        );
      }

      // Refresh license list
      dispatch(fetchMaintenance());

      form.resetFields();
      dispatch(EditSettingListSysadminSlice.actions.close());
    } catch (err) {
      if (
        err instanceof Error &&
        err.message !== "The validation of the form failed."
      ) {
        const error = err as AxiosError<ApiErrorResponse>;
        dispatch(
          EditSettingListSysadminSlice.actions.setError(
            error.response?.data?.message ||
              "Failed to save setting. Please try again."
          )
        );
      }
    }
  };

  // Handle close modal
  const handleClose = () => {
    dispatch(EditSettingListSysadminSlice.actions.close());
  };

  // Reset form when modal opens or license changes
  useEffect(() => {
    if (open) {
      // Reset form with license data if editing
      if (mode === "update" && maintenanceEdit) {
        form.setFieldsValue({
            is_maintenance: is_maintenance,
            description: description
        });
      } else {
        // Default values for new license
        form.setFieldsValue({
          is_maintenance: false,
          description: ''
        });
      }
    }
  }, [
    form,
    open,
    mode,
    maintenanceEdit,
    is_maintenance,
    description,
  ]);

  // Show error messages from Redux state
  useEffect(() => {
    if (errorMessage && errorMessage.length > 0) {
      errorMessage.forEach((err) => {
        message.error(err);
      });
    }
  }, [errorMessage, message]);

  return (
    <Modal
      title={
        <div className="text-lg font-semibold">
          {mode === "update"
            ? "Edit Setting"
            : "Add New License"}
        </div>
      }
      open={open}
      onCancel={handleClose}
      onOk={handleSubmit}
      width={700}
      className="rounded-xl"
      okButtonProps={{
        className: "bg-blue-500 hover:bg-blue-600 border-0",
      }}
      confirmLoading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        className="mt-4"
      >
        {/* License Information Section */}
        <div className="mb-6">

          <Form.Item
            name="is_maintenance"
            label="Maintenance"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          <Form.Item
                      name="description"
                      label="Setting Description"
                    >
                      <Input.TextArea
                        className="rounded-lg"
                        placeholder="Setting Description"
                        rows={4}
                      />
            </Form.Item>
        </div>
      </Form>
    </Modal>
  );
} 