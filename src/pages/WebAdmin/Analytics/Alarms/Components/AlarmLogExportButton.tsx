import React, { useState } from "react";
import { <PERSON><PERSON>, Dropdown, Toolt<PERSON> } from "antd";
import { FiDownload } from "react-icons/fi";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { alarmLogAdmin<PERSON>pi } from "../../../../../services/mainApi/admin/alarmLog.admin.mainApi";
import { AdminRole } from "../../../../../services/mainApi/admin/types/roles.admin.mainApi.types.ts";
import { AdminUser } from "../../../../../services/mainApi/admin/types/user.admin.mainApi.types.ts";
import { AdminLabel } from "../../../../../services/mainApi/admin/types/label.admin.mainApi.types.ts";
import { AdminDevice } from "../../../../../services/mainApi/admin/types/device.admin.mainApi.types.ts";
import { AdminBranch } from "../../../../../services/mainApi/admin/types/branch.admin.mainApi.types.ts";
import { AdminZone } from "../../../../../services/mainApi/admin/types/zone.admin.mainApi.types.ts";
import { AdminAlarmLogListParams } from "../../../../../services/mainApi/admin/types/alarmLog.admin.mainApi.types.ts";
import type { MenuProps } from "antd";

interface AlarmLogExportButtonProps {
  filterInfo: {
    role: AdminRole | null;
    user: AdminUser | null;
    userLabels: AdminLabel[];
    device: AdminDevice | null;
    deviceLabels: AdminLabel[];
    site: AdminZone | null;
    siteLabels: AdminLabel[];
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    selectedBranch: AdminBranch | null;
    branchName?: string;
  };
  className?: string;
  buttonText?: string;
  showPdf?: boolean;
  showSpreadsheet?: boolean;
}

type ExportFormat = "pdf" | "spreadsheet";

const AlarmLogExportButton: React.FC<
  AlarmLogExportButtonProps
> = ({
  filterInfo,
  className,
  buttonText,
  showPdf = true,
  showSpreadsheet = true,
}) => {
  const { t } = useTranslation();
  const [isDownloading, setIsDownloading] = useState(false);
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  const handleDownload = async (format: ExportFormat) => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const filterParams: AdminAlarmLogListParams = {
        start_date: filterInfo.startDate || undefined,
        end_date: filterInfo.endDate || undefined,
        start_time: filterInfo.startTime || undefined,
        end_time: filterInfo.endTime || undefined,
        branch_id:
          filterInfo.selectedBranch?.id || undefined,
        role_id: filterInfo.role?.id || undefined,
        user_id: filterInfo.user?.id || undefined,
        user_labels:
          filterInfo.userLabels?.map((label) => label.id) ||
          undefined,
        device_id: filterInfo.device?.id || undefined,
        device_labels:
          filterInfo.deviceLabels?.map(
            (label) => label.id
          ) || undefined,
        site_id: filterInfo.site?.id || undefined,
        site_labels:
          filterInfo.siteLabels?.map((label) => label.id) ||
          undefined,
      };

      const blob = await alarmLogAdminApi.exportAlarmLogs(
        branchCode,
        filterParams,
        format,
        "buffer"
      );

      if (format === "pdf") {
        // Create URL and open in new tab
        const url = URL.createObjectURL(blob);
        window.open(url, "_blank");

        // Delay URL revocation to ensure the new tab has time to load
        setTimeout(() => {
          URL.revokeObjectURL(url);
        }, 5000);
      } else {
        // Create URL and trigger download
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `alarm_logs_${new Date().toISOString().slice(0, 10)}.xlsx`;
        link.click();

        // Clean up
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error(`Error downloading ${format}:`, error);
    } finally {
      setIsDownloading(false);
    }
  };

  // If only one format is shown, render a simple button
  if (
    (showPdf && !showSpreadsheet) ||
    (!showPdf && showSpreadsheet)
  ) {
    const format = showPdf ? "pdf" : "spreadsheet";
    const tooltipText = showPdf
      ? t("alarms.downloadPdf", "Download as PDF")
      : t(
          "alarms.downloadSpreadsheet",
          "Download as Spreadsheet"
        );

    return (
      <Tooltip title={tooltipText}>
        <Button
          icon={<FiDownload />}
          loading={isDownloading}
          onClick={() => handleDownload(format)}
          size="large"
          className={className}
        >
          {buttonText ||
            (showPdf
              ? t("alarms.pdf", "PDF")
              : t("alarms.spreadsheet", "Spreadsheet"))}
        </Button>
      </Tooltip>
    );
  }

  // Otherwise, render a dropdown with both options
  const items: MenuProps["items"] = [
    {
      key: "pdf",
      label: t("alarms.exportPdf", "Export as PDF"),
      onClick: () => handleDownload("pdf"),
      disabled: !showPdf,
    },
    {
      key: "spreadsheet",
      label: t(
        "alarms.exportSpreadsheet",
        "Export as Spreadsheet"
      ),
      onClick: () => handleDownload("spreadsheet"),
      disabled: !showSpreadsheet,
    },
  ];

  return (
    <Dropdown
      menu={{ items }}
      disabled={isDownloading}
    >
      <Button
        icon={<FiDownload />}
        loading={isDownloading}
        size="large"
        className={className}
      >
        {buttonText || t("alarms.export", "Export")}
      </Button>
    </Dropdown>
  );
};

export default AlarmLogExportButton;
