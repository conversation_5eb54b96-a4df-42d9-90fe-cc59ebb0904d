.dashboard-tabs .ant-tabs-nav {
  margin-bottom: 16px;
  background: white;
  padding: 0 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.dark .dashboard-tabs .ant-tabs-nav {
  background: #1f2937;
}

.dashboard-tabs .ant-tabs-tab {
  padding: 12px 0;
  font-weight: 600;
}

/* User Activity List Styles */
.user-activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.user-activity-list::-webkit-scrollbar {
  width: 4px;
}

.user-activity-list::-webkit-scrollbar-track {
  background: transparent;
}

.user-activity-list::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
}

.dark .user-activity-list::-webkit-scrollbar-thumb {
  background: #475569;
}

.user-activity-list .ant-list-item {
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
  margin-bottom: 8px !important;
}

.dark .user-activity-list .ant-list-item {
  border-color: #374151;
}

.user-activity-list .ant-list-item:hover {
  transform: translateX(-2px);
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05) !important;
}

.dark .user-activity-list .ant-list-item:hover {
  border-color: #60a5fa;
  background: rgba(96, 165, 250, 0.05) !important;
}

.map-container {
  height: 100%;
  width: 100%;
  border-radius: 8px;
}

.dashboard-map-container {
  height: 100%;
  width: 100%;
  border-radius: 8px;
}

.leaflet-container {
  height: 100%;
  width: 100%;
  border-radius: 8px;
}

/* Hapus filter untuk dark mode karena sudah menggunakan CARTO dark theme */
.dark .leaflet-tile {
  filter: none;
}

.dark .leaflet-container {
  background: #1f2937;
}

.dark .leaflet-control-attribution {
  background: rgba(31, 41, 55, 0.8) !important;
  color: #d1d5db !important;
}

.dark .leaflet-control-zoom a {
  background: #374151 !important;
  color: #d1d5db !important;
  border-color: #4b5563 !important;
}

.dark .leaflet-control-zoom a:hover {
  background: #4b5563 !important;
}
