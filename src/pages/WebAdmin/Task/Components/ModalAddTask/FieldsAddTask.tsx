import { Divider, Button, Select, Input, Form } from "antd";
import { t } from "i18next";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks.ts";
import addTaskAdminSlice from "../../../../../store/slices/admin/addTask.admin.slice.ts";
import { FieldType } from "../../../../../types/FieldType.enum.ts";

const FieldsAddTask = () => {
  const dispatch = useAppDispatch();
  const modalAdd = useAppSelector(
    (state) => state.addTaskAdmin
  );
  const actions = addTaskAdminSlice.actions;

  const disableAddField = () => {
    return modalAdd.fields.length >= 10;
  };

  return (
    <div className="space-y-4">
      <Divider
        orientation="left"
        className="text-gray-500 dark:text-gray-400"
      >
        {t("task.modal.sections.fields")}
      </Divider>

      {modalAdd.fields.map((field, index) => (
        <div
          key={field.rowId}
          className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg space-y-4"
        >
          <div className="flex justify-between items-center mb-2">
            <span className="font-medium">
              {t("task.modal.form.fields.field")} #
              {index + 1}
            </span>
            <Button
              danger
              onClick={() =>
                dispatch(actions.removeField(field.rowId))
              }
              size="small"
            >
              {t("common.remove")}
            </Button>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              label={t(
                "task.modal.form.fields.type",
                "Field Type"
              )}
              required
              className="mb-0"
            >
              <Select
                placeholder={t(
                  "task.modal.form.fields.typePlaceholder",
                  "Select field type"
                )}
                className="rounded-lg w-full"
                options={[
                  {
                    value: FieldType.INPUT,
                    label: t(
                      "task.modal.form.fields.type.input",
                      "Text Input"
                    ),
                  },
                  {
                    value: FieldType.TEXT,
                    label: t(
                      "task.modal.form.fields.type.text",
                      "Text Area"
                    ),
                  },
                  {
                    value: FieldType.CHECKBOX,
                    label: t(
                      "task.modal.form.fields.type.checkbox",
                      "Checkbox"
                    ),
                  },
                  {
                    value: FieldType.IMAGE,
                    label: t(
                      "task.modal.form.fields.type.image",
                      "Image"
                    ),
                  },
                ]}
                onChange={(value) =>
                  dispatch(
                    actions.setFieldType({
                      rowId: field.rowId,
                      field_type_id: value,
                    })
                  )
                }
                value={field.field_type_id}
              />
            </Form.Item>

            <Form.Item
              label={t(
                "task.modal.form.fields.name",
                "Field Name"
              )}
              required
              rules={[
                {
                  required: true,
                  message: t(
                    "task.modal.form.fields.nameRequired",
                    "Please enter field name"
                  ),
                },
              ]}
              className="mb-0"
            >
              <Input
                placeholder={t(
                  "task.modal.form.fields.namePlaceholder",
                  "Enter field name"
                )}
                onChange={(e) =>
                  dispatch(
                    actions.setFieldName({
                      rowId: field.rowId,
                      task_field_name: e.target.value,
                    })
                  )
                }
                value={field.task_field_name || ""}
              />
            </Form.Item>
          </div>
        </div>
      ))}
      <Button
        type="dashed"
        onClick={() => dispatch(actions.addField())}
        disabled={disableAddField()}
        className="w-full"
        icon={<span className="mr-2">+</span>}
      >
        {t("task.modal.form.fields.add")}
      </Button>
    </div>
  );
};

export default FieldsAddTask;
