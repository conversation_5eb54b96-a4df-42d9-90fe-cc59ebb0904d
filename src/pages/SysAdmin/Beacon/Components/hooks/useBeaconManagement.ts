import { useCallback, useEffect } from "react";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks.ts";
import {
  fetchBeacons,
  setSearchQuery,
  setActiveFilter,
  setSort,
  setPage,
  selectBeacons,
  selectBeaconLoading,
  selectBeaconError,
  selectBeaconPagination,
  selectBeaconFilters,
} from "../../../../../store/slices/sysadmin/beaconList.sysadmin.slice.ts";
import {
  type SortField,
  type SortOrder,
} from "../../../../../store/slices/sysadmin/beaconList.sysadmin.slice.ts";

export const useBeaconManagement = () => {
  const dispatch = useAppDispatch();

  // Selectors
  const beacons = useAppSelector(selectBeacons);
  const loading = useAppSelector(selectBeaconLoading);
  const error = useAppSelector(selectBeaconError);
  const pagination = useAppSelector(selectBeaconPagination);
  const filters = useAppSelector(selectBeaconFilters);

  // Effects
  useEffect(() => {
    dispatch(fetchBeacons());
  }, [
    dispatch,
    pagination.current,
    filters.searchQuery,
    filters.sortField,
    filters.sortOrder,
    filters.active,
  ]);

  // Callbacks
  const handleSearch = useCallback(
    (query: string) => {
      dispatch(setSearchQuery(query));
    },
    [dispatch]
  );

  const handleActiveFilter = useCallback(
    (active: boolean | undefined) => {
      dispatch(setActiveFilter(active));
    },
    [dispatch]
  );

  const handleSort = useCallback(
    (field: SortField, order: SortOrder) => {
      dispatch(setSort({ field, order }));
    },
    [dispatch]
  );

  const handlePageChange = useCallback(
    (page: number) => {
      dispatch(setPage(page));
    },
    [dispatch]
  );

  return {
    // State
    beacons,
    loading,
    error,
    pagination,
    filters,

    // Actions
    handleSearch,
    handleActiveFilter,
    handleSort,
    handlePageChange,
  };
}; 