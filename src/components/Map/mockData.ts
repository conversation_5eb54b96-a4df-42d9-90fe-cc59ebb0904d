// Mockup data untuk peta Indonesia
export const mockGeofences: {
  id: string;
  geofence: [number, number][];
}[] = [
  {
    id: "jakarta-area",
    geofence: [
      [-6.1754, 106.8272], // Jakarta Utara
      [-6.2088, 106.8456], // Jakarta Pusat
      [-6.2382, 106.8255], // Jakarta Barat
      [-6.2697, 106.8281], // Jakarta Selatan
      [-6.2251, 106.9005], // Jakarta Timur
      [-6.1754, 106.8272], // Closing point
    ],
  },
  {
    id: "bandung-area",
    geofence: [
      [-6.8875, 107.6048],
      [-6.9147, 107.6098],
      [-6.9334, 107.6358],
      [-6.9175, 107.6571],
      [-6.8922, 107.6398],
      [-6.8875, 107.6048],
    ],
  },
  {
    id: "surabaya-area",
    geofence: [
      [-7.2575, 112.7521],
      [-7.2754, 112.7514],
      [-7.2891, 112.7684],
      [-7.2754, 112.7891],
      [-7.2575, 112.7821],
      [-7.2575, 112.7521],
    ],
  },
];

export const mockMarkers: {
  id: string;
  position: [number, number];
  title: string;
  description: string;
  date: string;
}[] = [
  {
    id: "jakarta-monas",
    position: [-6.2088, 106.8456],
    title: "Jakarta Pusat",
    description: "Pusat Kota Jakarta - Monumen Nasional",
    date: "2024-03-20T08:00:00.000Z",
  },
  {
    id: "bandung-alun",
    position: [-6.9175, 107.6191],
    title: "Bandung",
    description: "Kota Bandung - Alun-alun",
    date: "2024-03-20T09:30:00.000Z",
  },
  {
    id: "surabaya-tugu",
    position: [-7.2575, 112.7521],
    title: "Surabaya",
    description: "Kota Surabaya - Tugu Pahlawan",
    date: "2024-03-20T10:15:00.000Z",
  },
  {
    id: "padang-pantai",
    position: [-0.9433, 100.4172],
    title: "Padang",
    description: "Kota Padang - Pantai Padang",
    date: "2024-03-20T11:00:00.000Z",
  },
  {
    id: "palembang-ampera",
    position: [-2.9714, 104.7461],
    title: "Palembang",
    description: "Kota Palembang - Jembatan Ampera",
    date: "2024-03-20T12:30:00.000Z",
  },
]; 