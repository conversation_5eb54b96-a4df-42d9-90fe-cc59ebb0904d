import baseMainApi from "../base.mainApi";
import {
  AdminTimeOnZoneListParams,
  AdminTimeOnZoneListResponse,
} from "./types/timeOnZone.admin.mainApi.types";

/**
 * Time On Zone Admin API service
 */
export const timeOnZoneAdminMainApi = {
  /**
   * Get time on zone data
   * @param branchCode - Branch code
   * @param params - Query parameters
   * @returns Time on zone data response
   */
  getTimeOnZone: async (
    branchCode: string,
    params?: AdminTimeOnZoneListParams
  ) => {
    const response = await baseMainApi.get<AdminTimeOnZoneListResponse>(
      `/web-api/admin/${branchCode}/time-on-zone`,
      { params }
    );
    return response.data;
  },

  /**
   * Export time on zone data
   * @param branchCode - Branch code
   * @param params - Query parameters
   * @param format - Export format (pdf or spreadsheet)
   * @param saveType - Save type (link or buffer)
   * @returns Exported data (Blob or link)
   */
  exportTimeOnZone: async (
    branchCode: string,
    params: AdminTimeOnZoneListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/time-on-zone/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
};
