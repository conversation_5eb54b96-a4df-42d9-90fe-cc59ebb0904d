import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import formAdminMainApi from "../../services/mainApi/admin/form.admin.mainApi";
import { AdminForm } from "../../services/mainApi/admin/types/form.admin.mainApi.types";
import { useParams } from "react-router-dom";

interface SelectFormProps {
  value?: string;
  onChange?: (
    value: string,
    form: AdminForm | null
  ) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  clearable?: boolean;
}

export default function SelectForm({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select form",
  clearable = true,
}: SelectFormProps) {
  const [forms, setForms] = useState<AdminForm[]>([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);
  const { branchCode } = useParams();

  useEffect(() => {
    const fetchForms = async () => {
      if (isFetching.current) return;

      try {
        isFetching.current = true;
        setLoading(true);

        const response = await formAdminMainApi.getForms(
          branchCode || ""
        );

        setForms(response.data || []);
      } catch (error) {
        console.error("Error fetching forms:", error);
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchForms();
  }, [branchCode]);

  const handleChange = (
    selectedValue: string | undefined
  ) => {
    if (!onChange) return;

    if (!selectedValue) {
      onChange(selectedValue || "", null);
      return;
    }

    const selectedForm =
      forms.find((form) => form.id === selectedValue) ||
      null;
    onChange(selectedValue, selectedForm);
  };

  return (
    <Select
      value={value}
      onChange={handleChange}
      className={className}
      disabled={disabled}
      loading={loading}
      showSearch
      optionFilterProp="label"
      placeholder={placeholder}
      allowClear={clearable}
      options={forms.map((form) => ({
        value: form.id,
        label: form.form_name,
      }))}
    />
  );
}
