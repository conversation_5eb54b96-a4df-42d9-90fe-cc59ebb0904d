import { BaseResponse } from "../../types/base.mainApi.types";
import { AdminRole } from "./roles.admin.mainApi.types";
import { AdminBranch } from "./branch.admin.mainApi.types";
import { FieldType as FieldTypeEnum } from "../../../../types/FieldType.enum";

interface FieldType {
  id: FieldTypeEnum;
  field_type_name: string;
  field_type_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

interface TaskField {
  id: string;
  task_id: string;
  field_type_id: FieldTypeEnum;
  task_field_name: string;
  task_field_description: string | null;
  order: number;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
  field_type: FieldType;
}

interface AdminUser {
  id: string;
  parent_branch_id: string;
  role_id: string;
  name: string;
  email: string;
  phone: string;
  system_access: boolean;
  web_access: boolean;
  mobile_access: boolean;
  active: boolean;
  created_at: string;
  updated_at: string;
}

interface AdminCheckpoint {
  id: string;
  branch_id: string;
  parent_branch_id: string;
  checkpoint_type_id: string;
  zone_id: string;
  beacon_id: string | null;
  geofence_id: string | null;
  checkpoint_name: string;
  checkpoint_description: string | null;
  major_value: string | null;
  minor_value: string | null;
  serial_number_hex: string;
  serial_number_dec: string;
  serial_number_second_hex: string | null;
  serial_number_second_dec: string | null;
  latitude: number;
  longitude: number;
  monday: boolean | null;
  monday_count: number | null;
  tuesday: boolean | null;
  tuesday_count: number | null;
  wednesday: boolean | null;
  wednesday_count: number | null;
  thursday: boolean | null;
  thursday_count: number | null;
  friday: boolean | null;
  friday_count: number | null;
  saturday: boolean | null;
  saturday_count: number | null;
  sunday: boolean | null;
  sunday_count: number | null;
  rotation_interval: number | null;
  warning_notification: string | null;
  latest_check_time: string | null;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
}

export interface AdminTask {
  id: string;
  branch_id: string;
  parent_branch_id: string;
  check_point_id: string | null;
  role_id: string | null;
  user_id: string | null;
  task_type: "SCHEDULED" | "REPEATING";
  task_name: string;
  task_description: string | null;
  start_time: string;
  end_time: string;
  allowed_time: number;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
  fields: TaskField[];
  branch: AdminBranch;
  parent_branch: AdminBranch;
  role: AdminRole | null;
  checkpoint: AdminCheckpoint | null;
  user: AdminUser;
}

interface TaskFieldRequest {
  field_type_id: FieldTypeEnum;
  task_field_name: string;
  task_field_description: string | null;
  active: boolean;
}

export interface CreateTaskRequest {
  task_name: string;
  task_type: "SCHEDULED" | "REPEATING";
  task_description: string | null;
  start_date_time: string;
  end_date_time: string;
  allowed_time: number;
  fields: TaskFieldRequest[];
  role_id: string | null;
  user_id: string | null;
  checkpoint_id: string | null;
}

export interface UpdateTaskRequest
  extends CreateTaskRequest {
  active: boolean;
}

export type AdminTaskListResponse = BaseResponse<
  AdminTask[]
>;
