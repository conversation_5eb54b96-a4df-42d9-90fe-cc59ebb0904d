import {useCallback} from "react";
import {Button, Empty, Pagination} from "antd";
import {FiAlertTriangle, FiCamera, FiClock, FiEdit, FiMapPin, FiMessageSquare,} from "react-icons/fi";
import {useTranslation} from "react-i18next";
import {useAppDispatch, useAppSelector,} from "../../../../store/hooks.ts";
import {AdminActivity} from "../../../../services/mainApi/admin/types/activity.admin.mainApi.types.ts";
import addActivityAdminSlice from "../../../../store/slices/admin/addActivity.admin.slice.ts";
import activityAdminSlice from "../../../../store/slices/admin/activity.admin.slice.ts";
import dayjs from "dayjs";

const ActivityList = () => {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const {activities, loading, pagination, filter} =
    useAppSelector((state) => state.activityAdmin);

  const handleEdit = useCallback(
    (activity: AdminActivity) => {
      dispatch(
        addActivityAdminSlice.actions.openEdit(activity)
      );
    },
    [dispatch]
  );

  const handlePageChange = useCallback(
    (page: number, pageSize: number) => {
      dispatch(
        activityAdminSlice.actions.setFilter({
          page,
          limit: pageSize,
        })
      );
    },
    [dispatch]
  );

  // Check loading state first
  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8 flex justify-center items-center">
        <div className="text-gray-500 dark:text-gray-400">{t("common.loading")}</div>
      </div>
    );
  }

  if (!activities.length) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8">
        <Empty
          description={t("activity.list.empty")}
          className="dark:text-gray-400"
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {activities.map((activity) => (
          <div
            key={activity.id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden border border-gray-100 dark:border-gray-700 h-full"
          >
            <div className="p-4">
              {/* Header Section */}
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 line-clamp-1">
                      {activity.activity_name}
                    </h3>
                    {!activity.active && (
                      <div
                        className="flex items-center text-xs px-2 py-0.5 rounded-full bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 gap-1">
                        <FiAlertTriangle className="text-xs"/>
                        {t(
                          "activity.table.status.inactive"
                        )}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <FiClock className="text-xs"/>
                      {t("common.createdAt")}:{" "}
                      {dayjs(activity.created_at).format(
                        "DD MMM YYYY"
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mt-2">
                    {activity.activity_description ||
                      t("activity.noDescription")}
                  </p>
                </div>
                <Button
                  type="text"
                  icon={<FiEdit className="text-sm"/>}
                  onClick={() => handleEdit(activity)}
                  className="text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400"
                />
              </div>

              {/* Requirements Section */}
              <div className="flex flex-wrap items-center gap-2 mb-3">
                {activity.gps_required && (
                  <div
                    className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium gap-1 bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400">
                    <FiMapPin className="text-xs"/>
                    {t("activity.form.gpsRequired")}
                  </div>
                )}
                {activity.photo_required && (
                  <div
                    className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium gap-1 bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400">
                    <FiCamera className="text-xs"/>
                    {t("activity.form.photoRequired")}
                  </div>
                )}
                {activity.comment_required && (
                  <div
                    className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium gap-1 bg-purple-50 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400">
                    <FiMessageSquare className="text-xs"/>
                    {t("activity.form.commentRequired")}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <Pagination
          current={filter.page || 1}
          pageSize={filter.limit || 10}
          total={pagination.total}
          onChange={handlePageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              {t("common.pagination.total", {total})}
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

export default ActivityList;
