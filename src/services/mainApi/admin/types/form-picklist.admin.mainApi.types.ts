import {
  BaseGetRequest,
  BaseResponse,
} from "../../types/base.mainApi.types";
import { AdminBranch } from "./branch.admin.mainApi.types";

export type AdminFormPicklistGetRequest = BaseGetRequest

export interface AdminFormPicklistOption {
  id: string;
  form_picklist_id: string;
  form_picklist_option_name: string;
  form_picklist_option_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

export interface AdminFormPicklist {
  id: string;
  branch_id: string;
  form_picklist_name: string;
  form_picklist_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
  options: AdminFormPicklistOption[];
  branch: AdminBranch;
}

export type AdminFormPicklistListResponse = BaseResponse<
  AdminFormPicklist[]
>;
