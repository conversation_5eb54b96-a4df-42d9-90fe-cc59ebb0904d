import {
  BaseResponse,
  BaseGetRequest,
} from "../../types/base.mainApi.types";
import { Branch } from "../../sysadmin/types/branch.mainApi.types";

export interface FormPicklistOption {
  id: string;
  form_picklist_id: string;
  form_picklist_option_name: string;
  form_picklist_option_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
}

export interface AdminFormPicklist {
  id: string;
  branch_id: string;
  form_picklist_name: string;
  form_picklist_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
  options: FormPicklistOption[];
  branch: Branch;
}

export type AdminFormPicklistListResponse = BaseResponse<
  AdminFormPicklist[]
>;

export interface GetFormPicklistRequest
  extends BaseGetRequest {
  ignore_active_status?: boolean;
}

export interface CreateFormPicklistOptionRequest {
  form_picklist_option_name: string;
  form_picklist_option_description: string | null;
  active: boolean;
}

export interface CreateFormPicklistRequest {
  form_picklist_name: string;
  form_picklist_description: string | null;
  options: CreateFormPicklistOptionRequest[];
  active: boolean;
}

export type UpdateFormPicklistRequest =
  CreateFormPicklistRequest;
