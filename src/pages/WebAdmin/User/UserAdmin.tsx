import { Button, Input, Select } from "antd";
import WebAdminLayout from "../../../components/Layout/WebAdminLayout";
import {
  PlusOutlined,
  SearchOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
} from "@ant-design/icons";
import { <PERSON>d<PERSON><PERSON> } from "react-icons/md";
import PageHeader from "../../../components/Admin/Common/PageHeader";
import UserGrid from "./Components/UserGrid.tsx";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../store/hooks";
import {
  fetchUsers,
  setFilter,
} from "../../../store/slices/admin/user.admin.slice";
import { useCallback, useRef } from "react";
import { useParams } from "react-router-dom";
import _ from "lodash";
import ModalAddUser from "./Components/ModalAddUser/ModalAddUser.tsx";
import addUserAdminSlice from "../../../store/slices/admin/addUser.admin.slice.ts";
import { useTranslation } from "react-i18next";

const { Option } = Select;

const UserAdmin = () => {
  const dispatch = useAppDispatch();
  const { filter } = useAppSelector(
    (state) => state.userAdmin
  );
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const { t } = useTranslation();
  const debouncedFetchRef = useRef(
    _.debounce(async () => {
      fetch();
    }, 500)
  );

  const fetch = useCallback(async () => {
    await dispatch(fetchUsers(branchCode || ""));
  }, [branchCode, dispatch]);

  const handleSearch = (value: string) => {
    dispatch(setFilter({ search: value || null }));
    debouncedFetchRef.current();
  };

  const handleSort = (value: string) => {
    dispatch(setFilter({ orderBy: value }));
    fetch();
  };

  const toggleSortDirection = () => {
    dispatch(
      setFilter({
        orderDirection:
          filter.orderDirection === "ASC" ? "DESC" : "ASC",
      })
    );
    fetch();
  };

  const onAdd = useCallback(() => {
    dispatch(addUserAdminSlice.actions.open());
  }, [dispatch]);

  return (
    <WebAdminLayout activePage="users">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdPerson />}
            title={t("users.title")}
            description={t("users.description")}
            actions={[
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                className="bg-blue-500 hover:bg-blue-600"
                onClick={onAdd}
              >
                {t("users.addButton")}
              </Button>,
            ]}
          />
          {/* Filters Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder={t("users.search.placeholder")}
                prefix={<SearchOutlined />}
                size="large"
                className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                onChange={(e) =>
                  handleSearch(e.target.value)
                }
                value={filter.search || ""}
              />
              <div className="flex gap-2">
                <Select
                  value={filter.orderBy}
                  size="large"
                  style={{ minWidth: 120 }}
                  onChange={handleSort}
                  className="dark:bg-gray-700"
                >
                  <Option value="created_at">
                    {t(
                      "users.search.sortOptions.createdAt"
                    )}
                  </Option>
                  <Option value="name">
                    {t("users.search.sortOptions.name")}
                  </Option>
                  <Option value="email">
                    {t("users.search.sortOptions.email")}
                  </Option>
                </Select>
                <Button
                  icon={
                    filter.orderDirection === "ASC" ? (
                      <SortAscendingOutlined />
                    ) : (
                      <SortDescendingOutlined />
                    )
                  }
                  size="large"
                  onClick={toggleSortDirection}
                  className="dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100 dark:hover:bg-gray-600"
                >
                  {filter.orderDirection === "ASC"
                    ? t("users.search.sortDirection.asc")
                    : t("users.search.sortDirection.desc")}
                </Button>
              </div>
            </div>
          </div>

          {/* Users Grid */}
          <UserGrid />
        </div>
      </div>
      {/* Modals */}
      <ModalAddUser />
      {/* <ModalViewUser /> */}
    </WebAdminLayout>
  );
};

export default UserAdmin;
