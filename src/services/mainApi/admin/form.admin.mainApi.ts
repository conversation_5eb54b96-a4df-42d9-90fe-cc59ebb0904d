import baseMainApi from "../base.mainApi";
import {
  AdminFormListResponse,
  GetFormRequest,
  CreateFormRequest,
  UpdateFormRequest,
} from "./types/form.admin.mainApi.types";

const formAdminMainApi = {
  getForms: async (
    branchCode: string,
    params: GetFormRequest = {}
  ) => {
    const response =
      await baseMainApi.get<AdminFormListResponse>(
        `/web-api/admin/${branchCode}/form/form`,
        { params }
      );
    return response.data;
  },
  createForm: async (
    branchCode: string,
    data: CreateFormRequest
  ) => {
    const response =
      await baseMainApi.post<AdminFormListResponse>(
        `/web-api/admin/${branchCode}/form/form`,
        data
      );
    return response.data;
  },
  updateForm: async (
    branchCode: string,
    id: string,
    data: UpdateFormRequest
  ) => {
    const response =
      await baseMainApi.put<AdminFormListResponse>(
        `/web-api/admin/${branchCode}/form/form/${id}`,
        data
      );
    return response.data;
  },
  getFormById: async (branchCode: string, id: string) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/form/form/${id}`
    );
    return response.data;
  },
  deleteForm: async (branchCode: string, id: string) => {
    const response = await baseMainApi.delete(
      `/web-api/admin/${branchCode}/form/form/${id}`
    );
    return response.data;
  },
};

export default formAdminMainApi;
