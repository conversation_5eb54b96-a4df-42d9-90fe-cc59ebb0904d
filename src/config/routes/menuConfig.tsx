import { MenuProps } from "antd";
import React from "react";
import {
  MdDashboard, // Dashboard
  MdBusinessCenter, // Branch - lebih tepat untuk representasi bisnis
  MdSecurity, // Roles - representasi keamanan dan akses
  MdGroup, // Users - icon grup untuk users
  MdLabel, // Labels
  MdSchedule, // Scheduler
  MdNotificationsActive, // Alerts - lebih menunjukkan urgensi
  MdDomain, // Sites - representasi lokasi/bangunan
  MdLocationOn, // Checkpoint
  MdMyLocation, // Geofence - lebih tepat untuk area
  MdAssignment, // Forms
  MdPlaylistAddCheck, // Task - menunjukkan checklist/tugas
  MdTimeline, // Activity - menunjukkan aktivitas/timeline
  MdSettings, // Settings
  MdVpnKey, // Icon untuk license
  MdInsights, // Icon untuk analytics
  MdMonitorHeart, // Alarms
  MdRotateRight, // Rotation
  MdBusinessCenter as MdBranchDetails, // Branch Details
  MdOutlineLocationOn, // Checkpoint Activity
  MdBatteryAlert, // Battery
  MdReportProblem, // Exception
  MdZoomIn, // Exception Detailed
  MdRadar, // Geofence activity
  MdMissedVideoCall, // Missed Zone
  MdLogin, // Sign on/off
  MdHeatPump as MdHeatmap, // GPS Heatmap
  MdTask, // Tasks
  MdTimerOff, // Time Rotation
  MdTimer, // Time On Zone
  MdHelpOutline, // Unknown
  MdPerson, // Client
  MdDevicesOther, // Device
  MdBluetoothSearching,
  MdFormatListBulleted, // Beacon
  MdDescription, // Forms Log
} from "react-icons/md";
import { useTranslation } from "react-i18next";
import { useMemo } from "react";
import { useAppSelector } from "../../store/hooks";
import { useParams } from "react-router-dom";
import {
  ModuleType,
  PermissionType,
} from "../../pages/WebAdmin/Roles/Components/ModalAddAdminRoles/types";
import { checkModule } from "../../utils/permission";

type MenuItem = Required<MenuProps>["items"][number];

// Menggunakan Tailwind class untuk styling icon
const iconClass = "text-[16px] align-middle";

// Helper function to create menu items
const createMenuItem = (
  key: string,
  icon: React.ReactNode,
  label: string,
  children?: MenuItem[]
): MenuItem => ({
  key,
  icon,
  label,
  ...(children && { children }),
});

// Helper function to generate path with branch code
const generatePath = (
  path: string,
  branchCode?: string
) => {
  return branchCode
    ? `/admin/branch/${branchCode}${path}`
    : path;
};

export const useMenuConfig = () => {
  const { t } = useTranslation();
  const { branchCode } = useParams();
  const authorizedModule = useAppSelector(
    (state) => state.auth.role?.permissions
  );

  // System Admin Configuration
  const sysAdminConfig = useMemo(() => {
    const sysAdminItems = [
      {
        key: "dashboard",
        icon: <MdDashboard className={iconClass} />,
        label: t("menu.dashboard"),
        path: "/sysadmin/dashboard",
      },
      {
        key: "branch",
        icon: <MdBusinessCenter className={iconClass} />,
        label: t("menu.branch"),
        path: "/sysadmin/branch",
      },
      {
        key: "beacon",
        icon: (
          <MdBluetoothSearching className={iconClass} />
        ),
        label: t("menu.beacon"),
        path: "/sysadmin/beacon",
      },
      {
        key: "license",
        icon: <MdVpnKey className={iconClass} />,
        label: t("menu.license"),
        path: "/sysadmin/license",
      },
      {
        key: "settings",
        icon: <MdSettings className={iconClass} />,
        label: t("menu.settings"),
        path: "/sysadmin/settings",
      },
    ];

    return {
      menuItems: sysAdminItems.map((item) =>
        createMenuItem(item.key, item.icon, item.label)
      ) as MenuItem[],
      menuPaths: new Map(
        sysAdminItems.map((item) => [item.key, item.path])
      ),
    };
  }, [t]);

  // Web Admin Configuration
  const webAdminConfig = useMemo(() => {
    // Track authorized menu keys for path generation
    const authorizedMenuKeys = new Set<string>();

    // Helper function to check if a module is authorized and create a menu item if it is
    const checkAndCreateMenuItem = (
      key: string,
      icon: React.ReactNode,
      label: string,
      moduleCode: string,
      type: ModuleType = "MODULE",
      permissionType: PermissionType = "allow_view"
    ): MenuItem | null => {
      const isAuthorized = checkModule(
        authorizedModule,
        moduleCode,
        type,
        permissionType
      );

      if (isAuthorized) {
        authorizedMenuKeys.add(key);
        return createMenuItem(key, icon, label);
      }
      return null;
    };

    // Helper function to create a group menu item with filtered children
    const createGroupMenuItem = (
      key: string,
      icon: React.ReactNode,
      label: string,
      children: (MenuItem | null)[]
    ): MenuItem | null => {
      const filteredChildren = children.filter(
        Boolean
      ) as MenuItem[];
      return filteredChildren.length > 0
        ? createMenuItem(key, icon, label, filteredChildren)
        : null;
    };

    // Basic menu items
    const menuItems: (MenuItem | null)[] = [
      createMenuItem(
        "dashboard",
        <MdDashboard className={iconClass} />,
        t("menu.dashboard")
      ),
    ];

    // Always add dashboard to authorized keys
    authorizedMenuKeys.add("dashboard");

    // Add other basic items
    menuItems.push(
      checkAndCreateMenuItem(
        "activity",
        <MdTimeline className={iconClass} />,
        t("menu.activity"),
        "activity"
      ),
      checkAndCreateMenuItem(
        "task",
        <MdPlaylistAddCheck className={iconClass} />,
        t("menu.task"),
        "task"
      ),
      checkAndCreateMenuItem(
        "alert",
        <MdNotificationsActive className={iconClass} />,
        t("menu.alert"),
        "alert"
      ),
      checkAndCreateMenuItem(
        "scheduler",
        <MdSchedule className={iconClass} />,
        t("menu.scheduler"),
        "scheduler"
      )
    );

    // Forms group
    const formsItems: (MenuItem | null)[] = [
      checkAndCreateMenuItem(
        "forms",
        <MdAssignment className={iconClass} />,
        t("menu.forms"),
        "form"
      ),
      checkAndCreateMenuItem(
        "forms-picklist",
        <MdFormatListBulleted className={iconClass} />,
        t("menu.forms-picklist"),
        "form"
      ),
    ];

    menuItems.push(
      createGroupMenuItem(
        "forms-group",
        <MdAssignment className={iconClass} />,
        t("menu.forms"),
        formsItems
      )
    );

    // Monitoring group
    const monitoringItems: (MenuItem | null)[] = [
      checkAndCreateMenuItem(
        "monitoring-site",
        <MdDomain className={iconClass} />,
        t("menu.site"),
        "site"
      ),
      checkAndCreateMenuItem(
        "geofence",
        <MdMyLocation className={iconClass} />,
        t("menu.geofence"),
        "geofence"
      ),
      checkAndCreateMenuItem(
        "checkpoint",
        <MdLocationOn className={iconClass} />,
        t("menu.checkpoint"),
        "checkpoint"
      ),
    ];

    menuItems.push(
      createGroupMenuItem(
        "monitoring-group",
        <MdLocationOn className={iconClass} />,
        t("menu.field-monitoring"),
        monitoringItems
      )
    );

    // Analytics group
    const analyticsConfigs = [
      {
        key: "activity-log",
        icon: <MdTimeline className={iconClass} />,
        label: t("menu.activity-log"),
        moduleCode: "activity_log",
        type: "REPORT" as ModuleType,
      },
      {
        key: "alarms-log",
        icon: <MdMonitorHeart className={iconClass} />,
        label: t("menu.alarms-log"),
        moduleCode: "alarms",
        type: "REPORT" as ModuleType,
      },
      {
        key: "average-rotation-log",
        icon: <MdRotateRight className={iconClass} />,
        label: t("menu.average-rotation-log"),
        moduleCode: "average_rotation",
        type: "REPORT" as ModuleType,
      },
      {
        key: "branch-details-log",
        icon: <MdBranchDetails className={iconClass} />,
        label: t("menu.branch-details-log"),
        moduleCode: "branch_detail",
        type: "REPORT" as ModuleType,
      },
      {
        key: "checkpoint-activity-log",
        icon: <MdOutlineLocationOn className={iconClass} />,
        label: t("menu.checkpoint-activity-log"),
        moduleCode: "checkpoint_activity",
        type: "REPORT" as ModuleType,
      },
      {
        key: "checkpoint-battery-log",
        icon: <MdBatteryAlert className={iconClass} />,
        label: t("menu.checkpoint-battery-log"),
        moduleCode: "checkpoint_battery",
        type: "REPORT" as ModuleType,
      },
      {
        key: "exception-log",
        icon: <MdReportProblem className={iconClass} />,
        label: t("menu.exception-log"),
        moduleCode: "exception",
        type: "REPORT" as ModuleType,
      },
      {
        key: "exception-detailed-log",
        icon: <MdZoomIn className={iconClass} />,
        label: t("menu.exception-detailed-log"),
        moduleCode: "exception_detailed",
        type: "REPORT" as ModuleType,
      },
      {
        key: "analytics-forms-log",
        icon: <MdDescription className={iconClass} />,
        label: t("menu.forms-log"),
        moduleCode: "forms",
        type: "REPORT" as ModuleType,
      },
      {
        key: "analytics-geofence-log",
        icon: <MdRadar className={iconClass} />,
        label: t("menu.geofence-log"),
        moduleCode: "geofence",
        type: "REPORT" as ModuleType,
      },
      {
        key: "missed-zone-log",
        icon: <MdMissedVideoCall className={iconClass} />,
        label: t("menu.missed-zone-log"),
        moduleCode: "missed_zone",
        type: "REPORT" as ModuleType,
      },
      {
        key: "sign-on-off-log",
        icon: <MdLogin className={iconClass} />,
        label: t("menu.sign-on-off-log"),
        moduleCode: "sign_on_off",
        type: "REPORT" as ModuleType,
      },
      {
        key: "gps-heatmap-log",
        icon: <MdHeatmap className={iconClass} />,
        label: t("menu.gps-heatmap-log"),
        moduleCode: "gps_heatmap",
        type: "REPORT" as ModuleType,
      },
      {
        key: "analytics-tasks-log",
        icon: <MdTask className={iconClass} />,
        label: t("menu.tasks-log"),
        moduleCode: "tasks",
        type: "REPORT" as ModuleType,
      },
      {
        key: "time-rotation-log",
        icon: <MdTimerOff className={iconClass} />,
        label: t("menu.time-rotation-log"),
        moduleCode: "time_rotation",
        type: "REPORT" as ModuleType,
      },
      {
        key: "time-on-zone-log",
        icon: <MdTimer className={iconClass} />,
        label: t("menu.time-on-zone-log"),
        moduleCode: "time_on_zone",
        type: "REPORT" as ModuleType,
      },
      {
        key: "unknown-data",
        icon: <MdHelpOutline className={iconClass} />,
        label: t("menu.unknown-log"),
        moduleCode: "unknown_data",
        type: "REPORT" as ModuleType,
      },
    ];

    const analyticsItems = analyticsConfigs.map((config) =>
      checkAndCreateMenuItem(
        config.key,
        config.icon,
        config.label,
        config.moduleCode,
        config.type
      )
    );

    menuItems.push(
      createGroupMenuItem(
        "analytics-group",
        <MdInsights className={iconClass} />,
        t("menu.analytics"),
        analyticsItems
      )
    );

    // Settings group
    const settingsConfigs = [
      {
        key: "client",
        icon: <MdPerson className={iconClass} />,
        label: t("menu.client"),
        moduleCode: "sub_branch",
      },
      {
        key: "roles",
        icon: <MdSecurity className={iconClass} />,
        label: t("menu.roles"),
        moduleCode: "permission",
      },
      {
        key: "users",
        icon: <MdGroup className={iconClass} />,
        label: t("menu.users"),
        moduleCode: "user",
      },
      {
        key: "label",
        icon: <MdLabel className={iconClass} />,
        label: t("menu.label"),
        moduleCode: "label",
      },
      {
        key: "device",
        icon: <MdDevicesOther className={iconClass} />,
        label: t("menu.device"),
        moduleCode: "device",
      },
      {
        key: "beacon",
        icon: (
          <MdBluetoothSearching className={iconClass} />
        ),
        label: t("menu.beacon"),
        moduleCode: "beacon",
      },
      {
        key: "license",
        icon: <MdVpnKey className={iconClass} />,
        label: t("menu.license"),
        moduleCode: "license",
      },
    ];

    const settingsItems = settingsConfigs.map((config) =>
      checkAndCreateMenuItem(
        config.key,
        config.icon,
        config.label,
        config.moduleCode
      )
    );

    menuItems.push(
      createGroupMenuItem(
        "settings-group",
        <MdSettings className={iconClass} />,
        t("menu.settings"),
        settingsItems
      )
    );

    // Define path configurations - only include paths for authorized menu items
    const pathConfigs = [
      { key: "dashboard", path: "/dashboard" },
      { key: "activity", path: "/activity" },
      { key: "task", path: "/task" },
      { key: "forms", path: "/forms" },
      { key: "forms-picklist", path: "/formPicklist" },
      { key: "alert", path: "/alert" },
      { key: "scheduler", path: "/scheduler" },
      { key: "monitoring-site", path: "/site" },
      { key: "geofence", path: "/geofence" },
      { key: "checkpoint", path: "/checkpoint" },
      { key: "activity-log", path: "/activity-log" },
      { key: "alarms-log", path: "/alarms-log" },
      {
        key: "average-rotation-log",
        path: "/average-rotation-log",
      },
      {
        key: "branch-details-log",
        path: "/branch-details-log",
      },
      {
        key: "checkpoint-activity-log",
        path: "/checkpoint-activity-log",
      },
      {
        key: "checkpoint-battery-log",
        path: "/checkpoint-battery-log",
      },
      { key: "exception-log", path: "/exception-log" },
      {
        key: "exception-detailed-log",
        path: "/exception-detailed-log",
      },
      { key: "analytics-forms-log", path: "/forms-log" },
      {
        key: "analytics-geofence-log",
        path: "/analytics-geofence-log",
      },
      { key: "missed-zone-log", path: "/missed-zone-log" },
      { key: "sign-on-off-log", path: "/sign-on-off-log" },
      { key: "gps-heatmap-log", path: "/gps-heatmap-log" },
      { key: "analytics-tasks-log", path: "/tasks-log" },
      {
        key: "time-rotation-log",
        path: "/time-rotation-log",
      },
      {
        key: "time-on-zone-log",
        path: "/time-on-zone-log",
      },
      { key: "unknown-data", path: "/unknown-data" },
      { key: "client", path: "/branch" },
      { key: "roles", path: "/roles" },
      { key: "users", path: "/users" },
      { key: "label", path: "/label" },
      { key: "device", path: "/device" },
      { key: "beacon", path: "/beacon" },
      { key: "license", path: "/license" },
      // Legacy compatibility
      { key: "branch", path: "/branch" },
      { key: "analytics", path: "/analytics" },
    ];

    // Filter paths to only include authorized ones, plus always include dashboard
    const authorizedPathConfigs = pathConfigs.filter(
      (config) =>
        authorizedMenuKeys.has(config.key) ||
        config.key === "dashboard"
    );

    // Generate menu paths with branch code
    const menuPaths = new Map(
      authorizedPathConfigs.map((config) => [
        config.key,
        generatePath(config.path, branchCode),
      ])
    );

    return {
      menuItems: menuItems.filter(Boolean) as MenuItem[],
      menuPaths,
    };
  }, [authorizedModule, branchCode, t]);

  return {
    sysAdminConfig,
    webAdminConfig,
  };
};
