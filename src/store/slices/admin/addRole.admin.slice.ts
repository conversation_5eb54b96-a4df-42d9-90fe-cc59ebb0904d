import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { Module } from "../../../services/mainApi/types/module.mainApi.types";
import moduleMainApi from "../../../services/mainApi/module.mainApi";
import { AdminRole } from "../../../services/mainApi/admin/types/roles.admin.mainApi.types.ts";
import { RootState } from "../../index.ts";
import rolesAdminMainApi from "../../../services/mainApi/admin/roles.admin.mainApi.ts";

interface State {
  open: boolean;
  mode: "create" | "update";
  selectedAdminRole: AdminRole | null;
  loading: boolean;
  submitting: boolean;
  errorMessage: string | null;
  availableModules: Module[];
  roleName: string;
  permissions: {
    module_code: string;
    module_type: string;
    allow_create: boolean;
    allow_update: boolean;
    allow_view: boolean;
    allow_delete: boolean;
  }[];
}

const initialState: State = {
  open: false,
  mode: "create",
  selectedAdminRole: null,
  loading: false,
  submitting: false,
  errorMessage: null,
  availableModules: [],
  roleName: "",
  permissions: [],
};

export const fetchModules = createAsyncThunk(
  "addRoleAdmin/fetchModules",
  async (_, { rejectWithValue }) => {
    try {
      const response = await moduleMainApi.getModules();
      return response.data || [];
    } catch {
      return rejectWithValue("Failed to fetch modules");
    }
  }
);

export const fetchDetailEditAdminRole = createAsyncThunk(
  "addRoleAdmin/fetchDetailAdminRole",
  async (_, { rejectWithValue, getState }) => {
    const state = getState() as RootState;
    const { selectedAdminRole } = state.addRoleAdmin;
    const id = selectedAdminRole?.id || "";
    try {
      const response =
        await rolesAdminMainApi.getRolesById(id);
      return response.data;
    } catch {
      return rejectWithValue("Failed to fetch detail role");
    }
  }
);

const addRoleAdminSlice = createSlice({
  name: "addRoleAdmin",
  initialState,
  reducers: {
    open: (state) => {
      state.open = true;
      state.errorMessage = null;
    },
    openEdit: (state, action: PayloadAction<AdminRole>) => {
      state.open = true;
      state.mode = "update";
      state.roleName = action.payload.role_name;
      state.selectedAdminRole = action.payload;
    },
    close: () => {
      return { ...initialState };
    },
    setRoleName: (state, action: PayloadAction<string>) => {
      state.roleName = action.payload;
    },
    setPermission: (
      state,
      action: PayloadAction<{
        moduleCode: string;
        moduleType: string;
        permission:
          | "allow_view"
          | "allow_create"
          | "allow_update"
          | "allow_delete";
        value: boolean;
      }>
    ) => {
      const { moduleCode, permission, value, moduleType } =
        action.payload;
      console.log(
        moduleCode,
        permission,
        value,
        moduleType
      );
      const existingPermission = state.permissions.find(
        (p) =>
          p.module_code === moduleCode &&
          p.module_type === moduleType
      );

      if (existingPermission) {
        console.log(
          "EXISTING MODULE",
          existingPermission.module_code
        );
        console.log(
          "BEFORE",
          existingPermission[permission]
        );
        existingPermission[permission] = value;
        console.log(
          "AFTER",
          existingPermission[permission]
        );
      } else {
        state.permissions.push({
          module_code: moduleCode,
          module_type: action.payload.moduleType,
          allow_view:
            permission === "allow_view" ? value : false,
          allow_create:
            permission === "allow_create" ? value : false,
          allow_update:
            permission === "allow_update" ? value : false,
          allow_delete:
            permission === "allow_delete" ? value : false,
        });
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchModules.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(fetchModules.fulfilled, (state, action) => {
        state.loading = false;
        state.availableModules = action.payload;
        // Initialize permissions array with default values
        state.permissions = action.payload.map(
          (module) => ({
            module_code: module.module_code,
            module_type: module.module_type,
            allow_view: false,
            allow_create: false,
            allow_update: false,
            allow_delete: false,
          })
        );
      })
      .addCase(fetchModules.rejected, (state, action) => {
        state.loading = false;
        state.errorMessage = action.payload as string;
      });

    builder
      .addCase(
        fetchDetailEditAdminRole.pending,
        (state) => {
          state.loading = true;
          state.errorMessage = null;
        }
      )
      .addCase(
        fetchDetailEditAdminRole.fulfilled,
        (state, action) => {
          state.loading = false;
          if (action.payload) {
            state.roleName = action.payload.role_name;
            state.permissions =
              action.payload.permissions.map(
                (permission) => ({
                  module_code:
                    permission.module.module_code,
                  module_type:
                    permission.module.module_type,
                  allow_view: permission.allow_view,
                  allow_create: permission.allow_create,
                  allow_update: permission.allow_update,
                  allow_delete: permission.allow_delete,
                })
              );
          }
        }
      )
      .addCase(
        fetchDetailEditAdminRole.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      );
  },
});

export default addRoleAdminSlice;
