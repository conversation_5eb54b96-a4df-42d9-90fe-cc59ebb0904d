import { useTranslation } from "react-i18next";
import {
  Md<PERSON><PERSON><PERSON><PERSON>atermark,
  MdFilterList,
} from "react-icons/md";
import { useEffect, useState, useRef } from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import ActiveFilters, {
  createSimpleFilter,
  FilterGroup,
} from "../../../../components/Admin/Common/ActiveFilters";
import { But<PERSON>, Drawer } from "antd";
import AnalyticDrawerForm, {
  AnalyticsFilterState,
} from "../../Analytics/Components/AnalyticDrawerForm";
import { EReportType } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { AdminBranch } from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks";
import { useParams } from "react-router-dom";
import BranchDetailsLogList from "./Components/BranchDetailsLogList";
import BranchDetailsLogExportButton from "./Components/BranchDetailsLogExportButton";
import branchDetailsLogAdminSlice, {
  fetchBranchDetailsLogs,
} from "../../../../store/slices/admin/branchDetailsLog.admin.slice";

interface FilterState {
  selectedBranch: AdminBranch | null;
}

const BranchDetailsLogAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const isFetchingRef = useRef(false);

  // Configuration for the grid layout - number of items per row
  const itemsPerRow = 3;

  // Get branch code from URL params
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  // Redux state
  // We need to check if the reducer is registered in the store
  const branchDetailsLogState = useAppSelector(
    (state) => state.branchDetailsLogAdmin
  );
  const loading = branchDetailsLogState?.loading || false;
  const pagination = branchDetailsLogState?.pagination || {
    page: 1,
    total: 0,
  };
  const branchDetailsLogs =
    branchDetailsLogState?.branchDetailsLogs || [];
  const selectedBranch =
    branchDetailsLogState?.selectedBranch || null;
  const filterState = branchDetailsLogState?.filter || {
    branchId: null,
  };

  // Load data on mount and when filters change
  useEffect(() => {
    if (branchCode && !isFetchingRef.current) {
      isFetchingRef.current = true;
      dispatch(
        fetchBranchDetailsLogs({
          branchCode,
        })
      )
        .finally(() => {
          isFetchingRef.current = false;
        });
    }
  }, [dispatch, branchCode, filterState.branchId]);

  const { actions } = branchDetailsLogAdminSlice;

  // Convert applied filter state to format expected by drawer
  const getAppliedFilters = (): FilterState => {
    return {
      selectedBranch: selectedBranch,
    };
  };

  // Apply filters from the drawer
  const handleApplyFilters = (
    newFilters: AnalyticsFilterState
  ) => {
    // Update redux state
    dispatch(
      actions.setSelectedBranch(
        newFilters.selectedBranch || null
      )
    );

    setDrawerVisible(false);
  };

  // Check if there are any active filters
  const hasActiveFilters = (): boolean => {
    // Check if any filter is active
    return !!filterState.branchId;
  };

  // Get filter groups configuration for ActiveFilters component
  const getFilterGroups = (): FilterGroup[] => {
    const appliedFilters = getAppliedFilters();

    return [
      {
        key: "other",
        filters: [
          createSimpleFilter(
            "selectedBranch",
            "selectedBranch",
            appliedFilters.selectedBranch,
            "branch_name"
          ),
        ],
      },
    ];
  };

  const handleClearAll = () => {
    dispatch(actions.clearFilters());
  };

  const handleRemoveFilter = (
    filterKey: keyof FilterState
  ) => {
    switch (filterKey) {
      case "selectedBranch":
        dispatch(actions.setSelectedBranch(null));
        break;
    }
  };

  // Handle clicking on an active filter to edit it
  const handleEditFilter = () => {
    // We only need to open the drawer
    setDrawerVisible(true);
  };

  // Convert FilterState to AnalyticsFilterState for the drawer form
  const getInitialDrawerValues =
    (): Partial<AnalyticsFilterState> => {
      const appliedFilters = getAppliedFilters();

      return {
        selectedBranch:
          appliedFilters.selectedBranch || null,
      };
    };

  // Create a reusable function to get filter info for export button
  const getFilterInfo = (): {
    selectedBranch: AdminBranch | null;
    branchId: string | number | null;
  } => ({
    selectedBranch:
      branchDetailsLogState?.selectedBranch || null,
    branchId:
      branchDetailsLogState?.selectedBranch?.id || null,
  });

  // Handle page change for pagination
  const handlePageChange = () => {
    // Currently, the fetchBranchDetailsLogs function might not support pagination
    // We're leaving this as a placeholder for future implementation
    if (branchCode) {
      dispatch(
        fetchBranchDetailsLogs({
          branchCode,
        })
      );
    }
  };

  return (
    <WebAdminLayout activePage="branch-details-log">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdBrandingWatermark />}
            title={t(
              "branchDetailsLog.title",
              "Branch Details Log"
            )}
            description={t(
              "branchDetailsLog.description",
              "View branch details and statistics"
            )}
            actions={[
              // Desktop view - show all buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <BranchDetailsLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                />
                <Button
                  key="filter"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button and dropdown for downloads
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <BranchDetailsLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                  buttonText={t(
                    "common.download",
                    "Download"
                  )}
                />
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList />}
                  size="large"
                  type="primary"
                  className="bg-indigo-600 hover:bg-indigo-700 border-0 shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters using the reusable component */}
          <ActiveFilters<FilterState>
            filterGroups={getFilterGroups()}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAll}
            onShowFilters={() => {
              setDrawerVisible(true);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Results List with new grid layout */}
          <BranchDetailsLogList
            branchDetailsLogs={branchDetailsLogs}
            loading={loading}
            total={pagination.total || 0}
            page={pagination.page || 1}
            onPageChange={handlePageChange}
            itemsPerRow={itemsPerRow}
          />
        </div>

        {/* Filter Drawer - Using the refactored component with initialValues */}
        <Drawer
          title={
            <div className="text-lg font-semibold text-gray-800 dark:text-white">
              {t(
                "branchDetailsLog.filters.title",
                "Filter Branch Details Log"
              )}
            </div>
          }
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          className="dark:bg-gray-800"
        >
          <AnalyticDrawerForm
            reportType={EReportType.BRANCH_DETAILS}
            onApply={handleApplyFilters}
            onClose={() => setDrawerVisible(false)}
            initialValues={getInitialDrawerValues()}
          />
        </Drawer>
      </div>
    </WebAdminLayout>
  );
};

export default BranchDetailsLogAdmin;
