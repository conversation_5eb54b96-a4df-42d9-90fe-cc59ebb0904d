{"name": "react-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\""}, "dependencies": {"@ant-design/cssinjs": "^1.18.4", "@react-pdf/renderer": "^4.3.0", "@reduxjs/toolkit": "^2.5.1", "antd": "^5.14.1", "axios": "^1.7.9", "clsx": "^2.1.1", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "leaflet.heat": "^0.2.0", "lodash": "^4.17.21", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "react-icons": "^5.4.0", "react-leaflet": "^5.0.0-rc.2", "react-redux": "^9.2.0", "react-router": "^7.1.5", "react-router-dom": "^7.1.5", "universal-cookie": "^7.2.2", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/leaflet": "^1.9.16", "@types/leaflet-draw": "^1.0.11", "@types/lodash": "^4.17.15", "@types/node": "^22.13.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.17", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.4.35", "prettier": "3.5.1", "tailwindcss": "^3.4.1", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}