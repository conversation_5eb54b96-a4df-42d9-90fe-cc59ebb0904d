import {BaseGetRequest, BaseResponse} from "../../types/base.mainApi.types";
import {Branch} from "../../sysadmin/types/branch.mainApi.types";

export type AdminBranch = Branch;

export interface AdminBranchCreateRequestBody {
  name: string;
  description: string;
  timezone: string;
  active: boolean;
}

export type AdminBranchGetRequestQuery = BaseGetRequest

export type AdminBranchListResponse = BaseResponse<
  AdminBranch[]
>;
