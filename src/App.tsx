import { Routes } from "react-router-dom";
import { getAuthRoutes, getAdminRoutes, getSysAdminRoutes, getNotFoundRoutes } from "./config/routes";

function App() {
  return (
    <>
      <Routes>
        {/* Auth Routes */}
        {getAuthRoutes()}
        
        {/* System Admin Routes */}
        {getSysAdminRoutes()}
        
        {/* Admin Routes */}
        {getAdminRoutes()}
        
        {/* Not Found Routes */}
        {getNotFoundRoutes()}
      </Routes>
    </>
  );
}

export default App;
