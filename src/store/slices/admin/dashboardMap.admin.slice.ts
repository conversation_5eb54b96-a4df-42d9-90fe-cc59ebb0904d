import {createAsyncThunk, createSlice} from "@reduxjs/toolkit";
import {AdminRole} from "../../../services/mainApi/admin/types/roles.admin.mainApi.types.ts";
import {AdminUser} from "../../../services/mainApi/admin/types/user.admin.mainApi.types.ts";
import {RootState} from "../../index.ts";
import {checkpointLogAdminApi} from "../../../services/mainApi/admin/checkpointLog.admin.mainApi.ts";
import {isAxiosError} from "axios";
import {handleApiError} from "../../../utils/error.ts";
import {AdminGeofence} from "../../../services/mainApi/admin/types/geofence.admin.mainApi.types.ts";
import geofenceAdminMainApi from "../../../services/mainApi/admin/geofence.admin.mainApi.ts";
import dayjs from "dayjs";

interface Data {
  id: string;
  userName: string;
  eventId: string;
  eventName: string;
  eventSubmittedTime: string;
  latitude: number | null;
  longitude: number | null;
}

interface State {
  fetchingCheckpointActivities: boolean;
  fetchingGeofences: boolean;
  error: string[] | string | null;
  filters: {
    startDate: string;
    endDate: string;
    startTime: string;
    endTime: string;
    role: AdminRole | null;
    user: AdminUser | null;
  }

  checkpointActivities: Data[];
  geofences: AdminGeofence[];

  filterModal: {
    visible: boolean;
  }
}

const initialState: State = {
  fetchingCheckpointActivities: false,
  fetchingGeofences: false,
  error: null,
  filters: {
    startDate: dayjs().startOf('months').toISOString(),
    endDate: dayjs().endOf('months').toISOString(),
    startTime: "00:00:00",
    endTime: "23:59:59",
    role: null,
    user: null,
  },
  filterModal: {
    visible: false,
  },
  checkpointActivities: [],
  geofences: [],
};

export const fetchCheckpointActivitiesDashboard = createAsyncThunk(
  "dashboardMapAdmin/fetchCheckpointActivitiesDashboard",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const branchCode = state.auth.user?.current_branch.branch_code;

      if (!branchCode) {
        return rejectWithValue("Branch code not found");
      }

      const { filters } = state.dashboardMapAdmin;

      const params = {
        start_date: filters.startDate || undefined,
        end_date: filters.endDate || undefined,
        start_time: filters.startTime || undefined,
        end_time: filters.endTime || undefined,
        role_id: filters.role?.id || undefined,
        user_id: filters.user?.id || undefined,
      };

      const response = await checkpointLogAdminApi.getCheckpointLogs(branchCode, params);

      if (!response.data) {
        return rejectWithValue(response.error);
      }

      // Transform the response data to match the expected format
      return response.data.map(item => ({
        id: item.id,
        userName: item.user_name,
        eventId: item.checkpoint_id,
        eventName: item.checkpoint_name,
        eventSubmittedTime: item.original_submitted_time,
        latitude: item.latitude,
        longitude: item.longitude,
      }));
    }
    catch (error) {
      if(isAxiosError(error)) {
        const apiError = handleApiError(error);
        return rejectWithValue(apiError.errors);
      }
      return rejectWithValue("Failed to fetch checkpoint activities");
    }
  }
);

export const fetchGeofencesDashboard = createAsyncThunk(
  "dashboardMapAdmin/fetchGeofencesDashboard",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const branchCode = state.auth.user?.current_branch.branch_code;

      if (!branchCode) {
        return rejectWithValue("Branch code not found");
      }

        const response = await geofenceAdminMainApi.getGeofences(branchCode);

      if (!response.data) {
        return rejectWithValue(response.error);
      }

      return response.data;
    }
    catch (error) {
      if(isAxiosError(error)) {
        const apiError = handleApiError(error);
        return rejectWithValue(apiError.errors);
      }
      return rejectWithValue("Failed to fetch geofences");
    }
  }
);

const dashboardMapAdminSlice = createSlice({
  name: "dashboardMapAdmin",
  initialState: {
    ...initialState,
  },
  reducers: {
    setLoading: (state, action) => {
      state.fetchingCheckpointActivities = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    setCheckpointActivities: (state, action) => {
      state.checkpointActivities = action.payload;
    },
    setFilter: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setStartDate: (state, action) => {
      state.filters.startDate = action.payload;
    },
    setEndDate: (state, action) => {
      state.filters.endDate = action.payload;
    },
    setStartTime: (state, action) => {
      state.filters.startTime = action.payload;
    },
    setEndTime: (state, action) => {
      state.filters.endTime = action.payload;
    },
    setRole: (state, action) => {
      state.filters.role = action.payload;
    },
    setUser: (state, action) => {
      state.filters.user = action.payload;
    },
    toggleFilterModal: (state) => {
      state.filterModal.visible = !state.filterModal.visible;
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCheckpointActivitiesDashboard.pending, (state) => {
        state.fetchingCheckpointActivities = true;
        state.error = null;
      })
      .addCase(fetchCheckpointActivitiesDashboard.fulfilled, (state, action) => {
        state.fetchingCheckpointActivities = false;
        state.checkpointActivities = action.payload;
      })
      .addCase(fetchCheckpointActivitiesDashboard.rejected, (state, action) => {
        state.fetchingCheckpointActivities = false;
        state.error = action.payload as string;
      });

    builder
      .addCase(fetchGeofencesDashboard.pending, (state) => {
        state.fetchingGeofences = true;
        state.error = null;
      })
      .addCase(fetchGeofencesDashboard.fulfilled, (state, action) => {
        state.fetchingGeofences = false;
        state.geofences = action.payload;
      })
      .addCase(fetchGeofencesDashboard.rejected, (state, action) => {
        state.fetchingGeofences = false;
        state.error = action.payload as string;
      });
  },
});

export default dashboardMapAdminSlice;
