export interface AdminLabel {
  id: string;
  branch_id: string;
  label_name: string;
  label_description: string;
  active: boolean;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  updated_by: string | null;
}

export interface AdminLabelDetail extends AdminLabel {
  branch: {
    id: string;
    parent_id: string;
    branch_name: string;
    branch_description: string;
    license_id: string;
    timezone_id: string;
    active: boolean;
    created_at: string;
    created_by: string | null;
    updated_at: string;
    updated_by: string | null;
    branch_code: string;
  };
}
