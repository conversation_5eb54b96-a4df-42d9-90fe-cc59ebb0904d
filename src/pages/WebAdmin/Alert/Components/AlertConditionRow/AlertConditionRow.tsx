import { <PERSON><PERSON>, Select } from "antd";
import { DeleteOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { EAlertConditionType } from "../../../../../services/mainApi/admin/types/alert.admin.mainApi.types";
import React from "react";
import addAlertAdminSlice, {
  AddAlertAdminState,
} from "../../../../../store/slices/admin/addAlert.admin.slice";
import { useAppDispatch } from "../../../../../store/hooks";
import SelectUser from "../../../../../components/SelectUser/SelectUser";
import SelectRole from "../../../../../components/Roles/SelectRole";
import SelectCheckpoint from "../../../../../components/Checkpoint/SelectCheckpoint";
import SelectGeofence from "../../../../../components/Geofence/SelectGeofence";
import SelectDevice from "../../../../../components/SelectDevice/SelectDevice";
import SelectZone from "../../../../../components/Zone/SelectZone.tsx";

const { Option } = Select;

interface AlertConditionRowProps {
  rowId: string;
  condition: AddAlertAdminState["conditions"][0];
}

const AlertConditionRow: React.FC<
  AlertConditionRowProps
> = ({ rowId, condition }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { actions } = addAlertAdminSlice;

  return (
    <div className="grid grid-cols-12 gap-3 items-start">
      <div className="col-span-3">
        <Select
          className="w-full"
          placeholder={t(
            "alert.form.condition.type.placeholder",
            "Select condition type"
          )}
          value={condition?.type}
          onChange={(value) => {
            dispatch(
              actions.changeType({
                rowUuid: rowId,
                type: value,
              })
            );
          }}
        >
          <Select.Option value={EAlertConditionType.USER}>
            User
          </Select.Option>
          <Select.Option value={EAlertConditionType.ROLE}>
            Role
          </Select.Option>
          <Select.Option
            value={EAlertConditionType.DAY_OF_MONTH}
          >
            Day of Month
          </Select.Option>
          <Select.Option
            value={EAlertConditionType.DAY_OF_WEEK}
          >
            Day of Week
          </Select.Option>
          <Select.Option value={EAlertConditionType.HOURS}>
            Hours
          </Select.Option>
          <Select.Option
            value={EAlertConditionType.MINUTES}
          >
            Minutes
          </Select.Option>
          <Select.Option
            value={EAlertConditionType.CHECKPOINT}
          >
            Checkpoint
          </Select.Option>
          <Select.Option
            value={EAlertConditionType.GEOFENCE_NAME}
          >
            Geofence Name
          </Select.Option>
          <Select.Option
            value={
              EAlertConditionType.GEOFENCE_ASSIGNED_ZONE
            }
          >
            Geofence Assigned Zone
          </Select.Option>
          <Select.Option
            value={EAlertConditionType.DEVICE_NAME}
          >
            Device Name
          </Select.Option>
        </Select>
      </div>

      <div className="col-span-2">
        <Select
          className="w-full"
          placeholder={t(
            "alert.form.condition.operator.placeholder",
            "Operator"
          )}
          value={
            condition?.logicalOperatorConditionType || "="
          }
          onChange={(value) =>
            dispatch(
              actions.changeOperatorCondition({
                rowUuid: rowId,
                logicalOperatorConditionType: value,
              })
            )
          }
        >
          <Option value="=">=</Option>
          <Option value="!=">!=</Option>
        </Select>
      </div>

      <div className="col-span-6">
        {condition.type === EAlertConditionType.USER && (
          <SelectUser
            className="w-full"
            value={condition?.value as string}
            onChange={(_value, adminUser) =>
              dispatch(
                actions.changeValue({
                  rowUuid: rowId,
                  value: adminUser?.id || "",
                })
              )
            }
          />
        )}
        {condition.type === EAlertConditionType.ROLE && (
          <SelectRole
            className="w-full"
            value={condition?.value as string}
            onChange={(value) =>
              dispatch(
                actions.changeValue({
                  rowUuid: rowId,
                  value: value,
                })
              )
            }
          />
        )}
        {condition.type ===
          EAlertConditionType.DAY_OF_MONTH && (
          <Select
            className="w-full"
            value={condition?.value as string}
            onChange={(value) =>
              dispatch(
                actions.changeValue({
                  rowUuid: rowId,
                  value: value,
                })
              )
            }
          >
            {Array.from(
              { length: 31 },
              (_, i) => i + 1
            ).map((day) => (
              <Option
                key={day}
                value={day.toString()}
              >
                {day}
              </Option>
            ))}
          </Select>
        )}
        {condition.type ===
          EAlertConditionType.DAY_OF_WEEK && (
          <Select
            className="w-full"
            placeholder={t(
              "alert.form.dayOfWeek.placeholder",
              "Select day of week"
            )}
            value={condition?.value as string}
            onChange={(value) =>
              dispatch(
                actions.changeValue({
                  rowUuid: rowId,
                  value: value,
                })
              )
            }
          >
            {Array.from({ length: 7 }, (_, i) => i).map(
              (day) => (
                <Option
                  key={day}
                  value={day.toString()}
                >
                  {t(
                    `common.days.in_number.${day}`,
                    `${day}`
                  )}
                </Option>
              )
            )}
          </Select>
        )}
        {condition.type === EAlertConditionType.HOURS && (
          <Select
            className="w-full"
            value={condition?.value as string}
            onChange={(value) =>
              dispatch(
                actions.changeValue({
                  rowUuid: rowId,
                  value: value,
                })
              )
            }
          >
            {Array.from({ length: 24 }, (_, i) => i).map(
              (hour) => (
                <Option
                  key={hour}
                  value={hour.toString()}
                >
                  {hour}
                </Option>
              )
            )}
          </Select>
        )}
        {condition.type === EAlertConditionType.MINUTES && (
          <Select
            className="w-full"
            value={condition?.value as string}
            onChange={(value) =>
              dispatch(
                actions.changeValue({
                  rowUuid: rowId,
                  value: value,
                })
              )
            }
          >
            {Array.from({ length: 60 }, (_, i) => i).map(
              (minute) => (
                <Option
                  key={minute}
                  value={minute.toString()}
                >
                  {minute}
                </Option>
              )
            )}
          </Select>
        )}
        {condition.type ===
          EAlertConditionType.CHECKPOINT && (
          <SelectCheckpoint
            className="w-full"
            value={condition?.value as string}
            onChange={(value) =>
              dispatch(
                actions.changeValue({
                  rowUuid: rowId,
                  value: value,
                })
              )
            }
          />
        )}
        {condition.type ===
          EAlertConditionType.GEOFENCE_NAME && (
          <SelectGeofence
            className="w-full"
            value={condition?.value as string}
            onChange={(value) =>
              dispatch(
                actions.changeValue({
                  rowUuid: rowId,
                  value: value,
                })
              )
            }
          />
        )}
        {condition.type ===
          EAlertConditionType.GEOFENCE_ASSIGNED_ZONE && (
          <SelectZone
            query={{ assigned_to_geofence_id: true }}
            className="w-full"
            value={condition?.value as string}
            onChange={(value) =>
              dispatch(
                actions.changeValue({
                  rowUuid: rowId,
                  value: value,
                })
              )
            }
          />
        )}
        {condition.type ===
          EAlertConditionType.DEVICE_NAME && (
          <SelectDevice
            className="w-full"
            value={condition?.value as string}
            onChange={(value) =>
              dispatch(
                actions.changeValue({
                  rowUuid: rowId,
                  value: value || null,
                })
              )
            }
          />
        )}
      </div>

      <div className="col-span-1 flex gap-2 items-center justify-end">
        <Button
          icon={<DeleteOutlined />}
          danger
          type="text"
          onClick={() =>
            dispatch(actions.removeCondition(rowId))
          }
        />
      </div>
    </div>
  );
};

export default AlertConditionRow;
