import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import {
  AdminRole,
  AdminRoleDetail,
} from "../../../services/mainApi/admin/types/roles.admin.mainApi.types.ts";
import rolesAdminMainApi from "../../../services/mainApi/admin/roles.admin.mainApi.ts";
import { RootState } from "../../index.ts";

interface State {
  open: boolean;
  roleView: AdminRole | null;
  loading: boolean;
  errorMessage: string | null;
  detail: AdminRoleDetail | null;
}

const initialState: State = {
  open: false,
  roleView: null,
  loading: false,
  errorMessage: null,
  detail: null,
};

export const fetchDetailAdminRole = createAsyncThunk(
  "viewRoleAdmin/fetchDetailAdminRole",
  async (_, { rejectWithValue, getState }) => {
    const state = getState() as RootState;
    const { roleView: selectedAdminRole } =
      state.viewRoleAdmin;
    const id = selectedAdminRole?.id || "";
    try {
      const response =
        await rolesAdminMainApi.getRolesById(id);
      return response.data;
    } catch {
      return rejectWithValue("Failed to fetch detail role");
    }
  }
);

const viewRoleAdminSlice = createSlice({
  name: "viewRoleAdmin",
  initialState,
  reducers: {
    open: (state, action: PayloadAction<AdminRole>) => {
      state.open = true;
      state.roleView = action.payload;
      state.errorMessage = null;
    },
    close: (state) => {
      state.open = false;
      state.roleView = null;
      state.errorMessage = null;
      state.loading = false;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.errorMessage = action.payload;
    },
    setDetail: (
      state,
      action: PayloadAction<AdminRoleDetail | null>
    ) => {
      state.detail = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDetailAdminRole.pending, (state) => {
        state.loading = true;
        state.errorMessage = null;
      })
      .addCase(
        fetchDetailAdminRole.fulfilled,
        (state, action) => {
          state.loading = false;
          state.detail = action.payload;
        }
      )
      .addCase(
        fetchDetailAdminRole.rejected,
        (state, action) => {
          state.loading = false;
          state.errorMessage = action.payload as string;
        }
      );
  },
});

export default viewRoleAdminSlice;
