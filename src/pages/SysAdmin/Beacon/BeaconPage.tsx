import { Button, Input, Pagination, Select } from "antd";
import {
  PlusOutlined,
  SearchOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
} from "@ant-design/icons";
import SysAdminLayout from "../../../components/Layout/SysAdminLayout";
import BeaconList from "./Components/BeaconList";
import AddEditBeaconModal from "./Components/AddEditBeaconModal";
import { useBeaconManagement } from "./Components/hooks/useBeaconManagement";
import { Beacon } from "../../../services/mainApi/sysadmin/types/beacon.mainApi.types";
import { SortField } from "../../../store/slices/sysadmin/beaconList.sysadmin.slice";
import { useAppDispatch } from "../../../store/hooks";
import addBeaconSysadminSlice from "../../../store/slices/sysadmin/addBeacon.sysadmin.slice";

const { Option } = Select;

export default function BeaconPage() {
  const {
    beacons,
    loading,
    pagination,
    filters,
    handleSearch,
    handleActiveFilter,
    handleSort,
    handlePageChange,
  } = useBeaconManagement();

  const dispatch = useAppDispatch();

  const handleAddClick = () => {
    dispatch(addBeaconSysadminSlice.actions.open());
  };

  const handleEditClick = (beacon: Beacon) => {
    dispatch(addBeaconSysadminSlice.actions.openEdit(beacon));
  };

  const toggleSortOrder = () => {
    handleSort(
      filters.sortField,
      filters.sortOrder === "asc" ? "desc" : "asc"
    );
  };

  const handleStatusFilterChange = (value: string) => {
    if (value === "all") {
      handleActiveFilter(undefined);
    } else {
      handleActiveFilter(value === "active");
    }
  };

  return (
    <SysAdminLayout activePage="beacon">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Sticky Header */}
        <div className="sticky top-0 z-10 bg-gray-50 dark:bg-gray-900 pb-6">
          {/* Header Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                  Beacon Management
                </h1>
                <p className="text-gray-500 dark:text-gray-400 mt-1">
                  Manage your beacons for branch locations
                </p>
              </div>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddClick}
                size="large"
                className="bg-blue-500 hover:bg-blue-600 border-0 shadow-md hover:shadow-lg transition-all duration-200"
              >
                Add New Beacon
              </Button>
            </div>
          </div>

          {/* Search and Sort Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="w-full md:w-1/3">
                <Input
                  placeholder="Search beacons..."
                  prefix={
                    <SearchOutlined className="text-gray-400 dark:text-gray-500" />
                  }
                  value={filters.searchQuery}
                  onChange={(e) =>
                    handleSearch(e.target.value)
                  }
                  className="rounded-lg"
                  size="large"
                />
              </div>
              <div className="flex flex-wrap gap-2 items-center">
                <Select
                  value={filters.active === undefined ? "all" : (filters.active ? "active" : "inactive")}
                  onChange={handleStatusFilterChange}
                  className="w-32"
                  size="large"
                >
                  <Option value="all">All Status</Option>
                  <Option value="active">Active</Option>
                  <Option value="inactive">Inactive</Option>
                </Select>
                <Select
                  value={filters.sortField}
                  onChange={(value: SortField) =>
                    handleSort(value, filters.sortOrder)
                  }
                  className="w-40"
                  size="large"
                >
                  <Option value="created_at">
                    Creation Date
                  </Option>
                  <Option value="beacon_name">
                    Beacon Name
                  </Option>
                </Select>
                <Button
                  icon={
                    filters.sortOrder === "asc" ? (
                      <SortAscendingOutlined />
                    ) : (
                      <SortDescendingOutlined />
                    )
                  }
                  onClick={toggleSortOrder}
                  size="large"
                  className="flex items-center dark:border-gray-600 dark:text-gray-300"
                >
                  {filters.sortOrder === "asc"
                    ? "Ascending"
                    : "Descending"}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Beacon List with Pagination */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm mt-6">
          <BeaconList
            beacons={beacons}
            loading={loading}
            onEdit={handleEditClick}
            onDelete={() => {}}
          />

          {/* Pagination */}
          <div className="p-6 border-t border-gray-100 dark:border-gray-700">
            <Pagination
              current={pagination.current}
              total={pagination.total}
              pageSize={pagination.pageSize}
              onChange={handlePageChange}
              showTotal={(total) => `Total ${total} items`}
              showSizeChanger={false}
              className="flex justify-end"
            />
          </div>
        </div>

        {/* Modals */}
        <AddEditBeaconModal />

      </div>
    </SysAdminLayout>
  );
} 