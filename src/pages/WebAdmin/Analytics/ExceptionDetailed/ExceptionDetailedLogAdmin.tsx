import { useTranslation } from "react-i18next";
import { <PERSON>d<PERSON><PERSON>In, MdFilterList } from "react-icons/md";
import { useCallback, useEffect, useRef, useState } from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import { <PERSON><PERSON>, Drawer } from "antd";
import ExceptionDetailedLogList from "./Components/ExceptionDetailedLogList";
import { useParams } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../../../store/hooks";
import exceptionDetailedLogAdminSlice, {
  fetchExceptionDetailedLogs,
} from "../../../../store/slices/admin/exceptionDetailedLog.admin.slice";
import ActiveFilters, {
  createEndDateFilter,
  createEndTimeFilter,
  createLabelFilter,
  createSimpleFilter,
  createStartDateFilter,
  createStartTimeFilter,
  FilterGroup,
} from "../../../../components/Admin/Common/ActiveFilters";
import AnalyticDrawerForm, {
  AnalyticsFilterState,
} from "../../Analytics/Components/AnalyticDrawerForm";
import { EReportType } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { AdminRole } from "../../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminBranch } from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminZone } from "../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { AdminLabel } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminUser } from "../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminCheckpoint } from "../../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";
import ExceptionDetailedLogExportButton from "./Components/ExceptionDetailedLogExportButton";

interface FilterState {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: null | AdminBranch;
  site: null | AdminZone;
  siteLabels: AdminLabel[];
  checkpoint: null | AdminCheckpoint;
  checkpointLabels: AdminLabel[];
  role: null | AdminRole;
  user: null | AdminUser;
  userLabels: AdminLabel[];
  device: null | AdminDevice;
  deviceLabels: AdminLabel[];
}


const ExceptionDetailedLogAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const isFetching = useRef(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  // Get branch code from URL params
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  // Redux state with explicit type casting since the slice might not be registered yet
  const exceptionDetailedLogState = useAppSelector(
    (state) => state.exceptionDetailedLogAdmin 
  );
  const loading = exceptionDetailedLogState.loading;
  const pagination = exceptionDetailedLogState.pagination;
  const { actions } = exceptionDetailedLogAdminSlice;

  const fetch = useCallback(async () => {
    if (isFetching.current) return;
    isFetching.current = true;
    await dispatch(fetchExceptionDetailedLogs({ branchCode: branchCode || "" }));
    isFetching.current = false;
  }, [branchCode, dispatch]);

  // Load data on mount and when filters change
  useEffect(() => {
    if (branchCode) {
      fetch().then();
    }
  }, [
    fetch,
    branchCode,
    exceptionDetailedLogState.filter.page,
    exceptionDetailedLogState.filter.limit,
    exceptionDetailedLogState.filter.startDate,
    exceptionDetailedLogState.filter.endDate,
    exceptionDetailedLogState.filter.startTime,
    exceptionDetailedLogState.filter.endTime,
    exceptionDetailedLogState.filter.branchId,
    exceptionDetailedLogState.filter.siteId,
    exceptionDetailedLogState.filter.siteLabels,
    exceptionDetailedLogState.filter.checkpointId,
    exceptionDetailedLogState.filter.checkpointLabels,
    exceptionDetailedLogState.filter.roleId,
    exceptionDetailedLogState.filter.userId,
    exceptionDetailedLogState.filter.userLabels,
    exceptionDetailedLogState.filter.deviceId,
    exceptionDetailedLogState.filter.deviceLabels,
  ]);

  // Convert applied filter state to format expected by drawer
  const getAppliedFilters = (): FilterState => {
    return {
      startDate: exceptionDetailedLogState.filter.startDate,
      endDate: exceptionDetailedLogState.filter.endDate,
      startTime: exceptionDetailedLogState.filter.startTime,
      endTime: exceptionDetailedLogState.filter.endTime,
      branch: exceptionDetailedLogState.selectedBranch,
      site: exceptionDetailedLogState.selectedSite,
      siteLabels: exceptionDetailedLogState.selectedSiteLabels || [],
      checkpoint: exceptionDetailedLogState.selectedCheckpoint,
      checkpointLabels: exceptionDetailedLogState.selectedCheckpointLabels || [],
      role: exceptionDetailedLogState.selectedRole,
      user: exceptionDetailedLogState.selectedUser,
      userLabels: exceptionDetailedLogState.selectedUserLabels || [],
      device: exceptionDetailedLogState.selectedDevice,
      deviceLabels: exceptionDetailedLogState.selectedDeviceLabels || [],
    };
  };

  // Apply filters from the drawer
  const handleApplyFilters = (
    newFilters: AnalyticsFilterState
  ) => {
    // Update redux state
    dispatch(actions.setSelectedRole(newFilters.role || null));
    dispatch(actions.setSelectedUser(newFilters.user || null));
    dispatch(actions.setSelectedUserLabels(newFilters.userLabels || []));
    dispatch(actions.setSelectedDevice(newFilters.device || null));
    dispatch(actions.setSelectedDeviceLabels(newFilters.deviceLabels || []));
    dispatch(actions.setSelectedSite(newFilters.site || null));
    dispatch(actions.setSelectedSiteLabels(newFilters.siteLabels || []));
    dispatch(actions.setSelectedCheckpoint(newFilters.checkpoint || null));
    dispatch(actions.setSelectedCheckpointLabels(newFilters.checkpointLabels || []));
    dispatch(actions.setSelectedBranch(newFilters.selectedBranch || null));
    dispatch(actions.setStartDate(newFilters.startDate || null));
    dispatch(actions.setEndDate(newFilters.endDate || null));
    dispatch(actions.setStartTime(newFilters.startTime || null));
    dispatch(actions.setEndTime(newFilters.endTime || null));

    // Reset page to 1
    dispatch(actions.setPage(1));
    setDrawerVisible(false);
  };

  // Handle page change
  const handlePageChange = (page: number, pageSize?: number) => {
    dispatch(actions.setPage(page));
    if (pageSize) {
      dispatch(actions.setLimit(pageSize));
    }
  };

  // Check if there are any active filters
  const hasActiveFilters = (): boolean => {
    const filter = exceptionDetailedLogState.filter;
    return !!(
      filter.startDate ||
      filter.endDate ||
      filter.startTime ||
      filter.endTime ||
      filter.branchId ||
      filter.siteId ||
      filter.siteLabels?.length > 0 ||
      filter.checkpointId ||
      filter.checkpointLabels?.length > 0 ||
      filter.roleId ||
      filter.userId ||
      filter.userLabels?.length > 0 ||
      filter.deviceId ||
      filter.deviceLabels?.length > 0
    );
  };

  // Get filter groups configuration for ActiveFilters component
  const getFilterGroups = (): FilterGroup[] => {
    const appliedFilters = getAppliedFilters();

    return [
      {
        key: "datetime",
        filters: [
          createStartDateFilter(appliedFilters.startDate),
          createEndDateFilter(appliedFilters.endDate),
          createStartTimeFilter(appliedFilters.startTime),
          createEndTimeFilter(appliedFilters.endTime),
        ],
      },
      {
        key: "user",
        filters: [
          createSimpleFilter(
            "role",
            "role",
            appliedFilters.role,
            "role_name"
          ),
          createSimpleFilter(
            "user",
            "user",
            appliedFilters.user,
            "name"
          ),
          createLabelFilter(
            "userLabels",
            "userLabels",
            appliedFilters.userLabels
          ),
        ],
      },
      {
        key: "device",
        filters: [
          createSimpleFilter(
            "device",
            "device",
            appliedFilters.device,
            "device_name"
          ),
          createLabelFilter(
            "deviceLabels",
            "deviceLabels",
            appliedFilters.deviceLabels
          ),
        ],
      },
      {
        key: "location",
        filters: [
          createSimpleFilter(
            "site",
            "site",
            appliedFilters.site,
            "zone_name"
          ),
          createLabelFilter(
            "siteLabels",
            "siteLabels",
            appliedFilters.siteLabels
          ),
          createSimpleFilter(
            "checkpoint",
            "checkpoint",
            appliedFilters.checkpoint,
            "checkpoint_name"
          ),
          createLabelFilter(
            "checkpointLabels",
            "checkpointLabels",
            appliedFilters.checkpointLabels
          ),
        ],
      },
    ];
  };

  // Handle clearing all filters
  const handleClearAll = () => {
    dispatch(actions.clearFilters());
  };

  // Handle removing a specific filter
  const handleRemoveFilter = (
    filterKey: keyof FilterState
  ) => {
    switch (filterKey) {
      case "startDate":
        dispatch(actions.setStartDate(null));
        break;
      case "endDate":
        dispatch(actions.setEndDate(null));
        break;
      case "startTime":
        dispatch(actions.setStartTime(null));
        break;
      case "endTime":
        dispatch(actions.setEndTime(null));
        break;
      case "branch":
        dispatch(actions.setSelectedBranch(null));
        break;
      case "site":
        dispatch(actions.setSelectedSite(null));
        break;
      case "siteLabels":
        dispatch(actions.setSelectedSiteLabels([]));
        break;
      case "checkpoint":
        dispatch(actions.setSelectedCheckpoint(null));
        break;
      case "checkpointLabels":
        dispatch(actions.setSelectedCheckpointLabels([]));
        break;
      case "role":
        dispatch(actions.setSelectedRole(null));
        break;
      case "user":
        dispatch(actions.setSelectedUser(null));
        break;
      case "userLabels":
        dispatch(actions.setSelectedUserLabels([]));
        break;
      case "device":
        dispatch(actions.setSelectedDevice(null));
        break;
      case "deviceLabels":
        dispatch(actions.setSelectedDeviceLabels([]));
        break;
    }
  };

  // Handle edit filter, which shows the drawer with the current filter state
  const handleEditFilter = () => {
    setDrawerVisible(true);
  };

  // Get initial values for the drawer form based on current filter state
  const getInitialDrawerValues = (): Partial<AnalyticsFilterState> => {
    const appliedFilters = getAppliedFilters();
    return {
      startDate: appliedFilters.startDate || "",
      endDate: appliedFilters.endDate || "",
      startTime: appliedFilters.startTime || "",
      endTime: appliedFilters.endTime || "",
      selectedBranch: appliedFilters.branch,
      role: appliedFilters.role,
      user: appliedFilters.user,
      userLabels: appliedFilters.userLabels,
      device: appliedFilters.device,
      deviceLabels: appliedFilters.deviceLabels,
      site: appliedFilters.site,
      siteLabels: appliedFilters.siteLabels,
      checkpoint: appliedFilters.checkpoint,
      checkpointLabels: appliedFilters.checkpointLabels,
    };
  };

  // Calculate pagination display values
  const currentPage = pagination?.page || 1;
  const pageSize = pagination?.limit || 10;
  const totalItems = pagination?.total || 0;

  // Create a reusable function to get filter info
  const getFilterInfo = (): {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    branch: AdminBranch | null;
    site: AdminZone | null;
    siteLabels: AdminLabel[];
    checkpoint: AdminCheckpoint | null;
    checkpointLabels: AdminLabel[];
    role: AdminRole | null;
    user: AdminUser | null;
    userLabels: AdminLabel[];
    device: AdminDevice | null;
    deviceLabels: AdminLabel[];
  } => ({
    startDate: exceptionDetailedLogState.filter.startDate || null,
    endDate: exceptionDetailedLogState.filter.endDate || null,
    startTime: exceptionDetailedLogState.filter.startTime || null,
    endTime: exceptionDetailedLogState.filter.endTime || null,
    branch: exceptionDetailedLogState.selectedBranch || null,
    site: exceptionDetailedLogState.selectedSite || null,
    siteLabels: exceptionDetailedLogState.selectedSiteLabels || [],
    checkpoint: exceptionDetailedLogState.selectedCheckpoint || null,
    checkpointLabels: exceptionDetailedLogState.selectedCheckpointLabels || [],
    role: exceptionDetailedLogState.selectedRole || null,
    user: exceptionDetailedLogState.selectedUser || null,
    userLabels: exceptionDetailedLogState.selectedUserLabels || [],
    device: exceptionDetailedLogState.selectedDevice || null,
    deviceLabels: exceptionDetailedLogState.selectedDeviceLabels || [],
  });

  return (
    <WebAdminLayout activePage="exception-detailed-log">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdZoomIn className="text-blue-500" />}
            title={t(
              "exceptionDetailedLog.title",
              "Exception Detailed Log"
            )}
            description={t(
              "exceptionDetailedLog.description",
              "View and analyze detailed system exception logs with comprehensive information"
            )}
            actions={[
              // Desktop view - show all buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <ExceptionDetailedLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                />
                <Button
                  key="filter"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => setDrawerVisible(true)}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <ExceptionDetailedLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                  buttonText={t(
                    "common.download",
                    "Download"
                  )}
                />
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => setDrawerVisible(true)}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters */}
          <ActiveFilters<FilterState>
            filterGroups={getFilterGroups()}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAll}
            onShowFilters={() => setDrawerVisible(true)}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Exception Detailed Log List */}
          <ExceptionDetailedLogList
            exceptionDetailedLogs={exceptionDetailedLogState.exceptionDetailedLogs}
            loading={loading}
            total={totalItems}
            page={currentPage}
            limit={pageSize}
            onPageChange={handlePageChange}
          />
        </div>

        {/* Filter Drawer */}
        <Drawer
          title={
            <div className="text-lg font-semibold text-gray-800 dark:text-white">
              {t(
                "exceptionDetailedLog.filters.title",
                "Filter Exception Detailed Logs"
              )}
            </div>
          }
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          className="dark:bg-gray-800"
        >
          <AnalyticDrawerForm
            reportType={EReportType.EXCEPTION_DETAILED}
            onApply={handleApplyFilters}
            onClose={() => setDrawerVisible(false)}
            initialValues={getInitialDrawerValues()}
          />
        </Drawer>
      </div>
    </WebAdminLayout>
  );
};

export default ExceptionDetailedLogAdmin;
