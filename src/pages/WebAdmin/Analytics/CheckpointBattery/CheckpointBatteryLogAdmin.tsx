import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MdFilterList,
} from "react-icons/md";
import {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import { <PERSON><PERSON>, Drawer } from "antd";
import CheckpointBatteryLogList from "./Components/CheckpointBatteryLogList";
import AnalyticDrawerForm, {
  AnalyticsFilterState,
} from "../../Analytics/Components/AnalyticDrawerForm";
import { EReportType } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import ActiveFilters, {
  FilterGroup,
  createStartDateFilter,
  createEndDateFilter,
  createSimpleFilter,
} from "../../../../components/Admin/Common/ActiveFilters";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../../store";
import batteryLevelLogAdminSlice, {
  fetchBatteryLevelLogs,
} from "../../../../store/slices/admin/batteryLevelLog.admin.slice";
import { useParams } from "react-router-dom";
import { AdminBranch } from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { AdminCheckpoint } from "../../../../services/mainApi/admin/types/checkpoint.admin.mainApi.types";
import { AdminZone } from "../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { AdminLabel } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import CheckpointBatteryLogExportButton from "./Components/CheckpointBatteryLogExportButton";

// Define interface for filter state
interface FilterState {
  startDate: string | null;
  endDate: string | null;
  selectedBranch: AdminBranch | null;
  site: AdminZone | null;
  siteLabels: AdminLabel[];
  checkpoint: AdminCheckpoint | null;
  checkpointLabels: AdminLabel[];
}

const CheckpointBatteryLogAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  const isFetchingRef = useRef(false);

  // Get state from redux store
  const {
    batteryLevelLogs,
    loading,
    pagination,
    filter,
    selectedBranch,
    selectedSite,
    selectedCheckpoint,
    selectedSiteLabels,
    selectedCheckpointLabels,
  } = useSelector(
    (state: RootState) => state.batteryLevelLogAdmin
  );

  // Filter drawer visibility
  const [filterDrawerVisible, setFilterDrawerVisible] =
    useState(false);

  // Fetch logs when component mounts or filter changes
  useEffect(() => {
    if (branchCode && !isFetchingRef.current) {
      isFetchingRef.current = true;
      dispatch(fetchBatteryLevelLogs({ branchCode }))
        .finally(() => {
          isFetchingRef.current = false;
        });
    }
  }, [dispatch, branchCode, filter]);

  // Handle page change
  const handlePageChange = (
    page: number,
    pageSize?: number
  ) => {
    dispatch(
      batteryLevelLogAdminSlice.actions.setPage(page)
    );
    if (pageSize) {
      dispatch(
        batteryLevelLogAdminSlice.actions.setLimit(pageSize)
      );
    }
  };

  // Get applied filters
  const getAppliedFilters = useCallback((): FilterState => {
    return {
      startDate: filter.startDate,
      endDate: filter.endDate,
      selectedBranch,
      site: selectedSite,
      siteLabels: selectedSiteLabels || [],
      checkpoint: selectedCheckpoint,
      checkpointLabels: selectedCheckpointLabels || [],
    };
  }, [
    filter,
    selectedBranch,
    selectedSite,
    selectedCheckpoint,
    selectedSiteLabels,
    selectedCheckpointLabels,
  ]);

  // Handle applying filters from the drawer
  const handleApplyFilters = (
    filters: AnalyticsFilterState
  ) => {
    // Update branch
    dispatch(
      batteryLevelLogAdminSlice.actions.setSelectedBranch(
        filters.selectedBranch
      )
    );

    // Update site and site labels
    dispatch(
      batteryLevelLogAdminSlice.actions.setSelectedSite(
        filters.site || null
      )
    );
    dispatch(
      batteryLevelLogAdminSlice.actions.setSelectedSiteLabels(
        filters.siteLabels || []
      )
    );

    // Update checkpoint and checkpoint labels
    dispatch(
      batteryLevelLogAdminSlice.actions.setSelectedCheckpoint(
        filters.checkpoint || null
      )
    );
    dispatch(
      batteryLevelLogAdminSlice.actions.setSelectedCheckpointLabels(
        filters.checkpointLabels || []
      )
    );

    // Update date and time filters
    dispatch(
      batteryLevelLogAdminSlice.actions.setStartDate(
        filters.startDate || null
      )
    );
    dispatch(
      batteryLevelLogAdminSlice.actions.setEndDate(
        filters.endDate || null
      )
    );

    // Reset to page 1 when filters change
    dispatch(batteryLevelLogAdminSlice.actions.setPage(1));

    // Close the drawer
    setFilterDrawerVisible(false);
  };

  // Handle clearing a specific filter
  const handleRemoveFilter = (
    filterKey: keyof FilterState
  ) => {
    switch (filterKey) {
      case "startDate":
        dispatch(
          batteryLevelLogAdminSlice.actions.setStartDate(
            null
          )
        );
        break;
      case "endDate":
        dispatch(
          batteryLevelLogAdminSlice.actions.setEndDate(null)
        );
        break;
      case "selectedBranch":
        dispatch(
          batteryLevelLogAdminSlice.actions.setSelectedBranch(
            null
          )
        );
        break;
      case "site":
        dispatch(
          batteryLevelLogAdminSlice.actions.setSelectedSite(
            null
          )
        );
        break;
      case "siteLabels":
        dispatch(
          batteryLevelLogAdminSlice.actions.setSelectedSiteLabels(
            []
          )
        );
        break;
      case "checkpoint":
        dispatch(
          batteryLevelLogAdminSlice.actions.setSelectedCheckpoint(
            null
          )
        );
        break;
      case "checkpointLabels":
        dispatch(
          batteryLevelLogAdminSlice.actions.setSelectedCheckpointLabels(
            []
          )
        );
        break;
    }
  };

  // Handle clearing all filters
  const handleClearAllFilters = () => {
    dispatch(
      batteryLevelLogAdminSlice.actions.clearFilters()
    );
  };

  // Check if there are any active filters
  const hasActiveFilters = () => {
    return (
      !!filter.startDate ||
      !!filter.endDate ||
      !!filter.branchId ||
      !!filter.siteId ||
      filter.siteLabels.length > 0 ||
      !!filter.checkpointId ||
      filter.checkpointLabels.length > 0
    );
  };

  // Configure filter groups for ActiveFilters component
  const filterGroups: FilterGroup[] = useMemo(() => {
    const appliedFilters = getAppliedFilters();

    return [
      {
        key: "dateTime",
        filters: [
          createStartDateFilter(appliedFilters.startDate),
          createEndDateFilter(appliedFilters.endDate),
        ],
      },
      {
        key: "branch",
        filters: [
          createSimpleFilter(
            "branch",
            "selectedBranch",
            appliedFilters.selectedBranch,
            "name"
          ),
        ],
      },
      {
        key: "site",
        filters: [
          createSimpleFilter(
            "site",
            "site",
            appliedFilters.site,
            "name"
          ),
          {
            key: "siteLabels",
            editKey: "siteLabels",
            value:
              appliedFilters.siteLabels?.length > 0
                ? `${appliedFilters.siteLabels.length} site labels`
                : null,
          },
        ],
      },
      {
        key: "checkpoint",
        filters: [
          createSimpleFilter(
            "checkpoint",
            "checkpoint",
            appliedFilters.checkpoint,
            "name"
          ),
          {
            key: "checkpointLabels",
            editKey: "checkpointLabels",
            value:
              appliedFilters.checkpointLabels?.length > 0
                ? `${appliedFilters.checkpointLabels.length} checkpoint labels`
                : null,
          },
        ],
      },
    ];
  }, [getAppliedFilters]);

  // Function to get initial drawer values for the filter form
  const getInitialDrawerValues =
    (): Partial<AnalyticsFilterState> => {
      const appliedFilters = getAppliedFilters();

      return {
        startDate: appliedFilters.startDate || "",
        endDate: appliedFilters.endDate || "",
        selectedBranch:
          appliedFilters.selectedBranch || null,
        site: appliedFilters.site || null,
        siteLabels: appliedFilters.siteLabels || [],
        checkpoint: appliedFilters.checkpoint || null,
        checkpointLabels:
          appliedFilters.checkpointLabels || [],
      };
    };

  // Function to handle editing a filter
  const handleEditFilter = () => {
    setFilterDrawerVisible(true);
  };

  // Function to get filter info for export button
  const getFilterInfo = () => ({
    startDate: filter.startDate,
    endDate: filter.endDate,
    selectedBranch,
    site: selectedSite,
    siteLabels: selectedSiteLabels || [],
    checkpoint: selectedCheckpoint,
    checkpointLabels: selectedCheckpointLabels || [],
  });

  return (
    <WebAdminLayout activePage="checkpoint-battery-log">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdBatteryAlert />}
            title={t(
              "checkpointBatteryLog.title",
              "Checkpoint Battery Log"
            )}
            description={t(
              "checkpointBatteryLog.description",
              "View and manage checkpoint battery status logs"
            )}
            actions={[
              // Desktop view - show both buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <CheckpointBatteryLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                />
                <Button
                  key="filter"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => setFilterDrawerVisible(true)}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button and dropdown for downloads
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <CheckpointBatteryLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                  buttonText={t(
                    "common.download",
                    "Download"
                  )}
                />
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList />}
                  size="large"
                  type="primary"
                  className="bg-indigo-600 hover:bg-indigo-700 border-0 shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
                  onClick={() => setFilterDrawerVisible(true)}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters using the reusable component */}
          <ActiveFilters<FilterState>
            filterGroups={filterGroups}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAllFilters}
            onShowFilters={() => {
              setFilterDrawerVisible(true);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Battery Log List */}
          <CheckpointBatteryLogList
            batteryLogs={batteryLevelLogs}
            loading={loading}
            total={pagination.total || 0}
            page={pagination.page || 1}
            limit={pagination.limit || 10}
            onPageChange={handlePageChange}
          />
        </div>
      </div>

      {/* Filter Drawer with modern styling */}
      <Drawer
        title={
          <div className="text-lg font-semibold text-gray-800 dark:text-white">
            {t(
              "checkpointBatteryLog.filterDrawerTitle",
              "Filter Battery Logs"
            )}
          </div>
        }
        placement="right"
        width={400}
        onClose={() => setFilterDrawerVisible(false)}
        open={filterDrawerVisible}
        bodyStyle={{ padding: 0 }}
        className="dark:bg-gray-800"
      >
        <AnalyticDrawerForm
          reportType={EReportType.CHECKPOINT_BATTERY}
          onApply={handleApplyFilters}
          onClose={() => setFilterDrawerVisible(false)}
          initialValues={getInitialDrawerValues()}
        />
      </Drawer>
    </WebAdminLayout>
  );
};

export default CheckpointBatteryLogAdmin;
