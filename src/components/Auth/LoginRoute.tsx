import { Navigate } from "react-router-dom";
import { useAuth } from "../../hooks/useAuth";
import { Spin } from "antd";
import { routeConfig } from "../../config/routes/routes.tsx";

const LoginRoute = () => {
  const auth = useAuth();

  if (auth.loading) {
    return (
      <div className="h-screen w-screen flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  if (auth.isAuthenticated) {
    if (auth.hasSystemAccess) {
      return (
        <Navigate
          to="/sysadmin/dashboard"
          replace
        />
      );
    }
    if (auth.hasWebAccess) {
      return (
        <Navigate
          to="/admin/branch"
          replace
        />
      );
    }
  }

  return routeConfig.auth.login.component;
};

export default LoginRoute;
