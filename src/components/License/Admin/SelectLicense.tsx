import { Select } from "antd";
import { useEffect, useRef, useState } from "react";
import licenseMainApi from "../../../services/mainApi/license.mainApi";
import { License } from "../../../services/mainApi/types/license.mainApi.types";

interface SelectLicenseProps {
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  placeholder?: string;
}

export default function SelectLicense({
  value,
  onChange,
  className = "",
  disabled = false,
  placeholder = "Select license",
}: SelectLicenseProps) {
  const [licenses, setLicenses] = useState<License[]>([]);
  const [loading, setLoading] = useState(false);
  const isFetching = useRef(false);

  useEffect(() => {
    const fetchLicenses = async () => {
      if (isFetching.current) return;

      try {
        isFetching.current = true;
        setLoading(true);
        const response = await licenseMainApi.getList();
        setLicenses(response.data || []);
      } catch (error) {
        console.error("Failed to fetch licenses:", error);
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchLicenses();
  }, []);

  return (
    <Select
      className={className}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      disabled={disabled}
      loading={loading}
      showSearch
      optionFilterProp="label"
      options={licenses.map((license) => ({
        value: license.id,
        label: license.license_name,
      }))}
    />
  );
}
