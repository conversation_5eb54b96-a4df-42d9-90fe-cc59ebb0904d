import baseMainApi from "../base.mainApi";
import {
  AdminExceptionReportLogListParams,
  AdminExceptionReportLogListResponse,
} from "./types/exceptionReportLog.admin.mainApi.types";

export const exceptionReportLogAdminApi = {
  getExceptionReportLogs: async (
    branchCode: string,
    params?: AdminExceptionReportLogListParams
  ) => {
    const response =
      await baseMainApi.get<AdminExceptionReportLogListResponse>(
        `/web-api/admin/${branchCode}/exception-report-log`,
        { params }
      );
    return response.data;
  },

  exportExceptionReportLogs: async (
    branchCode: string,
    params: AdminExceptionReportLogListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/exception-report-log/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
};