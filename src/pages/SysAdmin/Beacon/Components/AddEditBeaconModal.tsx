import {
  App,
  Form,
  Input,
  Modal,
  Select,
  Switch,
} from "antd";
import { AxiosError } from "axios";
import { useEffect, useCallback, useState } from "react";
import branchSysAdminMainApi from "../../../../services/mainApi/sysadmin/branch.sysadmin.mainApi.ts";
import { Branch } from "../../../../services/mainApi/sysadmin/types/branch.mainApi.types.ts";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks.ts";
import { fetchBeacons } from "../../../../store/slices/sysadmin/beaconList.sysadmin.slice.ts";
import addBeaconSysadminSlice, {
  createBeacon,
  updateBeacon,
} from "../../../../store/slices/sysadmin/addBeacon.sysadmin.slice.ts";

interface ApiErrorResponse {
  message: string;
}

export default function AddEditBeaconModal() {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const dispatch = useAppDispatch();

  // Get state from Redux
  const {
    open,
    mode,
    beaconEdit,
    loading,
    errorMessage,
    parent_branch_id,
    beacon_name,
    beacon_description,
    beacon_uuid,
    counter_customer_code,
    pip_api_key,
    active,
  } = useAppSelector((state) => state.addBeaconSysadmin);

  // Get branches data
  const [branches, setBranches] = useState<Branch[]>([]);

  const fetchData = useCallback(async () => {
    try {
      const branchResponse =
        await branchSysAdminMainApi.getList();
      setBranches(branchResponse.data || []);
    } catch (err) {
      const error = err as AxiosError<ApiErrorResponse>;
      message.error(
        error.response?.data?.message ||
          "Failed to fetch data. Please try again."
      );
    }
  }, [message]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // Update form values to Redux state
      dispatch(
        addBeaconSysadminSlice.actions.setParentBranchId(
          values.branch
        )
      );
      dispatch(
        addBeaconSysadminSlice.actions.setBeaconName(
          values.name
        )
      );
      dispatch(
        addBeaconSysadminSlice.actions.setBeaconDescription(
          values.description || ""
        )
      );
      dispatch(
        addBeaconSysadminSlice.actions.setBeaconUuid(
          values.uuid
        )
      );
      dispatch(
        addBeaconSysadminSlice.actions.setCounterCustomerCode(
          values.counter_code || ""
        )
      );
      dispatch(
        addBeaconSysadminSlice.actions.setPipApiKey(
          values.pip_api_key || ""
        )
      );
      dispatch(
        addBeaconSysadminSlice.actions.setActive(
          values.active
        )
      );

      if (mode === "update" && beaconEdit) {
        // Update existing beacon
        await dispatch(
          updateBeacon(beaconEdit.id)
        ).unwrap();
        message.success(
          `Beacon "${values.name}" has been updated successfully`
        );
      } else {
        // Create new beacon
        await dispatch(createBeacon()).unwrap();
        message.success(
          `New beacon "${values.name}" has been created successfully`
        );
      }

      // Refresh beacon list
      dispatch(fetchBeacons());

      form.resetFields();
      dispatch(addBeaconSysadminSlice.actions.close());
    } catch (err) {
      if (
        err instanceof Error &&
        err.message !== "The validation of the form failed."
      ) {
        const error = err as AxiosError<ApiErrorResponse>;
        dispatch(
          addBeaconSysadminSlice.actions.setError(
            error.response?.data?.message ||
              "Failed to save beacon. Please try again."
          )
        );
      }
    }
  };

  // Handle close modal
  const handleClose = () => {
    dispatch(addBeaconSysadminSlice.actions.close());
  };

  // Reset form when modal opens or beacon changes
  useEffect(() => {
    if (open) {
      fetchData();

      // Reset form with beacon data if editing
      if (mode === "update" && beaconEdit) {
        form.setFieldsValue({
          name: beacon_name,
          description: beacon_description,
          branch: parent_branch_id,
          uuid: beacon_uuid,
          counter_code: counter_customer_code,
          pip_api_key: pip_api_key,
          active: active,
        });
      } else {
        // Default values for new beacon
        form.setFieldsValue({
          active: true,
        });
      }
    }
  }, [
    fetchData,
    form,
    open,
    mode,
    beaconEdit,
    parent_branch_id,
    beacon_name,
    beacon_description,
    beacon_uuid,
    counter_customer_code,
    pip_api_key,
    active,
  ]);

  // Show error messages from Redux state
  useEffect(() => {
    if (errorMessage && errorMessage.length > 0) {
      errorMessage.forEach((err) => {
        message.error(err);
      });
    }
  }, [errorMessage, message]);

  return (
    <Modal
      title={
        <div className="text-lg font-semibold">
          {mode === "update"
            ? "Edit Beacon"
            : "Add New Beacon"}
        </div>
      }
      open={open}
      onCancel={handleClose}
      onOk={handleSubmit}
      width={700}
      className="rounded-xl"
      okButtonProps={{
        className: "bg-blue-500 hover:bg-blue-600 border-0",
      }}
      confirmLoading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        className="mt-4"
      >
        {/* Beacon Information Section */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-4">
            Beacon Information
          </h3>

          <Form.Item
            name="name"
            label="Beacon Name"
            rules={[
              {
                required: true,
                message: "Please enter beacon name",
              },
            ]}
          >
            <Input
              className="rounded-lg"
              placeholder="Enter beacon name"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea
              className="rounded-lg"
              placeholder="Enter beacon description"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="branch"
            label="Parent Branch"
            rules={[
              {
                required: true,
                message: "Please select a branch",
              },
            ]}
          >
            <Select
              className="rounded-lg"
              placeholder="Select branch"
              loading={loading}
            >
              {branches.map((branch) => (
                <Select.Option
                  key={branch.id}
                  value={branch.id}
                >
                  {branch.branch_name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              name="uuid"
              label="Beacon UUID"
              rules={[
                {
                  required: true,
                  message: "Please enter UUID",
                },
                {
                  pattern:
                    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
                  message:
                    "Please enter a valid UUID format",
                },
              ]}
            >
              <Input
                className="rounded-lg"
                placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
              />
            </Form.Item>

            <Form.Item
              name="counter_code"
              label="Counter Customer Code"
              rules={[
                {
                  required: true,
                  message:
                    "Please enter counter customer code",
                },
              ]}
            >
              <Input
                className="rounded-lg"
                placeholder="Enter counter customer code"
              />
            </Form.Item>
          </div>

          <Form.Item
            name="pip_api_key"
            label="PIP API Key"
          >
            <Input
              className="rounded-lg"
              placeholder="Enter PIP API key (optional)"
            />
          </Form.Item>

          <Form.Item
            name="active"
            label="Status"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </div>
      </Form>
    </Modal>
  );
}
