import { ReactNode, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Spin } from "antd";
import i18n from "../i18n";

interface I18nProviderProps {
  children: ReactNode;
}

export function I18nProvider({
  children,
}: I18nProviderProps) {
  const [initialized, setInitialized] = useState(false);
  const { ready } = useTranslation();

  useEffect(() => {
    const handleLanguageChanged = () => {
      if (!initialized && ready) {
        setInitialized(true);
      }
    };

    i18n.on("initialized", handleLanguageChanged);
    i18n.on("loaded", handleLanguageChanged);

    // Check if already initialized
    if (i18n.isInitialized && ready) {
      setInitialized(true);
    }

    return () => {
      i18n.off("initialized", handleLanguageChanged);
      i18n.off("loaded", handleLanguageChanged);
    };
  }, [initialized, ready]);

  if (!initialized || !ready) {
    return (
      <div className="h-screen w-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <Spin
          size="large"
          tip="Loading translations..."
        />
      </div>
    );
  }

  return <>{children}</>;
}
