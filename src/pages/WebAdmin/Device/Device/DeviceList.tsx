import { useCallback, useEffect, useRef } from "react";
import { Empty, Pagination, Spin } from "antd";
import DeviceListItem from "./DeviceListItem";
import { AppDispatch, RootState } from "../../../../store";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import deviceAdminSlice, {
  fetchDevicesAdmin,
} from "../../../../store/slices/admin/device.admin.slice";
import ModalAddDevice from "../Components/ModalAddDevice.tsx";
import { useTranslation } from "react-i18next";

interface DeviceListProps {
  itemsPerRow?: number;
}

const DeviceList = ({ itemsPerRow = 3 }: DeviceListProps) => {
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation();
  const isFetching = useRef(false);
  const { actions } = deviceAdminSlice;
  const { branchCode } = useParams<{
    branchCode: string;
  }>();
  
  // Function to get grid columns class based on items per row
  const getGridColsClass = () => {
    switch(itemsPerRow) {
      case 1:
        return "grid-cols-1";
      case 2:
        return "grid-cols-1 md:grid-cols-2";
      case 3:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
      case 4:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-4";
      case 5:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-5";
      case 6:
        return "grid-cols-1 md:grid-cols-3 lg:grid-cols-6";
      default:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
    }
  };

  const { devices, loading, pagination, filter } =
    useSelector((state: RootState) => state.deviceAdmin);

  const fetch = useCallback(async () => {
    if (isFetching.current) return;
    isFetching.current = true;
    await dispatch(fetchDevicesAdmin(branchCode || ""));
    isFetching.current = false;
  }, [dispatch, branchCode]);

  useEffect(() => {
    fetch();
  }, [fetch, filter.orderBy, filter.orderDirection]);

  const handlePageChange = (
    page: number,
    pageSize: number
  ) => {
    actions.setFilter({
      page,
      limit: pageSize,
    });
    fetch();
  };

  return (
    <div className="space-y-6">
      {/* Device Modal Component */}
      <ModalAddDevice />

      {/* Device List */}
      <div>
        {loading ? (
          <div className="flex justify-center py-8 items-center bg-white dark:bg-gray-800 rounded-xl shadow-sm">
            <Spin size="large" />
          </div>
        ) : devices.length === 0 ? (
          <Empty
            description={t("device.noDevicesFound")}
            className="my-8"
          />
        ) : (
          <div className={`grid ${getGridColsClass()} gap-4`}>
            {devices.map((device) => (
              <DeviceListItem
                key={device.id}
                device={device}
              />
            ))}
          </div>
        )}
      </div>

      {/* Pagination */}
      <div className="flex justify-end bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
        <Pagination
          current={filter.page || 1}
          pageSize={filter.limit || 10}
          total={pagination.total || 0}
          onChange={handlePageChange}
          showSizeChanger={false}
          showTotal={(total) => (
            <span className="text-gray-500 dark:text-gray-400">
              {t("common.pagination.total", { total })}
            </span>
          )}
          className="dark:[&_.ant-pagination-item-active]:bg-blue-600
                     dark:[&_.ant-pagination-item-active]:border-blue-600
                     dark:[&_.ant-pagination-item]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:border-gray-600
                     dark:[&_.ant-pagination-item-link]:bg-transparent
                     dark:[&_.ant-pagination-item]:bg-transparent
                     dark:[&_.ant-pagination-item-active_a]:text-white
                     dark:[&_.ant-pagination-item_a]:text-gray-400
                     dark:[&_.ant-pagination-prev_button]:text-gray-400
                     dark:[&_.ant-pagination-next_button]:text-gray-400"
        />
      </div>
    </div>
  );
};

export default DeviceList;
