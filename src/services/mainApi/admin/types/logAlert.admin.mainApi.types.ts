import { BaseResponse, BasePaginationMeta } from "../../types/base.mainApi.types";
import { AdminBranch } from "./branch.admin.mainApi.types";
import { AdminRole } from "./roles.admin.mainApi.types";
import { AdminUser } from "./user.admin.mainApi.types";

export interface AdminAlertEvent {
  id: string;
  event_name: string;
  event_description: string | null;
  active: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string | null;
}

// Define payload types based on alert_event_id
export interface AlarmPayload {
  type: "alarm";
  alarm: {
    id: string;
    uuid: string;
    role_id: string;
    user_id: string;
    branch_id: string;
    device_id: number | null;
    role_name: string;
    user_name: string;
    event_time: string;
    branch_name: string;
    device_name: string | null;
    timezone_id: string;
    end_latitude: number | null;
    end_date_time: string | null;
    end_longitude: number | null;
    timezone_name: string;
    start_latitude: number | null;
    start_date_time: string;
    start_longitude: number | null;
    parent_branch_id: string;
    original_submitted_time: string;
  };
}

// Union type for all possible payload types
export type LogAlertPayload =
  | AlarmPayload
  | any; // Fallback for other event types not yet defined

export interface AdminLogAlert {
  id: string;
  uuid: string;
  alert_event_id: string;
  alert_event_name: string;
  log_id: string;
  log_uuid: string;
  reference_name: string;
  parent_branch_id: string;
  branch_id: string;
  user_id: string;
  role_id: string;
  payload_data: LogAlertPayload;
  deleted_on_dashboard: boolean;
  original_submitted_time: string;
  event_time: string;
  branch: AdminBranch;
  role: AdminRole;
  user: AdminUser;
  alert_event: AdminAlertEvent;
}

export interface AdminLogAlertListParams {
  page?: number;
  limit?: number | null;
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  branch_id?: string | number;
  role_id?: string | number;
  user_id?: string | number;
  user_labels?: string | number | (string | number)[];
  alert_event_ids?: string | number | (string | number)[];
  order_by?: string;
  order_direction?: "ASC" | "DESC";
}

export interface AdminLogAlertListMeta extends BasePaginationMeta {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export type AdminLogAlertListResponse = BaseResponse<AdminLogAlert[], AdminLogAlertListMeta>;
