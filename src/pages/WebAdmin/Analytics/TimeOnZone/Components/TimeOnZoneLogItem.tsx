import React from "react";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import duration from "dayjs/plugin/duration";
import {Bad<PERSON>, Tooltip} from "antd";
import {FiClock, FiLogIn, FiLogOut, FiMonitor, FiUser, FiMapPin, FiHome, FiCalendar} from "react-icons/fi";
import {useTranslation} from "react-i18next";
import {TimeOnZoneEntry} from "../../../../../services/mainApi/admin/types/timeOnZone.admin.mainApi.types.ts";

dayjs.extend(relativeTime);
dayjs.extend(duration);

// Interface for the time on zone log item props
interface TimeOnZoneLogItemProps {
  timeOnZoneLog: TimeOnZoneEntry
}

const TimeOnZoneLogItem: React.FC<TimeOnZoneLogItemProps> = ({timeOnZoneLog}) => {
  const {t} = useTranslation();

  // Format duration in hours, minutes and seconds
  const formatDuration = (durationInSeconds: number) => {
    const durationObj = dayjs.duration(durationInSeconds, 'seconds');
    const hours = Math.floor(durationObj.asHours());
    const minutes = Math.floor(durationObj.minutes());
    const seconds = Math.floor(durationObj.seconds());

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-0 p-4 mb-4">
      {/* Header - Zone Name with Branch info */}
      <div
        className="mb-3 -mx-4 -mt-4 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 rounded-t-xl border-b border-gray-100 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
            {timeOnZoneLog.zone_name}
          </h3>
          <Tooltip title={`${t("timeOnZoneLog.branchCode", "Branch Code")}: ${timeOnZoneLog.branch_code}`}>
            <Badge color="blue" count={timeOnZoneLog.branch_name} className="font-medium text-xs" />
          </Tooltip>
        </div>
      </div>

      {/* Content - Three Column Layout */}
      <div className="grid grid-cols-3 gap-3 px-1">
        {/* Left Column */}
        <div className="space-y-2">
          {/* User Info */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
              <FiUser className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("timeOnZoneLog.user", "User")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {timeOnZoneLog.user_name}
                </span>
              </div>
            </div>
          </div>

          {/* Device Info */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-purple-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 flex-shrink-0">
              <FiMonitor className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("timeOnZoneLog.device", "Device")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {timeOnZoneLog.device_name}
                </span>
              </div>
            </div>
          </div>

          {/* Branch Info */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-amber-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-amber-100 dark:bg-amber-900 text-amber-600 dark:text-amber-300 flex-shrink-0">
              <FiHome className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("timeOnZoneLog.branch", "Branch")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {timeOnZoneLog.branch_name}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Middle Column */}
        <div className="space-y-2">
          {/* Entry Time */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
              <FiLogIn className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("timeOnZoneLog.entry", "Entry")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {dayjs(timeOnZoneLog.entry).format("DD MMM YYYY HH:mm:ss")}
                </span>
              </div>
            </div>
          </div>

          {/* Exit Time */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-red-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300 flex-shrink-0">
              <FiLogOut className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("timeOnZoneLog.exit", "Exit")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {dayjs(timeOnZoneLog.exit).format("DD MMM YYYY HH:mm:ss")}
                </span>
              </div>
            </div>
          </div>

          {/* Original Submission Time */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
              <FiCalendar className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("timeOnZoneLog.originalSubmission", "Original Submission")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {dayjs(timeOnZoneLog.original_submitted_time).format("DD MMM YYYY HH:mm:ss")}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-2">
          {/* Duration */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-indigo-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex-shrink-0">
              <FiClock className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("timeOnZoneLog.duration", "Duration")}:
                </span>
                <Badge color="blue" count={formatDuration(timeOnZoneLog.duration)} className="font-medium text-xs"/>
              </div>
            </div>
          </div>

          {/* Location */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-cyan-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-cyan-100 dark:bg-cyan-900 text-cyan-600 dark:text-cyan-300 flex-shrink-0">
              <FiMapPin className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("timeOnZoneLog.coordinates", "Coordinates")}:
                </span>
                <Tooltip title={`${timeOnZoneLog.latitude}, ${timeOnZoneLog.longitude}`}>
                  <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate cursor-pointer">
                    {timeOnZoneLog.latitude.toFixed(5)}, {timeOnZoneLog.longitude.toFixed(5)}
                  </span>
                </Tooltip>
              </div>
            </div>
          </div>

          {/* View on Map Button */}
          <button
            onClick={() => window.open(`https://maps.google.com/?q=${timeOnZoneLog.latitude},${timeOnZoneLog.longitude}`, '_blank')}
            className="w-full mt-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 px-3 py-1.5 rounded-md transition-colors duration-200 flex items-center justify-center gap-1"
          >
            <FiMapPin className="w-3 h-3" />
            {t("timeOnZoneLog.viewOnMap", "View on Map")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TimeOnZoneLogItem;
