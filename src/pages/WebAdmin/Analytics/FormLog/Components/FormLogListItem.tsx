import React, {useState} from "react";
import {Button, Dropdown, Image, Modal, Spin, Tag, Tooltip,} from "antd";
import {
  FiCalendar,
  FiChevronDown,
  FiChevronUp,
  FiClock,
  FiDownload,
  FiFileText,
  FiGrid,
  FiImage,
  FiList,
  FiUser,
} from "react-icons/fi";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import {useTranslation} from "react-i18next";
import {AdminFormLog} from "../../../../../services/mainApi/admin/types/formLog.admin.mainApi.types";
import {useParams} from "react-router-dom";
import {formLogAdminApi} from "../../../../../services/mainApi/admin/formLog.admin.mainApi";
import {MdTask} from "react-icons/md";

dayjs.extend(relativeTime);

interface FormLogListItemProps {
  formLog: AdminFormLog;
}

const FormLogListItem: React.FC<FormLogListItemProps> = ({
                                                           formLog,
                                                         }) => {
  const {t} = useTranslation();
  const {branchCode} = useParams<{
    branchCode: string;
  }>();
  const [fieldsVisible, setFieldsVisible] = useState(false);
  const [imageModalVisible, setImageModalVisible] =
    useState(false);
  const [currentImage, setCurrentImage] = useState("");
  const [isDownloading, setIsDownloading] = useState(false);

  // Handle image preview
  const handleImagePreview = (imageUrl: string) => {
    setCurrentImage(imageUrl);
    setImageModalVisible(true);
  };

  // Safe format with dayjs that handles null values
  const formatDateTime = (
    dateStr: string | null,
    format: string
  ): string => {
    if (!dateStr) return "-";
    try {
      return dayjs(dateStr).format(format);
    } catch {
      // Ignore format errors, return the original string
      return dateStr || "-";
    }
  };

  // Safe fromNow function that handles null values
  const formatTimeAgo = (
    dateStr: string | null
  ): string => {
    if (!dateStr) return "-";
    try {
      return dayjs(dateStr).fromNow();
    } catch {
      // Ignore format errors, return the original string
      return dateStr || "-";
    }
  };

  // Updated handler functions for downloads
  const handleDownloadSpreadsheet = async (
    formLog: AdminFormLog
  ) => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const blob = await formLogAdminApi.exportFormLogById(
        branchCode,
        formLog.id,
        "spreadsheet",
        "buffer"
      );

      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `form_log_${formLog.id}.xlsx`;
      link.click();

      URL.revokeObjectURL(url);
    } catch (error) {
      console.error(
        "Error downloading spreadsheet:",
        error
      );
    } finally {
      setIsDownloading(false);
    }
  };

  const downloadPdf = async () => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const blob = await formLogAdminApi.exportFormLogById(
        branchCode,
        formLog.id,
        "pdf",
        "buffer"
      );

      // Create URL and open in new tab instead of downloading
      const url = URL.createObjectURL(blob);
      window.open(url, "_blank");

      // Still need to revoke the URL when done, but we need to delay it
      // to ensure the new tab has time to load the resource
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 5000); // 5 seconds delay before cleanup
    } catch (error) {
      console.error("Error opening PDF:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  const exportMenu = {
    items: [
      {
        key: "spreadsheet",
        icon: <FiGrid className="text-lg"/>,
        label: t(
          "formLog.downloadSpreadsheet",
          "Export as Spreadsheet"
        ),
        onClick: () => handleDownloadSpreadsheet(formLog),
      },
      {
        key: "pdf",
        icon: <FiFileText className="text-lg"/>,
        label: t("formLog.downloadPdf", "Export as PDF"),
        onClick: downloadPdf,
      },
    ],
  };

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-0 p-4 mb-4">
      {/* Header - Form Name with gradient background */}
      <div
        className="mb-3 -mx-4 -mt-4 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 rounded-t-xl border-b border-gray-100 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
            {formLog.form_name}
          </h3>
          <Tooltip
            title={formatDateTime(
              formLog.original_submitted_time,
              "DD MMM YYYY HH:mm:ss"
            )}
          >
            <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
              <FiClock className="inline"/>
              {formatTimeAgo(formLog.original_submitted_time)}
            </span>
          </Tooltip>
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {formLog.branch_name ||
            t("formLog.noBranch", "No Branch")}
        </p>
      </div>

      {/* Two Column Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Left Column */}
        <div className="space-y-3">
          {/* User Info - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-blue-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex-shrink-0">
              <FiUser className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("formLog.user", "User")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {formLog.user_name}
                </span>
                <Tag color="blue" className="ml-1">{formLog.role_name}</Tag>
              </div>
            </div>
          </div>

          {/* Branch - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-green-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex-shrink-0">
              <FiList className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("formLog.branch", "Branch")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {formLog.branch_name || t("formLog.noBranch", "No Branch")}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-3">
          {/* Timestamp Info - compact */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-purple-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200">
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 flex-shrink-0">
              <FiCalendar className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("formLog.dateTime", "Date/Time")}:
                </span>
                <span className="text-sm text-gray-800 dark:text-gray-200 font-medium truncate">
                  {formatDateTime(formLog.original_submitted_time, "DD MMM YYYY HH:mm:ss")}
                </span>
              </div>
            </div>
          </div>

          {/* Form Fields Toggle Button - styled consistently */}
          <div
            className="group flex items-center gap-2 text-sm hover:bg-indigo-50 dark:hover:bg-gray-700 py-1 px-2 rounded-md transition-colors duration-200"
          >
            <div
              className="flex items-center justify-center w-7 h-7 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex-shrink-0"
            >
              <MdTask className="w-4 h-4"/>
            </div>
            <div className="flex flex-col min-w-0">
              <Button
                type="default"
                size="small"
                className="flex items-center gap-1"
                onClick={() => setFieldsVisible(!fieldsVisible)}
                icon={fieldsVisible ? <FiChevronUp/> : <FiChevronDown/>}
              >
                {t("formLog.viewFields", "View Form Fields")}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Collapsible Fields Section */}
      {fieldsVisible && formLog.fields.length > 0 && (
        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            {t("formLog.fields", "Form Fields")}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-3">
            {formLog.fields.map((field) => (
              <div
                key={field.id}
                className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border border-gray-100 dark:border-gray-600"
              >
                <div className="font-medium text-gray-700 dark:text-gray-200 mb-1">
                  {field.form_field_name}
                </div>

                {(field.field_type_name === "image" || field.field_type_name === "signature") ? (
                  <div className="flex items-center gap-2 mt-1">
                    <FiImage className="text-indigo-500"/>
                    {field.field_type_value ? (
                      <div className="flex items-center gap-2">
                        <Image
                          src={field.field_type_value}
                          alt={field.form_field_name}
                          width={40}
                          height={40}
                          className="rounded object-cover cursor-pointer border border-gray-200 dark:border-gray-500"
                          preview={false}
                          onClick={() => handleImagePreview(field.field_type_value as string)}
                        />
                        <Button
                          type="link"
                          size="small"
                          onClick={() => handleImagePreview(field.field_type_value as string)}
                          className="text-blue-500 hover:text-blue-700 dark:text-blue-400 p-0"
                        >
                          {t("formLog.viewImage", "View Image")}
                        </Button>
                      </div>
                    ) : (
                      <span className="text-gray-500 dark:text-gray-400 italic">
                        {t("formLog.noImage", "No image provided")}
                      </span>
                    )}
                  </div>
                ) : field.field_type_name === "checkbox" ? (
                  <div className="mt-1">
                    <Tag
                      color={field.field_type_value === "true" ? "success" : "error"}
                      className="px-3 py-1"
                    >
                      {field.field_type_value === "true"
                        ? <span className="flex items-center gap-1">✓ {t("common.yes", "Yes")}</span>
                        : <span className="flex items-center gap-1">✗ {t("common.no", "No")}</span>
                      }
                    </Tag>
                  </div>
                ) : (
                  <div className="text-gray-800 dark:text-gray-200 mt-1 break-words">
                    {field.field_type_value ||
                        <span className="text-gray-500 dark:text-gray-400 italic">
                        {t("formLog.noValue", "No value")}
                      </span>
                    }
                  </div>
                )}

                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  {t("formLog.fieldType", "Field Type")}: {field.field_type_name}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Footer Section */}
      <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <div className="text-xs text-gray-500 dark:text-gray-400 flex flex-wrap gap-2">
          <span>
            {t("formLog.id", "ID")}: {formLog.uuid}
          </span>
          <span>•</span>
          <span>
            {t("formLog.submittedTime", "Submitted")}:{" "}
            {formatDateTime(
              formLog.original_submitted_time,
              "DD MMM YYYY HH:mm"
            )}
          </span>
        </div>
        <div className="flex gap-2">
          <Dropdown
            menu={exportMenu}
            placement="bottomRight"
          >
            <Button
              type="default"
              icon={
                isDownloading ? (
                  <Spin size="small"/>
                ) : (
                  <FiDownload/>
                )
              }
              disabled={isDownloading}
              className="flex items-center gap-2"
            >
              {t("common.export", "Export")}
            </Button>
          </Dropdown>
        </div>
      </div>

      {/* Image Preview Modal */}
      <Modal
        open={imageModalVisible}
        footer={null}
        onCancel={() => setImageModalVisible(false)}
        width={800}
      >
        <Image
          src={currentImage}
          alt={t("formLog.imagePreview", "Image Preview")}
          style={{width: "100%"}}
          preview={false}
        />
      </Modal>
    </div>
  );
};

export default FormLogListItem;
