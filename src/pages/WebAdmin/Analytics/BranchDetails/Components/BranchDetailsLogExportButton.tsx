import React, { useState } from "react";
import { <PERSON><PERSON>, Dropdown, Toolt<PERSON> } from "antd";
import { useTranslation } from "react-i18next";
import { FiDownload, FiGrid, FiFileText } from "react-icons/fi";
import { useParams } from "react-router-dom";
import { AdminBranch } from "../../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import { branchDetailsLogAdminMainApi } from "../../../../../services/mainApi/admin/branchDetailsLog.admin.mainApi";
import type { MenuProps } from "antd";

interface BranchDetailsLogExportButtonProps {
  filters: {
    selectedBranch: AdminBranch | null;
    branchId: string | number | null;
  };
  className?: string;
  buttonText?: string;
  showPdf?: boolean;
  showSpreadsheet?: boolean;
}

type ExportFormat = "pdf" | "spreadsheet";

const BranchDetailsLogExportButton: React.FC<BranchDetailsLogExportButtonProps> = ({
  filters,
  className,
  buttonText,
  showPdf = true,
  showSpreadsheet = true,
}) => {
  const { t } = useTranslation();
  const [isDownloading, setIsDownloading] = useState(false);
  const { branchCode } = useParams<{ branchCode: string }>();

  const handleDownload = async (format: ExportFormat) => {
    try {
      setIsDownloading(true);

      if (!branchCode) {
        console.error("Branch code is missing");
        return;
      }

      const blob = await branchDetailsLogAdminMainApi.exportBranchDetailsLogs(
        branchCode,
        {
          branch_id: filters.branchId?.toString() || undefined,
        },
        format,
        "buffer"
      );

      if (format === "pdf") {
        // Create URL and open in new tab
        const url = URL.createObjectURL(blob);
        window.open(url, "_blank");

        // Delay URL revocation to ensure the new tab has time to load
        setTimeout(() => {
          URL.revokeObjectURL(url);
        }, 5000);
      } else {
        // Create URL and trigger download
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `branch_details_logs_${new Date().toISOString().slice(0, 10)}.xlsx`;
        link.click();

        // Clean up
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error(`Error downloading ${format}:`, error);
    } finally {
      setIsDownloading(false);
    }
  };

  // If only one format is shown, render a simple button
  if (
    (showPdf && !showSpreadsheet) ||
    (!showPdf && showSpreadsheet)
  ) {
    const format = showPdf ? "pdf" : "spreadsheet";
    const tooltipText = showPdf
      ? t("branchDetailsLog.downloadPdf", "Download as PDF")
      : t(
          "branchDetailsLog.downloadSpreadsheet",
          "Download as Spreadsheet"
        );

    return (
      <Tooltip title={tooltipText}>
        <Button
          icon={<FiDownload />}
          loading={isDownloading}
          onClick={() => handleDownload(format)}
          size="large"
          className={className}
        >
          {buttonText ||
            (showPdf
              ? t("branchDetailsLog.pdf", "PDF")
              : t(
                  "branchDetailsLog.spreadsheet",
                  "Spreadsheet"
                ))}
        </Button>
      </Tooltip>
    );
  }

  // Otherwise, render a dropdown with both options
  const items: MenuProps["items"] = [
    {
      key: "pdf",
      label: t("branchDetailsLog.exportPdf", "Export as PDF"),
      icon: <FiFileText className="text-lg" />,
      onClick: () => handleDownload("pdf"),
      disabled: !showPdf,
    },
    {
      key: "spreadsheet",
      label: t(
        "branchDetailsLog.exportSpreadsheet",
        "Export as Spreadsheet"
      ),
      icon: <FiGrid className="text-lg" />,
      onClick: () => handleDownload("spreadsheet"),
      disabled: !showSpreadsheet,
    },
  ];

  return (
    <Dropdown
      menu={{ items }}
      disabled={isDownloading}
      placement="bottomRight"
    >
      <Button
        icon={<FiDownload />}
        loading={isDownloading}
        size="large"
        className={className}
      >
        {buttonText || t("branchDetailsLog.export", "Export")}
      </Button>
    </Dropdown>
  );
};

export default BranchDetailsLogExportButton;
