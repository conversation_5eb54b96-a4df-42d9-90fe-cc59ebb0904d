import { Modal } from "antd";
import { Branch } from "../../../../services/mainApi/sysadmin/types/branch.mainApi.types.ts";

interface DeleteBranchModalProps {
  visible: boolean;
  branch: Branch | null;
  onClose: () => void;
  onSuccess: () => Promise<void>;
}

export default function DeleteBranchModal({
  visible,
  branch,
  onClose,
  onSuccess,
}: DeleteBranchModalProps) {
  const handleDelete = async () => {
    try {
      await onSuccess();
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  return (
    <Modal
      title={
        <div className="text-lg font-semibold text-red-600">
          Delete Branch
        </div>
      }
      open={visible}
      onCancel={onClose}
      onOk={handleDelete}
      okText="Delete"
      okButtonProps={{
        danger: true,
        className:
          "bg-red-500 hover:bg-red-600 border-0 text-white",
      }}
      width={500}
      className="rounded-xl"
    >
      <div className="py-4">
        <p className="text-gray-600">
          Are you sure you want to delete the branch{" "}
          <span className="font-semibold text-gray-800">
            {branch?.branch_name}
          </span>
          ? This action cannot be undone.
        </p>
      </div>
    </Modal>
  );
}
