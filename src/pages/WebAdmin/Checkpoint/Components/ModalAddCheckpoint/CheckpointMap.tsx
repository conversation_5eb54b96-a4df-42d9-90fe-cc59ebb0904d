import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useMap,
  useMapEvents,
  ZoomControl,
} from "react-leaflet";
import { LatLng, Icon } from "leaflet";
import "leaflet/dist/leaflet.css";
import { useEffect, useState } from "react";
import { AutoComplete, Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import debounce from "lodash/debounce";

// Definisikan custom icon menggunakan file lokal
const customIcon = new Icon({
  iconUrl: "/marker-icon.png",
  shadowUrl: "/marker-shadow.png",
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
});

interface LocationInfo {
  latitude: number;
  longitude: number;
  address?: string;
  name?: string;
  raw?: any; // untuk menyimpan data mentah dari API jika diperlukan
}

interface CheckpointMapProps {
  onLocationSelect?: (locationInfo: LocationInfo) => void;
  initialLocation?: {
    latitude: number;
    longitude: number;
  };
}

interface SearchResult {
  label: string;
  value: string;
  latitude: number;
  longitude: number;
  raw?: any;
}

// Komponen untuk menangani pencarian dan pemindahan map
const SearchControl = ({
  onLocationSelect,
}: {
  onLocationSelect?: (info: LocationInfo) => void;
}) => {
  const map = useMap();
  const [options, setOptions] = useState<SearchResult[]>(
    []
  );
  const [loading, setLoading] = useState(false);

  const searchLocation = async (query: string) => {
    if (!query) {
      setOptions([]);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}`
      );
      const data = await response.json();

      const results: SearchResult[] = data.map(
        (item: any) => ({
          label: item.display_name,
          value: item.display_name,
          latitude: parseFloat(item.lat),
          longitude: parseFloat(item.lon),
          raw: item,
        })
      );

      setOptions(results);
    } catch (error) {
      console.error("Error searching location:", error);
    } finally {
      setLoading(false);
    }
  };

  const debouncedSearch = debounce(searchLocation, 500);

  const handleSelect = (
    _value: string,
    option: SearchResult
  ) => {
    const { latitude, longitude, raw } = option;
    map.setView([latitude, longitude], 16);

    onLocationSelect?.({
      latitude,
      longitude,
      address: option.label,
      name: raw?.name || raw?.display_name,
      raw,
    });
  };

  return (
    <div
      className="leaflet-top leaflet-right"
      style={{
        zIndex: 1000,
        margin: "10px",
        right: "10px",
        width: "300px",
      }}
    >
      <div className="leaflet-control">
        <AutoComplete
          options={options}
          onSelect={handleSelect}
          onSearch={debouncedSearch}
          style={{ width: "100%" }}
        >
          <Input
            size="large"
            placeholder="Search location..."
            prefix={<SearchOutlined />}
            disabled={loading}
          />
        </AutoComplete>
      </div>
    </div>
  );
};

// Komponen untuk menangani resize map
const MapResizer = () => {
  const map = useMap();

  useEffect(() => {
    setTimeout(() => {
      map.invalidateSize();
    }, 200);
  }, [map]);

  return null;
};

// Komponen untuk menangani klik pada peta
const LocationMarker = ({
  onLocationSelect,
  initialLocation,
}: {
  onLocationSelect?: (info: LocationInfo) => void;
  initialLocation?: { latitude: number; longitude: number };
}) => {
  const [position, setPosition] = useState<LatLng | null>(
    initialLocation
      ? new LatLng(
          initialLocation.latitude,
          initialLocation.longitude
        )
      : null
  );

  useMapEvents({
    async click(e) {
      const { lat, lng } = e.latlng;
      setPosition(e.latlng);

      // Reverse geocoding untuk mendapatkan alamat
      try {
        const response = await fetch(
          `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`
        );
        const data = await response.json();

        onLocationSelect?.({
          latitude: lat,
          longitude: lng,
          address: data.display_name,
          name: data.name,
          raw: data,
        });
      } catch (error) {
        console.error("Error reverse geocoding:", error);
        onLocationSelect?.({
          latitude: lat,
          longitude: lng,
        });
      }
    },
  });

  return position ? (
    <Marker
      position={position}
      icon={customIcon}
    >
      <Popup>
        Selected Location
        <br />
        Lat: {position.lat.toFixed(6)}
        <br />
        Lng: {position.lng.toFixed(6)}
      </Popup>
    </Marker>
  ) : null;
};

const CheckpointMap = ({
  onLocationSelect,
  initialLocation,
}: CheckpointMapProps) => {
  const defaultCenter = initialLocation || {
    latitude: -6.2088,
    longitude: 106.8456,
  };

  return (
    <div className="h-[400px] w-full mb-4">
      <MapContainer
        center={[
          defaultCenter.latitude,
          defaultCenter.longitude,
        ]}
        zoom={13}
        scrollWheelZoom={true}
        style={{
          height: "100%",
          width: "100%",
          borderRadius: "8px",
        }}
        zoomControl={false}
      >
        <MapResizer />
        <ZoomControl position="bottomright" />
        <SearchControl
          onLocationSelect={onLocationSelect}
        />
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        <LocationMarker
          onLocationSelect={onLocationSelect}
          initialLocation={initialLocation}
        />
      </MapContainer>
    </div>
  );
};

export default CheckpointMap;
