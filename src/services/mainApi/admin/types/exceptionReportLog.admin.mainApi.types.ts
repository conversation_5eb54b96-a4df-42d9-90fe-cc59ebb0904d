import { BaseResponse } from "../../types/base.mainApi.types";

export interface AdminExceptionReportLogEntry {
  checkpoint_id: string;
  checkpoint_name: string;
  serial_number: string;
  expected_visit_time: number;
  actual_visit_time: number;
  missed_visit_time: number;
}

export interface AdminExceptionReportLogListParams {
  page?: number;
  limit?: number;
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  order_by?: string;
  order_direction?: "ASC" | "DESC";
  branch_id?: string | number;
  role_id?: string | number;
  user_id?: string | number;
  user_labels?: string | number | (string | number)[];
  site_id?: string | number;
  site_labels?: string | number | (string | number)[];
  checkpoint_id?: string | number;
  checkpoint_labels?: string | number | (string | number)[];
  device_id?: string | number;
  device_labels?: string | number | (string | number)[];
}

export type AdminExceptionReportLogListResponse = BaseResponse<
  AdminExceptionReportLogEntry[]
>;