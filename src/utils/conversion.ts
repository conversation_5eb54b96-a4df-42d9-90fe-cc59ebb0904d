/**
 * Utility functions for number base conversions
 */

/**
 * Converts a hexadecimal string to a decimal number
 * @param hex - The hexadecimal string to convert (with or without '0x' prefix)
 * @returns The decimal representation as a number
 */
export function hexToDecimal(hex: string): number {
  // Remove '0x' prefix if present
  const cleanHex = hex.startsWith('0x') ? hex.slice(2) : hex;
  
  // Validate that the input is a valid hex string
  if (!/^[0-9A-Fa-f]+$/.test(cleanHex)) {
    throw new Error('Invalid hexadecimal string');
  }
  
  return parseInt(cleanHex, 16);
}

/**
 * Converts a decimal number to a hexadecimal string
 * @param decimal - The decimal number to convert
 * @param prefix - Whether to include '0x' prefix (default: false)
 * @returns The hexadecimal representation as a string
 */
export function decimalToHex(decimal: number, prefix: boolean = false): string {
  // Validate that the input is a valid number
  if (!Number.isInteger(decimal) || decimal < 0) {
    throw new Error('Invalid decimal number');
  }
  
  const hexString = decimal.toString(16).toUpperCase();
  return prefix ? `0x${hexString}` : hexString;
}
