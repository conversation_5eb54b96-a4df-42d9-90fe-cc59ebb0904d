import {useState} from "react";
import {AdminZone} from "../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import {<PERSON><PERSON>, <PERSON>, Tooltip} from "antd";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import {useTranslation} from "react-i18next";
import {FiChevronDown, FiChevronUp, FiClock, FiEdit, FiInfo, FiMail, FiMapPin, FiTag, FiTrash2} from "react-icons/fi";
import addSiteAdminSlice from "../../../../store/slices/admin/addSite.admin.slice";
import {useAppDispatch} from "../../../../store/hooks";

// Extend dayjs with relativeTime plugin
dayjs.extend(relativeTime);

interface Props {
  site: AdminZone;
}

export default function SiteListItem({site}: Props) {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);

  const handleDelete = () => {
    console.log("Delete site:", site);
    // Implement delete functionality
  };

  const onEdit = () => {
    dispatch(addSiteAdminSlice.actions.openEdit(site));
  };

  const hasIntervalSchedule =
    site.interval_active &&
    site.interval_start_time &&
    site.interval_end_time;

  // Function to truncate text and add ellipsis
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return "-";
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Header section */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <FiMapPin className="text-blue-500"/>
            {site.zone_name}
            {!site.active && (
              <Tag
                color="red"
                className="ml-2"
              >
                {t("site.list.status.inactive", "Inactive")}
              </Tag>
            )}
          </h3>
          {site.timezone?.timezone_name && (
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              {site.timezone.timezone_name}
            </p>
          )}
          <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            <p>
              {isDescriptionExpanded
                ? site.zone_description || "-"
                : truncateText(site.zone_description || "-", 100)}
            </p>
            {site.zone_description && site.zone_description.length > 100 && (
              <button
                onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                className="text-blue-500 hover:text-blue-600 flex items-center mt-1 text-xs font-medium"
              >
                {isDescriptionExpanded
                  ? <><FiChevronUp className="mr-1"/> {t("common.showLess", "Show less")}</>
                  : <><FiChevronDown className="mr-1"/> {t("common.showMore", "Show more")}</>
                }
              </button>
            )}
          </div>
        </div>
        <div>
          <Tooltip title={t("common.edit", "Edit")}>
            <Button
              type="text"
              icon={<FiEdit className="text-lg"/>}
              onClick={onEdit}
              className="text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400"
            />
          </Tooltip>
          <Tooltip title={t("common.remove", "Remove")}>
            <Button
              type="text"
              icon={<FiTrash2 className="text-lg"/>}
              onClick={handleDelete}
              className="text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400"
            />
          </Tooltip>
        </div>
      </div>

      {/* Content section */}
      <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Left column */}
        <div className="space-y-4">
          {/* Location section */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
              <FiMapPin className="text-red-500"/>
              {t("site.list.location", "Location")}
            </h4>
            <div className="grid grid-cols-1 gap-x-2 gap-y-1 text-xs p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="flex items-center gap-1">
                <span className="text-gray-600 dark:text-gray-300">
                  {t("site.list.address", "Address")}:
                </span>
                <span className="font-medium">
                  {site.zone_address || "-"}
                </span>
              </div>
              <div className="flex items-center gap-1 mt-1">
                <span className="text-gray-600 dark:text-gray-300">
                  {t("site.list.coordinates", "Coordinates")}:
                </span>
                <span className="font-medium">
                  {site.latitude.toFixed(6)}, {site.longitude.toFixed(6)}
                </span>
              </div>
            </div>
          </div>

          {/* Recipients section */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
              <FiMail className="text-purple-500"/>
              {t("site.list.recipients", "Recipients")}
            </h4>
            <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <div className="text-xs">
                <span className="text-gray-600 dark:text-gray-300">
                  {site.recipients?.length || 0} {t("site.list.recipientCount", "recipient", {count: site.recipients?.length || 0})}
                </span>
                {site.subject && (
                  <div className="mt-1 flex items-center gap-1">
                    <span className="text-gray-600 dark:text-gray-300">
                      {t("site.list.subject", "Subject")}:
                    </span>
                    <span className="font-medium">
                      {site.subject}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Right column */}
        <div className="space-y-4">
          {/* Schedule section */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
              <FiClock className="text-blue-500"/>
              {t("site.list.schedule", "Schedule")}
            </h4>
            <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              {hasIntervalSchedule ? (
                <div className="text-xs">
                  <div className="flex items-center gap-1">
                    <span className="text-gray-600 dark:text-gray-300">
                      {t("site.list.activeHours", "Active hours")}:
                    </span>
                    <span className="font-medium">
                      {site.interval_start_time} - {site.interval_end_time}
                    </span>
                  </div>
                </div>
              ) : (
                <div className="text-xs text-gray-600 dark:text-gray-300">
                  {t("site.list.noSchedule", "No active schedule")}
                </div>
              )}
            </div>
          </div>

          {/* Last update section */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
              <FiInfo className="text-yellow-500"/>
              {t("site.list.status", "Status")}
            </h4>
            <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
              <Tooltip title={new Date(site.updated_at).toLocaleString()}>
                <div className="text-xs flex items-center gap-1">
                  <span className="text-gray-600 dark:text-gray-300">
                    {t("site.list.lastUpdate", "Last update")}:
                  </span>
                  <span className="font-medium">
                    {dayjs(site.updated_at).fromNow()}
                  </span>
                </div>
              </Tooltip>
            </div>
          </div>

          {/* Labels section */}
          {site.labels && site.labels.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                <FiTag className="text-green-500"/>
                {t("common.labels", "Labels")}
              </h4>
              <div className="flex flex-wrap gap-2 p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                {site.labels.map((label) => (
                  <Tag
                    key={label.id}
                    className="rounded-full m-0"
                    color={label.active ? "processing" : "default"}
                  >
                    {label.label_name}
                  </Tag>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer section */}
      <div
        className="px-4 py-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400">
        {t("common.created", "Created")}{" "}
        {dayjs(site.created_at).format("DD MMM YYYY HH:mm")}
      </div>
    </div>
  );
}
