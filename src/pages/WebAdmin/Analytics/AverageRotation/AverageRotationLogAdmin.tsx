import { useTranslation } from "react-i18next";
import {
  Md<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Md<PERSON><PERSON>erList,
} from "react-icons/md";
import { useEffect, useRef } from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import ActiveFilters, {
  FilterGroup,
  createStartDateFilter,
  createEndDateFilter,
  createStartTimeFilter,
  createEndTimeFilter,
  createSimpleFilter,
  createLabelFilter,
} from "../../../../components/Admin/Common/ActiveFilters";
import { <PERSON><PERSON>, Drawer } from "antd";
import { useState } from "react";
import AnalyticDrawerForm, {
  AnalyticsFilterState,
} from "../../Analytics/Components/AnalyticDrawerForm";
import { EReportType } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { AdminBranch } from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks";
import { useParams } from "react-router-dom";
import AverageRotationLogList from "./Components/AverageRotationLogList";
import AverageRotationLogExportButton from "./Components/AverageRotationLogExportButton";
import averageRotationLogAdminSlice, {
  fetchAverageRotationLogs,
} from "../../../../store/slices/admin/averageRotationLog.admin.slice";
import { AdminZone } from "../../../../services/mainApi/admin/types/zone.admin.mainApi.types";
import { AdminLabel } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types";

interface FilterState {
  site: null | AdminZone;
  siteLabels: AdminLabel[];
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  selectedBranch: AdminBranch | null;
}

const AverageRotationLogAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const isFetchingRef = useRef(false);

  // Get branch code from URL params
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  // Redux state
  // We need to check if the reducer is registered in the store
  const averageRotationLogState = useAppSelector(
    (state) => state.averageRotationLogAdmin
  );
  const loading = averageRotationLogState?.loading || false;
  const pagination =
    averageRotationLogState?.pagination || {
      page: 1,
      limit: 10,
      total: 0,
    };
  const averageRotationLogs =
    averageRotationLogState?.averageRotationLogs || [];
  const selectedSite =
    averageRotationLogState?.selectedSite || null;
  const selectedSiteLabels =
    averageRotationLogState?.selectedSiteLabels || [];
  const selectedBranch =
    averageRotationLogState?.selectedBranch || null;
  const filterState = averageRotationLogState?.filter;

  // Load data on mount and when filters change
  useEffect(() => {
    if (branchCode && !isFetchingRef.current) {
      isFetchingRef.current = true;
      dispatch(
        fetchAverageRotationLogs({
          branchCode,
          filter: {
            page: filterState.page,
            limit: filterState.limit,
            startDate: filterState.startDate,
            endDate: filterState.endDate,
            startTime: filterState.startTime,
            endTime: filterState.endTime,
            branchId: filterState.branchId,
            siteId: filterState.siteId,
            orderBy: filterState.orderBy,
            orderDirection: filterState.orderDirection,
            siteLabels: filterState.siteLabels,
            roleId: filterState.roleId,
          },
        })
      ).finally(() => {
        isFetchingRef.current = false;
      });
    }
  }, [
    dispatch,
    branchCode,
    filterState.page,
    filterState.limit,
    filterState.startDate,
    filterState.endDate,
    filterState.startTime,
    filterState.endTime,
    filterState.branchId,
    filterState.siteId,
    filterState.orderBy,
    filterState.orderDirection,
    filterState.siteLabels,
    filterState,
  ]);

  const { actions } = averageRotationLogAdminSlice;

  // Convert applied filter state to format expected by drawer
  const getAppliedFilters = (): FilterState => {
    return {
      site: selectedSite,
      siteLabels: selectedSiteLabels,
      startDate: filterState.startDate,
      endDate: filterState.endDate,
      startTime: filterState.startTime,
      endTime: filterState.endTime,
      selectedBranch: selectedBranch,
    };
  };

  // Apply filters from the drawer
  const handleApplyFilters = (
    newFilters: AnalyticsFilterState
  ) => {
    // Update redux state
    dispatch(
      actions.setSelectedSite(newFilters.site || null)
    );
    dispatch(
      actions.setSelectedSiteLabels(
        newFilters.siteLabels || []
      )
    );
    dispatch(
      actions.setSelectedBranch(
        newFilters.selectedBranch || null
      )
    );
    dispatch(
      actions.setStartDate(newFilters.startDate || null)
    );
    dispatch(
      actions.setEndDate(newFilters.endDate || null)
    );
    dispatch(
      actions.setStartTime(newFilters.startTime || null)
    );
    dispatch(
      actions.setEndTime(newFilters.endTime || null)
    );

    // Reset page to 1
    dispatch(actions.setPage(1));

    setDrawerVisible(false);
  };

  // Handle page change
  const handlePageChange = (
    page: number,
    pageSize?: number
  ) => {
    if (pageSize && pageSize !== filterState.limit) {
      dispatch(actions.setLimit(pageSize));
    }
    dispatch(actions.setPage(page));
  };

  // Check if there are any active filters
  const hasActiveFilters = (): boolean => {
    // Check if any filter is active
    if (
      filterState.startDate ||
      filterState.endDate ||
      filterState.startTime ||
      filterState.endTime ||
      filterState.branchId ||
      filterState.siteId ||
      filterState.siteLabels.length > 0
    ) {
      return true;
    }

    return false;
  };

  // Get filter groups configuration for ActiveFilters component
  const getFilterGroups = (): FilterGroup[] => {
    const appliedFilters = getAppliedFilters();

    return [
      {
        key: "datetime",
        filters: [
          createStartDateFilter(appliedFilters.startDate),
          createEndDateFilter(appliedFilters.endDate),
          createStartTimeFilter(appliedFilters.startTime),
          createEndTimeFilter(appliedFilters.endTime),
        ],
      },
      {
        key: "other",
        filters: [
          createSimpleFilter(
            "site",
            "site",
            appliedFilters.site,
            "site_name"
          ),
          createSimpleFilter(
            "selectedBranch",
            "selectedBranch",
            appliedFilters.selectedBranch,
            "branch_name"
          ),
        ],
      },
      {
        key: "siteLabels",
        filters: [
          createLabelFilter(
            "siteLabels",
            "siteLabels",
            appliedFilters.siteLabels
          ),
        ],
      },
    ];
  };

  const handleClearAll = () => {
    dispatch(actions.clearFilters());
  };

  const handleRemoveFilter = (
    filterKey: keyof FilterState
  ) => {
    switch (filterKey) {
      case "site":
        dispatch(actions.setSelectedSite(null));
        break;
      case "selectedBranch":
        dispatch(actions.setSelectedBranch(null));
        break;
      case "startDate":
        dispatch(actions.setStartDate(null));
        break;
      case "endDate":
        dispatch(actions.setEndDate(null));
        break;
      case "startTime":
        dispatch(actions.setStartTime(null));
        break;
      case "endTime":
        dispatch(actions.setEndTime(null));
        break;
    }
  };

  // Handle clicking on an active filter to edit it
  const handleEditFilter = () => {
    // We only need to open the drawer
    setDrawerVisible(true);
  };

  // Convert FilterState to AnalyticsFilterState for the drawer form
  const getInitialDrawerValues =
    (): Partial<AnalyticsFilterState> => {
      const appliedFilters = getAppliedFilters();

      return {
        startDate: appliedFilters.startDate || "",
        endDate: appliedFilters.endDate || "",
        startTime: appliedFilters.startTime || "",
        endTime: appliedFilters.endTime || "",
        selectedBranch:
          appliedFilters.selectedBranch || null,
        site: appliedFilters.site,
      };
    };

  // Create a reusable function to get filter info for export button
  const getFilterInfo = (): {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    selectedBranch: AdminBranch | null;
    zoneId: string | number | null;
    branchId: string | number | null;
  } => ({
    startDate:
      averageRotationLogState?.filter.startDate || null,
    endDate:
      averageRotationLogState?.filter.endDate || null,
    startTime:
      averageRotationLogState?.filter.startTime || null,
    endTime:
      averageRotationLogState?.filter.endTime || null,
    selectedBranch:
      averageRotationLogState?.selectedBranch || null,
    zoneId:
      averageRotationLogState?.selectedSite?.id || null,
    branchId:
      averageRotationLogState?.selectedBranch?.id || null,
  });

  return (
    <WebAdminLayout activePage="average-rotation-log">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdRotateRight />}
            title={t(
              "averageRotationLog.title",
              "Average Rotation Log"
            )}
            description={t(
              "averageRotationLog.description",
              "View average rotation time and statistics"
            )}
            actions={[
              // Desktop view - show all buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <AverageRotationLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                />
                <Button
                  key="filter"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button and dropdown for downloads
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <AverageRotationLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                  buttonText={t(
                    "common.download",
                    "Download"
                  )}
                />
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList />}
                  size="large"
                  type="primary"
                  className="bg-indigo-600 hover:bg-indigo-700 border-0 shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters using the reusable component */}
          <ActiveFilters<FilterState>
            filterGroups={getFilterGroups()}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAll}
            onShowFilters={() => {
              setDrawerVisible(true);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Results List */}
          <AverageRotationLogList
            averageRotationLogs={averageRotationLogs}
            loading={loading}
            total={pagination.total || 0}
            page={pagination.page || 1}
            limit={pagination.limit || 10}
            onPageChange={handlePageChange}
          />
        </div>

        {/* Filter Drawer - Using the refactored component with initialValues */}
        <Drawer
          title={
            <div className="text-lg font-semibold text-gray-800 dark:text-white">
              {t(
                "averageRotationLog.filters.title",
                "Filter Average Rotation Log"
              )}
            </div>
          }
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          className="dark:bg-gray-800"
        >
          <AnalyticDrawerForm
            reportType={EReportType.AVERAGE_ROTATION}
            onApply={handleApplyFilters}
            onClose={() => setDrawerVisible(false)}
            initialValues={getInitialDrawerValues()}
          />
        </Drawer>
      </div>
    </WebAdminLayout>
  );
};

export default AverageRotationLogAdmin;
