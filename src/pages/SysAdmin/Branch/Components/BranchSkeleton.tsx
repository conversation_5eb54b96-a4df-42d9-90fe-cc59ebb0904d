import { Skeleton, Card } from "antd";

export const BranchSkeleton = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
      {[...Array(6)].map((_, index) => (
        <Card
          key={index}
          className="shadow-sm hover:shadow-md transition-shadow duration-200"
        >
          <Skeleton
            active
            paragraph={{ rows: 3 }}
          />
          <div className="flex justify-end mt-4">
            <Skeleton.Button
              active
              size="small"
              className="mr-2"
            />
            <Skeleton.Button
              active
              size="small"
            />
          </div>
        </Card>
      ))}
    </div>
  );
};
