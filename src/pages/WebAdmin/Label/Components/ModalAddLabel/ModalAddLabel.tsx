import { App, Form, Input, Modal } from "antd";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../store/hooks.ts";
import { close } from "../../../../../store/slices/admin/addLabel.admin.slice.ts";
import { useCallback, useEffect } from "react";
import { useParams } from "react-router-dom";
import labelAdminMainApi from "../../../../../services/mainApi/admin/label.admin.mainApi.ts";
import { fetchLabelsAdmin } from "../../../../../store/slices/admin/label.admin.slice.ts";
import { useTranslation } from "react-i18next";

interface FormValues {
  label_name: string;
  label_description: string;
}

const ModalAddLabel = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { message } = App.useApp();
  const { isOpen, mode, selectedLabel } = useAppSelector(
    (state) => state.addLabelAdmin
  );
  const { branchCode } = useParams();
  const [form] = Form.useForm<FormValues>();

  useEffect(() => {
    if (isOpen && mode === "edit" && selectedLabel) {
      form.setFieldsValue({
        label_name: selectedLabel.label_name,
        label_description: selectedLabel.label_description,
      });
    } else {
      form.resetFields();
    }
  }, [form, isOpen, mode, selectedLabel]);

  const handleSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields();

      if (mode === "create") {
        await labelAdminMainApi.createLabel(
          branchCode || "",
          values
        );
        message.success(t("label.modal.add.success"));
      } else {
        await labelAdminMainApi.updateLabel(
          branchCode || "",
          selectedLabel?.id || "",
          values
        );
        message.success(t("label.modal.edit.success"));
      }

      dispatch(close());
      dispatch(fetchLabelsAdmin(branchCode || ""));
    } catch (error) {
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error(
          t(
            mode === "create"
              ? "label.modal.add.error"
              : "label.modal.edit.error"
          )
        );
      }
    }
  }, [
    branchCode,
    dispatch,
    form,
    message,
    mode,
    selectedLabel?.id,
    t,
  ]);

  return (
    <Modal
      title={
        <div className="text-lg font-semibold">
          {mode === "create"
            ? t("label.modal.add.title")
            : t("label.modal.edit.title")}
        </div>
      }
      open={isOpen}
      onOk={handleSubmit}
      onCancel={() => dispatch(close())}
      className="rounded-xl"
      okButtonProps={{
        className: "bg-blue-500 hover:bg-blue-600 border-0",
      }}
      okText={t(
        mode === "create"
          ? "common.create"
          : "common.update"
      )}
      cancelText={t("common.cancel")}
    >
      <Form
        form={form}
        layout="vertical"
        className="mt-4 space-y-6"
      >
        <div className="space-y-4">
          <Form.Item
            label={
              <span className="text-sm font-medium">
                {t("label.modal.form.name.label")}
                <span className="text-red-500 ml-1">*</span>
              </span>
            }
            name="label_name"
            rules={[
              {
                required: true,
                message: t(
                  "label.modal.form.name.required"
                ),
              },
            ]}
          >
            <Input
              placeholder={t(
                "label.modal.form.name.placeholder"
              )}
              className="rounded-lg"
            />
          </Form.Item>

          <Form.Item
            label={
              <span className="text-sm font-medium">
                {t("label.modal.form.description.label")}
                <span className="text-red-500 ml-1">*</span>
              </span>
            }
            name="label_description"
            rules={[
              {
                required: true,
                message: t(
                  "label.modal.form.description.required"
                ),
              },
            ]}
          >
            <Input.TextArea
              placeholder={t(
                "label.modal.form.description.placeholder"
              )}
              rows={4}
              className="rounded-lg"
            />
          </Form.Item>
        </div>
      </Form>
    </Modal>
  );
};

export default ModalAddLabel;
