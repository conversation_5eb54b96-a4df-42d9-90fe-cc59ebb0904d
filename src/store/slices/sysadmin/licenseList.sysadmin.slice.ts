import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState } from "../..";
import licenseSysAdminMainApi from "../../../services/mainApi/sysadmin/license.sysadmin.mainApi";
import { License } from "../../../services/mainApi/types/license.mainApi.types";

export type SortField = "created_at" | "license_name";
export type SortOrder = "asc" | "desc";

// Types
interface LicenseState {
  items: License[];
  loading: boolean;
  error: string | null;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  filters: {
    searchQuery: string;
    sortField: SortField;
    sortOrder: SortOrder;
    active: boolean | undefined;
  };
}

// Initial state
const initialState: LicenseState = {
  items: [],
  loading: false,
  error: null,
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0,
  },
  filters: {
    searchQuery: "",
    sortField: "created_at",
    sortOrder: "desc",
    active: undefined,
  },
};

// Async thunks
export const fetchLicenses = createAsyncThunk(
  "license/fetchLicenses",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const { pagination, filters } = state.licenseSysadmin;

      const response = await licenseSysAdminMainApi.getList({
        search: filters.searchQuery,
        active: filters.active,
        page: pagination.current,
        limit: pagination.pageSize,
        order_by: filters.sortField,
        order_direction: filters.sortOrder.toUpperCase() as "ASC" | "DESC",
      });

      return {
        data: response.data || [],
        total: response.meta.total || 0,
      };
    } catch {
      return rejectWithValue("Failed to fetch licenses");
    }
  }
);

// Slice
const licenseListSysadminSlice = createSlice({
  name: "license",
  initialState,
  reducers: {
    setSearchQuery: (
      state,
      action: PayloadAction<string>
    ) => {
      state.filters.searchQuery = action.payload;
      state.pagination.current = 1; // Reset to first page
    },
    setActiveFilter: (
      state, 
      action: PayloadAction<boolean | undefined>
    ) => {
      state.filters.active = action.payload;
      state.pagination.current = 1; // Reset to first page
    },
    setSort: (
      state,
      action: PayloadAction<{
        field: SortField;
        order: SortOrder;
      }>
    ) => {
      state.filters.sortField = action.payload.field;
      state.filters.sortOrder = action.payload.order;
      state.pagination.current = 1; // Reset to first page
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.pagination.current = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchLicenses.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLicenses.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.data;
        state.pagination.total = action.payload.total;
      })
      .addCase(fetchLicenses.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Actions
export const { setSearchQuery, setActiveFilter, setSort, setPage } =
  licenseListSysadminSlice.actions;

// Selectors
export const selectLicenses = (state: RootState) =>
  state.licenseSysadmin.items;
export const selectLicenseLoading = (state: RootState) =>
  state.licenseSysadmin.loading;
export const selectLicenseError = (state: RootState) =>
  state.licenseSysadmin.error;
export const selectLicensePagination = (state: RootState) =>
  state.licenseSysadmin.pagination;
export const selectLicenseFilters = (state: RootState) =>
  state.licenseSysadmin.filters;

export default licenseListSysadminSlice; 