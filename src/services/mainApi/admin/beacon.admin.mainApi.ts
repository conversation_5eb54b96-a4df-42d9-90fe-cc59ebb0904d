import baseMainApi from "../base.mainApi";
import { BaseGetRequest } from "../types/base.mainApi.types";
import { AdminBeaconListResponse } from "./types/beacon.admin.mainApi.types";

const beaconAdminMainApi = {
  getBeacons: async (
    branchCode: string,
    params?: BaseGetRequest
  ) => {
    const response =
      await baseMainApi.get<AdminBeaconListResponse>(
        `/web-api/admin/${branchCode}/beacon`,
        { params }
      );
    return response.data;
  },
};

export default beaconAdminMainApi;
