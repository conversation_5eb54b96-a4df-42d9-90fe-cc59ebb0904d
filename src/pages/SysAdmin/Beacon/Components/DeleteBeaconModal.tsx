import { App, Modal } from "antd";
import { Beacon } from "../../../../services/mainApi/sysadmin/types/beacon.mainApi.types";
import { useAppDispatch } from "../../../../store/hooks";
import { fetchBeacons } from "../../../../store/slices/sysadmin/beaconList.sysadmin.slice";

interface DeleteBeaconModalProps {
  visible: boolean;
  beacon: Beacon | null;
  onClose: () => void;
  onSuccess?: () => Promise<void>;
}

export default function DeleteBeaconModal({
  visible,
  beacon,
  onClose,
  onSuccess,
}: DeleteBeaconModalProps) {
  const { message } = App.useApp();
  const dispatch = useAppDispatch();

  const handleDelete = async () => {
    if (!beacon) return;

    try {
      // TODO: Implement API call to delete beacon
      // await beaconSysAdminMainApi.delete(beacon.id);
      
      message.success(`Beacon "${beacon.beacon_name}" has been deleted.`);
      
      // Refresh beacon list
      dispatch(fetchBeacons());
      
      if (onSuccess) {
        await onSuccess();
      } else {
        onClose();
      }
    } catch {
      message.error("Failed to delete beacon. Please try again.");
    }
  };

  return (
    <Modal
      title="Delete Beacon"
      open={visible}
      onOk={handleDelete}
      onCancel={onClose}
      okText="Delete"
      okButtonProps={{
        danger: true,
      }}
      cancelButtonProps={{
        className: "border-gray-200 text-gray-700",
      }}
    >
      <p>
        Are you sure you want to delete beacon{" "}
        <strong>{beacon?.beacon_name}</strong>?
      </p>
      <p className="text-gray-500 mt-2">
        This action cannot be undone.
      </p>
    </Modal>
  );
} 