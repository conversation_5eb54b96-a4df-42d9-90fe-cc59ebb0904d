import {
  App,
  Form,
  Input,
  InputNumber,
  Modal,
  Switch,
} from "antd";
import { useEffect } from "react";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks.ts";
import { fetchLicenses } from "../../../../store/slices/sysadmin/licenseList.sysadmin.slice.ts";
import addLicenseSysadminSlice, {
  createLicense,
  updateLicense,
} from "../../../../store/slices/sysadmin/addLicense.sysadmin.slice.ts";
import ErrorMessage from "../../../../components/Common/ErrorMessage/ErrorMessage.tsx";

export default function AddEditLicenseModal() {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  const dispatch = useAppDispatch();

  // Get state from Redux
  const {
    open,
    mode,
    licenseEdit,
    loading,
    errorMessage,
    license_name,
    max_subbranch,
    max_user,
    max_label,
    max_scheduler,
    max_alert,
    max_device,
    max_site,
    max_checkpoint,
    max_geofence,
    max_form,
    max_task,
    max_activity,
    max_beacon,
    active,
  } = useAppSelector((state) => state.addLicenseSysadmin);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // Update form values to Redux state
      dispatch(
        addLicenseSysadminSlice.actions.setLicenseName(
          values.license_name
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxSubbranch(
          values.max_subbranch
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxUser(
          values.max_user
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxLabel(
          values.max_label
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxScheduler(
          values.max_scheduler
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxAlert(
          values.max_alert
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxDevice(
          values.max_device
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxSite(
          values.max_site
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxCheckpoint(
          values.max_checkpoint
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxGeofence(
          values.max_geofence
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxForm(
          values.max_form
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxTask(
          values.max_task
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxActivity(
          values.max_activity
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setMaxBeacon(
          values.max_beacon
        )
      );
      dispatch(
        addLicenseSysadminSlice.actions.setActive(
          values.active
        )
      );

      if (mode === "update" && licenseEdit) {
        // Update existing license
        const update = await dispatch(
          updateLicense(licenseEdit.id)
        );
        if (update.meta.requestStatus === "rejected") {
          throw new Error("Failed to update license");
        }
        message.success(
          `License "${values.license_name}" has been updated successfully`
        );
      } else {
        // Create new license
        const create = await dispatch(createLicense());
        if (create.meta.requestStatus === "rejected") {
          throw new Error("Failed to create license");
        }
        message.success(
          `New license "${values.license_name}" has been created successfully`
        );
      }

      // Refresh license list
      dispatch(fetchLicenses());

      form.resetFields();
      dispatch(addLicenseSysadminSlice.actions.close());
    } catch {
      message.error(
        "An unexpected error occurred while saving the license"
      );
    }
  };

  // Handle close modal
  const handleClose = () => {
    dispatch(addLicenseSysadminSlice.actions.close());
  };

  // Reset form when modal opens or license changes
  useEffect(() => {
    if (open) {
      // Reset form with license data if editing
      if (mode === "update" && licenseEdit) {
        form.setFieldsValue({
          license_name: license_name,
          max_subbranch: max_subbranch,
          max_user: max_user,
          max_label: max_label,
          max_scheduler: max_scheduler,
          max_alert: max_alert,
          max_device: max_device,
          max_site: max_site,
          max_checkpoint: max_checkpoint,
          max_geofence: max_geofence,
          max_form: max_form,
          max_task: max_task,
          max_activity: max_activity,
          max_beacon: max_beacon,
          active: active,
        });
      } else {
        // Default values for new license
        form.setFieldsValue({
          active: true,
          max_subbranch: 0,
          max_user: 0,
          max_label: 0,
          max_scheduler: 0,
          max_alert: 0,
          max_device: 0,
          max_site: 0,
          max_checkpoint: 0,
          max_geofence: 0,
          max_form: 0,
          max_task: 0,
          max_activity: 0,
          max_beacon: 0,
        });
      }
    }
  }, [
    form,
    open,
    mode,
    licenseEdit,
    license_name,
    max_subbranch,
    max_user,
    max_label,
    max_scheduler,
    max_alert,
    max_device,
    max_site,
    max_checkpoint,
    max_geofence,
    max_form,
    max_task,
    max_activity,
    max_beacon,
    active,
  ]);

  return (
    <Modal
      title={
        <div className="text-lg font-semibold">
          {mode === "update"
            ? "Edit License"
            : "Add New License"}
        </div>
      }
      open={open}
      onCancel={handleClose}
      onOk={handleSubmit}
      width={700}
      className="rounded-xl"
      okButtonProps={{
        className: "bg-blue-500 hover:bg-blue-600 border-0",
      }}
      confirmLoading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        className="mt-4"
      >
        {/* License Information Section */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-4">
            License Information
          </h3>

          <Form.Item
            name="license_name"
            label="License Name"
            rules={[
              {
                required: true,
                message: "Please enter license name",
              },
            ]}
          >
            <Input
              className="rounded-lg"
              placeholder="Enter license name"
            />
          </Form.Item>

          {/* Limitations Section */}
          <h3 className="text-lg font-medium my-4">
            Feature Limitations
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              name="max_subbranch"
              label="Max Sub-branches"
              rules={[
                {
                  required: true,
                  message: "Please enter max sub-branches",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>

            <Form.Item
              name="max_user"
              label="Max Users"
              rules={[
                {
                  required: true,
                  message: "Please enter max users",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>

            <Form.Item
              name="max_label"
              label="Max Labels"
              rules={[
                {
                  required: true,
                  message: "Please enter max labels",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>

            <Form.Item
              name="max_scheduler"
              label="Max Schedulers"
              rules={[
                {
                  required: true,
                  message: "Please enter max schedulers",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>

            <Form.Item
              name="max_alert"
              label="Max Alerts"
              rules={[
                {
                  required: true,
                  message: "Please enter max alerts",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>

            <Form.Item
              name="max_device"
              label="Max Devices"
              rules={[
                {
                  required: true,
                  message: "Please enter max devices",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>

            <Form.Item
              name="max_site"
              label="Max Sites"
              rules={[
                {
                  required: true,
                  message: "Please enter max sites",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>

            <Form.Item
              name="max_checkpoint"
              label="Max Checkpoints"
              rules={[
                {
                  required: true,
                  message: "Please enter max checkpoints",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>

            <Form.Item
              name="max_geofence"
              label="Max Geofences"
              rules={[
                {
                  required: true,
                  message: "Please enter max geofences",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>

            <Form.Item
              name="max_form"
              label="Max Forms"
              rules={[
                {
                  required: true,
                  message: "Please enter max forms",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>

            <Form.Item
              name="max_task"
              label="Max Tasks"
              rules={[
                {
                  required: true,
                  message: "Please enter max tasks",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>

            <Form.Item
              name="max_activity"
              label="Max Activities"
              rules={[
                {
                  required: true,
                  message: "Please enter max activities",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>

            <Form.Item
              name="max_beacon"
              label="Max Beacons"
              rules={[
                {
                  required: true,
                  message: "Please enter max beacons",
                },
              ]}
            >
              <InputNumber
                className="w-full rounded-lg"
                min={0}
                placeholder="0"
              />
            </Form.Item>
          </div>

          <Form.Item
            name="active"
            label="Status"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </div>
      </Form>
      {errorMessage && (
        <div className="mt-4">
          <ErrorMessage message={errorMessage} />
        </div>
      )}
    </Modal>
  );
}
